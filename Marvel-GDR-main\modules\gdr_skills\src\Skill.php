<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

namespace Modules\Skills;

use Common\SimpleModelTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Traits\RecordTrait;

class Skill extends Model
{
    use SimpleModelTrait;
    use RecordTrait;
    use SoftDeletes;

    protected $table = 'gdr_skills';
    protected $primaryKey = 'id';
    protected $module = 'Skills';

    protected $guarded = [];

    public function getLivelliAttribute($value)
    {
        // Lara<PERSON> con il cast 'object' già decodifica il JSON,
        return is_null($value) ? [] : json_decode($value,true);
    }

    public static function build($nome = '', $descrizione = '')
    {
        $model = new static();

        $model->nome = $nome ?: '';
        $model->descrizione = $descrizione ?: '';
        $model->save();

        return $model;
    }

    public function save(array $options = [])
    {

        return parent::save($options);
    }

    // Attributi Eloquent

    public function getModuleAttribute()
    {
        return 'Skills';
    }
}
