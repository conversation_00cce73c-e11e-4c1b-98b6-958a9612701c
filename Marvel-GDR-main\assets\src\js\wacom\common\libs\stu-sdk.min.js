!function(t){!function(t){class e extends Error{constructor(t,e){if("string"!=typeof t&&null!==t||!(null!=e&&e.__classes&&e.__classes.indexOf("java.lang.Throwable")>=0||null!=e&&e instanceof Error||null===e))if("string"!=typeof t&&null!==t||void 0!==e)if((null!=t&&t.__classes&&t.__classes.indexOf("java.lang.Throwable")>=0||null!=t&&t instanceof Error||null===t)&&void 0===e){let t=arguments[0];super(t),this.message=t}else{if(void 0!==t||void 0!==e)throw new Error("invalid overload");super()}else{super(t),this.message=t}else{super(t),this.message=t}}}t.Exception=e,e.__class="com.WacomGSS.Exception",e.__interfaces=["java.io.Serializable"]}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){class e extends Error{constructor(t,e){if("string"!=typeof t&&null!==t||!(null!=e&&e.__classes&&e.__classes.indexOf("java.lang.Throwable")>=0||null!=e&&e instanceof Error||null===e))if("string"!=typeof t&&null!==t||void 0!==e)if((null!=t&&t.__classes&&t.__classes.indexOf("java.lang.Throwable")>=0||null!=t&&t instanceof Error||null===t)&&void 0===e){let t=arguments[0];super(t),this.message=t}else{if(void 0!==t||void 0!==e)throw new Error("invalid overload");super()}else{super(t),this.message=t}else{super(t),this.message=t}}}t.RuntimeException=e,e.__class="com.WacomGSS.RuntimeException",e.__interfaces=["java.io.Serializable"]}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){class o extends t.WacomGSS.Exception{constructor(t,e){if("string"!=typeof t&&null!==t||!(null!=e&&e.__classes&&e.__classes.indexOf("java.lang.Throwable")>=0||null!=e&&e instanceof Error||null===e))if("string"!=typeof t&&null!==t||void 0!==e)if((null!=t&&t.__classes&&t.__classes.indexOf("java.lang.Throwable")>=0||null!=t&&t instanceof Error||null===t)&&void 0===e){super(arguments[0])}else{if(void 0!==t||void 0!==e)throw new Error("invalid overload");super()}else{super(t)}else{super(t,e)}}}e.STUException=o,o.__class="com.WacomGSS.STU.STUException",o.__interfaces=["java.io.Serializable"]}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){let e;!function(t){t[t.RSA1024=0]="RSA1024",t[t.RSA1536=1]="RSA1536",t[t.RSA2048=2]="RSA2048"}(e=t.AsymmetricKeyType||(t.AsymmetricKeyType={}));class o{constructor(t,e,o){this._$ordinal=t,this._$name=e,void 0===this.value&&(this.value=0),this.value=o}getValue(){return this.value}name(){return this._$name}ordinal(){return this._$ordinal}compareTo(t){return this._$ordinal-(isNaN(t)?t._$ordinal:t)}}t.AsymmetricKeyType_$WRAPPER=o,e.__class="com.WacomGSS.STU.Protocol.AsymmetricKeyType",e.__interfaces=["java.lang.constant.Constable","java.lang.Comparable","java.io.Serializable"],e._$wrappers=[new o(0,"RSA1024",0),new o(1,"RSA1536",1),new o(2,"RSA2048",2)]}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){let e;!function(t){t[t.None=0]="None",t[t.PKCS1=1]="PKCS1",t[t.OAEP=2]="OAEP"}(e=t.AsymmetricPaddingType||(t.AsymmetricPaddingType={}));class o{constructor(t,e,o){this._$ordinal=t,this._$name=e,void 0===this.value&&(this.value=0),this.value=o}getValue(){return this.value}name(){return this._$name}ordinal(){return this._$ordinal}compareTo(t){return this._$ordinal-(isNaN(t)?t._$ordinal:t)}}t.AsymmetricPaddingType_$WRAPPER=o,e.__class="com.WacomGSS.STU.Protocol.AsymmetricPaddingType",e.__interfaces=["java.lang.constant.Constable","java.lang.Comparable","java.io.Serializable"],e._$wrappers=[new o(0,"None",0),new o(1,"PKCS1",1),new o(2,"OAEP",2)]}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{}e.STU500=115200,e.STU430_530=460800,t.BaudRate=e,e.__class="com.WacomGSS.STU.Protocol.BaudRate"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){let e;!function(t){t[t.Disable=0]="Disable",t[t.Enable=1]="Enable"}(e=t.BootScreenFlag||(t.BootScreenFlag={}));class o{constructor(t,e,o){this._$ordinal=t,this._$name=e,void 0===this.value&&(this.value=0),this.value=o}getValue(){return this.value}name(){return this._$name}ordinal(){return this._$ordinal}compareTo(t){return this._$ordinal-(isNaN(t)?t._$ordinal:t)}}t.BootScreenFlag_$WRAPPER=o,e.__class="com.WacomGSS.STU.Protocol.BootScreenFlag",e.__interfaces=["java.lang.constant.Constable","java.lang.Comparable","java.io.Serializable"],e._$wrappers=[new o(0,"Disable",0),new o(1,"Enable",1)]}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,e,o,a,n,r,i,c){if("number"!=typeof t&&null!==t||"number"!=typeof e&&null!==e||"number"!=typeof o&&null!==o||"number"!=typeof a&&null!==a||"number"!=typeof n&&null!==n||"number"!=typeof r&&null!==r||"number"!=typeof i&&null!==i||"number"!=typeof c&&null!==c){if("number"!=typeof t&&null!==t||"number"!=typeof e&&null!==e||"number"!=typeof o&&null!==o||"number"!=typeof a&&null!==a||"number"!=typeof n&&null!==n||"number"!=typeof r&&null!==r||"number"!=typeof i&&null!==i||"boolean"!=typeof c&&null!==c)throw new Error("invalid overload");{let c=arguments[7];void 0===this.tabletMaxX&&(this.tabletMaxX=0),void 0===this.tabletMaxY&&(this.tabletMaxY=0),void 0===this.tabletMaxPressure&&(this.tabletMaxPressure=0),void 0===this.screenWidth&&(this.screenWidth=0),void 0===this.screenHeight&&(this.screenHeight=0),void 0===this.maxReportRate&&(this.maxReportRate=0),void 0===this.resolution&&(this.resolution=0),void 0===this.zlibColorSupport&&(this.zlibColorSupport=!1),void 0===this.encodingFlag&&(this.encodingFlag=0),this.tabletMaxX=t,this.tabletMaxY=e,this.tabletMaxPressure=o,this.screenWidth=a,this.screenHeight=n,this.maxReportRate=r,this.resolution=i,this.zlibColorSupport=c,this.encodingFlag=c?1:0}}else{void 0===this.tabletMaxX&&(this.tabletMaxX=0),void 0===this.tabletMaxY&&(this.tabletMaxY=0),void 0===this.tabletMaxPressure&&(this.tabletMaxPressure=0),void 0===this.screenWidth&&(this.screenWidth=0),void 0===this.screenHeight&&(this.screenHeight=0),void 0===this.maxReportRate&&(this.maxReportRate=0),void 0===this.resolution&&(this.resolution=0),void 0===this.zlibColorSupport&&(this.zlibColorSupport=!1),void 0===this.encodingFlag&&(this.encodingFlag=0),this.tabletMaxX=t,this.tabletMaxY=e,this.tabletMaxPressure=o,this.screenWidth=a,this.screenHeight=n,this.maxReportRate=r,this.resolution=i,this.zlibColorSupport=0!=(1&c),this.encodingFlag=c}}getTabletMaxX(){return this.tabletMaxX}getTabletMaxY(){return this.tabletMaxY}getTabletMaxPressure(){return this.tabletMaxPressure}getScreenWidth(){return this.screenWidth}getScreenHeight(){return this.screenHeight}getMaxReportRate(){return this.maxReportRate}getResolution(){return this.resolution}getZlibColorSupport(){return this.zlibColorSupport}getEncodingFlag(){return this.encodingFlag}}t.Capability=e,e.__class="com.WacomGSS.STU.Protocol.Capability"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){let e;!function(t){t[t.HID=0]="HID",t[t.Serial=1]="Serial"}(e=t.DefaultMode||(t.DefaultMode={}));class o{constructor(t,e,o){this._$ordinal=t,this._$name=e,void 0===this.value&&(this.value=0),this.value=o}getValue(){return this.value}name(){return this._$name}ordinal(){return this._$ordinal}compareTo(t){return this._$ordinal-(isNaN(t)?t._$ordinal:t)}}t.DefaultMode_$WRAPPER=o,e.__class="com.WacomGSS.STU.Protocol.DefaultMode",e.__interfaces=["java.lang.constant.Constable","java.lang.Comparable","java.io.Serializable"],e._$wrappers=[new o(0,"HID",1),new o(1,"Serial",2)]}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,o){if(!(null!=t&&t instanceof Array&&(0==t.length||null==t[0]||"number"==typeof t[0])||null===t)||"number"!=typeof o&&null!==o){if(!(null!=t&&t instanceof Array&&(0==t.length||null==t[0]||"number"==typeof t[0])||null===t)||void 0!==o)throw new Error("invalid overload");if(void 0===this.value&&(this.value=null),t.length!==e.myLength)throw Object.defineProperty(new Error(e.txtError),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});this.value=t.slice(0)}else{if(void 0===this.value&&(this.value=null),t.length-o<e.myLength)throw Object.defineProperty(new Error(e.txtError),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});this.value=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(e.myLength),((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(t,o,this.value,0,e.myLength)}}getValue(){return this.value.slice(0)}}e.txtError="invalid length",e.myLength=16,t.PublicKey=e,e.__class="com.WacomGSS.STU.Protocol.PublicKey"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o extends t.WacomGSS.STU.Protocol.PublicKey{constructor(t,e){if(!(null!=t&&t instanceof Array&&(0==t.length||null==t[0]||"number"==typeof t[0])||null===t)||"number"!=typeof e&&null!==e){if(!(null!=t&&t instanceof Array&&(0==t.length||null==t[0]||"number"==typeof t[0])||null===t)||void 0!==e)throw new Error("invalid overload");super(t)}else{super(t,e)}}static reportId_$LI$(){return null==o.reportId&&(o.reportId=t.WacomGSS.STU.Protocol.ReportId.DevicePublicKey_$LI$()),o.reportId}}o.reportSize=16,e.DevicePublicKey=o,o.__class="com.WacomGSS.STU.Protocol.DevicePublicKey"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,o){if(!(null!=t&&t instanceof Array&&(0==t.length||null==t[0]||"number"==typeof t[0])||null===t)||"number"!=typeof o&&null!==o){if(!(null!=t&&t instanceof Array&&(0==t.length||null==t[0]||"number"==typeof t[0])||null===t)||void 0!==o)throw new Error("invalid overload");if(void 0===this.value&&(this.value=null),t.length!==e.myLength)throw Object.defineProperty(new Error(e.txtError),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});this.value=t.slice(0)}else{if(void 0===this.value&&(this.value=null),t.length-o<e.myLength)throw Object.defineProperty(new Error(e.txtError),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});this.value=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(e.myLength),((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(t,o,this.value,0,e.myLength)}}getValue(){return this.value.slice(0)}}e.txtError="invalid length",e.myLength=2,t.DHbase=e,e.__class="com.WacomGSS.STU.Protocol.DHbase"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,o){if(!(null!=t&&t instanceof Array&&(0==t.length||null==t[0]||"number"==typeof t[0])||null===t)||"number"!=typeof o&&null!==o){if(!(null!=t&&t instanceof Array&&(0==t.length||null==t[0]||"number"==typeof t[0])||null===t)||void 0!==o)throw new Error("invalid overload");if(void 0===this.value&&(this.value=null),t.length!==e.myLength)throw Object.defineProperty(new Error(e.txtError),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});this.value=t.slice(0)}else{if(void 0===this.value&&(this.value=null),t.length-o<e.myLength)throw Object.defineProperty(new Error(e.txtError),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});this.value=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(e.myLength),((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(t,o,this.value,0,e.myLength)}}getValue(){return this.value.slice(0)}isEmpty(){let t=!0;for(let e=0;e<this.value.length;e++){if(0!==this.value[e]){t=!1;break}}return t}}e.txtError="invalid length",e.myLength=16,t.DHprime=e,e.__class="com.WacomGSS.STU.Protocol.DHprime"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{static EncodingFlag_Zlib_$LI$(){return null==e.EncodingFlag_Zlib&&(e.EncodingFlag_Zlib=1),e.EncodingFlag_Zlib}static EncodingFlag_1bit_$LI$(){return null==e.EncodingFlag_1bit&&(e.EncodingFlag_1bit=2),e.EncodingFlag_1bit}static EncodingFlag_16bit_$LI$(){return null==e.EncodingFlag_16bit&&(e.EncodingFlag_16bit=4),e.EncodingFlag_16bit}static EncodingFlag_24bit_$LI$(){return null==e.EncodingFlag_24bit&&(e.EncodingFlag_24bit=8),e.EncodingFlag_24bit}}t.EncodingFlag=e,e.__class="com.WacomGSS.STU.Protocol.EncodingFlag"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){let e;!function(t){t[t.EncodingMode_1bit=0]="EncodingMode_1bit",t[t.EncodingMode_1bit_ZLib=1]="EncodingMode_1bit_ZLib",t[t.EncodingMode_16bit=2]="EncodingMode_16bit",t[t.EncodingMode_24bit=3]="EncodingMode_24bit",t[t.EncodingMode_1bit_Bulk=4]="EncodingMode_1bit_Bulk",t[t.EncodingMode_16bit_Bulk=5]="EncodingMode_16bit_Bulk",t[t.EncodingMode_24bit_Bulk=6]="EncodingMode_24bit_Bulk"}(e=t.EncodingMode||(t.EncodingMode={}));class o{constructor(t,e,o){this._$ordinal=t,this._$name=e,void 0===this.value&&(this.value=0),this.value=o}static EncodingMode_Raw_$LI$(){return null==o.EncodingMode_Raw&&(o.EncodingMode_Raw=0),o.EncodingMode_Raw}static EncodingMode_Zlib_$LI$(){return null==o.EncodingMode_Zlib&&(o.EncodingMode_Zlib=1),o.EncodingMode_Zlib}static EncodingMode_16bit_565_$LI$(){return null==o.EncodingMode_16bit_565&&(o.EncodingMode_16bit_565=2),o.EncodingMode_16bit_565}static EncodingMode_Bulk_$LI$(){return null==o.EncodingMode_Bulk&&(o.EncodingMode_Bulk=16),o.EncodingMode_Bulk}getValue(){return this.value}name(){return this._$name}ordinal(){return this._$ordinal}compareTo(t){return this._$ordinal-(isNaN(t)?t._$ordinal:t)}}t.EncodingMode_$WRAPPER=o,e.__class="com.WacomGSS.STU.Protocol.EncodingMode",e.__interfaces=["java.lang.constant.Constable","java.lang.Comparable","java.io.Serializable"],e._$wrappers=[new o(0,"EncodingMode_1bit",0),new o(1,"EncodingMode_1bit_ZLib",1),new o(2,"EncodingMode_16bit",2),new o(3,"EncodingMode_24bit",4),new o(4,"EncodingMode_1bit_Bulk",16),new o(5,"EncodingMode_16bit_Bulk",18),new o(6,"EncodingMode_24bit_Bulk",20)]}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{constructor(e,a,n,r,i,c){if("number"!=typeof e&&null!==e||"number"!=typeof a&&null!==a||"number"!=typeof n&&null!==n||!(null!=r&&r instanceof Array&&(0==r.length||null==r[0]||"number"==typeof r[0])||null===r)||"number"!=typeof i&&null!==i||"number"!=typeof c&&null!==c){if("number"!=typeof e&&null!==e||"number"!=typeof a&&null!==a||"number"!=typeof n&&null!==n||!(null!=r&&r instanceof Array&&(0==r.length||null==r[0]||"number"==typeof r[0])||null===r)||void 0!==i||void 0!==c)throw new Error("invalid overload");if(void 0===this.encryptionCommandNumber&&(this.encryptionCommandNumber=0),void 0===this.parameter&&(this.parameter=0),void 0===this.lengthOrIndex&&(this.lengthOrIndex=0),void 0===this.data&&(this.data=null),null!=r&&r.length>o.maxDataLength)throw Object.defineProperty(new Error(o.txtError),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});this.encryptionCommandNumber=t.WacomGSS.STU.Protocol.EncryptionCommandNumber._$wrappers[e].getValue(),this.parameter=a,this.lengthOrIndex=n,this.data=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.maxDataLength),null!=r&&((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(r,0,this.data,0,r.length)}else{if(void 0===this.encryptionCommandNumber&&(this.encryptionCommandNumber=0),void 0===this.parameter&&(this.parameter=0),void 0===this.lengthOrIndex&&(this.lengthOrIndex=0),void 0===this.data&&(this.data=null),null==r&&0!==c)throw Object.defineProperty(new Error(o.txtError),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});if(c<0||c>o.maxDataLength||null!=r&&r.length-i<c)throw Object.defineProperty(new Error(o.txtError),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});this.encryptionCommandNumber=t.WacomGSS.STU.Protocol.EncryptionCommandNumber._$wrappers[e].getValue(),this.parameter=a,this.lengthOrIndex=n,this.data=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.maxDataLength),null!=r&&((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(r,i,this.data,0,c)}}getEncryptionCommandNumber(){return this.encryptionCommandNumber}getParameter(){return this.parameter}getLengthOrIndex(){return this.lengthOrIndex}getData(){return this.data}}o.maxDataLength=64,o.txtError="invalid data length",e.EncryptionCommand=o,o.__class="com.WacomGSS.STU.Protocol.EncryptionCommand"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){let e;!function(t){t[t.SetEncryptionType=0]="SetEncryptionType",t[t.SetParameterBlock=1]="SetParameterBlock",t[t.GetStatusBlock=2]="GetStatusBlock",t[t.GetParameterBlock=3]="GetParameterBlock",t[t.GenerateSymmetricKey=4]="GenerateSymmetricKey"}(e=t.EncryptionCommandNumber||(t.EncryptionCommandNumber={}));class o{constructor(t,e,o){this._$ordinal=t,this._$name=e,void 0===this.value&&(this.value=0),this.value=o}getValue(){return this.value}name(){return this._$name}ordinal(){return this._$ordinal}compareTo(t){return this._$ordinal-(isNaN(t)?t._$ordinal:t)}}t.EncryptionCommandNumber_$WRAPPER=o,e.__class="com.WacomGSS.STU.Protocol.EncryptionCommandNumber",e.__interfaces=["java.lang.constant.Constable","java.lang.Comparable","java.io.Serializable"],e._$wrappers=[new o(0,"SetEncryptionType",1),new o(1,"SetParameterBlock",2),new o(2,"GetStatusBlock",3),new o(3,"GetParameterBlock",4),new o(4,"GenerateSymmetricKey",5)]}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){let e;!function(t){t[t.RSAe=0]="RSAe",t[t.RSAn=1]="RSAn",t[t.RSAc=2]="RSAc",t[t.RSAm=3]="RSAm"}(e=t.EncryptionCommandParameterBlockIndex||(t.EncryptionCommandParameterBlockIndex={}));class o{constructor(t,e,o){this._$ordinal=t,this._$name=e,void 0===this.value&&(this.value=0),this.value=o}getValue(){return this.value}name(){return this._$name}ordinal(){return this._$ordinal}compareTo(t){return this._$ordinal-(isNaN(t)?t._$ordinal:t)}}t.EncryptionCommandParameterBlockIndex_$WRAPPER=o,e.__class="com.WacomGSS.STU.Protocol.EncryptionCommandParameterBlockIndex",e.__interfaces=["java.lang.constant.Constable","java.lang.Comparable","java.io.Serializable"],e._$wrappers=[new o(0,"RSAe",0),new o(1,"RSAn",1),new o(2,"RSAc",2),new o(3,"RSAm",3)]}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{constructor(t,e,o,a,n,r,i,c,l,s){void 0===this.symmetricKeyType&&(this.symmetricKeyType=0),void 0===this.asymmetricPaddingType&&(this.asymmetricPaddingType=0),void 0===this.asymmetricKeyType&&(this.asymmetricKeyType=0),void 0===this.statusCodeRSAe&&(this.statusCodeRSAe=0),void 0===this.statusCodeRSAn&&(this.statusCodeRSAn=0),void 0===this.statusCodeRSAc&&(this.statusCodeRSAc=0),void 0===this.lastResultCode&&(this.lastResultCode=0),void 0===this.rng&&(this.rng=!1),void 0===this.sha1&&(this.sha1=!1),void 0===this.aes&&(this.aes=!1),this.symmetricKeyType=t,this.asymmetricPaddingType=e,this.asymmetricKeyType=o,this.statusCodeRSAe=a,this.statusCodeRSAn=n,this.statusCodeRSAc=r,this.lastResultCode=i,this.rng=c,this.sha1=l,this.aes=s}static reportId_$LI$(){return null==o.reportId&&(o.reportId=t.WacomGSS.STU.Protocol.ReportId.EncryptionStatus_$LI$()),o.reportId}getSymmetricKeyType(){return this.symmetricKeyType}getAsymmetricPaddingType(){return this.asymmetricPaddingType}getAsymmetricKeyType(){return this.asymmetricKeyType}getStatusCodeRSAe(){return this.statusCodeRSAe}getStatusCodeRSAn(){return this.statusCodeRSAn}getStatusCodeRSAc(){return this.statusCodeRSAc}getLastResultCode(){return this.lastResultCode}getRng(){return this.rng}getSha1(){return this.sha1}getAes(){return this.aes}}o.reportSize=16,e.EncryptionStatus=o,o.__class="com.WacomGSS.STU.Protocol.EncryptionStatus"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){let e;!function(t){t[t.Commit=0]="Commit",t[t.Abandon=1]="Abandon"}(e=t.EndImageDataFlag||(t.EndImageDataFlag={}));class o{constructor(t,e,o){this._$ordinal=t,this._$name=e,void 0===this.value&&(this.value=0),this.value=o}getValue(){return this.value}name(){return this._$name}ordinal(){return this._$ordinal}compareTo(t){return this._$ordinal-(isNaN(t)?t._$ordinal:t)}}t.EndImageDataFlag_$WRAPPER=o,e.__class="com.WacomGSS.STU.Protocol.EndImageDataFlag",e.__interfaces=["java.lang.constant.Constable","java.lang.Comparable","java.io.Serializable"],e._$wrappers=[new o(0,"Commit",0),new o(1,"Abandon",1)]}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{static None_$LI$(){return null==e.None&&(e.None=0),e.None}static WrongReportId_$LI$(){return null==e.WrongReportId&&(e.WrongReportId=1),e.WrongReportId}static WrongState_$LI$(){return null==e.WrongState&&(e.WrongState=2),e.WrongState}static Crc_$LI$(){return null==e.Crc&&(e.Crc=3),e.Crc}static BadParameter_$LI$(){return null==e.BadParameter&&(e.BadParameter=4),e.BadParameter}static GraphicsImageTooLong_$LI$(){return null==e.GraphicsImageTooLong&&(e.GraphicsImageTooLong=17),e.GraphicsImageTooLong}static GraphicsZlibError_$LI$(){return null==e.GraphicsZlibError&&(e.GraphicsZlibError=18),e.GraphicsZlibError}static GraphicsWrongParameters_$LI$(){return null==e.GraphicsWrongParameters&&(e.GraphicsWrongParameters=21),e.GraphicsWrongParameters}static PadNotExist_$LI$(){return null==e.PadNotExist&&(e.PadNotExist=32),e.PadNotExist}static RomSizeOverflow_$LI$(){return null==e.RomSizeOverflow&&(e.RomSizeOverflow=51),e.RomSizeOverflow}static RomInvalidParameter_$LI$(){return null==e.RomInvalidParameter&&(e.RomInvalidParameter=52),e.RomInvalidParameter}static RomErrorCRC_$LI$(){return null==e.RomErrorCRC&&(e.RomErrorCRC=53),e.RomErrorCRC}}t.ErrorCode=e,e.__class="com.WacomGSS.STU.Protocol.ErrorCode"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{static None_$LI$(){return null==e.None&&(e.None=0),e.None}static BadParameter_$LI$(){return null==e.BadParameter&&(e.BadParameter=1),e.BadParameter}static ParameterTooLong_$LI$(){return null==e.ParameterTooLong&&(e.ParameterTooLong=2),e.ParameterTooLong}static PublicKeyNotReady_$LI$(){return null==e.PublicKeyNotReady&&(e.PublicKeyNotReady=3),e.PublicKeyNotReady}static PublicExponentNotReady_$LI$(){return null==e.PublicExponentNotReady&&(e.PublicExponentNotReady=4),e.PublicExponentNotReady}static SpecifiedKeyInUse_$LI$(){return null==e.SpecifiedKeyInUse&&(e.SpecifiedKeyInUse=5),e.SpecifiedKeyInUse}static SpecifiedKeyNotInUse_$LI$(){return null==e.SpecifiedKeyNotInUse&&(e.SpecifiedKeyNotInUse=6),e.SpecifiedKeyNotInUse}static BadCommandCode_$LI$(){return null==e.BadCommandCode&&(e.BadCommandCode=7),e.BadCommandCode}static CommandPending_$LI$(){return null==e.CommandPending&&(e.CommandPending=8),e.CommandPending}static SpecifiedKeyExists_$LI$(){return null==e.SpecifiedKeyExists&&(e.SpecifiedKeyExists=9),e.SpecifiedKeyExists}static SpecifiedKeyNotExist_$LI$(){return null==e.SpecifiedKeyNotExist&&(e.SpecifiedKeyNotExist=10),e.SpecifiedKeyNotExist}static NotInitialized_$LI$(){return null==e.NotInitialized&&(e.NotInitialized=11),e.NotInitialized}}t.ErrorCodeRSA=e,e.__class="com.WacomGSS.STU.Protocol.ErrorCodeRSA"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{static reportId_$LI$(){return null==o.reportId&&(o.reportId=t.WacomGSS.STU.Protocol.ReportId.EventData_$LI$()),o.reportId}}o.reportSize=9,e.EventData=o,o.__class="com.WacomGSS.STU.Protocol.EventData"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{static reportId_$LI$(){return null==o.reportId&&(o.reportId=t.WacomGSS.STU.Protocol.ReportId.EventDataEncrypted_$LI$()),o.reportId}}o.reportSize=16,o.encryptedSize=16,e.EventDataEncrypted=o,o.__class="com.WacomGSS.STU.Protocol.EventDataEncrypted"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{constructor(e,a){if(!(null!=e&&e instanceof Array&&(0==e.length||null==e[0]||"number"==typeof e[0])||null===e)||"number"!=typeof a&&null!==a){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.EventDataKeyPad||null===e)||void 0!==a)throw new Error("invalid overload");{let t=arguments[0];void 0===this.screenSelected&&(this.screenSelected=0),void 0===this.keyNumber&&(this.keyNumber=0),this.screenSelected=t.screenSelected,this.keyNumber=t.keyNumber}}else{if(void 0===this.screenSelected&&(this.screenSelected=0),void 0===this.keyNumber&&(this.keyNumber=0),e[a]!==o.operationModeType_$LI$())throw new t.WacomGSS.RuntimeException("Attempt to intialize EventDataKeyPad with wrong EventData");++a,this.screenSelected=e[a+0],this.keyNumber=e[a+1]}}static reportId_$LI$(){return null==o.reportId&&(o.reportId=t.WacomGSS.STU.Protocol.ReportId.EventData_$LI$()),o.reportId}static operationModeType_$LI$(){return null==o.operationModeType&&(o.operationModeType=t.WacomGSS.STU.Protocol.OperationModeType.KeyPad_$LI$()),o.operationModeType}getScreenSelected(){return this.screenSelected}getKeyNumber(){return this.keyNumber}}o.reportSize=9,e.EventDataKeyPad=o,o.__class="com.WacomGSS.STU.Protocol.EventDataKeyPad"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o extends t.WacomGSS.STU.Protocol.EventDataKeyPad{constructor(e,o){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.EventDataKeyPad||null===e)||"number"!=typeof o&&null!==o){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.EventDataKeyPadEncrypted||null===e)||void 0!==o)throw new Error("invalid overload");super(e),void 0===this.sessionId&&(this.sessionId=0),this.sessionId=e.sessionId}else{super(e),void 0===this.sessionId&&(this.sessionId=0),this.sessionId=o}}static reportId_$LI$(){return null==o.reportId&&(o.reportId=t.WacomGSS.STU.Protocol.ReportId.EventDataEncrypted_$LI$()),o.reportId}getSessionId(){return this.sessionId}}o.reportSize=16,o.encryptedSize=16,e.EventDataKeyPadEncrypted=o,o.__class="com.WacomGSS.STU.Protocol.EventDataKeyPadEncrypted"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{constructor(e,a){if(!(null!=e&&e instanceof Array&&(0==e.length||null==e[0]||"number"==typeof e[0])||null===e)||"number"!=typeof a&&null!==a){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.EventDataPinPad||null===e)||void 0!==a)throw new Error("invalid overload");{let t=arguments[0];void 0===this.keyInput&&(this.keyInput=0),this.pin=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(13),this.keyInput=t.keyInput,((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(t.pin,0,this.pin,0,t.pin.length)}}else{if(void 0===this.keyInput&&(this.keyInput=0),this.pin=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(13),e[a]!==o.operationModeType_$LI$())throw new t.WacomGSS.RuntimeException("Attempt to intialize EventDataPinPad with wrong EventData");++a,this.keyInput=e[a+0],this.pin[0]=e[a+1]>>4&15|0,this.pin[1]=15&e[a+1]|0,this.pin[2]=e[a+2]>>4&15|0,this.pin[3]=15&e[a+2]|0,this.pin[4]=e[a+3]>>4&15|0,this.pin[5]=15&e[a+3]|0,this.pin[6]=e[a+4]>>4&15|0,this.pin[7]=15&e[a+4]|0,this.pin[8]=e[a+5]>>4&15|0,this.pin[9]=15&e[a+5]|0,this.pin[10]=e[a+6]>>4&15|0,this.pin[11]=15&e[a+6]|0,this.pin[12]=e[a+7]>>4&15|0}}static reportId_$LI$(){return null==o.reportId&&(o.reportId=t.WacomGSS.STU.Protocol.ReportId.EventData_$LI$()),o.reportId}static operationModeType_$LI$(){return null==o.operationModeType&&(o.operationModeType=t.WacomGSS.STU.Protocol.OperationModeType.PinPad_$LI$()),o.operationModeType}getKeyInput(){return this.keyInput}getPIN(){const t={str:"",toString:function(){return this.str}};for(let o=0;o<this.pin.length;++o)switch(this.pin[o]){case 15:o=this.pin.length;break;case 10:(e=t).str=e.str.concat("*");break;case 11:(t=>{t.str=t.str.concat("#")})(t);break;case 12:(t=>{t.str=t.str.concat(".")})(t);break;default:0<=this.pin[o]&&this.pin[o]<=9&&(t=>{t.str=t.str.concat(String.fromCharCode("0".charCodeAt(0)+this.pin[o]))})(t)}var e;return t.str}}o.reportSize=9,e.EventDataPinPad=o,o.__class="com.WacomGSS.STU.Protocol.EventDataPinPad"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o extends t.WacomGSS.STU.Protocol.EventDataPinPad{constructor(e,o){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.EventDataPinPad||null===e)||"number"!=typeof o&&null!==o){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.EventDataPinPadEncrypted||null===e)||void 0!==o)throw new Error("invalid overload");super(e),void 0===this.sessionId&&(this.sessionId=0),this.sessionId=e.sessionId}else{super(e),void 0===this.sessionId&&(this.sessionId=0),this.sessionId=o}}static reportId_$LI$(){return null==o.reportId&&(o.reportId=t.WacomGSS.STU.Protocol.ReportId.EventDataEncrypted_$LI$()),o.reportId}getSessionId(){return this.sessionId}}o.reportSize=16,o.encryptedSize=16,e.EventDataPinPadEncrypted=o,o.__class="com.WacomGSS.STU.Protocol.EventDataPinPadEncrypted"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{constructor(e,a){if(!(null!=e&&e instanceof Array&&(0==e.length||null==e[0]||"number"==typeof e[0])||null===e)||"number"!=typeof a&&null!==a){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.EventDataSignature||null===e)||void 0!==a)throw new Error("invalid overload");{let t=arguments[0];void 0===this.keyValue&&(this.keyValue=0),this.keyValue=t.keyValue}}else{if(void 0===this.keyValue&&(this.keyValue=0),e[a]!==o.operationModeType_$LI$())throw new t.WacomGSS.RuntimeException("Attempt to intialize EventDataSignature with wrong EventData");++a,this.keyValue=e[a+0]}}static reportId_$LI$(){return null==o.reportId&&(o.reportId=t.WacomGSS.STU.Protocol.ReportId.EventData_$LI$()),o.reportId}static operationModeType_$LI$(){return null==o.operationModeType&&(o.operationModeType=t.WacomGSS.STU.Protocol.OperationModeType.Signature_$LI$()),o.operationModeType}getKeyValue(){return this.keyValue}}o.reportSize=9,e.EventDataSignature=o,o.__class="com.WacomGSS.STU.Protocol.EventDataSignature"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o extends t.WacomGSS.STU.Protocol.EventDataSignature{constructor(e,o){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.EventDataSignature||null===e)||"number"!=typeof o&&null!==o){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.EventDataSignatureEncrypted||null===e)||void 0!==o)throw new Error("invalid overload");super(e),void 0===this.sessionId&&(this.sessionId=0),this.sessionId=e.sessionId}else{super(e),void 0===this.sessionId&&(this.sessionId=0),this.sessionId=o}}static reportId_$LI$(){return null==o.reportId&&(o.reportId=t.WacomGSS.STU.Protocol.ReportId.EventDataEncrypted_$LI$()),o.reportId}getSessionId(){return this.sessionId}}o.reportSize=16,o.encryptedSize=16,e.EventDataSignatureEncrypted=o,o.__class="com.WacomGSS.STU.Protocol.EventDataSignatureEncrypted"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,e,o,a){void 0===this.upperLeftXpixel&&(this.upperLeftXpixel=0),void 0===this.upperLeftYpixel&&(this.upperLeftYpixel=0),void 0===this.lowerRightXpixel&&(this.lowerRightXpixel=0),void 0===this.lowerRightYpixel&&(this.lowerRightYpixel=0),this.upperLeftXpixel=t,this.upperLeftYpixel=e,this.lowerRightXpixel=o,this.lowerRightYpixel=a}getUpperLeftXpixel(){return this.upperLeftXpixel}getUpperLeftYpixel(){return this.upperLeftYpixel}getLowerRightXpixel(){return this.lowerRightXpixel}getLowerRightYpixel(){return this.lowerRightYpixel}}t.Rectangle=e,e.__class="com.WacomGSS.STU.Protocol.Rectangle"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o extends t.WacomGSS.STU.Protocol.Rectangle{constructor(t,e,o,a){super(t,e,o,a)}}e.HandwritingDisplayArea=o,o.__class="com.WacomGSS.STU.Protocol.HandwritingDisplayArea"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,e){void 0===this.penColor&&(this.penColor=0),void 0===this.penThickness&&(this.penThickness=0),this.penColor=t,this.penThickness=e}getPenColor(){return this.penColor}getPenThickness(){return this.penThickness}}t.HandwritingThicknessColor=e,e.__class="com.WacomGSS.STU.Protocol.HandwritingThicknessColor"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,e){void 0===this.penColor&&(this.penColor=0),void 0===this.penThickness&&(this.penThickness=0),this.penColor=t,this.penThickness=e}getPenColor(){return this.penColor}getPenThickness(){return this.penThickness}}t.HandwritingThicknessColor24=e,e.__class="com.WacomGSS.STU.Protocol.HandwritingThicknessColor24"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,e,o){void 0===this.idVendor&&(this.idVendor=0),void 0===this.idProduct&&(this.idProduct=0),void 0===this.bcdDevice&&(this.bcdDevice=0),this.idVendor=t,this.idProduct=e,this.bcdDevice=o}getIdVendor(){return this.idVendor}getIdProduct(){return this.idProduct}getBcdDevice(){return this.bcdDevice}}t.HidInformation=e,e.__class="com.WacomGSS.STU.Protocol.HidInformation"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,o,a){if(!(null!=t&&t instanceof Array&&(0==t.length||null==t[0]||"number"==typeof t[0])||null===t)||"number"!=typeof o&&null!==o||"number"!=typeof a&&null!==a){if(!(null!=t&&t instanceof Array&&(0==t.length||null==t[0]||"number"==typeof t[0])||null===t)||void 0!==o||void 0!==a)throw new Error("invalid overload");if(void 0===this.data&&(this.data=null),t.length>e.maxLength_$LI$())throw Object.defineProperty(new Error(e.txtError),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});this.data=t.slice(0)}else{if(void 0===this.data&&(this.data=null),a<0||a>e.maxLength_$LI$()||t.length-o<a)throw Object.defineProperty(new Error(e.txtError),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});this.data=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(a),((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(t,o,this.data,0,a)}}static maxLength_$LI$(){return null==e.maxLength&&(e.maxLength=e.maxLengthSerial),e.maxLength}getData(){return this.data.slice(0)}}e.txtError="invalid length",e.maxLengthHID=253,e.maxLengthSerial=2557,t.ImageDataBlock=e,e.__class="com.WacomGSS.STU.Protocol.ImageDataBlock"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,e,o,a,n){void 0===this.modelName&&(this.modelName=null),void 0===this.firmwareMajorVersion&&(this.firmwareMajorVersion=0),void 0===this.firmwareMinorVersion&&(this.firmwareMinorVersion=0),void 0===this.secureIc&&(this.secureIc=0),void 0===this.secureIcVersion&&(this.secureIcVersion=null),this.modelName=t,this.firmwareMajorVersion=e,this.firmwareMinorVersion=o,this.secureIc=a,this.secureIcVersion=n.slice(0)}getModelName(){return this.modelName}getFirmwareMajorVersion(){return this.firmwareMajorVersion}getFirmwareMinorVersion(){return this.firmwareMinorVersion}getSecureIc(){return this.secureIc}getSecureIcVersion(){return this.secureIcVersion}}t.Information=e,e.__class="com.WacomGSS.STU.Protocol.Information"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){let e;!function(t){t[t.Off=0]="Off",t[t.On=1]="On"}(e=t.InkingMode||(t.InkingMode={}));class o{constructor(t,e,o){this._$ordinal=t,this._$name=e,void 0===this.value&&(this.value=0),this.value=o}getValue(){return this.value}name(){return this._$name}ordinal(){return this._$ordinal}compareTo(t){return this._$ordinal-(isNaN(t)?t._$ordinal:t)}}t.InkingMode_$WRAPPER=o,e.__class="com.WacomGSS.STU.Protocol.InkingMode",e.__interfaces=["java.lang.constant.Constable","java.lang.Comparable","java.io.Serializable"],e._$wrappers=[new o(0,"Off",0),new o(1,"On",1)]}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{constructor(){void 0===this.inkThreshold&&(this.inkThreshold=null),void 0===this.inkState&&(this.inkState=null),this.inkThreshold=new t.WacomGSS.STU.Protocol.InkThreshold(t.WacomGSS.STU.Protocol.ProtocolHelper.k_factoryDefaultInkThreshold_$LI$()),this.inkState=t.WacomGSS.STU.Protocol.InkState.InkState_Up}setInkThreshold(e){this.inkThreshold=new t.WacomGSS.STU.Protocol.InkThreshold(e)}getInkThreshold(){return new t.WacomGSS.STU.Protocol.InkThreshold(this.inkThreshold)}setInkState(t){this.inkState=t}clear(){this.inkState=t.WacomGSS.STU.Protocol.InkState.InkState_Up}currentState(){return this.inkState}nextState(e,o){return this.inkState=t.WacomGSS.STU.Protocol.ProtocolHelper.getNextInkState(this.inkState,e,o,this.inkThreshold),this.inkState}}e.InkingState=o,o.__class="com.WacomGSS.STU.Protocol.InkingState"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){let e;!function(t){t[t.InkState_isOff=0]="InkState_isOff",t[t.InkState_isOn=1]="InkState_isOn",t[t.InkState_isInk=2]="InkState_isInk",t[t.InkState_isFirst=3]="InkState_isFirst",t[t.InkState_isLast=4]="InkState_isLast",t[t.InkState_Up=5]="InkState_Up",t[t.InkState_Down=6]="InkState_Down",t[t.InkState_Inking=7]="InkState_Inking",t[t.InkState_First=8]="InkState_First",t[t.InkState_Last=9]="InkState_Last"}(e=t.InkState||(t.InkState={}));class o{constructor(t,e,o){this._$ordinal=t,this._$name=e,void 0===this.value&&(this.value=0),this.value=o}getValue(){return this.value}name(){return this._$name}ordinal(){return this._$ordinal}compareTo(t){return this._$ordinal-(isNaN(t)?t._$ordinal:t)}}t.InkState_$WRAPPER=o,e.__class="com.WacomGSS.STU.Protocol.InkState",e.__interfaces=["java.lang.constant.Constable","java.lang.Comparable","java.io.Serializable"],e._$wrappers=[new o(0,"InkState_isOff",1),new o(1,"InkState_isOn",2),new o(2,"InkState_isInk",4),new o(3,"InkState_isFirst",8),new o(4,"InkState_isLast",16),new o(5,"InkState_Up",1),new o(6,"InkState_Down",2),new o(7,"InkState_Inking",6),new o(8,"InkState_First",14),new o(9,"InkState_Last",17)]}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{constructor(e,o){if("number"!=typeof e&&null!==e||"number"!=typeof o&&null!==o){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.InkThreshold||null===e)||void 0!==o)throw new Error("invalid overload");{let t=arguments[0];void 0===this.onPressureMark&&(this.onPressureMark=0),void 0===this.offPressureMark&&(this.offPressureMark=0),this.onPressureMark=t.onPressureMark,this.offPressureMark=t.offPressureMark}}else{void 0===this.onPressureMark&&(this.onPressureMark=0),void 0===this.offPressureMark&&(this.offPressureMark=0),this.onPressureMark=e,this.offPressureMark=o}}getOnPressureMark(){return this.onPressureMark}getOffPressureMark(){return this.offPressureMark}}e.InkThreshold=o,o.__class="com.WacomGSS.STU.Protocol.InkThreshold"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,e){void 0===this.screenSelect&&(this.screenSelect=0),void 0===this.idAfterSelect&&(this.idAfterSelect=0),this.screenSelect=t,this.idAfterSelect=e}getScreenSelect(){return this.screenSelect}getIdAfterSelect(){return this.idAfterSelect}}t.OperationMode_KeyPad=e,e.__class="com.WacomGSS.STU.Protocol.OperationMode_KeyPad"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,e,o,a,n,r,i){void 0===this.screenSelect&&(this.screenSelect=0),void 0===this.pinBypass&&(this.pinBypass=0),void 0===this.minDigit&&(this.minDigit=0),void 0===this.maxDigit&&(this.maxDigit=0),void 0===this.numberHiddenMode&&(this.numberHiddenMode=0),void 0===this.idAfterEnter&&(this.idAfterEnter=0),void 0===this.idAfterCancel&&(this.idAfterCancel=0),this.screenSelect=t,this.pinBypass=e,this.minDigit=o,this.maxDigit=a,this.numberHiddenMode=n,this.idAfterEnter=r,this.idAfterCancel=i}getScreenSelect(){return this.screenSelect}getPinBypass(){return this.pinBypass}getMinDigit(){return this.minDigit}getMaxDigit(){return this.maxDigit}getNumberHiddenMode(){return this.numberHiddenMode}getIdAfterEnter(){return this.idAfterEnter}getIdAfterCancel(){return this.idAfterCancel}}t.OperationMode_PinPad=e,e.__class="com.WacomGSS.STU.Protocol.OperationMode_PinPad"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,e,o,a){if(this.numberOfKeys=3,void 0===this.signatureScreen&&(this.signatureScreen=0),void 0===this.keyDefinition&&(this.keyDefinition=null),void 0===this.idAfterEnter&&(this.idAfterEnter=0),void 0===this.idAfterCancel&&(this.idAfterCancel=0),null==e||e.length!==this.numberOfKeys)throw Object.defineProperty(new Error("invalid keyDefinition"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});this.signatureScreen=t,this.keyDefinition=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(this.numberOfKeys),this.idAfterEnter=o,this.idAfterCancel=a,((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(e,0,this.keyDefinition,0,this.numberOfKeys)}getSignatureScreen(){return this.signatureScreen}getKeyDefinition(){return this.keyDefinition}getIdAfterEnter(){return this.idAfterEnter}getIdAfterCancel(){return this.idAfterCancel}}t.OperationMode_Signature=e,e.__class="com.WacomGSS.STU.Protocol.OperationMode_Signature"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,e,o,a){if(this.maxNumberOfSlides=10,void 0===this.workingMode&&(this.workingMode=0),void 0===this.numberOfSlides&&(this.numberOfSlides=0),void 0===this.slideNumber&&(this.slideNumber=null),void 0===this.interval&&(this.interval=0),null==o||o.length>this.maxNumberOfSlides)throw Object.defineProperty(new Error("invalid slideNumber"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});this.workingMode=t,this.slideNumber=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(this.maxNumberOfSlides),this.numberOfSlides=e,this.interval=a,((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(o,0,this.slideNumber,0,o.length)}getWorkingMode(){return this.workingMode}getSlideNumber(){return this.slideNumber}getNumberOfSlides(){return this.numberOfSlides}getInterval(){return this.interval}}t.OperationMode_SlideShow=e,e.__class="com.WacomGSS.STU.Protocol.OperationMode_SlideShow"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{constructor(e){if(null!=e&&e instanceof Array&&(0==e.length||null==e[0]||"number"==typeof e[0])||null===e){if(void 0===this.operationModeType&&(this.operationModeType=0),void 0===this.data&&(this.data=null),null==e||e.length!==o.dataBytes+2||e[0]!==t.WacomGSS.STU.Protocol.ReportId.OperationMode_$LI$())throw Object.defineProperty(new Error("invalid raw"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});this.operationModeType=e[1],this.data=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.dataBytes),((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(e,2,this.data,0,o.dataBytes)}else if(null!=e&&e instanceof t.WacomGSS.STU.Protocol.OperationMode||null===e){let t=arguments[0];{let e=t.operationModeType;void 0===this.operationModeType&&(this.operationModeType=0),void 0===this.data&&(this.data=null),this.operationModeType=e,this.data=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.dataBytes)}void 0===this.operationModeType&&(this.operationModeType=0),void 0===this.data&&(this.data=null),(()=>{((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(t.data,0,this.data,0,t.data.length)})()}else if(null!=e&&e instanceof t.WacomGSS.STU.Protocol.OperationMode_PinPad||null===e){let e=arguments[0];{let e=t.WacomGSS.STU.Protocol.OperationModeType.PinPad_$LI$();void 0===this.operationModeType&&(this.operationModeType=0),void 0===this.data&&(this.data=null),this.operationModeType=e,this.data=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.dataBytes)}void 0===this.operationModeType&&(this.operationModeType=0),void 0===this.data&&(this.data=null),(()=>{this.data[0]=e.getScreenSelect(),this.data[1]=e.getPinBypass(),this.data[2]=e.getMinDigit(),this.data[3]=e.getMaxDigit(),this.data[4]=e.getNumberHiddenMode(),this.data[5]=e.getIdAfterEnter(),this.data[6]=e.getIdAfterCancel()})()}else if(null!=e&&e instanceof t.WacomGSS.STU.Protocol.OperationMode_KeyPad||null===e){let e=arguments[0];{let e=t.WacomGSS.STU.Protocol.OperationModeType.KeyPad_$LI$();void 0===this.operationModeType&&(this.operationModeType=0),void 0===this.data&&(this.data=null),this.operationModeType=e,this.data=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.dataBytes)}void 0===this.operationModeType&&(this.operationModeType=0),void 0===this.data&&(this.data=null),(()=>{this.data[0]=e.getScreenSelect(),this.data[1]=e.getIdAfterSelect()})()}else if(null!=e&&e instanceof t.WacomGSS.STU.Protocol.OperationMode_Signature||null===e){let e=arguments[0];{let e=t.WacomGSS.STU.Protocol.OperationModeType.Signature_$LI$();void 0===this.operationModeType&&(this.operationModeType=0),void 0===this.data&&(this.data=null),this.operationModeType=e,this.data=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.dataBytes)}void 0===this.operationModeType&&(this.operationModeType=0),void 0===this.data&&(this.data=null),(()=>{this.data[0]=e.getSignatureScreen(),this.data[1]=e.getKeyDefinition()[0],this.data[2]=e.getKeyDefinition()[1],this.data[3]=e.getKeyDefinition()[2],this.data[4]=e.getIdAfterEnter(),this.data[5]=e.getIdAfterCancel()})()}else if(null!=e&&e instanceof t.WacomGSS.STU.Protocol.OperationMode_SlideShow||null===e){let e=arguments[0];{let e=t.WacomGSS.STU.Protocol.OperationModeType.SlideShow_$LI$();void 0===this.operationModeType&&(this.operationModeType=0),void 0===this.data&&(this.data=null),this.operationModeType=e,this.data=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.dataBytes)}void 0===this.operationModeType&&(this.operationModeType=0),void 0===this.data&&(this.data=null),(()=>{const t=e.getSlideNumber();this.data[0]=e.getWorkingMode(),this.data[1]=e.getNumberOfSlides(),this.data[2]=(15&t[0])<<4|15&t[1]|0,this.data[3]=(15&t[2])<<4|15&t[3]|0,this.data[4]=(15&t[4])<<4|15&t[5]|0,this.data[5]=(15&t[6])<<4|15&t[7]|0,this.data[6]=(15&t[8])<<4|15&t[9]|0,this.data[7]=e.getInterval()>>24&255|0,this.data[8]=e.getInterval()>>16&255|0,this.data[9]=e.getInterval()>>8&255|0,this.data[10]=255&e.getInterval()|0})()}else{if("number"!=typeof e&&null!==e)throw new Error("invalid overload");{let t=arguments[0];void 0===this.operationModeType&&(this.operationModeType=0),void 0===this.data&&(this.data=null),this.operationModeType=t,this.data=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.dataBytes)}}}getOperationModeType(){return this.operationModeType}getData(){return this.data}static initializeNormal(){return new o(t.WacomGSS.STU.Protocol.OperationModeType.Normal_$LI$())}static initializePinPad(t){return new o(t)}static initializeKeyPad(t){return new o(t)}static initializeSignature(t){return new o(t)}static initializeSlideShow(t){return new o(t)}}o.dataBytes=11,e.OperationMode=o,o.__class="com.WacomGSS.STU.Protocol.OperationMode"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{static Normal_$LI$(){return null==e.Normal&&(e.Normal=0),e.Normal}static PinPad_$LI$(){return null==e.PinPad&&(e.PinPad=1),e.PinPad}static SlideShow_$LI$(){return null==e.SlideShow&&(e.SlideShow=2),e.SlideShow}static KeyPad_$LI$(){return null==e.KeyPad&&(e.KeyPad=3),e.KeyPad}static Signature_$LI$(){return null==e.Signature&&(e.Signature=4),e.Signature}static MessageBox_$LI$(){return null==e.MessageBox&&(e.MessageBox=5),e.MessageBox}}t.OperationModeType=e,e.__class="com.WacomGSS.STU.Protocol.OperationModeType"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{constructor(e,o,a,n,r){if("number"!=typeof e&&null!==e||"number"!=typeof o&&null!==o||"number"!=typeof a&&null!==a||"number"!=typeof n&&null!==n||"number"!=typeof r&&null!==r){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.PenData||null===e)||void 0!==o||void 0!==a||void 0!==n||void 0!==r)throw new Error("invalid overload");{let t=arguments[0];{let e=t.getRdy(),o=t.getSw(),a=t.getPressure(),n=t.getX(),r=t.getY();void 0===this.rdy&&(this.rdy=0),void 0===this.sw&&(this.sw=0),void 0===this.pressure&&(this.pressure=0),void 0===this.x&&(this.x=0),void 0===this.y&&(this.y=0),this.rdy=e,this.sw=o,this.pressure=a,this.x=n,this.y=r}void 0===this.rdy&&(this.rdy=0),void 0===this.sw&&(this.sw=0),void 0===this.pressure&&(this.pressure=0),void 0===this.x&&(this.x=0),void 0===this.y&&(this.y=0)}}else{void 0===this.rdy&&(this.rdy=0),void 0===this.sw&&(this.sw=0),void 0===this.pressure&&(this.pressure=0),void 0===this.x&&(this.x=0),void 0===this.y&&(this.y=0),this.rdy=e,this.sw=o,this.pressure=a,this.x=n,this.y=r}}static reportId_$LI$(){return null==o.reportId&&(o.reportId=t.WacomGSS.STU.Protocol.ReportId.PenData_$LI$()),o.reportId}getRdy(){return this.rdy}getSw(){return this.sw}getPressure(){return this.pressure}getX(){return this.x}getY(){return this.y}}o.reportSize=6,e.PenData=o,o.__class="com.WacomGSS.STU.Protocol.PenData"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{constructor(e,o,a){if(("number"==typeof e||null===e)&&(null!=o&&o instanceof t.WacomGSS.STU.Protocol.PenData||null===o)&&(null!=a&&a instanceof t.WacomGSS.STU.Protocol.PenData||null===a)){void 0===this.sessionId&&(this.sessionId=0),void 0===this.penData1&&(this.penData1=null),void 0===this.penData2&&(this.penData2=null),this.sessionId=e,this.penData1=o,this.penData2=a}else{if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.PenDataEncrypted||null===e)||void 0!==o||void 0!==a)throw new Error("invalid overload");{let t=arguments[0];{let e=t.getSessionId(),o=t.getPenData1(),a=t.getPenData2();void 0===this.sessionId&&(this.sessionId=0),void 0===this.penData1&&(this.penData1=null),void 0===this.penData2&&(this.penData2=null),this.sessionId=e,this.penData1=o,this.penData2=a}void 0===this.sessionId&&(this.sessionId=0),void 0===this.penData1&&(this.penData1=null),void 0===this.penData2&&(this.penData2=null)}}}static reportId_$LI$(){return null==o.reportId&&(o.reportId=t.WacomGSS.STU.Protocol.ReportId.PenDataEncrypted_$LI$()),o.reportId}getSessionId(){return this.sessionId}getPenData1(){return this.penData1}getPenData2(){return this.penData2}}o.reportSize=16,o.encryptedSize=16,e.PenDataEncrypted=o,o.__class="com.WacomGSS.STU.Protocol.PenDataEncrypted"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o extends t.WacomGSS.STU.Protocol.PenDataEncrypted{constructor(t,e,o){super(t),void 0===this.option1&&(this.option1=0),void 0===this.option2&&(this.option2=0),this.option1=e,this.option2=o}static reportId_$LI$(){return null==o.reportId&&(o.reportId=t.WacomGSS.STU.Protocol.ReportId.PenDataEncryptedOption_$LI$()),o.reportId}getOption1(){return this.option1}getOption2(){return this.option2}getPenDataOption1(){return new t.WacomGSS.STU.Protocol.PenDataOption(this.getPenData1(),this.getOption1())}getPenDataOption2(){return new t.WacomGSS.STU.Protocol.PenDataOption(this.getPenData2(),this.getOption2())}}o.reportSize=20,e.PenDataEncryptedOption=o,o.__class="com.WacomGSS.STU.Protocol.PenDataEncryptedOption"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o extends t.WacomGSS.STU.Protocol.PenData{constructor(e,o){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.PenData||null===e)||"number"!=typeof o&&null!==o){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.PenDataOption||null===e)||void 0!==o)throw new Error("invalid overload");{let t=arguments[0];super(t),void 0===this.option&&(this.option=0),this.option=t.getOption()}}else{super(e),void 0===this.option&&(this.option=0),this.option=o}}static reportId_$LI$(){return null==o.reportId&&(o.reportId=t.WacomGSS.STU.Protocol.ReportId.PenDataOption_$LI$()),o.reportId}getOption(){return this.option}}o.reportSize=8,e.PenDataOption=o,o.__class="com.WacomGSS.STU.Protocol.PenDataOption"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){let e;!function(t){t[t.None=0]="None",t[t.TimeCount=1]="TimeCount",t[t.SequenceNumber=2]="SequenceNumber",t[t.TimeCountSequence=3]="TimeCountSequence"}(e=t.PenDataOptionMode||(t.PenDataOptionMode={}));class o{constructor(t,e,o){this._$ordinal=t,this._$name=e,void 0===this.value&&(this.value=0),this.value=o}getValue(){return this.value}name(){return this._$name}ordinal(){return this._$ordinal}compareTo(t){return this._$ordinal-(isNaN(t)?t._$ordinal:t)}}t.PenDataOptionMode_$WRAPPER=o,e.__class="com.WacomGSS.STU.Protocol.PenDataOptionMode",e.__interfaces=["java.lang.constant.Constable","java.lang.Comparable","java.io.Serializable"],e._$wrappers=[new o(0,"None",0),new o(1,"TimeCount",1),new o(2,"SequenceNumber",2),new o(3,"TimeCountSequence",3)]}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o extends t.WacomGSS.STU.Protocol.PenData{constructor(e,o,a){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.PenData||null===e)||"number"!=typeof o&&null!==o||"number"!=typeof a&&null!==a){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.PenDataTimeCountSequence||null===e)||void 0!==o||void 0!==a)throw new Error("invalid overload");super(e),void 0===this.timeCount&&(this.timeCount=0),void 0===this.sequence&&(this.sequence=0),this.timeCount=e.getTimeCount(),this.sequence=e.getSequence()}else{super(e),void 0===this.timeCount&&(this.timeCount=0),void 0===this.sequence&&(this.sequence=0),this.timeCount=o,this.sequence=a}}static reportId_$LI$(){return null==o.reportId&&(o.reportId=t.WacomGSS.STU.Protocol.ReportId.PenDataTimeCountSequence_$LI$()),o.reportId}getTimeCount(){return this.timeCount}getSequence(){return this.sequence}}o.reportSize=10,e.PenDataTimeCountSequence=o,o.__class="com.WacomGSS.STU.Protocol.PenDataTimeCountSequence"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o extends t.WacomGSS.STU.Protocol.PenDataTimeCountSequence{constructor(e,o){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.PenDataTimeCountSequence||null===e)||"number"!=typeof o&&null!==o){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.PenDataTimeCountSequenceEncrypted||null===e)||void 0!==o)throw new Error("invalid overload");super(e),void 0===this.sessionId&&(this.sessionId=0),this.sessionId=e.getSessionId()}else{super(e),void 0===this.sessionId&&(this.sessionId=0),this.sessionId=o}}static reportId_$LI$(){return null==o.reportId&&(o.reportId=t.WacomGSS.STU.Protocol.ReportId.PenDataTimeCountSequenceEncrypted_$LI$()),o.reportId}getSessionId(){return this.sessionId}}o.reportSize=16,o.encryptedSize=16,e.PenDataTimeCountSequenceEncrypted=o,o.__class="com.WacomGSS.STU.Protocol.PenDataTimeCountSequenceEncrypted"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{constructor(t){void 0===this.intf&&(this.intf=null),this.intf=t}static B0(t){return 255&t|0}static B1(t){return t>>8&255|0}static B2(t){return t>>16&255|0}static B3(t){return t>>24&255|0}static I$byte(t){return 255&t}static I$byte$byte(t,e){return o.I$byte(t)<<8|o.I$byte(e)}static I$byte$byte$byte$byte(t,e,a,n){return o.I$byte(t)<<24|o.I$byte(e)<<16|o.I$byte(a)<<8|o.I$byte(n)}static I(e,o,a,n){if("number"!=typeof e&&null!==e||"number"!=typeof o&&null!==o||"number"!=typeof a&&null!==a||"number"!=typeof n&&null!==n){if("number"!=typeof e&&null!==e||"number"!=typeof o&&null!==o||void 0!==a||void 0!==n){if("number"!=typeof e&&null!==e||void 0!==o||void 0!==a||void 0!==n)throw new Error("invalid overload");return t.WacomGSS.STU.Protocol.Protocol.I$byte(e)}return t.WacomGSS.STU.Protocol.Protocol.I$byte$byte(e,o)}return t.WacomGSS.STU.Protocol.Protocol.I$byte$byte$byte$byte(e,o,a,n)}static S(t,e){return 65535&o.I$byte$byte(t,e)|0}getInterface(){return this.intf}setInterface(t){this.intf=t}async getStatus(){let e=[t.WacomGSS.STU.Protocol.ReportId.Status_$LI$(),0,0,0,0],a=await this.intf.get(e);return new t.WacomGSS.STU.Protocol.Status(a[1],a[2],o.S(a[3],a[4]))}async setReset(e){const o=[t.WacomGSS.STU.Protocol.ReportId.Reset_$LI$(),t.WacomGSS.STU.Protocol.ResetFlag._$wrappers[e].getValue()];await this.intf.set(o)}async getHidInformation(){let e=[t.WacomGSS.STU.Protocol.ReportId.HidInformation_$LI$(),0,0,0,0,0,0,0,0];e=await this.intf.get(e);return new t.WacomGSS.STU.Protocol.HidInformation(o.S(e[1],e[2]),o.S(e[3],e[4]),o.S(e[5],e[6]))}async getDefaultMode(){let e=[t.WacomGSS.STU.Protocol.ReportId.DefaultMode_$LI$(),0,0];return e=await this.intf.get(e),e[1]}async setDefaultMode(e){const o=[t.WacomGSS.STU.Protocol.ReportId.DefaultMode_$LI$(),t.WacomGSS.STU.Protocol.DefaultMode._$wrappers[e].getValue(),0];await this.intf.set(o)}async getInformation(){let e=[t.WacomGSS.STU.Protocol.ReportId.Information_$LI$(),0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];e=await this.intf.get(e);const a={str:"",toString:function(){return this.str}};for(let t=0;t<9&&0!==e[1+t];++t)(n=a).str=n.str.concat(String.fromCharCode(o.I$byte(e[1+t])));var n;return new t.WacomGSS.STU.Protocol.Information(a.str,o.I$byte(e[10]),o.I$byte(e[11]),e[12],[o.I$byte(e[13]),o.I$byte(e[14]),o.I$byte(e[15]),o.I$byte(e[16])])}async getCapability(){let e=[t.WacomGSS.STU.Protocol.ReportId.Capability_$LI$(),0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];return e=await this.intf.get(e),e.length>11?new t.WacomGSS.STU.Protocol.Capability(o.I$byte$byte(e[1],e[2]),o.I$byte$byte(e[3],e[4]),o.I$byte$byte(e[5],e[6]),o.I$byte$byte(e[7],e[8]),o.I$byte$byte(e[9],e[10]),o.I$byte(e[11]),o.I$byte$byte(e[12],e[13]),e[14]):new t.WacomGSS.STU.Protocol.Capability(o.I$byte$byte(e[1],e[2]),o.I$byte$byte(e[3],e[4]),o.I$byte$byte(e[5],e[6]),o.I$byte$byte(e[7],e[8]),o.I$byte$byte(e[9],e[10]),0,0,0)}async getUid(){let e=[t.WacomGSS.STU.Protocol.ReportId.Uid_$LI$(),0,0,0,0];e=await this.intf.get(e);return o.I$byte$byte$byte$byte(e[1],e[2],e[3],e[4])}async setUid(e){const a=[t.WacomGSS.STU.Protocol.ReportId.Uid_$LI$(),o.B3(e),o.B2(e),o.B1(e),o.B0(e)];await this.intf.set(a)}async getUid2(){let e=[t.WacomGSS.STU.Protocol.ReportId.Uid2_$LI$(),0,0,0,0,0,0,0,0,0,0];e=await this.intf.get(e);const a={str:"",toString:function(){return this.str}};for(let t=0;t<10&&0!==e[1+t];++t)(n=a).str=n.str.concat(String.fromCharCode(o.I$byte(e[1+t])));var n;return a.str}async getEserial(){let e=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(14);e[0]=t.WacomGSS.STU.Protocol.ReportId.Eserial_$LI$(),e=await this.intf.get(e);const a={str:"",toString:function(){return this.str}};for(let t=0;t<13&&0!==e[1+t];++t)(n=a).str=n.str.concat(String.fromCharCode(o.I$byte(e[1+t])));var n;return a.str}async getHostPublicKey(){let e=[t.WacomGSS.STU.Protocol.ReportId.HostPublicKey_$LI$(),0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];e=await this.intf.get(e);return new t.WacomGSS.STU.Protocol.PublicKey(e,1)}async setHostPublicKey(e){const o=e.getValue(),a=[t.WacomGSS.STU.Protocol.ReportId.HostPublicKey_$LI$(),o[0],o[1],o[2],o[3],o[4],o[5],o[6],o[7],o[8],o[9],o[10],o[11],o[12],o[13],o[14],o[15]];await this.intf.set(a)}async getDevicePublicKey(){let e=[t.WacomGSS.STU.Protocol.ReportId.DevicePublicKey_$LI$(),0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];e=await this.intf.get(e);return new t.WacomGSS.STU.Protocol.DevicePublicKey(e,1)}async setStartCapture(e){const a=[t.WacomGSS.STU.Protocol.ReportId.StartCapture_$LI$(),o.B3(e),o.B2(e),o.B1(e),o.B0(e)];await this.intf.set(a)}async setEndCapture(){const e=[t.WacomGSS.STU.Protocol.ReportId.EndCapture_$LI$(),0];await this.intf.set(e)}async getDHprime(){const e=[t.WacomGSS.STU.Protocol.ReportId.DHprime_$LI$(),0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];let o=await this.intf.get(e);return new t.WacomGSS.STU.Protocol.DHprime(o,1)}async setDHprime(e){const o=e.getValue(),a=[t.WacomGSS.STU.Protocol.ReportId.DHprime_$LI$(),o[0],o[1],o[2],o[3],o[4],o[5],o[6],o[7],o[8],o[9],o[10],o[11],o[12],o[13],o[14],o[15]];await this.intf.set(a)}async getDHbase(){let e=[t.WacomGSS.STU.Protocol.ReportId.DHbase_$LI$(),0,0];e=await this.intf.get(e);return new t.WacomGSS.STU.Protocol.DHbase(e,1)}async setDHbase(e){const o=e.getValue(),a=[t.WacomGSS.STU.Protocol.ReportId.DHbase_$LI$(),o[0],o[1]];await this.intf.set(a)}async setClearScreen(){const e=[t.WacomGSS.STU.Protocol.ReportId.ClearScreen_$LI$(),0];await this.intf.set(e)}async setClearScreenArea(e){const a=[t.WacomGSS.STU.Protocol.ReportId.ClearScreenArea_$LI$(),0,1,o.B0(e.getUpperLeftXpixel()),o.B1(e.getUpperLeftXpixel()),o.B0(e.getUpperLeftYpixel()),o.B1(e.getUpperLeftYpixel()),o.B0(e.getLowerRightXpixel()),o.B1(e.getLowerRightXpixel()),o.B0(e.getLowerRightYpixel()),o.B1(e.getLowerRightYpixel())];await this.intf.set(a)}async getInkingMode(){let e=[t.WacomGSS.STU.Protocol.ReportId.InkingMode_$LI$(),0];return e=await this.intf.get(e),e[1]}async setInkingMode(e){const o=[t.WacomGSS.STU.Protocol.ReportId.InkingMode_$LI$(),t.WacomGSS.STU.Protocol.InkingMode._$wrappers[e].getValue()];await this.intf.set(o)}async getInkThreshold(){let e=[t.WacomGSS.STU.Protocol.ReportId.InkThreshold_$LI$(),0,0,0,0];e=await this.intf.get(e);return new t.WacomGSS.STU.Protocol.InkThreshold(o.I$byte$byte(e[2],e[1]),o.I$byte$byte(e[4],e[3]))}async setInkThreshold(e){const a=[t.WacomGSS.STU.Protocol.ReportId.InkThreshold_$LI$(),o.B0(e.getOnPressureMark()),o.B1(e.getOnPressureMark()),o.B0(e.getOffPressureMark()),o.B1(e.getOffPressureMark())];await this.intf.set(a)}async setStartImageData$com_WacomGSS_STU_Protocol_EncodingMode(e){const o=[t.WacomGSS.STU.Protocol.ReportId.StartImageData_$LI$(),t.WacomGSS.STU.Protocol.EncodingMode._$wrappers[e].getValue()];await this.intf.set(o)}async setStartImageData(t){if("number"==typeof t||null===t)return await this.setStartImageData$com_WacomGSS_STU_Protocol_EncodingMode(t);if("number"==typeof t||null===t)return await this.setStartImageData$int(t);throw new Error("invalid overload")}async setStartImageData$int(e){const a=[t.WacomGSS.STU.Protocol.ReportId.StartImageData_$LI$(),o.B0(e)];await this.intf.set(a)}async setStartImageDataArea(e,a){const n=[t.WacomGSS.STU.Protocol.ReportId.StartImageDataArea_$LI$(),t.WacomGSS.STU.Protocol.EncodingMode._$wrappers[e].getValue(),1,o.B0(a.getUpperLeftXpixel()),o.B1(a.getUpperLeftXpixel()),o.B0(a.getUpperLeftYpixel()),o.B1(a.getUpperLeftYpixel()),o.B0(a.getLowerRightXpixel()),o.B1(a.getLowerRightXpixel()),o.B0(a.getLowerRightYpixel()),o.B1(a.getLowerRightYpixel())];await this.intf.set(n)}async setImageDataBlock(e){const a=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(e.getData().length>t.WacomGSS.STU.Protocol.ImageDataBlock.maxLengthHID?2560:256),n=e.getData();a[0]=t.WacomGSS.STU.Protocol.ReportId.ImageDataBlock_$LI$(),a[1]=o.B0(n.length),a[2]=o.B1(n.length);for(let t=0;t<n.length;t++)a[t+3]=n[t];await this.intf.set(a)}async setEndImageData$com_WacomGSS_STU_Protocol_EndImageDataFlag(e){const o=[t.WacomGSS.STU.Protocol.ReportId.EndImageData_$LI$(),t.WacomGSS.STU.Protocol.EndImageDataFlag._$wrappers[e].getValue()];await this.intf.set(o)}async setEndImageData(t){if("number"==typeof t||null===t)return await this.setEndImageData$com_WacomGSS_STU_Protocol_EndImageDataFlag(t);if(void 0===t)return await this.setEndImageData$();throw new Error("invalid overload")}async setEndImageData$(){await this.setEndImageData$com_WacomGSS_STU_Protocol_EndImageDataFlag(t.WacomGSS.STU.Protocol.EndImageDataFlag.Commit)}async getHandwritingThicknessColor(){let e=[t.WacomGSS.STU.Protocol.ReportId.HandwritingThicknessColor_$LI$(),0,0,0];e=await this.intf.get(e);return new t.WacomGSS.STU.Protocol.HandwritingThicknessColor(o.I$byte$byte(e[2],e[1]),o.I$byte(e[3]))}async setHandwritingThicknessColor(e){const a=[t.WacomGSS.STU.Protocol.ReportId.HandwritingThicknessColor_$LI$(),o.B0(e.getPenColor()),o.B1(e.getPenColor()),o.B0(e.getPenThickness())];await this.intf.set(a)}async getHandwritingThicknessColor24(){let e=[t.WacomGSS.STU.Protocol.ReportId.HandwritingThicknessColor24_$LI$(),0,0,0,0];e=await this.intf.get(e);return new t.WacomGSS.STU.Protocol.HandwritingThicknessColor24(o.I$byte$byte$byte$byte(0,e[3],e[2],e[1]),o.I$byte(e[4]))}async setHandwritingThicknessColor24(e){const a=[t.WacomGSS.STU.Protocol.ReportId.HandwritingThicknessColor24_$LI$(),o.B0(e.getPenColor()),o.B1(e.getPenColor()),o.B2(e.getPenColor()),o.B0(e.getPenThickness())];await this.intf.set(a)}async getBackgroundColor(){let e=[t.WacomGSS.STU.Protocol.ReportId.BackgroundColor_$LI$(),0,0];e=await this.intf.get(e);return o.I$byte$byte(e[2],e[1])}async setBackgroundColor(e){const a=[t.WacomGSS.STU.Protocol.ReportId.BackgroundColor_$LI$(),o.B0(e),o.B1(e)];await this.intf.set(a)}async getBackgroundColor24(){let e=[t.WacomGSS.STU.Protocol.ReportId.BackgroundColor24_$LI$(),0,0,0];e=await this.intf.get(e);return o.I$byte$byte$byte$byte(0,e[3],e[2],e[1])}async setBackgroundColor24(e){const a=[t.WacomGSS.STU.Protocol.ReportId.BackgroundColor24_$LI$(),o.B0(e),o.B1(e),o.B2(e)];await this.intf.set(a)}async getScreenContrast(){let e=[t.WacomGSS.STU.Protocol.ReportId.ScreenContrast_$LI$(),0,0];e=await this.intf.get(e);return o.I$byte$byte(e[2],e[1])}async setScreenContrast(e){const a=[t.WacomGSS.STU.Protocol.ReportId.ScreenContrast_$LI$(),o.B0(e),o.B1(e)];await this.intf.set(a)}async getHandwritingDisplayArea(){let e=[t.WacomGSS.STU.Protocol.ReportId.HandwritingDisplayArea_$LI$(),0,0,0,0,0,0,0,0];e=await this.intf.get(e);return new t.WacomGSS.STU.Protocol.Rectangle(o.I$byte$byte(e[2],e[1]),o.I$byte$byte(e[4],e[3]),o.I$byte$byte(e[6],e[5]),o.I$byte$byte(e[8],e[7]))}async setHandwritingDisplayArea(e){const a=[t.WacomGSS.STU.Protocol.ReportId.HandwritingDisplayArea_$LI$(),o.B0(e.getUpperLeftXpixel()),o.B1(e.getUpperLeftXpixel()),o.B0(e.getUpperLeftYpixel()),o.B1(e.getUpperLeftYpixel()),o.B0(e.getLowerRightXpixel()),o.B1(e.getLowerRightXpixel()),o.B0(e.getLowerRightYpixel()),o.B1(e.getLowerRightYpixel())];await this.intf.set(a)}async getBacklightBrightness(){let e=[t.WacomGSS.STU.Protocol.ReportId.BacklightBrightness_$LI$(),0,0];e=await this.intf.get(e);return o.I$byte$byte(e[2],e[1])}async setBacklightBrightness(e){const a=[t.WacomGSS.STU.Protocol.ReportId.BacklightBrightness_$LI$(),o.B0(e),o.B1(e)];await this.intf.set(a)}async getPenDataOptionMode(){let e=[t.WacomGSS.STU.Protocol.ReportId.PenDataOptionMode_$LI$(),0];return e=await this.intf.get(e),e[1]}async setPenDataOptionMode(e){const o=[t.WacomGSS.STU.Protocol.ReportId.PenDataOptionMode_$LI$(),t.WacomGSS.STU.Protocol.PenDataOptionMode._$wrappers[e].getValue()];await this.intf.set(o)}async getEncryptionStatus(){let e=[t.WacomGSS.STU.Protocol.ReportId.EncryptionStatus_$LI$(),0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];e=await this.intf.get(e);return new t.WacomGSS.STU.Protocol.EncryptionStatus(e[1],e[2]>>6&3|0,63&e[2]|0,e[4],e[5],e[6],e[7],0!=(4&e[8]),0!=(2&e[8]),0!=(1&e[8]))}async getEncryptionCommand(e){let o=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(68);o[0]=t.WacomGSS.STU.Protocol.ReportId.EncryptionCommand_$LI$(),o[1]=t.WacomGSS.STU.Protocol.EncryptionCommandNumber._$wrappers[e].getValue(),o=await this.intf.get(o);return new t.WacomGSS.STU.Protocol.EncryptionCommand(e,o[2],o[3],o,4,t.WacomGSS.STU.Protocol.EncryptionCommand.maxDataLength)}async setEncryptionCommand(e){const o=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(68);o[0]=t.WacomGSS.STU.Protocol.ReportId.EncryptionCommand_$LI$(),o[1]=e.getEncryptionCommandNumber(),o[2]=e.getParameter(),o[3]=e.getLengthOrIndex(),((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(e.getData(),0,o,4,t.WacomGSS.STU.Protocol.EncryptionCommand.maxDataLength),await this.intf.set(o)}static initializeSetEncryptionType(e,o,a){const n=(3&t.WacomGSS.STU.Protocol.AsymmetricPaddingType._$wrappers[o].getValue())<<6|3&t.WacomGSS.STU.Protocol.AsymmetricKeyType._$wrappers[a].getValue()|0;return new t.WacomGSS.STU.Protocol.EncryptionCommand(t.WacomGSS.STU.Protocol.EncryptionCommandNumber.SetEncryptionType,t.WacomGSS.STU.Protocol.SymmetricKeyType._$wrappers[e].getValue(),n,null)}static initializeSetParameterBlock(e,o){return new t.WacomGSS.STU.Protocol.EncryptionCommand(t.WacomGSS.STU.Protocol.EncryptionCommandNumber.SetParameterBlock,t.WacomGSS.STU.Protocol.EncryptionCommandParameterBlockIndex._$wrappers[e].getValue(),255&o.length|0,o)}static initializeGenerateSymmetricKey(){return new t.WacomGSS.STU.Protocol.EncryptionCommand(t.WacomGSS.STU.Protocol.EncryptionCommandNumber.GenerateSymmetricKey,0,0,null)}static initializeGetParameterBlock(e,o){if(o<0||o>255)throw Object.defineProperty(new Error("offset out of range"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});return new t.WacomGSS.STU.Protocol.EncryptionCommand(t.WacomGSS.STU.Protocol.EncryptionCommandNumber.GetParameterBlock,t.WacomGSS.STU.Protocol.EncryptionCommandParameterBlockIndex._$wrappers[e].getValue(),255&o|0,null)}static decodePenData(e,a){return new t.WacomGSS.STU.Protocol.PenData(e[a+0]>>7&1,e[a+0]>>4&7,(15&e[a+0])<<8|o.I$byte(e[a+1]),o.I$byte$byte(e[a+2],e[a+3]),o.I$byte$byte(e[a+4],e[a+5]))}static decodePenDataOption(e,a){return new t.WacomGSS.STU.Protocol.PenDataOption(o.decodePenData(e,a),o.I$byte$byte(e[a+6],e[a+7]))}static decodeDevicePublicKey(e,o){return new t.WacomGSS.STU.Protocol.DevicePublicKey(e,o)}static decodePenDataEncrypted(e,a){const n=o.I$byte$byte$byte$byte(e[a+0],e[a+1],e[a+2],e[a+3]),r=o.decodePenData(e,a+4),i=o.decodePenData(e,a+10);return new t.WacomGSS.STU.Protocol.PenDataEncrypted(n,r,i)}static decodePenDataEncryptedOption(e,a){const n=o.I$byte$byte(e[a+16],e[a+17]),r=o.I$byte$byte(e[a+18],e[a+19]);return new t.WacomGSS.STU.Protocol.PenDataEncryptedOption(o.decodePenDataEncrypted(e,a),n,r)}static decodePenDataTimeCountSequence(e,a){return new t.WacomGSS.STU.Protocol.PenDataTimeCountSequence(o.decodePenData(e,a),o.I$byte$byte(e[a+6],e[a+7]),o.I$byte$byte(e[a+8],e[a+9]))}static decodePenDataTimeCountSequenceEncrypted(e,a){return new t.WacomGSS.STU.Protocol.PenDataTimeCountSequenceEncrypted(o.decodePenDataTimeCountSequence(e,a),o.I$byte$byte$byte$byte(e[a+12],e[a+13],e[a+14],e[a+15]))}static decodeEncryptionStatus(e,o){return new t.WacomGSS.STU.Protocol.EncryptionStatus(e[o+0],e[o+1]>>6&3|0,63&e[o+1]|0,e[o+3],e[o+4],e[o+5],e[o+6],0!=(4&e[o+7]),0!=(2&e[o+7]),0!=(1&e[o+7]))}static decodeEventDataPinPad(e,o){return new t.WacomGSS.STU.Protocol.EventDataPinPad(e,o)}static decodeEventDataKeyPad(e,o){return new t.WacomGSS.STU.Protocol.EventDataKeyPad(e,o)}static decodeEventDataSignature(e,o){return new t.WacomGSS.STU.Protocol.EventDataSignature(e,o)}static decodeEventDataPinPadEncrypted(e,a){const n=o.I$byte$byte$byte$byte(e[a+1],e[a+2],e[a+3],e[a+4]);return new t.WacomGSS.STU.Protocol.EventDataPinPadEncrypted(o.decodeEventDataPinPad(e,a+7),n)}static decodeEventDataKeyPadEncrypted(e,a){const n=o.I$byte$byte$byte$byte(e[a+1],e[a+2],e[a+3],e[a+4]);return new t.WacomGSS.STU.Protocol.EventDataKeyPadEncrypted(o.decodeEventDataKeyPad(e,a+7),n)}static decodeEventDataSignatureEncrypted(e,a){const n=o.I$byte$byte$byte$byte(e[a+1],e[a+2],e[a+3],e[a+4]);return new t.WacomGSS.STU.Protocol.EventDataSignatureEncrypted(o.decodeEventDataSignature(e,a+4),n)}async getReportSizeCollection(){let e=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(512);e[0]=t.WacomGSS.STU.Protocol.ReportId.ReportSizeCollection_$LI$(),e=await this.intf.get(e);const a=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(256);a[0]=0;for(let t=1;t<=255;++t)a[t]=o.I$byte$byte(e[0+2*t],e[1+2*t]);return a}async getReportRate(){let e=[t.WacomGSS.STU.Protocol.ReportId.ReportRate_$LI$(),0];return e=await this.intf.get(e),o.I$byte(e[1])}async setReportRate(e){const a=[t.WacomGSS.STU.Protocol.ReportId.DefaultMode_$LI$(),o.B0(e)];await this.intf.set(a)}async getRenderingMode(){t.WacomGSS.STU.Protocol.ReportId.RenderingMode_$LI$();return cmd=await this.intf.get(cmd),cmd[1]}async setRenderingMode(e){const o=[t.WacomGSS.STU.Protocol.ReportId.RenderingMode_$LI$(),t.WacomGSS.STU.Protocol.RenderingMode._$wrappers[e].getValue()];await this.intf.set(o)}async getBootScreen(){let e=[t.WacomGSS.STU.Protocol.ReportId.BootScreen_$LI$(),0];return e=await this.intf.get(e),e[1]}async setBootScreen(e){const o=[t.WacomGSS.STU.Protocol.ReportId.BootScreen_$LI$(),t.WacomGSS.STU.Protocol.BootScreenFlag._$wrappers[e].getValue()];await this.intf.set(o)}async getOperationMode(){let e=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(t.WacomGSS.STU.Protocol.OperationMode.dataBytes+2);return e[0]=t.WacomGSS.STU.Protocol.ReportId.OperationMode_$LI$(),e=await this.intf.get(e),new t.WacomGSS.STU.Protocol.OperationMode(e)}async setOperationMode(e){const o=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(t.WacomGSS.STU.Protocol.OperationMode.dataBytes+2);o[0]=t.WacomGSS.STU.Protocol.ReportId.OperationMode_$LI$(),o[1]=e.getOperationModeType(),((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(e.getData(),0,o,2,t.WacomGSS.STU.Protocol.OperationMode.dataBytes),await this.intf.set(o)}async setRomStartImageData(e){const o=[0,0,0,0,0,0,0];o[0]=t.WacomGSS.STU.Protocol.ReportId.RomStartImageData_$LI$(),o[1]=t.WacomGSS.STU.Protocol.EncodingMode._$wrappers[e.getEncodingMode()].getValue(),o[2]=e.getOperationModeType(),o[3]=(e.getImageType()?128:0)|127&e.getImageNumber()|0,((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(e.getData(),0,o,4,t.WacomGSS.STU.Protocol.RomStartImageData.dataBytes),await this.intf.set(o)}async getRomImageHash(){let e=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(t.WacomGSS.STU.Protocol.RomImageHash.myLength_$LI$());e[0]=t.WacomGSS.STU.Protocol.ReportId.RomImageHash_$LI$(),e=await this.intf.get(e);const o=e[1],a=0!=(128&e[2]),n=127&e[2]|0,r=e[3];return new t.WacomGSS.STU.Protocol.RomImageHash(o,a,n,r,e,4)}async setRomImageHash(e,o,a){const n=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(t.WacomGSS.STU.Protocol.RomImageHash.myLength_$LI$());n[0]=t.WacomGSS.STU.Protocol.ReportId.RomImageHash_$LI$(),n[1]=e,n[2]=(o?128:0)|127&a|0,await this.intf.set(n)}async setRomImageDelete(e,o,a){const n=[t.WacomGSS.STU.Protocol.ReportId.RomImageDelete_$LI$(),e,(o?128:0)|127&a|0];await this.intf.set(n)}async getCurrentImageArea(){const e=[0,0,0,0,0,0,0,0,0];return e[0]=t.WacomGSS.STU.Protocol.ReportId.CurrentImageArea_$LI$(),await this.intf.get(e),new t.WacomGSS.STU.Protocol.Rectangle(e[1]|e[2]<<8,e[3]|e[4]<<8,e[5]|e[6]<<8,e[7]|e[8]<<8)}async setRomImageDisplay(e,o,a){const n=[t.WacomGSS.STU.Protocol.ReportId.RomImageDisplay_$LI$(),e,(o?128:0)|127&a|0];await this.intf.set(n)}}e.Protocol=o,o.__class="com.WacomGSS.STU.Protocol.Protocol"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{static I(t){return 255&t}static B0(t){return 255&t|0}static B1(t){return t>>8&255|0}static B2(t){return t>>16&255|0}static sendTable_$LI$(){return null==o.sendTable&&(o.sendTable=[new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.Status_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot|o.OpStatus_RomBusy),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.Reset_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot|o.OpStatus_RomBusy),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.Information_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot|o.OpStatus_RomBusy),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.HidInformation_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot|o.OpStatus_RomBusy),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.Capability_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot|o.OpStatus_RomBusy),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.Uid_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot|o.OpStatus_RomBusy),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.Uid_$LI$()),o.OpStatus_Ready),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.Uid2_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.DefaultMode_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot|o.OpStatus_RomBusy),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.DefaultMode_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot|o.OpStatus_RomBusy),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.ReportRate_$LI$()),o.OpStatus_Ready|o.OpStatus_RomBusy),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.ReportRate_$LI$()),o.OpStatus_Ready|o.OpStatus_RomBusy),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.RenderingMode_$LI$()),o.OpStatus_Ready|o.OpStatus_Capture|o.OpStatus_RomBusy),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.RenderingMode_$LI$()),o.OpStatus_Ready|o.OpStatus_Capture|o.OpStatus_RomBusy),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.Eserial_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot|o.OpStatus_RomBusy),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.HostPublicKey_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.HostPublicKey_$LI$()),o.OpStatus_Ready),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.DevicePublicKey_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.StartCapture_$LI$()),o.OpStatus_Ready),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.EndCapture_$LI$()),o.OpStatus_Capture),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.DHprime_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.DHprime_$LI$()),o.OpStatus_Ready),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.DHbase_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.DHbase_$LI$()),o.OpStatus_Ready),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.ClearScreen_$LI$()),o.OpStatus_Ready),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.InkingMode_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.InkingMode_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.InkThreshold_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.InkThreshold_$LI$()),o.OpStatus_Ready),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.ClearScreenArea_$LI$()),o.OpStatus_Ready),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.StartImageDataArea_$LI$()),o.OpStatus_Ready),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.StartImageData_$LI$()),o.OpStatus_Ready),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.ImageDataBlock_$LI$()),o.OpStatus_Image),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.EndImageData_$LI$()),o.OpStatus_Image),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.HandwritingThicknessColor_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.HandwritingThicknessColor_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.BackgroundColor_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.BackgroundColor_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.HandwritingDisplayArea_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.HandwritingDisplayArea_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.BacklightBrightness_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.BacklightBrightness_$LI$()),o.OpStatus_Ready|o.OpStatus_Capture),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.ScreenContrast_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.ScreenContrast_$LI$()),o.OpStatus_Ready|o.OpStatus_Capture),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.HandwritingThicknessColor24_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.HandwritingThicknessColor24_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.BackgroundColor24_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.BackgroundColor24_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.BootScreen_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.BootScreen_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.PenDataOptionMode_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.PenDataOptionMode_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.EncryptionCommand_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.EncryptionCommand_$LI$()),o.OpStatus_Ready),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.EncryptionStatus_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.GetReport_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.PinOperationMode_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot|o.OpStatus_RomBusy),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.PinOperationMode_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot|o.OpStatus_RomBusy),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.OperationMode_$LI$()),o.OpStatus_Ready|o.OpStatus_Capture),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.OperationMode_$LI$()),o.OpStatus_Ready|o.OpStatus_Capture),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.RomStartImageData_$LI$()),o.OpStatus_Ready),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.RomImageOccupancy_$LI$()),o.OpStatus_Ready|o.OpStatus_Capture),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.RomImageOccupancy_$LI$()),o.OpStatus_Ready|o.OpStatus_Capture),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.RomImageHash_$LI$()),o.OpStatus_Ready|o.OpStatus_Capture),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.RomImageHash_$LI$()),o.OpStatus_Ready|o.OpStatus_Capture),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.RomImageDelete_$LI$()),o.OpStatus_Ready|o.OpStatus_Capture),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.RomImageDelete_$LI$()),o.OpStatus_Ready|o.OpStatus_Capture),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.CurrentImageArea_$LI$()),o.OpStatus_Ready|o.OpStatus_Capture),new o.SendEntry(o.OpDirection_Set|o.I(t.WacomGSS.STU.Protocol.ReportId.RomImageDisplay_$LI$()),o.OpStatus_Ready|o.OpStatus_Capture),new o.SendEntry(o.OpDirection_Get|o.I(t.WacomGSS.STU.Protocol.ReportId.ReportSizeCollection_$LI$()),o.OpStatus_Ready|o.OpStatus_Image|o.OpStatus_Capture|o.OpStatus_Calculation|o.OpStatus_Image_Boot|o.OpStatus_RomBusy)]),o.sendTable}static opStatus_from_StatusCode(e){let a;switch(e){case t.WacomGSS.STU.Protocol.StatusCode.Ready_$LI$():a=o.OpStatus_Ready;break;case t.WacomGSS.STU.Protocol.StatusCode.Image_$LI$():a=o.OpStatus_Image;break;case t.WacomGSS.STU.Protocol.StatusCode.Capture_$LI$():a=o.OpStatus_Capture;break;case t.WacomGSS.STU.Protocol.StatusCode.Calculation_$LI$():a=o.OpStatus_Calculation;break;case t.WacomGSS.STU.Protocol.StatusCode.Image_Boot_$LI$():a=o.OpStatus_Image_Boot;break;case t.WacomGSS.STU.Protocol.StatusCode.RomBusy_$LI$():a=o.OpStatus_RomBusy;break;case t.WacomGSS.STU.Protocol.StatusCode.SystemReset_$LI$():a=o.OpStatus_SystemReset;break;default:throw Object.defineProperty(new Error("Invalid status value"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]})}return a}static opBitsFromReport(t,e){const a=o.I(t)|e;for(let t=0;t<o.sendTable_$LI$().length;++t)if(o.sendTable_$LI$()[t].first===a)return o.sendTable_$LI$()[t].second;throw Object.defineProperty(new Error("combination of reportId and opDirection is invalid"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]})}static statusCanSend(t,e,a){const n=o.opBitsFromReport(e,a);return 0!=(o.opStatus_from_StatusCode(t)&n)}static async delay(t){return new Promise((e=>setTimeout(e,t)))}static async waitForStatusToSend(e,a,n,r,i){const c=o.opBitsFromReport(a,n);for(;;){const l=await e.getStatus();if(0!=(o.opStatus_from_StatusCode(l.getStatusCode())&c))break;if(0===r)throw new t.WacomGSS.STU.TimeoutException("Time out waiting for tablet to accept ReportId="+a+" opDirection="+n);--r,await this.delay(i)}}static async waitForStatus(e,o,a,n){for(;;){const r=await e.getStatus();if(await r.getStatusCode()===o)break;if(0===a)throw new t.WacomGSS.STU.TimeoutException("Time out waiting for tablet status "+o);--a,await this.delay(n)}}static supportsEncryption$com_WacomGSS_STU_Protocol_DHprime(t){return!t.isEmpty()}static async supportsEncryption(e){if(null!=e&&e instanceof t.WacomGSS.STU.Protocol.DHprime||null===e)return t.WacomGSS.STU.Protocol.ProtocolHelper.supportsEncryption$com_WacomGSS_STU_Protocol_DHprime(e);if(null!=e&&e instanceof t.WacomGSS.STU.Protocol.Protocol||null===e)return t.WacomGSS.STU.Protocol.ProtocolHelper.supportsEncryption$com_WacomGSS_STU_Protocol_Protocol(e);throw new Error("invalid overload")}static async supportsEncryption$com_WacomGSS_STU_Protocol_Protocol(t){const e=await t.getDHprime();return o.supportsEncryption$com_WacomGSS_STU_Protocol_DHprime(e)}static async setHostPublicKeyAndPollForDevicePublicKey(e,a,n,r){return await e.setHostPublicKey(a),await o.waitForStatusToSend(e,t.WacomGSS.STU.Protocol.ReportId.DevicePublicKey_$LI$(),o.OpDirection_Get,n,r),e.getDevicePublicKey()}static async generateSymmetricKeyAndWaitForEncryptionStatus(e,a,n,r){await o.waitForStatusToSend(e,t.WacomGSS.STU.Protocol.ReportId.EncryptionCommand_$LI$(),o.OpDirection_Set,a,n);const i=e.getInterface().interfaceQueue();await e.setEncryptionCommand(t.WacomGSS.STU.Protocol.Protocol.initializeGenerateSymmetricKey());{const o=await e.getEncryptionStatus();if(o.getLastResultCode()!==t.WacomGSS.STU.Protocol.ErrorCodeRSA.None_$LI$())return o}let c;for(;null!=(c=await i.wait_until_getReport(r));)if(c.length>=t.WacomGSS.STU.Protocol.EncryptionStatus.reportSize&&c[0]===t.WacomGSS.STU.Protocol.ReportId.EncryptionStatus_$LI$()){return await t.WacomGSS.STU.Protocol.Protocol.decodeEncryptionStatus(c,1)}throw new t.WacomGSS.STU.TimeoutException("Time out waiting for report EncryptionStatus")}static simulateEncodingFlag(e,o){if(0==(o&(t.WacomGSS.STU.Protocol.EncodingFlag.EncodingFlag_24bit_$LI$()|t.WacomGSS.STU.Protocol.EncodingFlag.EncodingFlag_16bit_$LI$()|t.WacomGSS.STU.Protocol.EncodingFlag.EncodingFlag_1bit_$LI$())))switch(e){case t.WacomGSS.STU.UsbDevice.ProductId_540_$LI$():case t.WacomGSS.STU.UsbDevice.ProductId_530V_$LI$():case t.WacomGSS.STU.UsbDevice.ProductId_530_$LI$():o=t.WacomGSS.STU.Protocol.EncodingFlag.EncodingFlag_24bit_$LI$()|t.WacomGSS.STU.Protocol.EncodingFlag.EncodingFlag_16bit_$LI$()|t.WacomGSS.STU.Protocol.EncodingFlag.EncodingFlag_1bit_$LI$()|t.WacomGSS.STU.Protocol.EncodingFlag.EncodingFlag_Zlib_$LI$();break;case t.WacomGSS.STU.UsbDevice.ProductId_520A_$LI$():o=t.WacomGSS.STU.Protocol.EncodingFlag.EncodingFlag_16bit_$LI$()|t.WacomGSS.STU.Protocol.EncodingFlag.EncodingFlag_1bit_$LI$();break;case t.WacomGSS.STU.UsbDevice.ProductId_500_$LI$():o=t.WacomGSS.STU.Protocol.EncodingFlag.EncodingFlag_1bit_$LI$();break;case t.WacomGSS.STU.UsbDevice.ProductId_430V_$LI$():case t.WacomGSS.STU.UsbDevice.ProductId_430_$LI$():case t.WacomGSS.STU.UsbDevice.ProductId_300_$LI$():o=t.WacomGSS.STU.Protocol.EncodingFlag.EncodingFlag_1bit_$LI$();break;default:o=t.WacomGSS.STU.Protocol.EncodingFlag.EncodingFlag_1bit_$LI$()}return o}static encodingFlagSupportsColor(e){return 0!=(e&(t.WacomGSS.STU.Protocol.EncodingFlag.EncodingFlag_24bit_$LI$()|t.WacomGSS.STU.Protocol.EncodingFlag.EncodingFlag_16bit_$LI$()))}static resizeAndFlatten(e,a,n,r,i,c,l,s,S,u,d,p){let m=null;if(r!==c||i!==l){let o,s,p,h,_,g,y,I;switch(S){case t.WacomGSS.STU.Protocol.ProtocolHelper.Scale.Stretch:o=0,s=0,p=c,h=l,_=0,g=0,y=r,I=i;break;case t.WacomGSS.STU.Protocol.ProtocolHelper.Scale.Clip:if(r>c){switch(15&d){default:case 0:_=0;break;case 1:_=(r-c)/2|0;break;case 2:_=r-c-1}y=_+c,o=0,p=c}else _=0,y=r,o=(c-r)/2|0,p=o+r;if(i>l){switch(d>>4&15){default:case 0:g=0;break;case 1:g=(i-l)/2|0;break;case 2:g=i-l-1}I=g+l,s=0,h=l}else g=0,I=i,s=(l-i)/2|0,h=s+i;break;default:case t.WacomGSS.STU.Protocol.ProtocolHelper.Scale.Fit:{const t=Math.fround(r/i);let e,a;t>Math.fround(c/l)?(e=c,a=Math.round(Math.fround(e/t))):(a=l,e=Math.round(Math.fround(a*t))),o=(c-e)/2|0,s=(l-a)/2|0,p=o+e,h=s+a,_=0,g=0,y=r,I=i}}_+=a,g+=n,y+=a,I+=n,a=n=0;let P=document.createElement("canvas");P.width=p+o,P.height=h+s;let f=P.getContext("2d");u&&(f.fillStyle=u,f.fillRect(0,0,P.width,P.height)),f.drawImage(e,_,g,y,I,o,s,p,h),m=P}else m=e;switch(s){case t.WacomGSS.STU.Protocol.EncodingMode.EncodingMode_1bit:case t.WacomGSS.STU.Protocol.EncodingMode.EncodingMode_1bit_ZLib:case t.WacomGSS.STU.Protocol.EncodingMode.EncodingMode_1bit_Bulk:}let h=m.getContext("2d").getImageData(0,0,m.width,m.height);!p||s!=t.WacomGSS.STU.Protocol.EncodingMode.EncodingMode_1bit&&s!=t.WacomGSS.STU.Protocol.EncodingMode.EncodingMode_1bit_ZLib&&s!=t.WacomGSS.STU.Protocol.EncodingMode.EncodingMode_1bit_Bulk||(h=monochrome(h,220,"none"));let _=h.data,g=[];for(var y=0;y<_.byteLength;y+=4)g.push([_[y],_[y+1],_[y+2]]);const I=g.map((function(t){return t[0]<<16|t[1]<<8|t[2]}));return o.flatten(I,0,0,c,l,s)}static flatten(e,a,n,r,i,c){switch(c){case t.WacomGSS.STU.Protocol.EncodingMode.EncodingMode_1bit:case t.WacomGSS.STU.Protocol.EncodingMode.EncodingMode_1bit_ZLib:case t.WacomGSS.STU.Protocol.EncodingMode.EncodingMode_1bit_Bulk:return o.flattenMonochrome(e,r,i,r);case t.WacomGSS.STU.Protocol.EncodingMode.EncodingMode_16bit:case t.WacomGSS.STU.Protocol.EncodingMode.EncodingMode_16bit_Bulk:return o.flattenColor16_565(e,r,i,r);case t.WacomGSS.STU.Protocol.EncodingMode.EncodingMode_24bit:case t.WacomGSS.STU.Protocol.EncodingMode.EncodingMode_24bit_Bulk:return o.flattenColor24(e,r,i,r)}}static flattenMonochrome(t,e,a,n){if(n<e)throw Object.defineProperty(new Error("stride must be bigger than screenWidth"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});if(t.length!==n*a)throw Object.defineProperty(new Error("rgb incorrect length"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});const r=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})((((-8&e)+(0!=(7&e)?8:0))/8|0)*a);((t,e)=>{for(let e=0;e<t.length;e++)t[e]=255})(r);let i=0,c=0;for(let l=0;l<a;++l){let a=0;for(let n=0;n<e;++n){const e=t[i+n],l=o.isInk(e);r[c]&=0|~(l<<7-a%8),a++%8==7&&++c}i+=n,0!=(7&a)&&++c}return r}static rgb16_565(t){return t>>8&63488|t>>5&2016|t>>3&31}static flattenColor16_565(t,e,a,n){if(n<e)throw Object.defineProperty(new Error("stride must be bigger than screenWidth"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});if(t.length!==n*a)throw Object.defineProperty(new Error("rgb incorrect length"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});const r=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(2*e*a);for(let i=0,c=0,l=0;l<a;++l,i+=n)for(let a=0;a<e;++a){const e=t[i+a],n=o.rgb16_565(e);r[c++]=o.B1(n),r[c++]=o.B0(n)}return r}static flattenColor24(t,e,a,n,r){return o.flattenColor24(null,n,r,n)}static flattenColor24(t,e,a,n){if(n<e)throw Object.defineProperty(new Error("stride must be bigger than screenWidth"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});if(t.length!==n*a)throw Object.defineProperty(new Error("rgb incorrect length"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});const r=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(3*e*a);for(let i=0,c=0,l=0;l<a;++l,i+=n)for(let a=0;a<e;++a){const e=t[i+a];r[c++]=o.B0(e),r[c++]=o.B1(e),r[c++]=o.B2(e)}return r}static async writeImageCommon(e,a,n,r,i,c){if(await o.waitForStatusToSend(e,t.WacomGSS.STU.Protocol.ReportId.ImageDataBlock_$LI$(),o.OpDirection_Set,i,c),a===t.WacomGSS.STU.Protocol.EncodingMode.EncodingMode_1bit_Bulk||a===t.WacomGSS.STU.Protocol.EncodingMode.EncodingMode_16bit_Bulk||a===t.WacomGSS.STU.Protocol.EncodingMode.EncodingMode_24bit_Bulk)await e.getInterface().write(r);else{let o=0,a=r.length;for(;0!==a;){const i=a>n?n:a,c=new t.WacomGSS.STU.Protocol.ImageDataBlock(r,o,i);await e.setImageDataBlock(c),o+=i,a-=i}}await e.setEndImageData$com_WacomGSS_STU_Protocol_EndImageDataFlag(t.WacomGSS.STU.Protocol.EndImageDataFlag.Commit)}static async writeImage$com_WacomGSS_STU_Protocol_Protocol$com_WacomGSS_STU_Protocol_EncodingMode$int$byte_A$int$long(e,a,n,r,i,c){await o.waitForStatusToSend(e,await t.WacomGSS.STU.Protocol.ReportId.StartImageData_$LI$(),o.OpDirection_Set,i,c),await e.setStartImageData$com_WacomGSS_STU_Protocol_EncodingMode(a),await o.writeImageCommon(e,a,n,r,i,c)}static async writeImage(e,o,a,n,r,i){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.Protocol||null===e)||"number"!=typeof o&&null!==o||"number"!=typeof a&&null!==a||!(null!=n&&n instanceof Array&&(0==n.length||null==n[0]||"number"==typeof n[0])||null===n)||"number"!=typeof r&&null!==r||"number"!=typeof i&&null!==i){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.Protocol||null===e)||"number"!=typeof o&&null!==o||!(null!=a&&a instanceof Array&&(0==a.length||null==a[0]||"number"==typeof a[0])||null===a)||"number"!=typeof n&&null!==n||"number"!=typeof r&&null!==r||void 0!==i){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.Protocol||null===e)||"number"!=typeof o&&null!==o||!(null!=a&&a instanceof Array&&(0==a.length||null==a[0]||"number"==typeof a[0])||null===a)||"number"!=typeof n&&null!==n||"number"!=typeof r&&null!==r||void 0!==i)throw new Error("invalid overload");return await t.WacomGSS.STU.Protocol.ProtocolHelper.writeImage$com_WacomGSS_STU_Protocol_Protocol$int$byte_A$int$long(e,o,a,n,r)}return await t.WacomGSS.STU.Protocol.ProtocolHelper.writeImage$com_WacomGSS_STU_Protocol_Protocol$com_WacomGSS_STU_Protocol_EncodingMode$byte_A$int$long(e,o,a,n,r)}return await t.WacomGSS.STU.Protocol.ProtocolHelper.writeImage$com_WacomGSS_STU_Protocol_Protocol$com_WacomGSS_STU_Protocol_EncodingMode$int$byte_A$int$long(e,o,a,n,r,i)}static async writeImage$com_WacomGSS_STU_Protocol_Protocol$com_WacomGSS_STU_Protocol_EncodingMode$byte_A$int$long(e,a,n,r,i){await o.writeImage$com_WacomGSS_STU_Protocol_Protocol$com_WacomGSS_STU_Protocol_EncodingMode$int$byte_A$int$long(e,a,t.WacomGSS.STU.Protocol.ImageDataBlock.maxLengthHID,n,r,i)}static async writeImageArea$com_WacomGSS_STU_Protocol_Protocol$com_WacomGSS_STU_Protocol_EncodingMode$com_WacomGSS_STU_Protocol_Rectangle$int$byte_A$int$long(e,a,n,r,i,c,l){await o.waitForStatusToSend(e,t.WacomGSS.STU.Protocol.ReportId.StartImageData_$LI$(),o.OpDirection_Set,c,l),await e.setStartImageDataArea(a,n),await o.writeImageCommon(e,a,r,i,c,l)}static async writeImageArea(e,o,a,n,r,i,c){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.Protocol||null===e)||"number"!=typeof o&&null!==o||!(null!=a&&a instanceof t.WacomGSS.STU.Protocol.Rectangle||null===a)||"number"!=typeof n&&null!==n||!(null!=r&&r instanceof Array&&(0==r.length||null==r[0]||"number"==typeof r[0])||null===r)||"number"!=typeof i&&null!==i||"number"!=typeof c&&null!==c){if(!(null!=e&&e instanceof t.WacomGSS.STU.Protocol.Protocol||null===e)||"number"!=typeof o&&null!==o||!(null!=a&&a instanceof t.WacomGSS.STU.Protocol.Rectangle||null===a)||!(null!=n&&n instanceof Array&&(0==n.length||null==n[0]||"number"==typeof n[0])||null===n)||"number"!=typeof r&&null!==r||"number"!=typeof i&&null!==i||void 0!==c)throw new Error("invalid overload");return await t.WacomGSS.STU.Protocol.ProtocolHelper.writeImageArea$com_WacomGSS_STU_Protocol_Protocol$com_WacomGSS_STU_Protocol_EncodingMode$com_WacomGSS_STU_Protocol_Rectangle$byte_A$int$long(e,o,a,n,r,i)}return await t.WacomGSS.STU.Protocol.ProtocolHelper.writeImageArea$com_WacomGSS_STU_Protocol_Protocol$com_WacomGSS_STU_Protocol_EncodingMode$com_WacomGSS_STU_Protocol_Rectangle$int$byte_A$int$long(e,o,a,n,r,i,c)}static async writeImageArea$com_WacomGSS_STU_Protocol_Protocol$com_WacomGSS_STU_Protocol_EncodingMode$com_WacomGSS_STU_Protocol_Rectangle$byte_A$int$long(e,a,n,r,i,c){await o.writeImageArea$com_WacomGSS_STU_Protocol_Protocol$com_WacomGSS_STU_Protocol_EncodingMode$com_WacomGSS_STU_Protocol_Rectangle$int$byte_A$int$long(e,a,n,t.WacomGSS.STU.Protocol.ImageDataBlock.maxLengthHID,r,i,c)}static async writeImage$com_WacomGSS_STU_Protocol_Protocol$int$byte_A$int$long(e,a,n,r,i){if(await o.waitForStatusToSend(e,t.WacomGSS.STU.Protocol.ReportId.StartImageData_$LI$(),o.OpDirection_Set,r,i),await e.setStartImageData$int(a),await o.waitForStatusToSend(e,t.WacomGSS.STU.Protocol.ReportId.ImageDataBlock_$LI$(),o.OpDirection_Set,r,i),0!=(a&t.WacomGSS.STU.Protocol.EncodingMode._$wrappers[t.WacomGSS.STU.Protocol.EncodingMode].EncodingMode_Bulk))await e.getInterface().write(n);else{let o=0,a=n.length;for(;0!==a;){const r=a>t.WacomGSS.STU.Protocol.ImageDataBlock.maxLengthHID?t.WacomGSS.STU.Protocol.ImageDataBlock.maxLengthHID:a,i=new t.WacomGSS.STU.Protocol.ImageDataBlock(n,o,r);await e.setImageDataBlock(i),o+=r,a-=r}}await e.setEndImageData$com_WacomGSS_STU_Protocol_EndImageDataFlag(t.WacomGSS.STU.Protocol.EndImageDataFlag.Commit)}static isInk(t){return t<=14211289?1:0}static clamp(t){return Math.min(Math.max(Math.round(t),0),255)}static rgb16_565(t){return t>>8&63488|t>>5&2016|t>>3&31}static flattenColor16_565(t,e,a,n){if(n<e)throw Object.defineProperty(new Error("stride must be bigger than screenWidth"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});if(t.length!==n*a)throw Object.defineProperty(new Error("rgb incorrect length"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});const r=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(2*e*a);for(let i=0,c=0,l=0;l<a;++l,i+=n)for(let a=0;a<e;++a){const e=t[i+a],n=o.rgb16_565(e);r[c++]=o.B1(n),r[c++]=o.B0(n)}return r}static flattenColor24(t,e,a,n){if(n<e)throw Object.defineProperty(new Error("stride must be bigger than screenWidth"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});if(t.length!==n*a)throw Object.defineProperty(new Error("rgb incorrect length"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});const r=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(3*e*a);for(let i=0,c=0,l=0;l<a;++l,i+=n)for(let a=0;a<e;++a){const e=t[i+a];r[c++]=o.B0(e),r[c++]=o.B1(e),r[c++]=o.B2(e)}return r}static k_factoryDefaultInkThreshold_$LI$(){return null==o.k_factoryDefaultInkThreshold&&(o.k_factoryDefaultInkThreshold=new t.WacomGSS.STU.Protocol.InkThreshold(21,17)),o.k_factoryDefaultInkThreshold}static getNextInkState(e,o,a,n){return 0===o||0==(t.WacomGSS.STU.Protocol.InkState._$wrappers[e].getValue()&t.WacomGSS.STU.Protocol.InkState._$wrappers[t.WacomGSS.STU.Protocol.InkState.InkState_isOff].getValue())&&a<n.getOffPressureMark()||0!=(t.WacomGSS.STU.Protocol.InkState._$wrappers[e].getValue()&t.WacomGSS.STU.Protocol.InkState._$wrappers[t.WacomGSS.STU.Protocol.InkState.InkState_isOff].getValue())&&a<n.getOnPressureMark()?0!=(t.WacomGSS.STU.Protocol.InkState._$wrappers[e].getValue()&t.WacomGSS.STU.Protocol.InkState._$wrappers[t.WacomGSS.STU.Protocol.InkState.InkState_isInk].getValue())?t.WacomGSS.STU.Protocol.InkState.InkState_Last:t.WacomGSS.STU.Protocol.InkState.InkState_Up:e!==t.WacomGSS.STU.Protocol.InkState.InkState_Up?e===t.WacomGSS.STU.Protocol.InkState.InkState_Down?t.WacomGSS.STU.Protocol.InkState.InkState_First:t.WacomGSS.STU.Protocol.InkState.InkState_Inking:t.WacomGSS.STU.Protocol.InkState.InkState_Down}static async writeRomImage(e,a,n,r,i,c){await o.waitForStatusToSend(e,t.WacomGSS.STU.Protocol.ReportId.RomStartImageData_$LI$(),o.OpDirection_Set,i,c),await e.setRomStartImageData(a),await o.writeImageCommon(e,a.getEncodingMode(),n,r,i,c)}static makeLegacyGetReport(e){const o=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(t.WacomGSS.STU.Protocol.TlsProtocol.PACKET_HEADER_SIZE+1);return t.WacomGSS.STU.Protocol.TlsProtocol.setPacketHeader(o,t.WacomGSS.STU.Protocol.TlsProtocol.PacketId.Legacy_GetFeature_$LI$()),o[6]=e,o}static makeLegacySetReport(e){const o=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(t.WacomGSS.STU.Protocol.TlsProtocol.PACKET_HEADER_SIZE+e.length);return t.WacomGSS.STU.Protocol.TlsProtocol.setPacketHeader(o,t.WacomGSS.STU.Protocol.TlsProtocol.PacketId.Legacy_SetFeature_$LI$()),((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(e,0,o,t.WacomGSS.STU.Protocol.TlsProtocol.PACKET_HEADER_SIZE,e.length),o}static makeLegacyWrite(e){const o=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(t.WacomGSS.STU.Protocol.TlsProtocol.PACKET_HEADER_SIZE+e.length);return t.WacomGSS.STU.Protocol.TlsProtocol.setPacketHeader(o,t.WacomGSS.STU.Protocol.TlsProtocol.PacketId.Legacy_Write_$LI$()),((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(e,0,o,t.WacomGSS.STU.Protocol.TlsProtocol.PACKET_HEADER_SIZE,e.length),o}static waitForReturn(e,o,a){for(;;){const n=e.wait_until_getReport(o);if(null==n)break;const r=t.WacomGSS.STU.Protocol.TlsProtocol.decodeReport_ReturnValue(n);if(r.getPacketId()===t.WacomGSS.STU.Protocol.TlsProtocol.PacketId.ReturnValue_$LI$()){if(0==(r.getReturnValueStatus()&t.WacomGSS.STU.Protocol.TlsProtocol.ReturnValueStatus.Error_$LI$()))return n;throw new t.WacomGSS.STU.Protocol.SendException(r.getReturnValueStatus(),a)}}throw new t.WacomGSS.STU.TimeoutException("Time out waiting for tablet")}static checkReturnValueStatus(e){const o=t.WacomGSS.STU.Protocol.TlsProtocol.decodeReport_returnValueStatus(e);if(o!==t.WacomGSS.STU.Protocol.TlsProtocol.ReturnValueStatus.Success_$LI$())throw new t.WacomGSS.STU.Protocol.SendException(o,new t.WacomGSS.STU.Protocol.SendHint)}}o.OpDirection_Get=256,o.OpDirection_Set=512,o.OpStatus_Ready=1,o.OpStatus_Image=2,o.OpStatus_Capture=4,o.OpStatus_Calculation=8,o.OpStatus_Image_Boot=16,o.OpStatus_RomBusy=32,o.OpStatus_SystemReset=128,e.ProtocolHelper=o,o.__class="com.WacomGSS.STU.Protocol.ProtocolHelper",function(t){class e{constructor(t,e){void 0===this.first&&(this.first=0),void 0===this.second&&(this.second=0),this.first=t,this.second=e}}let o;t.SendEntry=e,e.__class="com.WacomGSS.STU.Protocol.ProtocolHelper.SendEntry",function(t){t[t.Stretch=0]="Stretch",t[t.Fit=1]="Fit",t[t.Clip=2]="Clip"}(o=t.Scale||(t.Scale={}));class a{constructor(){}}a.Left=0,a.Center=1,a.Right=2,a.Top=0,a.Middle=16,a.Bottom=32,t.Clip=a,a.__class="com.WacomGSS.STU.Protocol.ProtocolHelper.Clip"}(o=e.ProtocolHelper||(e.ProtocolHelper={}))}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={}));for(var com,bayerThresholdMap=[[15,135,45,165],[195,75,225,105],[60,180,30,150],[240,120,210,90]],lumR=[],lumG=[],lumB=[],i=0;i<256;i++)lumR[i]=.299*i,lumG[i]=.587*i,lumB[i]=.114*i;function monochrome(t,e,o){for(var a=t.data.length,n=0;n<=a;n+=4)t.data[n]=Math.floor(lumR[t.data[n]]+lumG[t.data[n+1]]+lumB[t.data[n+2]]);for(var r,i,c=t.width,l=0;l<=a;l+=4){if("none"===o)t.data[l]=t.data[l]<e?0:255;else if("bayer"===o){var s=l/4%c,S=Math.floor(l/4/c),u=Math.floor((t.data[l]+bayerThresholdMap[s%4][S%4])/2);t.data[l]=u<e?0:255}else"floydsteinberg"===o?(r=t.data[l]<129?0:255,i=Math.floor((t.data[l]-r)/16),t.data[l]=r,t.data[l+4]+=7*i,t.data[l+4*c-4]+=3*i,t.data[l+4*c]+=5*i,t.data[l+4*c+4]+=1*i):(r=t.data[l]<129?0:255,i=Math.floor((t.data[l]-r)/8),t.data[l]=r,t.data[l+4]+=i,t.data[l+8]+=i,t.data[l+4*c-4]+=i,t.data[l+4*c]+=i,t.data[l+4*c+4]+=i,t.data[l+8*c]+=i);t.data[l+1]=t.data[l+2]=t.data[l]}return t}!function(t){!function(t){!function(t){!function(t){let e;!function(t){t[t.Legacy=0]="Legacy",t[t.WILL=1]="WILL"}(e=t.RenderingMode||(t.RenderingMode={}));class o{constructor(t,e,o){this._$ordinal=t,this._$name=e,void 0===this.value&&(this.value=0),this.value=o}getValue(){return this.value}name(){return this._$name}ordinal(){return this._$ordinal}compareTo(t){return this._$ordinal-(isNaN(t)?t._$ordinal:t)}}t.RenderingMode_$WRAPPER=o,e.__class="com.WacomGSS.STU.Protocol.RenderingMode",e.__interfaces=["java.lang.constant.Constable","java.lang.Comparable","java.io.Serializable"],e._$wrappers=[new o(0,"Legacy",0),new o(1,"WILL",1)]}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{constructor(){void 0===this.handlers&&(this.handlers=null),this.handlers=[]}addReportHandler(t){this.handlers.push(t)}removeReportHandler(t){(e=>{let o=e.indexOf(t);o>=0&&e.splice(o,1)})(this.handlers)}static copy(t,e,o){const a=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o);return((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(t,e,a,0,o),a}decodeTls(e,a,n){const r=new o.decodeTls_RetVal(n),i=a.length-n;if(i>=6){const c=o.copy(a,n,6),l=t.WacomGSS.STU.Protocol.TlsProtocol.decodeReport_size(c);if(i>=l){switch(t.WacomGSS.STU.Protocol.TlsProtocol.decodeReport_packetId(c)){case t.WacomGSS.STU.Protocol.TlsProtocol.PacketId.Legacy_PenData_$LI$():try{e.onPenData(t.WacomGSS.STU.Protocol.Protocol.decodePenData(a,n+6)),r.success=!0,r.offset=n+6+8}catch(t){}r.success=!0,r.offset=n+l;break;case t.WacomGSS.STU.Protocol.TlsProtocol.PacketId.Legacy_EventData_$LI$():try{switch(a[n+6]){case t.WacomGSS.STU.Protocol.OperationModeType.PinPad_$LI$():e.onEventDataPinPad(t.WacomGSS.STU.Protocol.Protocol.decodeEventDataPinPad(a,n+6));break;case t.WacomGSS.STU.Protocol.OperationModeType.KeyPad_$LI$():e.onEventDataKeyPad(t.WacomGSS.STU.Protocol.Protocol.decodeEventDataKeyPad(a,n+6));break;case t.WacomGSS.STU.Protocol.OperationModeType.Signature_$LI$():e.onEventDataSignature(t.WacomGSS.STU.Protocol.Protocol.decodeEventDataSignature(a,n+6))}}catch(t){}r.success=!0,r.offset=n+l}}}return r}handleReport(e,a,n){let r=0;for(let i=0;i<this.handlers.length;i++){let c=this.handlers[i];r=0;t:for(;r<e.length;)if(n){const t=this.decodeTls(c,e,r);if(!t.success)break;r=t.offset}else switch(e[r]){case t.WacomGSS.STU.Protocol.ReportId.PenData_$LI$():if(r+t.WacomGSS.STU.Protocol.PenData.reportSize>e.length)break t;++r;try{c.onPenData(t.WacomGSS.STU.Protocol.Protocol.decodePenData(e,r),a)}catch(t){}r+=t.WacomGSS.STU.Protocol.PenData.reportSize;break;case t.WacomGSS.STU.Protocol.ReportId.PenDataOption_$LI$():if(r+t.WacomGSS.STU.Protocol.PenDataOption.reportSize>e.length)break t;++r;try{c.onPenDataOption(t.WacomGSS.STU.Protocol.Protocol.decodePenDataOption(e,r),a)}catch(t){}r+=t.WacomGSS.STU.Protocol.PenDataOption.reportSize;break;case t.WacomGSS.STU.Protocol.ReportId.PenDataEncrypted_$LI$():{if(r+t.WacomGSS.STU.Protocol.PenDataEncrypted.reportSize>e.length)break t;++r;const n=o.copy(e,r,t.WacomGSS.STU.Protocol.PenDataEncrypted.reportSize);let i=o.copy(n,0,t.WacomGSS.STU.Protocol.PenDataEncrypted.encryptedSize);try{i=c.onDecrypt(i)}catch(t){}if(null!=i&&i.length===t.WacomGSS.STU.Protocol.PenDataEncrypted.encryptedSize){for(let t=0;t<i.length;++t)n[t]=i[t];try{c.onPenDataEncrypted(t.WacomGSS.STU.Protocol.Protocol.decodePenDataEncrypted(n,0),a)}catch(t){}}r+=t.WacomGSS.STU.Protocol.PenDataEncrypted.reportSize}break;case t.WacomGSS.STU.Protocol.ReportId.PenDataEncryptedOption_$LI$():{if(r+t.WacomGSS.STU.Protocol.PenDataEncryptedOption.reportSize>e.length)break t;++r;const n=o.copy(e,r,t.WacomGSS.STU.Protocol.PenDataEncryptedOption.reportSize);let i=o.copy(n,0,t.WacomGSS.STU.Protocol.PenDataEncrypted.encryptedSize);try{i=c.onDecrypt(i)}catch(t){}if(null!=i&&i.length===t.WacomGSS.STU.Protocol.PenDataEncrypted.encryptedSize){for(let t=0;t<i.length;++t)n[t]=i[t];try{c.onPenDataEncryptedOption(t.WacomGSS.STU.Protocol.Protocol.decodePenDataEncryptedOption(n,0),a)}catch(t){}}r+=t.WacomGSS.STU.Protocol.PenDataEncryptedOption.reportSize}break;case t.WacomGSS.STU.Protocol.ReportId.EncryptionStatus_$LI$():if(r+t.WacomGSS.STU.Protocol.EncryptionStatus.reportSize>e.length)break t;++r;try{c.onEncryptionStatus(t.WacomGSS.STU.Protocol.Protocol.decodeEncryptionStatus(e,r))}catch(t){}r+=t.WacomGSS.STU.Protocol.EncryptionStatus.reportSize;break;case t.WacomGSS.STU.Protocol.ReportId.DevicePublicKey_$LI$():if(r+t.WacomGSS.STU.Protocol.DevicePublicKey.reportSize>e.length)break t;++r;try{c.onDevicePublicKey(t.WacomGSS.STU.Protocol.Protocol.decodeDevicePublicKey(e,r))}catch(t){}r+=t.WacomGSS.STU.Protocol.DevicePublicKey.reportSize;break;case t.WacomGSS.STU.Protocol.ReportId.PenDataTimeCountSequence_$LI$():if(r+t.WacomGSS.STU.Protocol.PenDataTimeCountSequence.reportSize>e.length)break t;++r;try{c.onPenDataTimeCountSequence(t.WacomGSS.STU.Protocol.Protocol.decodePenDataTimeCountSequence(e,r),a)}catch(t){}r+=t.WacomGSS.STU.Protocol.PenDataTimeCountSequence.reportSize;break;case t.WacomGSS.STU.Protocol.ReportId.PenDataTimeCountSequenceEncrypted_$LI$():{if(r+t.WacomGSS.STU.Protocol.PenDataTimeCountSequenceEncrypted.reportSize>e.length)break t;++r;const n=o.copy(e,r,t.WacomGSS.STU.Protocol.PenDataTimeCountSequenceEncrypted.reportSize);let i=o.copy(n,0,t.WacomGSS.STU.Protocol.PenDataTimeCountSequenceEncrypted.encryptedSize);try{i=c.onDecrypt(i)}catch(t){}if(null!=i&&i.length===t.WacomGSS.STU.Protocol.PenDataTimeCountSequenceEncrypted.encryptedSize){for(let t=0;t<i.length;++t)n[t]=i[t];try{c.onPenDataTimeCountSequenceEncrypted(t.WacomGSS.STU.Protocol.Protocol.decodePenDataTimeCountSequenceEncrypted(n,0),a)}catch(t){}}r+=t.WacomGSS.STU.Protocol.PenDataTimeCountSequenceEncrypted.reportSize}break;case t.WacomGSS.STU.Protocol.ReportId.EventData_$LI$():if(r+t.WacomGSS.STU.Protocol.EventData.reportSize>e.length)break t;++r;try{switch(e[r]){case t.WacomGSS.STU.Protocol.OperationModeType.PinPad_$LI$():c.onEventDataPinPad(t.WacomGSS.STU.Protocol.Protocol.decodeEventDataPinPad(e,r));break;case t.WacomGSS.STU.Protocol.OperationModeType.KeyPad_$LI$():c.onEventDataKeyPad(t.WacomGSS.STU.Protocol.Protocol.decodeEventDataKeyPad(e,r));break;case t.WacomGSS.STU.Protocol.OperationModeType.Signature_$LI$():c.onEventDataSignature(t.WacomGSS.STU.Protocol.Protocol.decodeEventDataSignature(e,r))}}catch(t){}r+=t.WacomGSS.STU.Protocol.EventData.reportSize;break;case t.WacomGSS.STU.Protocol.ReportId.EventDataEncrypted_$LI$():{if(r+t.WacomGSS.STU.Protocol.EventDataEncrypted.reportSize>e.length)break t;++r;const a=o.copy(e,r,t.WacomGSS.STU.Protocol.EventDataEncrypted.reportSize);let n=o.copy(a,0,t.WacomGSS.STU.Protocol.EventDataEncrypted.encryptedSize);try{n=c.onDecrypt(n)}catch(t){}if(null!=n&&n.length===t.WacomGSS.STU.Protocol.EventDataEncrypted.encryptedSize){for(let t=0;t<n.length;++t)a[t]=n[t];try{switch(a[4]){case t.WacomGSS.STU.Protocol.OperationModeType.PinPad_$LI$():c.onEventDataPinPadEncrypted(t.WacomGSS.STU.Protocol.Protocol.decodeEventDataPinPadEncrypted(a,0));break;case t.WacomGSS.STU.Protocol.OperationModeType.KeyPad_$LI$():c.onEventDataKeyPadEncrypted(t.WacomGSS.STU.Protocol.Protocol.decodeEventDataKeyPadEncrypted(a,0));break;case t.WacomGSS.STU.Protocol.OperationModeType.Signature_$LI$():c.onEventDataSignatureEncrypted(t.WacomGSS.STU.Protocol.Protocol.decodeEventDataSignatureEncrypted(a,0))}}catch(t){}}r+=t.WacomGSS.STU.Protocol.EventDataEncrypted.reportSize}break;default:break t}}return r!==e.length?o.copy(e,r,e.length-r):null}}e.ReportHandler=o,o.__class="com.WacomGSS.STU.Protocol.ReportHandler",function(t){class e{constructor(t){void 0===this.success&&(this.success=!1),void 0===this.offset&&(this.offset=0),this.success=!1,this.offset=t}}t.decodeTls_RetVal=e,e.__class="com.WacomGSS.STU.Protocol.ReportHandler.decodeTls_RetVal"}(o=e.ReportHandler||(e.ReportHandler={}))}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{static PenData_$LI$(){return null==e.PenData&&(e.PenData=1),e.PenData}static Status_$LI$(){return null==e.Status&&(e.Status=3),e.Status}static Reset_$LI$(){return null==e.Reset&&(e.Reset=4),e.Reset}static HidInformation_$LI$(){return null==e.HidInformation&&(e.HidInformation=6),e.HidInformation}static Information_$LI$(){return null==e.Information&&(e.Information=8),e.Information}static Capability_$LI$(){return null==e.Capability&&(e.Capability=9),e.Capability}static Uid_$LI$(){return null==e.Uid&&(e.Uid=10),e.Uid}static Uid2_$LI$(){return null==e.Uid2&&(e.Uid2=11),e.Uid2}static DefaultMode_$LI$(){return null==e.DefaultMode&&(e.DefaultMode=12),e.DefaultMode}static ReportRate_$LI$(){return null==e.ReportRate&&(e.ReportRate=13),e.ReportRate}static RenderingMode_$LI$(){return null==e.RenderingMode&&(e.RenderingMode=14),e.RenderingMode}static Eserial_$LI$(){return null==e.Eserial&&(e.Eserial=15),e.Eserial}static PenDataEncrypted_$LI$(){return null==e.PenDataEncrypted&&(e.PenDataEncrypted=16),e.PenDataEncrypted}static HostPublicKey_$LI$(){return null==e.HostPublicKey&&(e.HostPublicKey=19),e.HostPublicKey}static DevicePublicKey_$LI$(){return null==e.DevicePublicKey&&(e.DevicePublicKey=20),e.DevicePublicKey}static StartCapture_$LI$(){return null==e.StartCapture&&(e.StartCapture=21),e.StartCapture}static EndCapture_$LI$(){return null==e.EndCapture&&(e.EndCapture=22),e.EndCapture}static DHprime_$LI$(){return null==e.DHprime&&(e.DHprime=26),e.DHprime}static DHbase_$LI$(){return null==e.DHbase&&(e.DHbase=27),e.DHbase}static ClearScreen_$LI$(){return null==e.ClearScreen&&(e.ClearScreen=32),e.ClearScreen}static InkingMode_$LI$(){return null==e.InkingMode&&(e.InkingMode=33),e.InkingMode}static InkThreshold_$LI$(){return null==e.InkThreshold&&(e.InkThreshold=34),e.InkThreshold}static ClearScreenArea_$LI$(){return null==e.ClearScreenArea&&(e.ClearScreenArea=35),e.ClearScreenArea}static StartImageDataArea_$LI$(){return null==e.StartImageDataArea&&(e.StartImageDataArea=36),e.StartImageDataArea}static StartImageData_$LI$(){return null==e.StartImageData&&(e.StartImageData=37),e.StartImageData}static ImageDataBlock_$LI$(){return null==e.ImageDataBlock&&(e.ImageDataBlock=38),e.ImageDataBlock}static EndImageData_$LI$(){return null==e.EndImageData&&(e.EndImageData=39),e.EndImageData}static HandwritingThicknessColor_$LI$(){return null==e.HandwritingThicknessColor&&(e.HandwritingThicknessColor=40),e.HandwritingThicknessColor}static BackgroundColor_$LI$(){return null==e.BackgroundColor&&(e.BackgroundColor=41),e.BackgroundColor}static HandwritingDisplayArea_$LI$(){return null==e.HandwritingDisplayArea&&(e.HandwritingDisplayArea=42),e.HandwritingDisplayArea}static BacklightBrightness_$LI$(){return null==e.BacklightBrightness&&(e.BacklightBrightness=43),e.BacklightBrightness}static ScreenContrast_$LI$(){return null==e.ScreenContrast&&(e.ScreenContrast=44),e.ScreenContrast}static HandwritingThicknessColor24_$LI$(){return null==e.HandwritingThicknessColor24&&(e.HandwritingThicknessColor24=45),e.HandwritingThicknessColor24}static BackgroundColor24_$LI$(){return null==e.BackgroundColor24&&(e.BackgroundColor24=46),e.BackgroundColor24}static BootScreen_$LI$(){return null==e.BootScreen&&(e.BootScreen=47),e.BootScreen}static PenDataOption_$LI$(){return null==e.PenDataOption&&(e.PenDataOption=48),e.PenDataOption}static PenDataEncryptedOption_$LI$(){return null==e.PenDataEncryptedOption&&(e.PenDataEncryptedOption=49),e.PenDataEncryptedOption}static PenDataOptionMode_$LI$(){return null==e.PenDataOptionMode&&(e.PenDataOptionMode=50),e.PenDataOptionMode}static PenDataTimeCountSequenceEncrypted_$LI$(){return null==e.PenDataTimeCountSequenceEncrypted&&(e.PenDataTimeCountSequenceEncrypted=51),e.PenDataTimeCountSequenceEncrypted}static PenDataTimeCountSequence_$LI$(){return null==e.PenDataTimeCountSequence&&(e.PenDataTimeCountSequence=52),e.PenDataTimeCountSequence}static EncryptionCommand_$LI$(){return null==e.EncryptionCommand&&(e.EncryptionCommand=64),e.EncryptionCommand}static EncryptionStatus_$LI$(){return null==e.EncryptionStatus&&(e.EncryptionStatus=80),e.EncryptionStatus}static GetReport_$LI$(){return null==e.GetReport&&(e.GetReport=128),e.GetReport}static SetResult_$LI$(){return null==e.SetResult&&(e.SetResult=129),e.SetResult}static PinPadData_$LI$(){return null==e.PinPadData&&(e.PinPadData=144),e.PinPadData}static PinPadDataEncrypted_$LI$(){return null==e.PinPadDataEncrypted&&(e.PinPadDataEncrypted=145),e.PinPadDataEncrypted}static PinOperationMode_$LI$(){return null==e.PinOperationMode&&(e.PinOperationMode=146),e.PinOperationMode}static OperationMode_$LI$(){return null==e.OperationMode&&(e.OperationMode=147),e.OperationMode}static RomStartImageData_$LI$(){return null==e.RomStartImageData&&(e.RomStartImageData=148),e.RomStartImageData}static RomImageOccupancy_$LI$(){return null==e.RomImageOccupancy&&(e.RomImageOccupancy=149),e.RomImageOccupancy}static RomImageHash_$LI$(){return null==e.RomImageHash&&(e.RomImageHash=150),e.RomImageHash}static RomImageDelete_$LI$(){return null==e.RomImageDelete&&(e.RomImageDelete=151),e.RomImageDelete}static CurrentImageArea_$LI$(){return null==e.CurrentImageArea&&(e.CurrentImageArea=152),e.CurrentImageArea}static EventData_$LI$(){return null==e.EventData&&(e.EventData=153),e.EventData}static EventDataEncrypted_$LI$(){return null==e.EventDataEncrypted&&(e.EventDataEncrypted=154),e.EventDataEncrypted}static RomImageDisplay_$LI$(){return null==e.RomImageDisplay&&(e.RomImageDisplay=155),e.RomImageDisplay}static ReportSizeCollection_$LI$(){return null==e.ReportSizeCollection&&(e.ReportSizeCollection=255),e.ReportSizeCollection}}t.ReportId=e,e.__class="com.WacomGSS.STU.Protocol.ReportId"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){let e;!function(t){t[t.Software=0]="Software",t[t.Hardware=1]="Hardware"}(e=t.ResetFlag||(t.ResetFlag={}));class o{constructor(t,e,o){this._$ordinal=t,this._$name=e,void 0===this.value&&(this.value=0),this.value=o}getValue(){return this.value}name(){return this._$name}ordinal(){return this._$ordinal}compareTo(t){return this._$ordinal-(isNaN(t)?t._$ordinal:t)}}t.ResetFlag_$WRAPPER=o,e.__class="com.WacomGSS.STU.Protocol.ResetFlag",e.__interfaces=["java.lang.constant.Constable","java.lang.Comparable","java.io.Serializable"],e._$wrappers=[new o(0,"Software",0),new o(1,"Hardware",1)]}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{static All_$LI$(){return null==e.All&&(e.All=0),e.All}static PinPad_All_$LI$(){return null==e.PinPad_All&&(e.PinPad_All=1),e.PinPad_All}static SlideShow_All_$LI$(){return null==e.SlideShow_All&&(e.SlideShow_All=2),e.SlideShow_All}static KeyPad_All_$LI$(){return null==e.KeyPad_All&&(e.KeyPad_All=3),e.KeyPad_All}static Signature_All_$LI$(){return null==e.Signature_All&&(e.Signature_All=4),e.Signature_All}static MessageBox_All_$LI$(){return null==e.MessageBox_All&&(e.MessageBox_All=5),e.MessageBox_All}static PinPad_Number_$LI$(){return null==e.PinPad_Number&&(e.PinPad_Number=6),e.PinPad_Number}static SlideShow_Number_$LI$(){return null==e.SlideShow_Number&&(e.SlideShow_Number=7),e.SlideShow_Number}static KeyPad_Number_$LI$(){return null==e.KeyPad_Number&&(e.KeyPad_Number=8),e.KeyPad_Number}static Signature_Number_$LI$(){return null==e.Signature_Number&&(e.Signature_Number=9),e.Signature_Number}static MessageBox_Number_$LI$(){return null==e.MessageBox_Number&&(e.MessageBox_Number=10),e.MessageBox_Number}}t.RomImageDeleteMode=e,e.__class="com.WacomGSS.STU.Protocol.RomImageDeleteMode"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,o,a,n,r,i){if(void 0===this.operationModeType&&(this.operationModeType=0),void 0===this.imageType&&(this.imageType=!1),void 0===this.imageNumber&&(this.imageNumber=0),void 0===this.result&&(this.result=0),void 0===this.hash&&(this.hash=null),null==r||r.length!==e.myLength_$LI$()||i+e.hashSize>e.myLength_$LI$())throw Object.defineProperty(new Error("invalid RomImageHash"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});this.hash=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(e.hashSize),this.operationModeType=t,this.imageType=o,this.imageNumber=a,this.result=n,((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(r,i,this.hash,0,e.hashSize)}static myLength_$LI$(){return null==e.myLength&&(e.myLength=e.hashSize+4),e.myLength}getOperationModeType(){return this.operationModeType}getImageType(){return this.imageType}getImageNumber(){return this.imageNumber}getResult(){return this.result}getHash(){return this.hash}}e.hashSize=16,t.RomImageHash=e,e.__class="com.WacomGSS.STU.Protocol.RomImageHash"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{constructor(t,e,o,a,n,r,i){void 0===this.encodingMode&&(this.encodingMode=null),void 0===this.operationModeType&&(this.operationModeType=0),void 0===this.imageType&&(this.imageType=!1),void 0===this.imageNumber&&(this.imageNumber=0),void 0===this.data&&(this.data=null),this.encodingMode=t,this.operationModeType=e,this.imageType=o,this.imageNumber=a,this.data=[n,r,i]}getEncodingMode(){return this.encodingMode}getOperationModeType(){return this.operationModeType}getImageType(){return this.imageType}getImageNumber(){return this.imageNumber}getData(){return this.data}static initializePinPad(e,a,n,r,i){return new o(e,t.WacomGSS.STU.Protocol.OperationModeType.PinPad_$LI$(),a,n,r,i,0)}static initializeSlideShow(e,a){return new o(e,t.WacomGSS.STU.Protocol.OperationModeType.SlideShow_$LI$(),!1,a,0,0,0)}static initializeKeyPad(e,a,n,r,i){if(null==i||i.length!==o.keyPadKeyEnabledLength)throw Object.defineProperty(new Error("invalid keyEnabled"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});const c=r,l=0|(i[8]?1:0),s=(i[7]?128:0)|(i[6]?64:0)|(i[5]?32:0)|(i[4]?16:0)|(i[3]?8:0)|(i[2]?4:0)|(i[1]?2:0)|(i[0]?1:0)|0;return new o(e,t.WacomGSS.STU.Protocol.OperationModeType.KeyPad_$LI$(),a,n,c,l,s)}static initializeSignature(e,a,n,r){if(null==r||r.length!==o.sigKeyEnabledLength)throw Object.defineProperty(new Error("invalid keyEnabled"),"__classes",{configurable:!0,value:["java.lang.Throwable","java.lang.Object","java.lang.RuntimeException","java.lang.IllegalArgumentException","java.lang.Exception"]});const i=(r[2]?4:0)|(r[1]?2:0)|(r[0]?1:0)|0;return new o(e,t.WacomGSS.STU.Protocol.OperationModeType.Signature_$LI$(),a,n,i,0,0)}static initializeMessageBox(e,a){return new o(e,t.WacomGSS.STU.Protocol.OperationModeType.MessageBox_$LI$(),!1,a,0,0,0)}}o.keyPadKeyEnabledLength=9,o.sigKeyEnabledLength=3,o.dataBytes=3,e.RomStartImageData=o,o.__class="com.WacomGSS.STU.Protocol.RomStartImageData"}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o extends t.WacomGSS.STU.STUException{constructor(t,e){super(),Object.setPrototypeOf(this,o.prototype),void 0===this.returnValueStatus&&(this.returnValueStatus=0),void 0===this.sendHint&&(this.sendHint=null),this.returnValueStatus=t,this.sendHint=(t=>{if(null!=t.clone)return t.clone();{let e=Object.create(t);for(let o in t)t.hasOwnProperty(o)&&(e[o]=t[o]);return e}})(e)}getReturnValueStatus(){return this.returnValueStatus}getSendHint(){return(t=>{if(null!=t.clone)return t.clone();{let e=Object.create(t);for(let o in t)t.hasOwnProperty(o)&&(e[o]=t[o]);return e}})(this.sendHint)}}e.SendException=o,o.__class="com.WacomGSS.STU.Protocol.SendException",o.__interfaces=["java.io.Serializable"]}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,e){if("number"!=typeof t&&null!==t||"number"!=typeof e&&null!==e){if(void 0!==t||void 0!==e)throw new Error("invalid overload");void 0===this.packetId&&(this.packetId=0),void 0===this.reportId&&(this.reportId=0),this.packetId=0,this.reportId=0}else{void 0===this.packetId&&(this.packetId=0),void 0===this.reportId&&(this.reportId=0),this.packetId=t,this.reportId=e}}getPacketId(){return this.packetId}getReportId(){return this.reportId}clone(){return new e(this.packetId,this.reportId)}}t.SendHint=e,e.__class="com.WacomGSS.STU.Protocol.SendHint",e.__interfaces=["java.lang.Cloneable"]}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{constructor(t,e,o){void 0===this.statusCode&&(this.statusCode=0),void 0===this.lastResultCode&&(this.lastResultCode=0),void 0===this.statusWord&&(this.statusWord=0),this.statusCode=t,this.lastResultCode=e,this.statusWord=o}getStatusCode(){return this.statusCode}getLastResultCode(){return this.lastResultCode}getStatusWord(){return this.statusWord}}t.Status=e,e.__class="com.WacomGSS.STU.Protocol.Status"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{static Ready_$LI$(){return null==e.Ready&&(e.Ready=0),e.Ready}static Image_$LI$(){return null==e.Image&&(e.Image=1),e.Image}static Capture_$LI$(){return null==e.Capture&&(e.Capture=2),e.Capture}static Calculation_$LI$(){return null==e.Calculation&&(e.Calculation=3),e.Calculation}static Image_Boot_$LI$(){return null==e.Image_Boot&&(e.Image_Boot=4),e.Image_Boot}static RomBusy_$LI$(){return null==e.RomBusy&&(e.RomBusy=5),e.RomBusy}static SystemReset_$LI$(){return null==e.SystemReset&&(e.SystemReset=255),e.SystemReset}}t.StatusCode=e,e.__class="com.WacomGSS.STU.Protocol.StatusCode"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){class e{static Ready_$LI$(){return null==e.Ready&&(e.Ready=0),e.Ready}static Calculating_$LI$(){return null==e.Calculating&&(e.Calculating=250),e.Calculating}static Even_$LI$(){return null==e.Even&&(e.Even=251),e.Even}static Long_$LI$(){return null==e.Long&&(e.Long=252),e.Long}static Short_$LI$(){return null==e.Short&&(e.Short=253),e.Short}static Invalid_$LI$(){return null==e.Invalid&&(e.Invalid=254),e.Invalid}static NotReady_$LI$(){return null==e.NotReady&&(e.NotReady=255),e.NotReady}}t.StatusCodeRSA=e,e.__class="com.WacomGSS.STU.Protocol.StatusCodeRSA"}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){!function(t){let e;!function(t){t[t.AES128=0]="AES128",t[t.AES192=1]="AES192",t[t.AES256=2]="AES256"}(e=t.SymmetricKeyType||(t.SymmetricKeyType={}));class o{constructor(t,e,o){this._$ordinal=t,this._$name=e,void 0===this.value&&(this.value=0),this.value=o}getValue(){return this.value}name(){return this._$name}ordinal(){return this._$ordinal}compareTo(t){return this._$ordinal-(isNaN(t)?t._$ordinal:t)}}t.SymmetricKeyType_$WRAPPER=o,e.__class="com.WacomGSS.STU.Protocol.SymmetricKeyType",e.__interfaces=["java.lang.constant.Constable","java.lang.Comparable","java.io.Serializable"],e._$wrappers=[new o(0,"AES128",0),new o(1,"AES192",1),new o(2,"AES256",2)]}(t.Protocol||(t.Protocol={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{constructor(t){void 0===this.intf&&(this.intf=null),this.intf=t}static B0(t){return 255&t|0}static B1(t){return t>>8&255|0}static B2(t){return t>>16&255|0}static B3(t){return t>>24&255|0}static I$byte(t){return 255&t}static I$byte$byte(t,e){return o.I$byte(t)<<8|o.I$byte(e)}static I$byte$byte$byte$byte(t,e,a,n){return o.I$byte(t)<<24|o.I$byte(e)<<16|o.I$byte(a)<<8|o.I$byte(n)}static I(e,o,a,n){if("number"!=typeof e&&null!==e||"number"!=typeof o&&null!==o||"number"!=typeof a&&null!==a||"number"!=typeof n&&null!==n){if("number"!=typeof e&&null!==e||"number"!=typeof o&&null!==o||void 0!==a||void 0!==n){if("number"!=typeof e&&null!==e||void 0!==o||void 0!==a||void 0!==n)throw new Error("invalid overload");return t.WacomGSS.STU.Protocol.TlsProtocol.I$byte(e)}return t.WacomGSS.STU.Protocol.TlsProtocol.I$byte$byte(e,o)}return t.WacomGSS.STU.Protocol.TlsProtocol.I$byte$byte$byte$byte(e,o,a,n)}static S(t,e){return 65535&o.I$byte$byte(t,e)|0}getInterface(){return this.intf}setInterface(t){this.intf=t}static makeSendHint(e){const a=e.length>=6?o.decodeReport_packetId(e):0,n=e.length>=7&&(a===o.PacketId.Legacy_GetFeature_$LI$()||a===o.PacketId.Legacy_SetFeature_$LI$())?e[6]:0;return new t.WacomGSS.STU.Protocol.SendHint(a,n)}static setPacketHeader(t,e){t[0]=o.B3(t.length),t[1]=o.B2(t.length),t[2]=o.B1(t.length),t[3]=o.B0(t.length),t[4]=o.B1(e),t[5]=o.B0(e)}static decodeReport_size(t){return o.I$byte$byte$byte$byte(t[0],t[1],t[2],t[3])}static decodeReport_packetId(t){return o.S(t[4],t[5])}static decodeReport_returnValueStatus(t){return o.S(t[6],t[7])}static decodeReport_ReturnValue(t){return new o.ReturnValue(o.decodeReport_size(t),o.decodeReport_packetId(t),o.decodeReport_returnValueStatus(t))}static decodeReport_ReturnValue_ProtocolVersion(t){const e=o.S(t[8],t[9]),a=(t.length-10)/2|0,n=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(a);for(let e=0;e<a;++e)n[e]=o.S(t[10+2*e],t[11+2*e]);return new o.ReturnValue_ProtocolVersion(o.decodeReport_ReturnValue(t),e,n)}static decodeReport_ReturnValue_Pending(t){const e=o.S(t[8],t[9]),a=t[10];return new o.ReturnValue_Pending(o.decodeReport_ReturnValue(t),e,a)}static decodeReport_ReturnValue_GenerateCSR(t){const e=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(t.length-8);return((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(t,8,e,0,e.length),new o.ReturnValue_GenerateCSR(o.decodeReport_ReturnValue(t),e)}static decodeReport_ReturnValue_CurrentCertificate(t){const e=t[8],a=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(t.length-9);return((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(t,9,a,0,a.length),new o.ReturnValue_CurrentCertificate(o.decodeReport_ReturnValue(t),e,a)}static decodeReport$byte_A$com_WacomGSS_STU_Protocol_TlsProtocol_ReturnValue(t,e){o.decodeReport_ReturnValue(t)}static decodeReport$byte_A$com_WacomGSS_STU_Protocol_TlsProtocol_ReturnValue_ProtocolVersion(t,e){o.decodeReport_ReturnValue_ProtocolVersion(t)}static decodeReport(e,o){if((null!=e&&e instanceof Array&&(0==e.length||null==e[0]||"number"==typeof e[0])||null===e)&&(null!=o&&o instanceof t.WacomGSS.STU.Protocol.TlsProtocol.ReturnValue_ProtocolVersion||null===o))return t.WacomGSS.STU.Protocol.TlsProtocol.decodeReport$byte_A$com_WacomGSS_STU_Protocol_TlsProtocol_ReturnValue_ProtocolVersion(e,o);if((null!=e&&e instanceof Array&&(0==e.length||null==e[0]||"number"==typeof e[0])||null===e)&&(null!=o&&o instanceof t.WacomGSS.STU.Protocol.TlsProtocol.ReturnValue_Pending||null===o))return t.WacomGSS.STU.Protocol.TlsProtocol.decodeReport$byte_A$com_WacomGSS_STU_Protocol_TlsProtocol_ReturnValue_Pending(e,o);if((null!=e&&e instanceof Array&&(0==e.length||null==e[0]||"number"==typeof e[0])||null===e)&&(null!=o&&o instanceof t.WacomGSS.STU.Protocol.TlsProtocol.ReturnValue_GenerateCSR||null===o))return t.WacomGSS.STU.Protocol.TlsProtocol.decodeReport$byte_A$com_WacomGSS_STU_Protocol_TlsProtocol_ReturnValue_GenerateCSR(e,o);if((null!=e&&e instanceof Array&&(0==e.length||null==e[0]||"number"==typeof e[0])||null===e)&&(null!=o&&o instanceof t.WacomGSS.STU.Protocol.TlsProtocol.ReturnValue_CurrentCertificate||null===o))return t.WacomGSS.STU.Protocol.TlsProtocol.decodeReport$byte_A$com_WacomGSS_STU_Protocol_TlsProtocol_ReturnValue_CurrentCertificate(e,o);if((null!=e&&e instanceof Array&&(0==e.length||null==e[0]||"number"==typeof e[0])||null===e)&&(null!=o&&o instanceof t.WacomGSS.STU.Protocol.TlsProtocol.ReturnValue||null===o))return t.WacomGSS.STU.Protocol.TlsProtocol.decodeReport$byte_A$com_WacomGSS_STU_Protocol_TlsProtocol_ReturnValue(e,o);throw new Error("invalid overload")}static decodeReport$byte_A$com_WacomGSS_STU_Protocol_TlsProtocol_ReturnValue_Pending(t,e){o.decodeReport_ReturnValue_Pending(t)}static decodeReport$byte_A$com_WacomGSS_STU_Protocol_TlsProtocol_ReturnValue_GenerateCSR(t,e){o.decodeReport_ReturnValue_GenerateCSR(t)}static decodeReport$byte_A$com_WacomGSS_STU_Protocol_TlsProtocol_ReturnValue_CurrentCertificate(t,e){o.decodeReport_ReturnValue_CurrentCertificate(t)}sendProtocolVersion(t){const e=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.PACKET_HEADER_SIZE+2);o.setPacketHeader(e,o.PacketId.ProtocolVersion_$LI$()),e[6]=o.B1(t),e[7]=o.B0(t);const a=this.intf.send$byte_A(e);return o.decodeReport_ReturnValue_ProtocolVersion(a)}sendGenerateCSR(e,a,n,r,i,c,l,s,S){const u=[a.split("").map((t=>t.charCodeAt(0))),n.split("").map((t=>t.charCodeAt(0))),r.split("").map((t=>t.charCodeAt(0))),i.split("").map((t=>t.charCodeAt(0))),c.split("").map((t=>t.charCodeAt(0))),l.split("").map((t=>t.charCodeAt(0))),s.split("").map((t=>t.charCodeAt(0))),S.split("").map((t=>t.charCodeAt(0)))],d=a.length+n.length+r.length+i.length+c.length+l.length+s.length+S.length,p=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.PACKET_HEADER_SIZE+2+8+d);o.setPacketHeader(p,o.PacketId.GenerateCSR_$LI$()),p[6]=o.B1(e),p[7]=o.B0(e);let m=8;for(let t=0;t<u.length;++t)((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(u[m],0,p,m,u[m].length),m+=u[m].length,p[m++]=0;return this.intf.send$byte_A$com_WacomGSS_STU_InterfaceTLS_InterfaceQueueSelector(p,t.WacomGSS.STU.InterfaceTLS.InterfaceQueueSelector.tag)}sendGenerateCSR_Cancel(){const t=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.PACKET_HEADER_SIZE);o.setPacketHeader(t,o.PacketId.GenerateCSR_Cancel_$LI$());const e=this.intf.send$byte_A(t);return o.decodeReport_ReturnValue(e)}sendImportCertificate(e,a){const n=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.PACKET_HEADER_SIZE+1+a.length);o.setPacketHeader(n,o.PacketId.ImportCertificate_$LI$()),n[6]=t.WacomGSS.STU.Protocol.TlsProtocol.ClientAuthentication._$wrappers[e].getValue(),((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(a,0,n,7,a.length);const r=this.intf.send$byte_A(n);return o.decodeReport_ReturnValue(r)}sendDestroyCertificate(){const t=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.PACKET_HEADER_SIZE);o.setPacketHeader(t,o.PacketId.DestroyCertificate_$LI$());const e=this.intf.send$byte_A(t);return o.decodeReport_ReturnValue(e)}sendUpdateCRL(e){const a=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.PACKET_HEADER_SIZE+e.length);o.setPacketHeader(a,o.PacketId.UpdateCRL_$LI$()),((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(e,0,a,6,e.length);return this.intf.send$byte_A$com_WacomGSS_STU_InterfaceTLS_InterfaceQueueSelector(a,t.WacomGSS.STU.InterfaceTLS.InterfaceQueueSelector.tag)}sendDeleteCRL(){const t=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.PACKET_HEADER_SIZE);o.setPacketHeader(t,o.PacketId.DeleteCRL_$LI$());const e=this.intf.send$byte_A(t);return o.decodeReport_ReturnValue(e)}sendCurrentCertificate(){const t=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(o.PACKET_HEADER_SIZE);o.setPacketHeader(t,o.PacketId.CurrentCertificate_$LI$());const e=this.intf.send$byte_A(t);return o.decodeReport_ReturnValue_CurrentCertificate(e)}}o.PACKET_HEADER_SIZE=6,e.TlsProtocol=o,o.__class="com.WacomGSS.STU.Protocol.TlsProtocol",function(t){class e{constructor(){}static ProtocolVersion_$LI$(){return null==e.ProtocolVersion&&(e.ProtocolVersion=1),e.ProtocolVersion}static GenerateCSR_$LI$(){return null==e.GenerateCSR&&(e.GenerateCSR=16),e.GenerateCSR}static GenerateCSR_Cancel_$LI$(){return null==e.GenerateCSR_Cancel&&(e.GenerateCSR_Cancel=17),e.GenerateCSR_Cancel}static ImportCertificate_$LI$(){return null==e.ImportCertificate&&(e.ImportCertificate=18),e.ImportCertificate}static DestroyCertificate_$LI$(){return null==e.DestroyCertificate&&(e.DestroyCertificate=19),e.DestroyCertificate}static UpdateCRL_$LI$(){return null==e.UpdateCRL&&(e.UpdateCRL=20),e.UpdateCRL}static DeleteCRL_$LI$(){return null==e.DeleteCRL&&(e.DeleteCRL=21),e.DeleteCRL}static CurrentCertificate_$LI$(){return null==e.CurrentCertificate&&(e.CurrentCertificate=22),e.CurrentCertificate}static ReturnValue_$LI$(){return null==e.ReturnValue&&(e.ReturnValue=128),e.ReturnValue}static Legacy_GetFeature_$LI$(){return null==e.Legacy_GetFeature&&(e.Legacy_GetFeature=258),e.Legacy_GetFeature}static Legacy_SetFeature_$LI$(){return null==e.Legacy_SetFeature&&(e.Legacy_SetFeature=259),e.Legacy_SetFeature}static Legacy_PenData_$LI$(){return null==e.Legacy_PenData&&(e.Legacy_PenData=260),e.Legacy_PenData}static Legacy_EventData_$LI$(){return null==e.Legacy_EventData&&(e.Legacy_EventData=261),e.Legacy_EventData}static Legacy_Write_$LI$(){return null==e.Legacy_Write&&(e.Legacy_Write=262),e.Legacy_Write}}t.PacketId=e,e.__class="com.WacomGSS.STU.Protocol.TlsProtocol.PacketId";class o{constructor(){}static Success_$LI$(){return null==o.Success&&(o.Success=0),o.Success}static Pending_$LI$(){return null==o.Pending&&(o.Pending=16385),o.Pending}static Pending_CSR_$LI$(){return null==o.Pending_CSR&&(o.Pending_CSR=16386),o.Pending_CSR}static CSR_$LI$(){return null==o.CSR&&(o.CSR=16387),o.CSR}static Pending_CRL_$LI$(){return null==o.Pending_CRL&&(o.Pending_CRL=16388),o.Pending_CRL}static Error_$LI$(){return null==o.Error&&(o.Error=32768),o.Error}static Error_Busy_$LI$(){return null==o.Error_Busy&&(o.Error_Busy=32769),o.Error_Busy}static Error_Unspecified_$LI$(){return null==o.Error_Unspecified&&(o.Error_Unspecified=65535),o.Error_Unspecified}}t.ReturnValueStatus=o,o.__class="com.WacomGSS.STU.Protocol.TlsProtocol.ReturnValueStatus";class a{constructor(t,e,o){void 0===this.size&&(this.size=0),void 0===this.packetId&&(this.packetId=0),void 0===this.returnValueStatus&&(this.returnValueStatus=0),this.size=t,this.packetId=e,this.returnValueStatus=o}getSize(){return this.size}getPacketId(){return this.packetId}getReturnValueStatus(){return this.returnValueStatus}}let n;t.ReturnValue=a,a.__class="com.WacomGSS.STU.Protocol.TlsProtocol.ReturnValue",function(t){t[t.Single=0]="Single",t[t.Mutual=1]="Mutual"}(n=t.ClientAuthentication||(t.ClientAuthentication={}));class r{constructor(t,e,o){this._$ordinal=t,this._$name=e,void 0===this.value&&(this.value=0),this.value=o}getValue(){return this.value}name(){return this._$name}ordinal(){return this._$ordinal}compareTo(t){return this._$ordinal-(isNaN(t)?t._$ordinal:t)}}t.ClientAuthentication_$WRAPPER=r,n.__class="com.WacomGSS.STU.Protocol.TlsProtocol.ClientAuthentication",n.__interfaces=["java.lang.constant.Constable","java.lang.Comparable","java.io.Serializable"],n._$wrappers=[new r(0,"Single",0),new r(1,"Mutual",1)];class i extends t.ReturnValue{constructor(t,e,o){super(t.getSize(),t.getPacketId(),t.getReturnValueStatus()),void 0===this.activeLevel&&(this.activeLevel=0),void 0===this.supportedLevels&&(this.supportedLevels=null),this.activeLevel=e,this.supportedLevels=o.slice(0)}getActiveLevel(){return this.activeLevel}getSupportedLevels(){return this.supportedLevels.slice(0)}}t.ReturnValue_ProtocolVersion=i,i.__class="com.WacomGSS.STU.Protocol.TlsProtocol.ReturnValue_ProtocolVersion";class c extends t.ReturnValue{constructor(t,e,o){super(t.getSize(),t.getPacketId(),t.getReturnValueStatus()),void 0===this.counter&&(this.counter=0),void 0===this.statusFlags&&(this.statusFlags=0),this.counter=e,this.statusFlags=o}getCounter(){return this.counter}getStatusFlags(){return this.statusFlags}}t.ReturnValue_Pending=c,c.__class="com.WacomGSS.STU.Protocol.TlsProtocol.ReturnValue_Pending";class l extends t.ReturnValue{constructor(t,e){super(t.getSize(),t.getPacketId(),t.getReturnValueStatus()),void 0===this.csr&&(this.csr=null),this.csr=e.slice(0)}getCSR(){return this.csr.slice(0)}}t.ReturnValue_GenerateCSR=l,l.__class="com.WacomGSS.STU.Protocol.TlsProtocol.ReturnValue_GenerateCSR";class s extends t.ReturnValue{constructor(t,e,o){super(t.getSize(),t.getPacketId(),t.getReturnValueStatus()),void 0===this.certificateIndex&&(this.certificateIndex=0),void 0===this.certificateDER&&(this.certificateDER=null),this.certificateIndex=e,this.certificateDER=o.slice(0)}getCertificateIndex(){return this.certificateIndex}getCertificateDER(){return this.certificateDER.slice(0)}}t.ReturnValue_CurrentCertificate=s,s.__class="com.WacomGSS.STU.Protocol.TlsProtocol.ReturnValue_CurrentCertificate"}(o=e.TlsProtocol||(e.TlsProtocol={}))}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){!function(e){class o{constructor(t){void 0===this.intf&&(this.intf=null),this.intf=t}static B0(t){return 255&t|0}static B1(t){return t>>8&255|0}static B2(t){return t>>16&255|0}static B3(t){return t>>24&255|0}static I$byte(t){return 255&t}static I$byte$byte(t,e){return o.I$byte(t)<<8|o.I$byte(e)}static I$byte$byte$byte$byte(t,e,a,n){return o.I$byte(t)<<24|o.I$byte(e)<<16|o.I$byte(a)<<8|o.I$byte(n)}static I(e,o,a,n){if("number"!=typeof e&&null!==e||"number"!=typeof o&&null!==o||"number"!=typeof a&&null!==a||"number"!=typeof n&&null!==n){if("number"!=typeof e&&null!==e||"number"!=typeof o&&null!==o||void 0!==a||void 0!==n){if("number"!=typeof e&&null!==e||void 0!==o||void 0!==a||void 0!==n)throw new Error("invalid overload");return t.WacomGSS.STU.Protocol.TlsProtocolOOB.I$byte(e)}return t.WacomGSS.STU.Protocol.TlsProtocolOOB.I$byte$byte(e,o)}return t.WacomGSS.STU.Protocol.TlsProtocolOOB.I$byte$byte$byte$byte(e,o,a,n)}static S(t,e){return 65535&o.I$byte$byte(t,e)|0}getInterface(){return this.intf}setInterface(t){this.intf=t}getDescriptor(){const t=[o.ReportId.Descriptor_$LI$(),0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];this.intf.getOOB(t);const e={str:"",toString:function(){return this.str}};for(let n=0;n<7&&0!==t[15+n];++n)(a=e).str=a.str.concat(String.fromCharCode(o.I$byte(t[15+n])));var a;return new o.Descriptor(o.I$byte$byte$byte$byte(t[1],t[2],t[3],t[4]),o.S(t[5],t[6]),o.S(t[7],t[8]),o.S(t[9],t[10]),o.S(t[11],t[12]),e.str)}getStatus(){const t=[o.ReportId.Status_$LI$(),0,0,0,0,0,0];this.intf.getOOB(t);return new o.Status(o.S(t[1],t[2]),o.I$byte$byte$byte$byte(t[3],t[4],t[5],t[6]))}getFactoryReset(){const t=[o.ReportId.FactoryReset_$LI$(),0,0,0,0];this.intf.getOOB(t);return o.I$byte$byte$byte$byte(t[1],t[2],t[3],t[4])}setFactoryReset(t){const e=[o.ReportId.FactoryReset_$LI$(),o.B3(t),o.B2(t),o.B1(t),o.B0(t)];this.intf.setOOB(e)}setFirmwareUpdate(t){const e=[o.ReportId.FirmwareUpdate_$LI$(),o.B3(t),o.B2(t),o.B1(t),o.B0(t)];this.intf.setOOB(e)}setReset(e){const a=[o.ReportId.Reset_$LI$(),t.WacomGSS.STU.Protocol.TlsProtocolOOB.ResetFlag._$wrappers[e].getValue()];this.intf.setOOB(a)}getReportSizeCollection(){let t=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(512);t[0]=o.ReportId.ReportSizeCollection_$LI$(),t=this.intf.getOOB(t);const e=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(256);e[0]=0;for(let a=1;a<=255;++a)e[a]=o.I$byte$byte(t[0+2*a],t[1+2*a]);return e}}e.TlsProtocolOOB=o,o.__class="com.WacomGSS.STU.Protocol.TlsProtocolOOB",function(t){class e{constructor(){}static Descriptor_$LI$(){return null==e.Descriptor&&(e.Descriptor=1),e.Descriptor}static Status_$LI$(){return null==e.Status&&(e.Status=2),e.Status}static Reset_$LI$(){return null==e.Reset&&(e.Reset=3),e.Reset}static FactoryResetKey_$LI$(){return null==e.FactoryResetKey&&(e.FactoryResetKey=14),e.FactoryResetKey}static FactoryReset_$LI$(){return null==e.FactoryReset&&(e.FactoryReset=15),e.FactoryReset}static FirmwareUpdate_$LI$(){return null==e.FirmwareUpdate&&(e.FirmwareUpdate=128),e.FirmwareUpdate}static ReportSizeCollection_$LI$(){return null==e.ReportSizeCollection&&(e.ReportSizeCollection=255),e.ReportSizeCollection}}t.ReportId=e,e.__class="com.WacomGSS.STU.Protocol.TlsProtocolOOB.ReportId";class o{constructor(t,e,o,a,n,r){void 0===this.descriptorFlags&&(this.descriptorFlags=0),void 0===this.idVendor&&(this.idVendor=0),void 0===this.idProduct&&(this.idProduct=0),void 0===this.firmwareRevisionMajor&&(this.firmwareRevisionMajor=0),void 0===this.firmwareRevisionMinor&&(this.firmwareRevisionMinor=0),void 0===this.modelName&&(this.modelName=null),this.descriptorFlags=t,this.idVendor=e,this.idProduct=o,this.firmwareRevisionMajor=a,this.firmwareRevisionMinor=n,this.modelName=r}getDescriptorFlags(){return this.descriptorFlags}getIdVendor(){return this.idVendor}getIdProduct(){return this.idProduct}getFirmwareRevisionMajor(){return this.firmwareRevisionMajor}getFirmwareRevisionMinor(){return this.firmwareRevisionMinor}getModelName(){return this.modelName}}t.Descriptor=o,o.__class="com.WacomGSS.STU.Protocol.TlsProtocolOOB.Descriptor";class a{constructor(t,e){void 0===this.oobStatus&&(this.oobStatus=0),void 0===this.oobExtendedStatus&&(this.oobExtendedStatus=0),this.oobStatus=t,this.oobExtendedStatus=e}getOobStatus(){return this.oobStatus}getOobExtendedStatus(){return this.oobExtendedStatus}}let n;t.Status=a,a.__class="com.WacomGSS.STU.Protocol.TlsProtocolOOB.Status",function(t){t[t.Connection=0]="Connection",t[t.Software=1]="Software",t[t.Hardware=2]="Hardware"}(n=t.ResetFlag||(t.ResetFlag={}));class r{constructor(t,e,o){this._$ordinal=t,this._$name=e,void 0===this.value&&(this.value=0),this.value=o}getValue(){return this.value}name(){return this._$name}ordinal(){return this._$ordinal}compareTo(t){return this._$ordinal-(isNaN(t)?t._$ordinal:t)}}t.ResetFlag_$WRAPPER=r,n.__class="com.WacomGSS.STU.Protocol.TlsProtocolOOB.ResetFlag",n.__interfaces=["java.lang.constant.Constable","java.lang.Comparable","java.io.Serializable"],n._$wrappers=[new r(0,"Connection",0),new r(1,"Software",1),new r(2,"Hardware",2)]}(o=e.TlsProtocolOOB||(e.TlsProtocolOOB={}))}(e.Protocol||(e.Protocol={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){class o{static getProperty(t){return null}static setProperty$java_lang_String$boolean(t,e){}static setProperty$java_lang_String$byte(t,e){}static setProperty$java_lang_String$char(t,e){}static setProperty$java_lang_String$short(t,e){}static setProperty$java_lang_String$int(t,e){}static setProperty$java_lang_String$long(t,e){}static setProperty$java_lang_String$float(t,e){}static setProperty$java_lang_String$double(t,e){}static setProperty$java_lang_String$java_lang_String(t,e){}static setProperty(e,o){if("string"!=typeof e&&null!==e||"string"!=typeof o&&null!==o){if("string"!=typeof e&&null!==e||!(null!=o&&o instanceof Array&&(0==o.length||null==o[0]||"number"==typeof o[0])||null===o)){if("string"!=typeof e&&null!==e||"boolean"!=typeof o&&null!==o){if("string"!=typeof e&&null!==e||"number"!=typeof o&&null!==o){if("string"!=typeof e&&null!==e||"string"!=typeof o&&null!==o){if("string"!=typeof e&&null!==e||"number"!=typeof o&&null!==o){if("string"!=typeof e&&null!==e||"number"!=typeof o&&null!==o){if("string"!=typeof e&&null!==e||"number"!=typeof o&&null!==o){if("string"!=typeof e&&null!==e||"number"!=typeof o&&null!==o){if("string"!=typeof e&&null!==e||"number"!=typeof o&&null!==o){if("string"!=typeof e&&null!==e||null==o&&null!==o)throw new Error("invalid overload");return t.WacomGSS.STU.Component.setProperty$java_lang_String$java_lang_Object(e,o)}return t.WacomGSS.STU.Component.setProperty$java_lang_String$double(e,o)}return t.WacomGSS.STU.Component.setProperty$java_lang_String$float(e,o)}return t.WacomGSS.STU.Component.setProperty$java_lang_String$long(e,o)}return t.WacomGSS.STU.Component.setProperty$java_lang_String$int(e,o)}return t.WacomGSS.STU.Component.setProperty$java_lang_String$short(e,o)}return t.WacomGSS.STU.Component.setProperty$java_lang_String$char(e,o)}return t.WacomGSS.STU.Component.setProperty$java_lang_String$byte(e,o)}return t.WacomGSS.STU.Component.setProperty$java_lang_String$boolean(e,o)}return t.WacomGSS.STU.Component.setProperty$java_lang_String$byte_A(e,o)}return t.WacomGSS.STU.Component.setProperty$java_lang_String$java_lang_String(e,o)}static setProperty$java_lang_String$byte_A(t,e){}static setProperty$java_lang_String$java_lang_Object(t,e){}static getComponentFiles(){return null}static diagnosticInformation(t){return null}}e.Component=o,o.__class="com.WacomGSS.STU.Component",function(t){class e{constructor(t,e){void 0===this.name&&(this.name=null),void 0===this.version&&(this.version=null),this.name=t,this.version=e}getName(){return this.name}getVersion(){return this.version}}t.ComponentFile=e,e.__class="com.WacomGSS.STU.Component.ComponentFile"}(o=e.Component||(e.Component={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){class o extends t.WacomGSS.STU.STUException{constructor(t,e){if("string"!=typeof t&&null!==t||!(null!=e&&e.__classes&&e.__classes.indexOf("java.lang.Throwable")>=0||null!=e&&e instanceof Error||null===e))if("string"!=typeof t&&null!==t||void 0!==e)if((null!=t&&t.__classes&&t.__classes.indexOf("java.lang.Throwable")>=0||null!=t&&t instanceof Error||null===t)&&void 0===e){super(arguments[0])}else{if(void 0!==t||void 0!==e)throw new Error("invalid overload");super()}else{super(t)}else{super(t,e)}}}e.DeviceRemovedException=o,o.__class="com.WacomGSS.STU.DeviceRemovedException",o.__interfaces=["java.io.Serializable"]}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){class o extends t.WacomGSS.STU.STUException{constructor(t,e){if("string"!=typeof t&&null!==t||!(null!=e&&e.__classes&&e.__classes.indexOf("java.lang.Throwable")>=0||null!=e&&e instanceof Error||null===e))if("string"!=typeof t&&null!==t||void 0!==e)if((null!=t&&t.__classes&&t.__classes.indexOf("java.lang.Throwable")>=0||null!=t&&t instanceof Error||null===t)&&void 0===e){super(arguments[0])}else{if(void 0!==t||void 0!==e)throw new Error("invalid overload");super()}else{super(t)}else{super(t,e)}}}e.ErrorCodeException=o,o.__class="com.WacomGSS.STU.ErrorCodeException",o.__interfaces=["java.io.Serializable"]}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){class o{constructor(t){void 0===this.handlers&&(this.handlers=null),this.handlers=[],t.setRunnable(this)}run(e,o){0!=this.handlers.length&&new t.WacomGSS.STU.InterfaceHandler.InterfaceDispatch(this.handlers,e,o).run()}stop(){try{this.handlers.length=0,this.interfaceQueue.set_predicate(!0),this.interfaceQueue.notifyAll_getReport()}finally{}}addInterfaceHandler(t){this.handlers.push(t)}removeInterfaceHandler(t){(e=>{let o=e.indexOf(t);o>=0&&e.splice(o,1)})(this.handlers)}}e.InterfaceHandler=o,o.__class="com.WacomGSS.STU.InterfaceHandler",o.__interfaces=["java.lang.Runnable"],function(e){class o{constructor(e,o,a){if((null!=e&&e instanceof Array||null===e)&&(null!=o&&o instanceof Array&&(0==o.length||null==o[0]||"number"==typeof o[0])||null===o)){void 0===this.handlers&&(this.handlers=null),void 0===this.report&&(this.report=null),this.handlers=e.slice(0),this.report=new t.WacomGSS.STU.Report(o),this.time=a}else{if(!(null!=e&&e instanceof Array||null===e)||!(null!=o&&o instanceof t.WacomGSS.STU.STUException||null===o))throw new Error("invalid overload");{let o=arguments[1];void 0===this.handlers&&(this.handlers=null),void 0===this.report&&(this.report=null),this.handlers=e.slice(0),this.report=new t.WacomGSS.STU.Report(o)}}}run(){if(null!=this.handlers)for(let t=0;t<this.handlers.length;t++){let e=this.handlers[t];try{e.onReport(this.report,this.time)}catch(t){}}}}e.InterfaceDispatch=o,o.__class="com.WacomGSS.STU.InterfaceHandler.InterfaceDispatch",o.__interfaces=["java.lang.Runnable"]}(o=e.InterfaceHandler||(e.InterfaceHandler={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){class e{constructor(){this.reports=[],this.promise=null,this.outsideResolve}getReport(t,e){this.promise?(this.outsideResolve(t),this.promise=null):this.runnable&&this.runnable.run(t,e)}setRunnable(t){this.runnable=t}clear(){}isEmpty(){return!0}notify_getReport(){}notifyAll_getReport(){}try_getReport(){return this.reports.length>0?this.reports.shift():null}async wait_until_getReport(t){return this.promise=new Promise(((t,e)=>{this.outsideResolve=t})),this.promise}get_predicate(){return!1}set_predicate(t){}wait_getReport_predicate(){return null}}t.InterfaceQueue=e,e.__class="com.WacomGSS.STU.InterfaceQueue"}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){let e;!function(t){let e,o;!function(t){t[t.OOB=0]="OOB",t[t.SSL=1]="SSL"}(e=t.ConnectOption||(t.ConnectOption={})),function(t){t[t.tag=0]="tag"}(o=t.InterfaceQueueSelector||(t.InterfaceQueueSelector={}))}(e=t.InterfaceTLS||(t.InterfaceTLS={}))}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){class o extends t.WacomGSS.STU.STUException{constructor(t,e){if("string"!=typeof t&&null!==t||!(null!=e&&e.__classes&&e.__classes.indexOf("java.lang.Throwable")>=0||null!=e&&e instanceof Error||null===e))if("string"!=typeof t&&null!==t||void 0!==e)if((null!=t&&t.__classes&&t.__classes.indexOf("java.lang.Throwable")>=0||null!=t&&t instanceof Error||null===t)&&void 0===e){super(arguments[0])}else{if(void 0!==t||void 0!==e)throw new Error("invalid overload");super()}else{super(t)}else{super(t,e)}}}e.IOErrorException=o,o.__class="com.WacomGSS.STU.IOErrorException",o.__interfaces=["java.io.Serializable"]}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){class o extends t.WacomGSS.STU.STUException{constructor(t,e){if("string"!=typeof t&&null!==t||!(null!=e&&e.__classes&&e.__classes.indexOf("java.lang.Throwable")>=0||null!=e&&e instanceof Error||null===e))if("string"!=typeof t&&null!==t||void 0!==e)if((null!=t&&t.__classes&&t.__classes.indexOf("java.lang.Throwable")>=0||null!=t&&t instanceof Error||null===t)&&void 0===e){super(arguments[0])}else{if(void 0!==t||void 0!==e)throw new Error("invalid overload");super()}else{super(t)}else{super(t,e)}}}e.NotConnectedException=o,o.__class="com.WacomGSS.STU.NotConnectedException",o.__interfaces=["java.io.Serializable"]}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){class o extends t.WacomGSS.STU.STUException{constructor(t,e){if("string"!=typeof t&&null!==t||!(null!=e&&e.__classes&&e.__classes.indexOf("java.lang.Throwable")>=0||null!=e&&e instanceof Error||null===e))if("string"!=typeof t&&null!==t||void 0!==e)if((null!=t&&t.__classes&&t.__classes.indexOf("java.lang.Throwable")>=0||null!=t&&t instanceof Error||null===t)&&void 0===e){super(arguments[0])}else{if(void 0!==t||void 0!==e)throw new Error("invalid overload");super()}else{super(t)}else{super(t,e)}}}e.NotSupportedException=o,o.__class="com.WacomGSS.STU.NotSupportedException",o.__interfaces=["java.io.Serializable"]}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){class o{constructor(e){if(null!=e&&e instanceof Array&&(0==e.length||null==e[0]||"number"==typeof e[0])||null===e){void 0===this.report&&(this.report=null),void 0===this.e&&(this.e=null),this.report=e.slice(0),this.e=null}else{if(!(null!=e&&e instanceof t.WacomGSS.STU.STUException||null===e))throw new Error("invalid overload");{let t=arguments[0];void 0===this.report&&(this.report=null),void 0===this.e&&(this.e=null),this.report=null,this.e=t}}}getReport(){if(null!=this.e)throw this.e;return this.report.slice(0)}}e.Report=o,o.__class="com.WacomGSS.STU.Report"}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){class o extends t.WacomGSS.STU.STUException{constructor(t,e){if("string"!=typeof t&&null!==t||!(null!=e&&e.__classes&&e.__classes.indexOf("java.lang.Throwable")>=0||null!=e&&e instanceof Error||null===e))if("string"!=typeof t&&null!==t||void 0!==e)if((null!=t&&t.__classes&&t.__classes.indexOf("java.lang.Throwable")>=0||null!=t&&t instanceof Error||null===t)&&void 0===e){super(arguments[0])}else{if(void 0!==t||void 0!==e)throw new Error("invalid overload");super()}else{super(t)}else{super(t,e)}}}e.SetErrorException=o,o.__class="com.WacomGSS.STU.SetErrorException",o.__interfaces=["java.io.Serializable"]}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){class o{constructor(e,a){if((null!=e&&null!=e.constructor&&null!=e.constructor.__interfaces&&e.constructor.__interfaces.indexOf("com.WacomGSS.STU.Tablet.IEncryptionHandler")>=0||null===e)&&(null!=a&&null!=a.constructor&&null!=a.constructor.__interfaces&&a.constructor.__interfaces.indexOf("com.WacomGSS.STU.Tablet.IEncryptionHandler2")>=0||null===a)){void 0===this.encryptionHandler&&(this.encryptionHandler=null),void 0===this.encryptionHandler2&&(this.encryptionHandler2=null),void 0===this.protocol&&(this.protocol=null),void 0===this.isTls&&(this.isTls=!1),void 0===this.reportCountLengths&&(this.reportCountLengths=null),void 0===this.maxImageBlockSize&&(this.maxImageBlockSize=0),void 0===this.status&&(this.status=null),void 0===this.sessionId&&(this.sessionId=0),void 0===this.retries&&(this.retries=0),void 0===this.sleepBetweenRetries&&(this.sleepBetweenRetries=0),void 0===this.interfaceHandler&&(this.interfaceHandler=null),void 0===this.reportHandler&&(this.reportHandler=null),void 0===this.tabletHandlers&&(this.tabletHandlers=null),void 0===this.encryptionType&&(this.encryptionType=null),void 0===this.asymmetricKeySizeBytes&&(this.asymmetricKeySizeBytes=0),this.sessionId=0,this.retries=25,this.sleepBetweenRetries=50,this.interfaceHandler=null,this.reportHandler=new t.WacomGSS.STU.Protocol.ReportHandler,this.reportHandler.addReportHandler(this),this.maxImageBlockSize=0,this.tabletHandlers=[],this.encryptionHandler=null,this.encryptionHandler2=null,this.encryptionType=o.EncryptionType.Unknown,this.asymmetricKeySizeBytes=0;void 0===this.encryptionHandler&&(this.encryptionHandler=null),void 0===this.encryptionHandler2&&(this.encryptionHandler2=null),void 0===this.protocol&&(this.protocol=null),void 0===this.isTls&&(this.isTls=!1),void 0===this.reportCountLengths&&(this.reportCountLengths=null),void 0===this.maxImageBlockSize&&(this.maxImageBlockSize=0),void 0===this.status&&(this.status=null),void 0===this.sessionId&&(this.sessionId=0),void 0===this.retries&&(this.retries=0),void 0===this.sleepBetweenRetries&&(this.sleepBetweenRetries=0),void 0===this.interfaceHandler&&(this.interfaceHandler=null),void 0===this.reportHandler&&(this.reportHandler=null),void 0===this.tabletHandlers&&(this.tabletHandlers=null),void 0===this.encryptionType&&(this.encryptionType=null),void 0===this.asymmetricKeySizeBytes&&(this.asymmetricKeySizeBytes=0),(()=>{this.encryptionHandler=e,this.encryptionHandler2=a})()}else if((null!=e&&null!=e.constructor&&null!=e.constructor.__interfaces&&e.constructor.__interfaces.indexOf("com.WacomGSS.STU.Tablet.IEncryptionHandler")>=0||null===e)&&void 0===a){void 0===this.encryptionHandler&&(this.encryptionHandler=null),void 0===this.encryptionHandler2&&(this.encryptionHandler2=null),void 0===this.protocol&&(this.protocol=null),void 0===this.isTls&&(this.isTls=!1),void 0===this.reportCountLengths&&(this.reportCountLengths=null),void 0===this.maxImageBlockSize&&(this.maxImageBlockSize=0),void 0===this.status&&(this.status=null),void 0===this.sessionId&&(this.sessionId=0),void 0===this.retries&&(this.retries=0),void 0===this.sleepBetweenRetries&&(this.sleepBetweenRetries=0),void 0===this.interfaceHandler&&(this.interfaceHandler=null),void 0===this.reportHandler&&(this.reportHandler=null),void 0===this.tabletHandlers&&(this.tabletHandlers=null),void 0===this.encryptionType&&(this.encryptionType=null),void 0===this.asymmetricKeySizeBytes&&(this.asymmetricKeySizeBytes=0),this.sessionId=0,this.retries=25,this.sleepBetweenRetries=50,this.interfaceHandler=null,this.reportHandler=new t.WacomGSS.STU.Protocol.ReportHandler,this.reportHandler.addReportHandler(this),this.maxImageBlockSize=0,this.tabletHandlers=[],this.encryptionHandler=null,this.encryptionHandler2=null,this.encryptionType=o.EncryptionType.Unknown,this.asymmetricKeySizeBytes=0;void 0===this.encryptionHandler&&(this.encryptionHandler=null),void 0===this.encryptionHandler2&&(this.encryptionHandler2=null),void 0===this.protocol&&(this.protocol=null),void 0===this.isTls&&(this.isTls=!1),void 0===this.reportCountLengths&&(this.reportCountLengths=null),void 0===this.maxImageBlockSize&&(this.maxImageBlockSize=0),void 0===this.status&&(this.status=null),void 0===this.sessionId&&(this.sessionId=0),void 0===this.retries&&(this.retries=0),void 0===this.sleepBetweenRetries&&(this.sleepBetweenRetries=0),void 0===this.interfaceHandler&&(this.interfaceHandler=null),void 0===this.reportHandler&&(this.reportHandler=null),void 0===this.tabletHandlers&&(this.tabletHandlers=null),void 0===this.encryptionType&&(this.encryptionType=null),void 0===this.asymmetricKeySizeBytes&&(this.asymmetricKeySizeBytes=0),(()=>{this.encryptionHandler=e,this.encryptionHandler2=null})()}else if((null!=e&&null!=e.constructor&&null!=e.constructor.__interfaces&&e.constructor.__interfaces.indexOf("com.WacomGSS.STU.Tablet.IEncryptionHandler2")>=0||null===e)&&void 0===a){let e=arguments[0];void 0===this.encryptionHandler&&(this.encryptionHandler=null),void 0===this.encryptionHandler2&&(this.encryptionHandler2=null),void 0===this.protocol&&(this.protocol=null),void 0===this.isTls&&(this.isTls=!1),void 0===this.reportCountLengths&&(this.reportCountLengths=null),void 0===this.maxImageBlockSize&&(this.maxImageBlockSize=0),void 0===this.status&&(this.status=null),void 0===this.sessionId&&(this.sessionId=0),void 0===this.retries&&(this.retries=0),void 0===this.sleepBetweenRetries&&(this.sleepBetweenRetries=0),void 0===this.interfaceHandler&&(this.interfaceHandler=null),void 0===this.reportHandler&&(this.reportHandler=null),void 0===this.tabletHandlers&&(this.tabletHandlers=null),void 0===this.encryptionType&&(this.encryptionType=null),void 0===this.asymmetricKeySizeBytes&&(this.asymmetricKeySizeBytes=0),this.sessionId=0,this.retries=25,this.sleepBetweenRetries=50,this.interfaceHandler=null,this.reportHandler=new t.WacomGSS.STU.Protocol.ReportHandler,this.reportHandler.addReportHandler(this),this.maxImageBlockSize=0,this.tabletHandlers=[],this.encryptionHandler=null,this.encryptionHandler2=null,this.encryptionType=o.EncryptionType.Unknown,this.asymmetricKeySizeBytes=0;void 0===this.encryptionHandler&&(this.encryptionHandler=null),void 0===this.encryptionHandler2&&(this.encryptionHandler2=null),void 0===this.protocol&&(this.protocol=null),void 0===this.isTls&&(this.isTls=!1),void 0===this.reportCountLengths&&(this.reportCountLengths=null),void 0===this.maxImageBlockSize&&(this.maxImageBlockSize=0),void 0===this.status&&(this.status=null),void 0===this.sessionId&&(this.sessionId=0),void 0===this.retries&&(this.retries=0),void 0===this.sleepBetweenRetries&&(this.sleepBetweenRetries=0),void 0===this.interfaceHandler&&(this.interfaceHandler=null),void 0===this.reportHandler&&(this.reportHandler=null),void 0===this.tabletHandlers&&(this.tabletHandlers=null),void 0===this.encryptionType&&(this.encryptionType=null),void 0===this.asymmetricKeySizeBytes&&(this.asymmetricKeySizeBytes=0),(()=>{this.encryptionHandler=null,this.encryptionHandler2=e})()}else{if(void 0!==e||void 0!==a)throw new Error("invalid overload");void 0===this.encryptionHandler&&(this.encryptionHandler=null),void 0===this.encryptionHandler2&&(this.encryptionHandler2=null),void 0===this.protocol&&(this.protocol=null),void 0===this.isTls&&(this.isTls=!1),void 0===this.reportCountLengths&&(this.reportCountLengths=null),void 0===this.maxImageBlockSize&&(this.maxImageBlockSize=0),void 0===this.status&&(this.status=null),void 0===this.sessionId&&(this.sessionId=0),void 0===this.retries&&(this.retries=0),void 0===this.sleepBetweenRetries&&(this.sleepBetweenRetries=0),void 0===this.interfaceHandler&&(this.interfaceHandler=null),void 0===this.reportHandler&&(this.reportHandler=null),void 0===this.tabletHandlers&&(this.tabletHandlers=null),void 0===this.encryptionType&&(this.encryptionType=null),void 0===this.asymmetricKeySizeBytes&&(this.asymmetricKeySizeBytes=0),this.sessionId=0,this.retries=25,this.sleepBetweenRetries=50,this.interfaceHandler=null,this.reportHandler=new t.WacomGSS.STU.Protocol.ReportHandler,this.reportHandler.addReportHandler(this),this.maxImageBlockSize=0,this.tabletHandlers=[],this.encryptionHandler=null,this.encryptionHandler2=null,this.encryptionType=o.EncryptionType.Unknown,this.asymmetricKeySizeBytes=0}}static I(t){return 255&t}async connect2(e){let o=0;try{try{this.isTls=null!=e&&e instanceof t.WacomGSS.STU.TlsInterface;const o=new t.WacomGSS.STU.Protocol.Protocol(e);this.status=await o.getStatus(),this.protocol=o,this.reportCountLengths=e.getReportCountLengths(),null!=this.reportCountLengths?this.maxImageBlockSize=this.reportCountLengths[t.WacomGSS.STU.Protocol.ReportId.ImageDataBlock_$LI$()]-3:this.maxImageBlockSize=t.WacomGSS.STU.Protocol.ImageDataBlock.maxLengthHID;const a=this.status.getStatusCode();if(a!==t.WacomGSS.STU.Protocol.StatusCode.Ready_$LI$()){a===t.WacomGSS.STU.Protocol.StatusCode.Image_$LI$()?this.protocol.setEndImageData$com_WacomGSS_STU_Protocol_EndImageDataFlag(t.WacomGSS.STU.Protocol.EndImageDataFlag.Abandon):a===t.WacomGSS.STU.Protocol.StatusCode.Capture_$LI$()?await this.endCapture():t.WacomGSS.STU.Protocol.StatusCode.Image_Boot_$LI$();try{await this.waitForStatus(t.WacomGSS.STU.Protocol.StatusCode.Ready_$LI$())}catch(e){this.protocol.setReset(t.WacomGSS.STU.Protocol.ResetFlag.Software),await this.waitForStatus(t.WacomGSS.STU.Protocol.StatusCode.Ready_$LI$())}this.status=await this.protocol.getStatus()}0!=this.tabletHandlers.length&&(this.interfaceHandler=new t.WacomGSS.STU.InterfaceHandler(e.interfaceQueue()),this.interfaceHandler.addInterfaceHandler(this))}catch(t){throw e.disconnect(),t}}catch(e){if(null!=e&&e instanceof t.WacomGSS.STU.STUException){o=-1}if(null!=e&&e instanceof t.WacomGSS.RuntimeException){o=-100}if(null!=e&&e.__classes&&e.__classes.indexOf("java.lang.Throwable")>=0||null!=e&&e instanceof Error){o=-666}}return o}async initializeEncryption1(){if(this.encryptionHandler.requireDH()){const e=await this.protocol.getDHprime();if(await this.checkErrorCode(),!t.WacomGSS.STU.Protocol.ProtocolHelper.supportsEncryption$com_WacomGSS_STU_Protocol_DHprime(e))throw new t.WacomGSS.STU.NotSupportedException("Tablet does not have encryption enabled");const o=await this.protocol.getDHbase();await this.checkErrorCode(),await this.encryptionHandler.setDH(e.getValue(),o.getValue())}await this.reinitializeEncryption1()}async reinitializeEncryption1(){const e=new t.WacomGSS.STU.Protocol.PublicKey(this.encryptionHandler.generateHostPublicKey());await this.waitForStatusToSend(t.WacomGSS.STU.Protocol.ReportId.HostPublicKey_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set);const o=await t.WacomGSS.STU.Protocol.ProtocolHelper.setHostPublicKeyAndPollForDevicePublicKey(this.protocol,e,this.retries,this.sleepBetweenRetries);await this.checkErrorCode(),this.encryptionHandler.computeSharedKey(o.getValue())}async initializeEncryption2(e){switch(e||(this.symmetricKeyType=this.encryptionHandler2.getSymmetricKeyType(),this.asymmetricPaddingType=this.encryptionHandler2.getAsymmetricPaddingType(),this.asymmetricKeyType=this.encryptionHandler2.getAsymmetricKeyType()),this.asymmetricKeyType){case t.WacomGSS.STU.Protocol.AsymmetricKeyType.RSA1024:this.asymmetricKeySizeBytes=128;break;case t.WacomGSS.STU.Protocol.AsymmetricKeyType.RSA1536:this.asymmetricKeySizeBytes=192;break;case t.WacomGSS.STU.Protocol.AsymmetricKeyType.RSA2048:this.asymmetricKeySizeBytes=256;break;default:throw new t.WacomGSS.RuntimeException("asymmetricKeyType unknown")}{await this.waitForStatusToSend(t.WacomGSS.STU.Protocol.ReportId.EncryptionCommand_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set);const e=await t.WacomGSS.STU.Protocol.Protocol.initializeSetEncryptionType(this.symmetricKeyType,this.asymmetricPaddingType,this.asymmetricKeyType);await this.protocol.setEncryptionCommand(e)}{this.publicExponent=await this.encryptionHandler2.getPublicExponent(),this.publicKey=await this.encryptionHandler2.generatePublicKey();let e=t.WacomGSS.STU.Protocol.Protocol.initializeSetParameterBlock(t.WacomGSS.STU.Protocol.EncryptionCommandParameterBlockIndex.RSAe,this.publicExponent);await this.protocol.setEncryptionCommand(e);{let o=this.publicKey.length,a=0;for(;0!==o;){const n=o>64?64:o,r=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(n);((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(this.publicKey,a,r,0,n),e=t.WacomGSS.STU.Protocol.Protocol.initializeSetParameterBlock(t.WacomGSS.STU.Protocol.EncryptionCommandParameterBlockIndex.RSAn,r),await this.protocol.setEncryptionCommand(e),o-=n,a+=n}}}{const e=await this.protocol.getEncryptionStatus();if(e.getStatusCodeRSAe()!==t.WacomGSS.STU.Protocol.StatusCodeRSA.Ready_$LI$()||e.getStatusCodeRSAn()!==t.WacomGSS.STU.Protocol.StatusCodeRSA.Ready_$LI$())throw new t.WacomGSS.RuntimeException("failed to upload public exponent and public key to tablet")}await this.reinitializeEncryption2()}async reinitializeEncryption2(e){{const e=await t.WacomGSS.STU.Protocol.ProtocolHelper.generateSymmetricKeyAndWaitForEncryptionStatus(this.protocol,this.retries,this.sleepBetweenRetries,this.retries*this.sleepBetweenRetries);if(e.getLastResultCode()!==t.WacomGSS.STU.Protocol.ErrorCodeRSA.None_$LI$())throw new t.WacomGSS.RuntimeException("EncryptionStatus.lastResultCode != ErrorCodeRSA_None after generateSymmetricKeyAndWaitForEncryptionStatus()");e.getStatusCodeRSAc()!==t.WacomGSS.STU.Protocol.StatusCodeRSA.Ready_$LI$()&&new t.WacomGSS.RuntimeException("EncryptionStatus.statusCodeRSAc != StatusCodeRSA.Ready after generateSymmetricKeyAndWaitForEncryptionStatus()")}const o=(t=>{let e=[];for(;t-- >0;)e.push(0);return e})(this.asymmetricKeySizeBytes);{let e=0,a=0;for(e=0;e<o.length;++a){let n=await t.WacomGSS.STU.Protocol.Protocol.initializeGetParameterBlock(t.WacomGSS.STU.Protocol.EncryptionCommandParameterBlockIndex.RSAc,a);await this.protocol.setEncryptionCommand(n),n=await this.protocol.getEncryptionCommand(t.WacomGSS.STU.Protocol.EncryptionCommandNumber.GetParameterBlock);const r=n.getData();if(null==r||0===r.length)throw new t.WacomGSS.RuntimeException("EncryptionCommand.data returned zero-length byte array");((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(r,0,o,e,r.length),e+=r.length}}await this.encryptionHandler2.computeSessionKey(o)}async waitForStatusToSend(e,o){await t.WacomGSS.STU.Protocol.ProtocolHelper.waitForStatusToSend(this.protocol,e,o,this.retries,this.sleepBetweenRetries)}async waitForStatus(e){await t.WacomGSS.STU.Protocol.ProtocolHelper.waitForStatus(this.protocol,e,this.retries,this.sleepBetweenRetries)}async clear(){this.sessionId=0,null!=this.encryptionHandler&&this.encryptionHandler.clearKeys(),null!=this.encryptionHandler2&&this.encryptionHandler2.clearKeys()}async checkErrorCode(){if(this.status=await this.protocol.getStatus(),this.status.getLastResultCode()!==t.WacomGSS.STU.Protocol.ErrorCode.None_$LI$())throw new t.WacomGSS.STU.ErrorCodeException}checkSupported(e){if(!this.isSupported(e))throw new t.WacomGSS.STU.NotSupportedException("ReportId="+e)}checkProtocol(){if(null==this.protocol)throw new t.WacomGSS.STU.NotConnectedException}onDecrypt(e){switch(this.encryptionType){case t.WacomGSS.STU.Tablet.EncryptionType.v1:if(null==this.encryptionHandler)throw new t.WacomGSS.RuntimeException("onDecrypt but no encryptionHandler");return this.encryptionHandler.decrypt(e);case t.WacomGSS.STU.Tablet.EncryptionType.v2:if(null==this.encryptionHandler2)throw new t.WacomGSS.RuntimeException("onDecrypt but no encryptionHandler2");return this.encryptionHandler2.decrypt(e);case t.WacomGSS.STU.Tablet.EncryptionType.None:case t.WacomGSS.STU.Tablet.EncryptionType.Unknown:}throw new t.WacomGSS.RuntimeException("Unexpected call to onDecrypt")}getEncryptionHandler(){return this.encryptionHandler}setEncryptionHandler(e){if(this.isConnected())throw new t.WacomGSS.RuntimeException("cannot set EncryptionHandler while connected");this.encryptionHandler=e}getEncryptionHandler2(){return this.encryptionHandler2}setEncryptionHandler2(e){if(this.isConnected())throw new t.WacomGSS.RuntimeException("cannot set EncryptionHandler2 while connected");this.encryptionHandler2=e}tlsConnect(e){this.disconnect();const o=new t.WacomGSS.STU.TlsInterface;let a=-1;if(0===a){new t.WacomGSS.STU.Protocol.TlsProtocol(o).sendProtocolVersion(1);a=this.connect2(o)}return a}async usbConnect(e){await this.disconnect();const o=new t.WacomGSS.STU.UsbInterface;await o.connect(e),await this.connect2(o)}getProtocol(){return new t.WacomGSS.STU.Protocol.Protocol(new o.TabletInterface(this))}isEmpty(){return null==this.protocol}isConnected(){return null!=this.protocol&&this.protocol.getInterface().isConnected()}async disconnect(){if(null!=this.protocol&&this.protocol.getInterface().disconnect(),this.clear(),this.protocol=null,this.status=null,this.encryptionType=o.EncryptionType.Unknown,this.asymmetricKeySizeBytes=0,this.maxImageBlockSize=0,null!=this.interfaceHandler){this.interfaceHandler.removeInterfaceHandler(this);try{this.interfaceHandler.stop()}catch(t){}this.interfaceHandler=null}null!=this.encryptionHandler&&this.encryptionHandler.reset(),null!=this.encryptionHandler2&&this.encryptionHandler2.reset()}interfaceQueue(){return this.checkProtocol(),this.protocol.getInterface().interfaceQueue()}addTabletHandler(e){this.tabletHandlers.push(e),null==this.interfaceHandler&&null!=this.protocol&&(this.interfaceHandler=new t.WacomGSS.STU.InterfaceHandler(this.protocol.getInterface().interfaceQueue()),this.interfaceHandler.addInterfaceHandler(this))}removeTabletHandler(t){if((e=>{let o=e.indexOf(t);o>=0&&e.splice(o,1)})(this.tabletHandlers),0==this.tabletHandlers.length&&null!=this.interfaceHandler){this.interfaceHandler.removeInterfaceHandler(this);try{this.interfaceHandler.stop()}catch(t){}this.interfaceHandler=null}}queueNotifyAll(){this.checkProtocol(),this.protocol.getInterface().queueNotifyAll()}supportsWrite(){return this.checkProtocol(),this.protocol.getInterface().supportsWrite()}getReportCountLengths(){return this.checkProtocol(),this.reportCountLengths=this.protocol.getInterface().getReportCountLengths(),this.reportCountLengths}getProductId(){return this.checkProtocol(),this.protocol.getInterface().getProductId()}isSupported(t){return this.checkProtocol(),null==this.reportCountLengths||null!=this.reportCountLengths[o.I(t)]}async getStatus(){return this.checkProtocol(),this.status=await this.protocol.getStatus(),this.status}async reset(){this.clear(),this.checkProtocol(),await this.protocol.setReset(t.WacomGSS.STU.Protocol.ResetFlag.Software),await this.checkErrorCode()}async getInformation(){this.checkProtocol();const t=await this.protocol.getInformation();return await this.checkErrorCode(),t}async getHidInformation(){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.HidInformation_$LI$());const e=await this.protocol.getHidInformation();return await this.checkErrorCode(),e}async getDefaultMode(){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.DefaultMode_$LI$());const e=await this.protocol.getDefaultMode();return await this.checkErrorCode(),e}async setDefaultMode(e){const a=new o.StateHandler;await a.init(this,t.WacomGSS.STU.Protocol.ReportId.DefaultMode_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await this.protocol.setDefaultMode(e),await this.checkErrorCode(),await a.restore(),await a.close()}async getCapability(){this.checkProtocol();const t=await this.protocol.getCapability();return await this.checkErrorCode(),t}async getUid(){this.checkProtocol();const t=await this.protocol.getUid();return await this.checkErrorCode(),t}async setUid(e){const a=new o.StateHandler;await a.init(this,t.WacomGSS.STU.Protocol.ReportId.Uid_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await this.protocol.setUid(e),await this.checkErrorCode(),await a.restore(),await a.close()}async getUid2(){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.Uid2_$LI$());const e=await this.protocol.getUid2();return await this.checkErrorCode(),e}async getEserial(){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.Eserial_$LI$());const e=await this.protocol.getEserial();return await this.checkErrorCode(),e}async setClearScreenArea(e){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.ClearScreenArea_$LI$()),await this.protocol.setClearScreenArea(e),await await this.checkErrorCode()}async writeImageArea(e,a,n){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.StartImageDataArea_$LI$());const r=new o.StateHandler;await r.init(this,t.WacomGSS.STU.Protocol.ReportId.StartImageDataArea_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await t.WacomGSS.STU.Protocol.ProtocolHelper.writeImageArea$com_WacomGSS_STU_Protocol_Protocol$com_WacomGSS_STU_Protocol_EncodingMode$com_WacomGSS_STU_Protocol_Rectangle$int$byte_A$int$long(this.protocol,e,a,this.maxImageBlockSize,n,this.retries,this.sleepBetweenRetries),await this.checkErrorCode(),await r.restore(),await r.close()}async getHostPublicKey(){this.checkProtocol();const t=await this.protocol.getHostPublicKey();return await this.checkErrorCode(),t}async getDevicePublicKey(){this.checkProtocol();const t=await this.protocol.getDevicePublicKey();return await this.checkErrorCode(),t}async restartCapture(e){switch(this.encryptionType){case t.WacomGSS.STU.Tablet.EncryptionType.v1:await this.reinitializeEncryption1();break;case t.WacomGSS.STU.Tablet.EncryptionType.v2:await this.reinitializeEncryption2();break;case t.WacomGSS.STU.Tablet.EncryptionType.Unknown:case t.WacomGSS.STU.Tablet.EncryptionType.None:default:throw new t.WacomGSS.RuntimeException("unexpected restartCapture with invalid encryptionType setting")}t.WacomGSS.STU.Protocol.ProtocolHelper.statusCanSend(this.status.getStatusCode(),t.WacomGSS.STU.Protocol.ReportId.StartCapture_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set)||await this.waitForStatusToSend(t.WacomGSS.STU.Protocol.ReportId.StartCapture_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await this.protocol.setStartCapture(e),await this.checkErrorCode(),this.sessionId=e,await t.WacomGSS.STU.Protocol.ProtocolHelper.waitForStatus(this.protocol,t.WacomGSS.STU.Protocol.StatusCode.Capture_$LI$(),this.retries,this.sleepBetweenRetries)}async startCapture(e,a){if(this.checkProtocol(),null==this.encryptionHandler&&null==this.encryptionHandler2)throw new t.WacomGSS.RuntimeException("cannot startCapture without an EncryptionHandler or EncryptionHandler2");if(this.encryptionType===o.EncryptionType.None)throw new t.WacomGSS.STU.NotSupportedException("ReportId.StartCapture");if(this.status.getStatusCode()===t.WacomGSS.STU.Protocol.StatusCode.Capture_$LI$()&&(this.sessionId=0,await this.endCapture()),this.encryptionType==o.EncryptionType.Unknown)if(this.encryptionType=o.EncryptionType.None,null!=this.reportCountLengths[t.WacomGSS.STU.Protocol.ReportId.EncryptionStatus_$LI$()]){if(null==this.encryptionHandler2)throw new t.WacomGSS.STU.NotSupportedException("Cannot call startCapture on this tablet without an encryptionHandler2 set");this.encryptionType=o.EncryptionType.v2}else{if(!await t.WacomGSS.STU.Protocol.ProtocolHelper.supportsEncryption$com_WacomGSS_STU_Protocol_Protocol(this.protocol))throw new t.WacomGSS.STU.NotSupportedException("startCapture");if(null==this.encryptionHandler)throw new t.WacomGSS.STU.NotSupportedException("Cannot call startCapture on this tablet without an encryptionHandler set");this.encryptionType=o.EncryptionType.v1}this.encryptionType==o.EncryptionType.v2?await this.initializeEncryption2(a):await this.initializeEncryption1(),t.WacomGSS.STU.Protocol.ProtocolHelper.statusCanSend(this.status.getStatusCode(),t.WacomGSS.STU.Protocol.ReportId.StartCapture_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set)||await this.waitForStatusToSend(t.WacomGSS.STU.Protocol.ReportId.StartCapture_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await this.protocol.setStartCapture(e),await this.checkErrorCode(),this.sessionId=e,await this.waitForStatus(t.WacomGSS.STU.Protocol.StatusCode.Capture_$LI$())}async endCapture(){await new Promise((t=>setTimeout(t,100))),this.checkProtocol(),this.status.getStatusCode()===t.WacomGSS.STU.Protocol.StatusCode.Capture_$LI$()&&(this.sessionId=0,await this.protocol.setEndCapture(),await this.checkErrorCode(),this.status.getStatusCode()!==t.WacomGSS.STU.Protocol.StatusCode.Ready_$LI$()&&(this.status=await this.waitForStatus(t.WacomGSS.STU.Protocol.StatusCode.Ready_$LI$())))}async getDHprime(){this.checkProtocol();const t=await this.protocol.getDHprime();return await this.checkErrorCode(),t}async setDHprime(e){const a=new o.StateHandler;await a.init(this,t.WacomGSS.STU.Protocol.ReportId.DHprime_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),null!=this.encryptionHandler&&this.encryptionHandler.reset(),await this.protocol.setDHprime(e),await this.checkErrorCode(),await a.restore(),await a.close()}async getDHbase(){this.checkProtocol();const t=await this.protocol.getDHbase();return await this.checkErrorCode(),t}async setDHbase(e){const a=new o.StateHandler;await a.init(this,t.WacomGSS.STU.Protocol.ReportId.DHbase_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),null!=this.encryptionHandler&&this.encryptionHandler.reset(),await this.protocol.setDHbase(e),await this.checkErrorCode(),await a.restore(),await a.close()}async setClearScreen(){const e=new o.StateHandler;await e.init(this,t.WacomGSS.STU.Protocol.ReportId.ClearScreen_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await this.protocol.setClearScreen(),await this.checkErrorCode(),await e.restore(),await e.close()}async getInkingMode(){this.checkProtocol();const t=await this.protocol.getInkingMode();return await this.checkErrorCode(),t}async setInkingMode(t){this.checkProtocol(),await this.protocol.setInkingMode(t),await this.checkErrorCode()}async getInkThreshold(){this.checkProtocol();const t=await this.protocol.getInkThreshold();return await this.checkErrorCode(),t}async setInkThreshold(e){const a=new o.StateHandler;await a.init(this,t.WacomGSS.STU.Protocol.ReportId.InkThreshold_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await this.protocol.setInkThreshold(e),await this.checkErrorCode(),await a.restore(),await a.close()}async writeImage$com_WacomGSS_STU_Protocol_EncodingMode$byte_A(e,a){const n=new o.StateHandler;await n.init(this,t.WacomGSS.STU.Protocol.ReportId.StartImageData_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await t.WacomGSS.STU.Protocol.ProtocolHelper.writeImage$com_WacomGSS_STU_Protocol_Protocol$com_WacomGSS_STU_Protocol_EncodingMode$int$byte_A$int$long(this.protocol,e,this.maxImageBlockSize,a,this.retries,this.sleepBetweenRetries),await await this.checkErrorCode(),await n.restore(),await n.close()}async writeImage(t,e){if("number"!=typeof t&&null!==t||!(null!=e&&e instanceof Array&&(0==e.length||null==e[0]||"number"==typeof e[0])||null===e)){if("number"!=typeof t&&null!==t||!(null!=e&&e instanceof Array&&(0==e.length||null==e[0]||"number"==typeof e[0])||null===e))throw new Error("invalid overload");return await this.writeImage$int$byte_A(t,e)}return await this.writeImage$com_WacomGSS_STU_Protocol_EncodingMode$byte_A(t,e)}async writeImage$int$byte_A(e,a){const n=new o.StateHandler;await n.init(this,t.WacomGSS.STU.Protocol.ReportId.StartImageData_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await t.WacomGSS.STU.Protocol.ProtocolHelper.writeImage$com_WacomGSS_STU_Protocol_Protocol$int$byte_A$int$long(this.protocol,e,a,this.retries,this.sleepBetweenRetries),await await this.checkErrorCode(),await n.restore(),await n.close()}async endImageData(){this.checkProtocol(),this.status.getStatusCode()===t.WacomGSS.STU.Protocol.StatusCode.Image_$LI$()&&(await this.protocol.setEndImageData$com_WacomGSS_STU_Protocol_EndImageDataFlag(t.WacomGSS.STU.Protocol.EndImageDataFlag.Abandon),await await this.checkErrorCode())}async getHandwritingThicknessColor(){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.HandwritingThicknessColor_$LI$());const e=await this.protocol.getHandwritingThicknessColor();return await this.checkErrorCode(),e}async setHandwritingThicknessColor(e){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.HandwritingThicknessColor_$LI$()),await this.protocol.setHandwritingThicknessColor(e),await this.checkErrorCode()}async getBackgroundColor(){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.BackgroundColor_$LI$());const e=await this.protocol.getBackgroundColor();return await this.checkErrorCode(),e}async setBackgroundColor(e){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.BackgroundColor_$LI$()),await this.protocol.setBackgroundColor(e),await this.checkErrorCode()}async getHandwritingDisplayArea(){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.HandwritingDisplayArea_$LI$());const e=await this.protocol.getHandwritingDisplayArea();return await this.checkErrorCode(),e}async setHandwritingDisplayArea(e){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.HandwritingDisplayArea_$LI$()),await this.protocol.setHandwritingDisplayArea(e),await this.checkErrorCode()}async getBacklightBrightness(){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.BacklightBrightness_$LI$());const e=await this.protocol.getBacklightBrightness();return await this.checkErrorCode(),e}async setBacklightBrightness(e){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.BacklightBrightness_$LI$()),await this.waitForStatusToSend(t.WacomGSS.STU.Protocol.ReportId.BacklightBrightness_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await this.protocol.setBacklightBrightness(e),await this.checkErrorCode()}async getScreenContrast(){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.ScreenContrast_$LI$());const e=await this.protocol.getScreenContrast();return await this.checkErrorCode(),e}async setScreenContrast(e){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.ScreenContrast_$LI$()),await this.waitForStatusToSend(t.WacomGSS.STU.Protocol.ReportId.ScreenContrast_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await this.protocol.setScreenContrast(e),await this.checkErrorCode()}async getPenDataOptionMode(){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.PenDataOptionMode_$LI$());const e=await this.protocol.getPenDataOptionMode();return await this.checkErrorCode(),e}async setPenDataOptionMode(e){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.PenDataOptionMode_$LI$()),await this.protocol.setPenDataOptionMode(e),await this.checkErrorCode()}async getEncryptionStatus(){this.checkProtocol(),this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.EncryptionStatus_$LI$());const e=await this.protocol.getEncryptionStatus();return await this.checkErrorCode(),e}async getReportRate(){const e=new o.StateHandler;await e.init(this,t.WacomGSS.STU.Protocol.ReportId.ReportRate_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Get);const a=await this.protocol.getReportRate();return await this.checkErrorCode(),await e.restore(),a}async setReportRate(e){const a=new o.StateHandler;await a.init(this,t.WacomGSS.STU.Protocol.ReportId.ReportRate_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await this.protocol.setReportRate(e),await this.checkErrorCode(),await a.restore(),await a.close()}async setRenderingMode(e){this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.RenderingMode_$LI$());const a=new o.StateHandler;await a.init(this,t.WacomGSS.STU.Protocol.ReportId.RenderingMode_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await this.protocol.setRenderingMode(e),await this.checkErrorCode(),await a.restore(),await a.close()}async getRenderingMode(){this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.RenderingMode_$LI$());const e=new o.StateHandler;await e.init(this,t.WacomGSS.STU.Protocol.ReportId.RenderingMode_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Get);const a=await this.protocol.getRenderingMode();return await this.checkErrorCode(),await e.restore(),a}async setBootScreen(e){this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.BootScreen_$LI$());const a=new o.StateHandler;await a.init(this,t.WacomGSS.STU.Protocol.ReportId.BootScreen_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await this.protocol.setBootScreen(e),await this.checkErrorCode(),await a.restore(),await a.close()}async getBootScreen(){this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.BootScreen_$LI$());const e=new o.StateHandler;await e.init(this,t.WacomGSS.STU.Protocol.ReportId.BootScreen_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Get);const a=await this.protocol.getBootScreen();return await this.checkErrorCode(),await e.restore(),a}async setOperationMode(e){this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.OperationMode_$LI$());const a=new o.StateHandler;await a.init(this,t.WacomGSS.STU.Protocol.ReportId.OperationMode_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await this.protocol.setOperationMode(e),await this.checkErrorCode(),await a.restore(),await a.close()}async getOperationMode(){this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.OperationMode_$LI$());const e=new o.StateHandler;await e.init(this,t.WacomGSS.STU.Protocol.ReportId.OperationMode_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Get);const a=await this.protocol.getOperationMode();return await this.checkErrorCode(),await e.restore(),a}async writeRomImage(e,a){this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.RomStartImageData_$LI$());const n=new o.StateHandler;await n.init(this,t.WacomGSS.STU.Protocol.ReportId.RomStartImageData_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set);const r=this.reportCountLengths[t.WacomGSS.STU.Protocol.ReportId.ImageDataBlock_$LI$()]>3?this.reportCountLengths[t.WacomGSS.STU.Protocol.ReportId.ImageDataBlock_$LI$()]-3:t.WacomGSS.STU.Protocol.ImageDataBlock.maxLengthHID;await t.WacomGSS.STU.Protocol.ProtocolHelper.writeRomImage(this.protocol,e,r,a,this.retries,this.sleepBetweenRetries),await this.checkErrorCode(),await n.restore(),await n.close()}async setRomImageHash(e,a,n){this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.RomImageHash_$LI$());const r=new o.StateHandler;await r.init(this,t.WacomGSS.STU.Protocol.ReportId.RomImageHash_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await this.protocol.setRomImageHash(e,a,n),await await this.checkErrorCode(),await await r.restore(),await r.close()}async getRomImageHash(){this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.RomImageHash_$LI$());const e=new o.StateHandler;await e.init(this,t.WacomGSS.STU.Protocol.ReportId.RomImageHash_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Get);const a=await this.protocol.getRomImageHash();return await this.checkErrorCode(),await e.restore(),a}async setRomImageDelete(e,a,n){this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.RomImageDelete_$LI$());const r=new o.StateHandler;await r.init(this,t.WacomGSS.STU.Protocol.ReportId.RomImageDelete_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await this.protocol.setRomImageDelete(e,a,n),await this.checkErrorCode(),await r.restore(),await r.close()}async getCurrentImageArea(){this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.CurrentImageArea_$LI$());const e=new o.StateHandler;await e.init(this,t.WacomGSS.STU.Protocol.ReportId.CurrentImageArea_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Get);const a=await this.protocol.getCurrentImageArea();return await this.checkErrorCode(),await e.restore(),a}async setRomImageDisplay(e,a,n){this.checkSupported(t.WacomGSS.STU.Protocol.ReportId.RomImageDisplay_$LI$());const r=new o.StateHandler;await r.init(this,t.WacomGSS.STU.Protocol.ReportId.RomImageDisplay_$LI$(),t.WacomGSS.STU.Protocol.ProtocolHelper.OpDirection_Set),await this.protocol.setRomImageDisplay(e,a,n),await this.checkErrorCode(),await r.restore(),await r.close()}onReport(t,e){let o=null;try{o=t.getReport()}catch(t){this.onGetReportException(t)}null!=o&&(o=this.reportHandler.handleReport(o,e,this.isTls),null!=o&&this.onUnhandledReportData(o))}onPenData(t,e){for(let o=0;o<this.tabletHandlers.length;o++){let a=this.tabletHandlers[o];try{a.onPenData(t,e)}catch(t){}}}onPenDataOption(t,e){for(let o=0;o<this.tabletHandlers.length;o++){let a=this.tabletHandlers[o];try{a.onPenDataOption(t,e)}catch(t){}}}onPenDataEncrypted(t,e){for(let o=0;o<this.tabletHandlers.length;o++){let a=this.tabletHandlers[o];try{a.onPenDataEncrypted(t,e)}catch(t){}}}onPenDataEncryptedOption(t,e){for(let o=0;o<this.tabletHandlers.length;o++){let a=this.tabletHandlers[o];try{a.onPenDataEncryptedOption(t,e)}catch(t){}}}onPenDataTimeCountSequence(t,e){for(let o=0;o<this.tabletHandlers.length;o++){let a=this.tabletHandlers[o];try{a.onPenDataTimeCountSequence(t,e)}catch(t){}}}onPenDataTimeCountSequenceEncrypted(t,e){for(let o=0;o<this.tabletHandlers.length;o++){let a=this.tabletHandlers[o];try{a.onPenDataTimeCountSequenceEncrypted(t,e)}catch(t){}}}onEventDataPinPad(t){for(let e=0;e<this.tabletHandlers.length;e++){let o=this.tabletHandlers[e];try{o.onEventDataPinPad(t)}catch(t){}}}onEventDataKeyPad(t){for(let e=0;e<this.tabletHandlers.length;e++){let o=this.tabletHandlers[e];try{o.onEventDataKeyPad(t)}catch(t){}}}onEventDataSignature(t){for(let e=0;e<this.tabletHandlers.length;e++){let o=this.tabletHandlers[e];try{o.onEventDataSignature(t)}catch(t){}}}onEventDataPinPadEncrypted(t){for(let e=0;e<this.tabletHandlers.length;e++){let o=this.tabletHandlers[e];try{o.onEventDataPinPadEncrypted(t)}catch(t){}}}onEventDataKeyPadEncrypted(t){for(let e=0;e<this.tabletHandlers.length;e++){let o=this.tabletHandlers[e];try{o.onEventDataKeyPadEncrypted(t)}catch(t){}}}onEventDataSignatureEncrypted(t){for(let e=0;e<this.tabletHandlers.length;e++){let o=this.tabletHandlers[e];try{o.onEventDataSignatureEncrypted(t)}catch(t){}}}onDevicePublicKey(t){for(let e=0;e<this.tabletHandlers.length;e++){let o=this.tabletHandlers[e];try{o.onDevicePublicKey(t)}catch(t){}}}onEncryptionStatus(t){for(let e=0;e<this.tabletHandlers.length;e++){let o=this.tabletHandlers[e];try{o.onEncryptionStatus(t)}catch(t){}}}onGetReportException(t){for(let e=0;e<this.tabletHandlers.length;e++){let o=this.tabletHandlers[e];try{o.onGetReportException(t)}catch(t){}}}onUnhandledReportData(t){for(let e=0;e<this.tabletHandlers.length;e++){let o=this.tabletHandlers[e];try{o.onUnhandledReportData(t.slice(0))}catch(t){}}}}e.Tablet=o,o.__class="com.WacomGSS.STU.Tablet",o.__interfaces=["com.WacomGSS.STU.IInterfaceHandler","com.WacomGSS.STU.Protocol.IReportHandler"],function(e){let o;!function(t){t[t.Unknown=0]="Unknown",t[t.None=1]="None",t[t.v1=2]="v1",t[t.v2=3]="v2"}(o=e.EncryptionType||(e.EncryptionType={}));class a{constructor(t){this.__parent=t}isConnected(){return this.__parent.protocol.getInterface().isConnected()}disconnect(){this.__parent.clear(),this.disconnect()}get(t){return this.__parent.protocol.getInterface().get(t)}set(t){this.__parent.clear(),this.__parent.protocol.getInterface().set(t)}supportsWrite(){return this.__parent.protocol.getInterface().supportsWrite()}write(t){this.__parent.clear(),this.__parent.protocol.getInterface().write(t)}interfaceQueue(){return this.__parent.protocol.getInterface().interfaceQueue()}queueNotifyAll(){this.__parent.protocol.getInterface().queueNotifyAll()}getReportCountLengths(){return this.__parent.protocol.getInterface().getReportCountLengths()}async getProductId(){return await this.__parent.protocol.getInterface().getProductId()}}e.TabletInterface=a,a.__class="com.WacomGSS.STU.Tablet.TabletInterface",a.__interfaces=["com.WacomGSS.STU.Interface"];class n{async init(e,o,a){this.__parent=e,this.encryptionEnabled=!1,this.sessionId=0,e.checkProtocol(),null==e.status&&(e.status=await e.protocol.getStatus()),t.WacomGSS.STU.Protocol.ProtocolHelper.statusCanSend(e.status.getStatusCode(),o,a)||(e.status.getStatusCode()===t.WacomGSS.STU.Protocol.StatusCode.Capture_$LI$()?(this.encryptionEnabled=!0,this.sessionId=e.sessionId,await e.endCapture()):e.status.getStatusCode()===t.WacomGSS.STU.Protocol.StatusCode.Image_$LI$()?await e.protocol.setEndImageData$com_WacomGSS_STU_Protocol_EndImageDataFlag(t.WacomGSS.STU.Protocol.EndImageDataFlag.Abandon):(e.status.getStatusCode(),t.WacomGSS.STU.Protocol.StatusCode.Image_Boot_$LI$()),await e.waitForStatusToSend(o,a))}async close(){await this.restore()}async restore(){this.encryptionEnabled&&(this.encryptionEnabled=!1,await this.__parent.startCapture(this.sessionId,!0))}}e.StateHandler=n,n.__class="com.WacomGSS.STU.Tablet.StateHandler",n.__interfaces=["java.lang.AutoCloseable"]}(o=e.Tablet||(e.Tablet={}))}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){class o extends t.WacomGSS.STU.STUException{constructor(t,e){if("string"!=typeof t&&null!==t||!(null!=e&&e.__classes&&e.__classes.indexOf("java.lang.Throwable")>=0||null!=e&&e instanceof Error||null===e))if("string"!=typeof t&&null!==t||void 0!==e)if((null!=t&&t.__classes&&t.__classes.indexOf("java.lang.Throwable")>=0||null!=t&&t instanceof Error||null===t)&&void 0===e){super(arguments[0])}else{if(void 0!==t||void 0!==e)throw new Error("invalid overload");super()}else{super(t)}else{super(t,e)}}}e.TimeoutException=o,o.__class="com.WacomGSS.STU.TimeoutException",o.__interfaces=["java.io.Serializable"]}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){class e{static getTlsDevices(){return null}}t.TlsDevice=e,e.__class="com.WacomGSS.STU.TlsDevice"}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){class o{connect(t,e){return-1}get(e){if(!this.isConnected())throw new t.WacomGSS.STU.NotConnectedException;if(null!=e&&0!==e.length){const o=t.WacomGSS.STU.Protocol.ProtocolHelper.makeLegacyGetReport(e[0]),a=this.send$byte_A(o);if(t.WacomGSS.STU.Protocol.ProtocolHelper.checkReturnValueStatus(a),a.length!==e.length+8)throw new t.WacomGSS.STU.STUException("report size doesn't match expected get size");((t,e,o,a,n)=>{if(t!==o||a>=e+n)for(;--n>=0;)o[a++]=t[e++];else{let r=t.slice(e,e+n);for(let t=0;t<n;t++)o[a++]=r[t]}})(a,8,e,0,e.length)}return e}set(e){if(!this.isConnected())throw new t.WacomGSS.STU.NotConnectedException;if(null!=e&&0!==e.length){const o=t.WacomGSS.STU.Protocol.ProtocolHelper.makeLegacySetReport(e),a=this.send$byte_A(o);t.WacomGSS.STU.Protocol.ProtocolHelper.checkReturnValueStatus(a)}}supportsWrite(){return!0}write(e){if(!this.isConnected())throw new t.WacomGSS.STU.NotConnectedException;if(null!=e&&0!==e.length){const o=t.WacomGSS.STU.Protocol.ProtocolHelper.makeLegacyWrite(e),a=this.send$byte_A(o);t.WacomGSS.STU.Protocol.ProtocolHelper.checkReturnValueStatus(a)}}send$byte_A(o){const a=t.WacomGSS.STU.Protocol.TlsProtocol.makeSendHint(o),n=this.send$byte_A$com_WacomGSS_STU_InterfaceTLS_InterfaceQueueSelector(o,e.InterfaceTLS.InterfaceQueueSelector.tag);return t.WacomGSS.STU.Protocol.ProtocolHelper.waitForReturn(n,2e3,a)}getProductId(){return new t.WacomGSS.STU.Protocol.TlsProtocolOOB(this).getDescriptor().getIdProduct()}getReportCountLengths(){return new t.WacomGSS.STU.Protocol.Protocol(this).getReportSizeCollection()}disconnect(){}isConnected(){return!1}interfaceQueue(){return null}queueNotifyAll(){}getPeerCertificate(){return 0}isConnectedOOB(){return!1}getOOB(t){return null}setOOB(t){}send$byte_A$com_WacomGSS_STU_InterfaceTLS_InterfaceQueueSelector(t,e){return null}send(t,e){if(!(null!=t&&t instanceof Array&&(0==t.length||null==t[0]||"number"==typeof t[0])||null===t)||"number"!=typeof e&&null!==e){if((null!=t&&t instanceof Array&&(0==t.length||null==t[0]||"number"==typeof t[0])||null===t)&&void 0===e)return this.send$byte_A(t);throw new Error("invalid overload")}return this.send$byte_A$com_WacomGSS_STU_InterfaceTLS_InterfaceQueueSelector(t,e)}constructor(){}}e.TlsInterface=o,o.__class="com.WacomGSS.STU.TlsInterface",o.__interfaces=["com.WacomGSS.STU.InterfaceTLS","com.WacomGSS.STU.Interface"]}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(t){!function(t){var e=function(){function t(t,e,o){void 0===this.idVendor&&(this.idVendor=0),void 0===this.idProduct&&(this.idProduct=0),void 0===this.bcdDevice&&(this.bcdDevice=0),this.idVendor=t,this.idProduct=e,this.bcdDevice=o}return t.VendorId_Wacom_$LI$=function(){return null==t.VendorId_Wacom&&(t.VendorId_Wacom=1386),t.VendorId_Wacom},t.ProductId_500_$LI$=function(){return null==t.ProductId_500&&(t.ProductId_500=161),t.ProductId_500},t.ProductId_300_$LI$=function(){return null==t.ProductId_300&&(t.ProductId_300=162),t.ProductId_300},t.ProductId_520A_$LI$=function(){return null==t.ProductId_520A&&(t.ProductId_520A=163),t.ProductId_520A},t.ProductId_430_$LI$=function(){return null==t.ProductId_430&&(t.ProductId_430=164),t.ProductId_430},t.ProductId_530_$LI$=function(){return null==t.ProductId_530&&(t.ProductId_530=165),t.ProductId_530},t.ProductId_430V_$LI$=function(){return null==t.ProductId_430V&&(t.ProductId_430V=166),t.ProductId_430V},t.ProductId_530V_$LI$=function(){return null==t.ProductId_530V&&(t.ProductId_530V=167),t.ProductId_530V},t.ProductId_540_$LI$=function(){return null==t.ProductId_540&&(t.ProductId_540=168),t.ProductId_540},t.ProductId_min_$LI$=function(){return null==t.ProductId_min&&(t.ProductId_min=161),t.ProductId_min},t.ProductId_max_$LI$=function(){return null==t.ProductId_max&&(t.ProductId_max=175),t.ProductId_max},t.prototype.getIdVendor=function(){return this.idVendor},t.prototype.getIdProduct=function(){return this.idProduct},t.prototype.getBcdDevice=function(){return this.bcdDevice},t.getUsbDevices=function(){const e=[];return navigator.hid.getDevices().then((o=>{o.forEach((o=>{e.push(new t(o.idVendor,o.idProduct,o.bcdDevice))}))})),e},t.requestDevices=async function(){let t={filters:[{vendorId:1386,productId:161},{vendorId:1386,productId:162},{vendorId:1386,productId:163},{vendorId:1386,productId:164},{vendorId:1386,productId:165},{vendorId:1386,productId:166},{vendorId:1386,productId:168},{vendorId:1386,productId:169}]};return navigator.hid.requestDevice(t)},t}();t.UsbDevice=e,e.__class="com.WacomGSS.STU.UsbDevice"}(t.STU||(t.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){class o{constructor(){this.connected=!1}handleConnectedDevice(t){this.usbDevice.addEventListener("inputreport",this.handleInputReport),this.connected=!0,console.log("Device connected: "+this.usbDevice.productName)}handleDisconnectedDevice(t){this.connected=!1,this.usbDevice.removeEventListener("inputreport",this.handleInputReport),console.log("Device disconnected: "+this.usbDevice.productName)}handleInputReport(t){const e=new Uint8Array(t.data.buffer);let o=Array.from(e);o.unshift(t.reportId),this.interfaceQueue.getReport(o,performance.now())}connect(e,o){return new Promise(((o,a)=>(this.usbDevice=e,this.usbDevice.open().then((()=>{this.usbDevice.interfaceQueue=new t.WacomGSS.STU.InterfaceQueue,this.handleConnectedDevice(),o()}),(t=>{a(t)})))))}async disconnect(){this.usbDevice&&(await this.usbDevice.close(),this.handleDisconnectedDevice())}isConnected(){return this.connected}async get(t){let e,o=await this.usbDevice.receiveFeatureReport(t[0]),a=[];for(e=0;e<o.byteLength;e++)a.push(o.getUint8(e));return a}async set(t){let e=new Uint8Array(t.slice(1));await this.usbDevice.sendFeatureReport(t[0],e.buffer)}supportsWrite(){return!1}async write(t){let e=new Uint8Array(t.length-1);await this.usbDevice.sendFeatureReport(t[0],e.buffer)}interfaceQueue(){return this.usbDevice.interfaceQueue}queueNotifyAll(){}getReportCountLengths(){const t=[];for(let e of this.usbDevice.collections){for(let o of e.inputReports)t[o.reportId]=o.items[0].reportCount;for(let o of e.outputReports)t[o.reportId]=o.items[0].reportCount;for(let o of e.featureReports)t[o.reportId]=o.items[0].reportCount}return t}getProductId(){return this.usbDevice.productId}}e.UsbInterface=o,o.__class="com.WacomGSS.STU.UsbInterface",o.__interfaces=["com.WacomGSS.STU.Interface"]}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={})),function(t){!function(e){!function(e){class o extends t.WacomGSS.STU.STUException{constructor(t,e){if("string"!=typeof t&&null!==t||!(null!=e&&e.__classes&&e.__classes.indexOf("java.lang.Throwable")>=0||null!=e&&e instanceof Error||null===e))if("string"!=typeof t&&null!==t||void 0!==e)if((null!=t&&t.__classes&&t.__classes.indexOf("java.lang.Throwable")>=0||null!=t&&t instanceof Error||null===t)&&void 0===e){super(arguments[0])}else{if(void 0!==t||void 0!==e)throw new Error("invalid overload");super()}else{super(t)}else{super(t,e)}}}e.WriteNotSupportedException=o,o.__class="com.WacomGSS.STU.WriteNotSupportedException",o.__interfaces=["java.io.Serializable"]}(e.STU||(e.STU={}))}(t.WacomGSS||(t.WacomGSS={}))}(com||(com={}));