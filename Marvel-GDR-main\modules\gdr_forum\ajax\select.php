<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

include_once __DIR__.'/../../../core.php';

switch ($resource) {
    case 'bacheche':
        // Selezione delle bacheche per il campo "Appartiene a"
        
        $query = "SELECT gdr_forum.id, gdr_forum.nome AS descrizione FROM gdr_forum |where| ORDER BY descrizione ASC";

        // Filtro per sezione se specificato
        if( isset($superselect['sezione']) ){
            $where[] = '`gdr_forum`.`sezione`='.prepare($superselect['sezione']);
        }

        // Solo bacheche principali (non sottobacheche)
        $where[] = '`gdr_forum`.`id` NOT IN (SELECT gdr_forum_post.id_forum FROM gdr_forum_post WHERE gdr_forum_post.deleted_at IS NULL)';

        // Filtro per elementi specifici
        foreach ($elements as $element) {
            $filter[] = '`gdr_forum`.`id`='.prepare($element);
        }

        // Esclude elementi eliminati se non specificato diversamente
        if (empty($filter)) {
            $where[] = '`gdr_forum`.`deleted_at` IS NULL';
        }

        // Ricerca per nome
        if (!empty($search)) {
            $search_fields[] = 'gdr_forum.nome LIKE '.prepare('%'.$search.'%');
        }

        break;

    case 'sottobacheche':
        // Selezione delle sottobacheche per una bacheca specifica
        
        $query = "SELECT gdr_forum.id, CONCAT(parent.nome, ' > ', gdr_forum.nome) AS descrizione 
                  FROM gdr_forum 
                  LEFT JOIN gdr_forum AS parent ON gdr_forum.id_parent = parent.id 
                  |where| 
                  ORDER BY parent.nome, gdr_forum.nome ASC";

        // Filtro per bacheca genitore se specificato
        if( isset($superselect['id_parent']) && $superselect['id_parent'] > 0 ){
            $where[] = '`gdr_forum`.`id_parent`='.prepare($superselect['id_parent']);
        } else {
            $where[] = '`gdr_forum`.`id_parent` > 0'; // Solo sottobacheche
        }

        // Filtro per sezione se specificato
        if( isset($superselect['sezione']) ){
            $where[] = '`gdr_forum`.`sezione`='.prepare($superselect['sezione']);
        }

        // Filtro per elementi specifici
        foreach ($elements as $element) {
            $filter[] = '`gdr_forum`.`id`='.prepare($element);
        }

        // Esclude elementi eliminati se non specificato diversamente
        if (empty($filter)) {
            $where[] = '`gdr_forum`.`deleted_at` IS NULL';
        }

        // Ricerca per nome
        if (!empty($search)) {
            $search_fields[] = 'gdr_forum.nome LIKE '.prepare('%'.$search.'%');
            $search_fields[] = 'parent.nome LIKE '.prepare('%'.$search.'%');
        }

        break;

    case 'bacheche_attive':
        // Selezione delle bacheche attive (sezione = 1)
        
        $query = "SELECT gdr_forum.id, 
                         CASE 
                             WHEN gdr_forum.id_parent > 0 THEN CONCAT(parent.nome, ' > ', gdr_forum.nome)
                             ELSE gdr_forum.nome 
                         END AS descrizione
                  FROM gdr_forum 
                  LEFT JOIN gdr_forum AS parent ON gdr_forum.id_parent = parent.id 
                  |where| 
                  ORDER BY parent.nome, gdr_forum.nome ASC";

        // Solo bacheche attive
        $where[] = '`gdr_forum`.`sezione`=1';

        // Filtro per elementi specifici
        foreach ($elements as $element) {
            $filter[] = '`gdr_forum`.`id`='.prepare($element);
        }

        // Esclude elementi eliminati se non specificato diversamente
        if (empty($filter)) {
            $where[] = '`gdr_forum`.`deleted_at` IS NULL';
        }

        // Ricerca per nome
        if (!empty($search)) {
            $search_fields[] = 'gdr_forum.nome LIKE '.prepare('%'.$search.'%');
            $search_fields[] = 'parent.nome LIKE '.prepare('%'.$search.'%');
        }

        break;

    case 'bacheche_complete':
        // Selezione completa di tutte le bacheche con gerarchia
        
        $query = "SELECT gdr_forum.id, 
                         CASE 
                             WHEN gdr_forum.id_parent > 0 THEN CONCAT(parent.nome, ' > ', gdr_forum.nome, ' (', CASE WHEN gdr_forum.sezione = 1 THEN 'ON' ELSE 'OFF' END, ')')
                             ELSE CONCAT(gdr_forum.nome, ' (', CASE WHEN gdr_forum.sezione = 1 THEN 'ON' ELSE 'OFF' END, ')')
                         END AS descrizione
                  FROM gdr_forum 
                  LEFT JOIN gdr_forum AS parent ON gdr_forum.id_parent = parent.id 
                  |where| 
                  ORDER BY parent.nome, gdr_forum.nome ASC";

        // Filtro per sezione se specificato
        if( isset($superselect['sezione']) ){
            $where[] = '`gdr_forum`.`sezione`='.prepare($superselect['sezione']);
        }

        // Filtro per elementi specifici
        foreach ($elements as $element) {
            $filter[] = '`gdr_forum`.`id`='.prepare($element);
        }

        // Esclude elementi eliminati se non specificato diversamente
        if (empty($filter)) {
            $where[] = '`gdr_forum`.`deleted_at` IS NULL';
        }

        // Ricerca per nome
        if (!empty($search)) {
            $search_fields[] = 'gdr_forum.nome LIKE '.prepare('%'.$search.'%');
            $search_fields[] = 'parent.nome LIKE '.prepare('%'.$search.'%');
        }

        break;
}
