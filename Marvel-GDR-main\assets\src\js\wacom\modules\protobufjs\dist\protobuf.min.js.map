{"version": 3, "sources": ["lib/prelude.js", "../node_modules/@protobufjs/aspromise/index.js", "../node_modules/@protobufjs/base64/index.js", "../node_modules/@protobufjs/codegen/index.js", "../node_modules/@protobufjs/eventemitter/index.js", "../node_modules/@protobufjs/fetch/index.js", "../node_modules/@protobufjs/float/index.js", "../node_modules/@protobufjs/inquire/index.js", "../node_modules/@protobufjs/path/index.js", "../node_modules/@protobufjs/pool/index.js", "../node_modules/@protobufjs/utf8/index.js", "../src/common.js", "../src/converter.js", "../src/decoder.js", "../src/encoder.js", "../src/enum.js", "../src/field.js", "../src/index-light.js", "../src/index-minimal.js", "../src/index", "../src/mapfield.js", "../src/message.js", "../src/method.js", "../src/namespace.js", "../src/object.js", "../src/oneof.js", "../src/parse.js", "../src/reader.js", "../src/reader_buffer.js", "../src/root.js", "../src/roots.js", "../src/rpc.js", "../src/rpc/service.js", "../src/service.js", "../src/tokenize.js", "../src/type.js", "../src/types.js", "../src/util.js", "../src/util/longbits.js", "../src/util/minimal.js", "../src/verifier.js", "../src/wrappers.js", "../src/writer.js", "../src/writer_buffer.js"], "names": ["undefined", "modules", "cache", "entries", "protobuf", "1", "require", "module", "exports", "fn", "ctx", "params", "Array", "arguments", "length", "offset", "index", "pending", "Promise", "resolve", "reject", "err", "apply", "base64", "string", "p", "n", "Math", "ceil", "b64", "s64", "i", "encode", "buffer", "start", "end", "t", "parts", "chunk", "j", "b", "push", "String", "fromCharCode", "slice", "join", "invalidEncoding", "decode", "c", "charCodeAt", "Error", "test", "codegen", "functionParams", "functionName", "body", "Codegen", "formatStringOrScope", "source", "toString", "verbose", "console", "log", "scopeKeys", "Object", "keys", "scopeParams", "scopeValues", "scopeOffset", "Function", "formatParams", "formatOffset", "replace", "$0", "$1", "value", "Number", "floor", "JSON", "stringify", "functionNameOverride", "EventEmitter", "this", "_listeners", "prototype", "on", "evt", "off", "listeners", "splice", "emit", "args", "fetch", "<PERSON><PERSON><PERSON><PERSON>", "fs", "inquire", "filename", "options", "callback", "xhr", "readFile", "contents", "XMLHttpRequest", "binary", "onreadystatechange", "readyState", "status", "response", "responseText", "Uint8Array", "overrideMimeType", "responseType", "open", "send", "factory", "writeFloat_ieee754", "writeUint", "val", "buf", "pos", "sign", "isNaN", "round", "exponent", "LN2", "pow", "readFloat_ieee754", "readUint", "uint", "mantissa", "NaN", "Infinity", "writeFloat_f32_cpy", "f32", "f8b", "writeFloat_f32_rev", "readFloat_f32_cpy", "readFloat_f32_rev", "f64", "le", "writeDouble_ieee754", "off0", "off1", "readDouble_ieee754", "lo", "hi", "writeDouble_f64_cpy", "writeDouble_f64_rev", "readDouble_f64_cpy", "readDouble_f64_rev", "Float32Array", "writeFloatLE", "writeFloatBE", "readFloatLE", "readFloatBE", "bind", "writeUintLE", "writeUintBE", "readUintLE", "readUintBE", "Float64Array", "writeDoubleLE", "writeDoubleBE", "readDoubleLE", "readDoubleBE", "moduleName", "mod", "eval", "e", "path", "isAbsolute", "normalize", "split", "absolute", "prefix", "shift", "originPath", "include<PERSON>ath", "alreadyNormalized", "alloc", "size", "SIZE", "MAX", "slab", "call", "utf8", "len", "read", "write", "c1", "c2", "common", "commonRe", "name", "json", "nested", "google", "Any", "fields", "type_url", "type", "id", "Duration", "timeType", "seconds", "nanos", "Timestamp", "Empty", "Struct", "keyType", "Value", "oneofs", "kind", "oneof", "nullValue", "numberValue", "stringValue", "boolValue", "structValue", "listValue", "Null<PERSON><PERSON>ue", "values", "NULL_VALUE", "ListValue", "rule", "DoubleValue", "FloatValue", "Int64Value", "UInt64Value", "Int32Value", "UInt32Value", "BoolValue", "StringValue", "BytesValue", "FieldMask", "paths", "get", "file", "converter", "Enum", "util", "genValuePartial_fromObject", "gen", "field", "fieldIndex", "prop", "resolvedType", "repeated", "typeDefault", "fullName", "isUnsigned", "genValuePartial_toObject", "fromObject", "mtype", "fieldsArray", "safeProp", "map", "toObject", "sort", "compareFieldsById", "repeatedFields", "mapFields", "normalFields", "partOf", "arrayDefault", "valuesById", "long", "low", "high", "unsigned", "toNumber", "bytes", "hasKs2", "_fieldsArray", "indexOf", "filter", "group", "ref", "types", "defaults", "basic", "packed", "rfield", "required", "wireType", "mapKey", "genTypePartial", "optional", "ReflectionObject", "create", "constructor", "className", "Namespace", "comment", "comments", "TypeError", "reserved", "fromJSON", "enm", "toJSON", "toJSONOptions", "keepComments", "add", "isString", "isInteger", "isReservedId", "isReservedName", "allow_alias", "remove", "Field", "Type", "ruleRe", "extend", "isObject", "toLowerCase", "message", "defaultValue", "<PERSON>", "extensionField", "declaringField", "_packed", "defineProperty", "getOption", "setOption", "ifNotSet", "resolved", "parent", "lookupTypeOrEnum", "fromNumber", "freeze", "new<PERSON>uffer", "emptyObject", "emptyArray", "ctor", "d", "fieldId", "fieldType", "fieldRule", "decorateType", "decorateEnum", "fieldName", "default", "_configure", "Type_", "build", "load", "root", "Root", "loadSync", "encoder", "decoder", "verifier", "OneOf", "MapField", "Service", "Method", "Message", "wrappers", "configure", "Writer", "BufferWriter", "Reader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpc", "roots", "tokenize", "parse", "resolvedKeyType", "fieldKeyType", "fieldValueType", "properties", "$type", "writer", "encodeDelimited", "reader", "decodeDelimited", "verify", "object", "requestType", "requestStream", "responseStream", "parsedOptions", "resolvedRequestType", "resolvedResponseType", "lookupType", "arrayToJSON", "array", "obj", "_nested<PERSON><PERSON>y", "clearCache", "namespace", "addJSON", "toArray", "nested<PERSON><PERSON><PERSON>", "nested<PERSON><PERSON>", "names", "methods", "getEnum", "prev", "setOptions", "onAdd", "onRemove", "define", "isArray", "ptr", "part", "resolveAll", "lookup", "filterTypes", "parentAlreadyChecked", "found", "lookupEnum", "lookupService", "Service_", "Enum_", "defineProperties", "unshift", "_handleAdd", "_handleRemove", "setParsedOption", "propName", "newValue", "newOpt", "opt", "find", "hasOwnProperty", "setProperty", "Root_", "fieldNames", "addFieldsToParent", "oneofName", "oneOfGetter", "set", "oneOfSetter", "keepCase", "base10Re", "base10NegRe", "base16Re", "base16NegRe", "base8Re", "base8NegRe", "numberRe", "nameRe", "typeRefRe", "fqTypeRefRe", "pkg", "imports", "weakImports", "syntax", "token", "preferTrailingComment", "tn", "alternateCommentMode", "next", "peek", "skip", "cmnt", "head", "isProto3", "applyCase", "camelCase", "illegal", "insideTryCatch", "line", "readString", "readValue", "acceptTypeRef", "substring", "parseInt", "parseFloat", "parseNumber", "readRanges", "target", "acceptStrings", "parseId", "acceptNegative", "parse<PERSON><PERSON><PERSON>", "parseOption", "ifBlock", "valueType", "parseInlineOptions", "parseMapField", "parseField", "parseOneOf", "extensions", "parseType", "dummy", "parseEnumValue", "parseEnum", "service", "commentText", "method", "parseMethod", "parseService", "reference", "parseExtension", "fnIf", "fnElse", "trailingLine", "lcFirst", "ucFirst", "parseGroup", "isCustom", "option", "substr", "optionValue", "parseOptionValue", "result", "prevValue", "concat", "simpleValue", "parsePackage", "whichImports", "parseImport", "parseSyntax", "package", "LongBits", "indexOutOfRange", "write<PERSON><PERSON>th", "RangeError", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "create_array", "readLongVarint", "bits", "readFixed32_end", "readFixed64", "_slice", "subarray", "uint32", "int32", "sint32", "bool", "fixed32", "sfixed32", "float", "double", "skipType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_", "merge", "int64", "uint64", "sint64", "zzDecode", "fixed64", "sfixed64", "utf8Slice", "min", "deferred", "files", "SYNC", "<PERSON><PERSON><PERSON>", "self", "sync", "finish", "cb", "getBundledFileName", "idx", "lastIndexOf", "altname", "process", "parsed", "queued", "weak", "setTimeout", "readFileSync", "isNode", "exposeRe", "tryHandleExtension", "extendedType", "sisterField", "parse_", "common_", "rpcImpl", "requestDelimited", "responseDelimited", "rpcCall", "requestCtor", "responseCtor", "request", "endedByRPC", "_methodsArray", "inherited", "methodsArray", "rpcService", "methodName", "isReserved", "m", "q", "s", "delimRe", "stringDoubleRe", "stringSingleRe", "setCommentRe", "setCommentAltRe", "setCommentSplitRe", "whitespaceRe", "unescapeRe", "unescapeMap", "0", "r", "unescape", "str", "commentType", "commentLine", "commentLineEmpty", "commentIsLeading", "stack", "<PERSON><PERSON><PERSON><PERSON>", "subject", "char<PERSON>t", "setComment", "isLeading", "commentOffset", "lines", "trim", "isDoubleSlashCommentLine", "startOffset", "endOffset", "findEndOfLine", "lineText", "cursor", "re", "lastIndex", "match", "exec", "repeat", "curr", "isDoc", "isLeadingComment", "expected", "actual", "ret", "_fieldsById", "_oneofsArray", "_ctor", "fieldsById", "oneofsArray", "generateConstructor", "ctorProperties", "setup", "wrapper", "originalThis", "fork", "l<PERSON>im", "typeName", "bake", "o", "key", "safePropBackslashRe", "safePropQuoteRe", "toUpperCase", "camelCaseRe", "a", "decorateRoot", "enumerable", "decorateEnumIndex", "dst", "setProp", "zero", "zzEncode", "zeroHash", "from", "fromString", "toLong", "fromHash", "hash", "toHash", "mask", "part0", "part1", "part2", "src", "newError", "CustomError", "captureStackTrace", "pool", "global", "versions", "node", "window", "isFinite", "isset", "isSet", "utf8Write", "_B<PERSON>er_from", "_Buffer_allocUnsafe", "sizeOrArray", "dcodeIO", "key2Re", "key32Re", "key64Re", "longToHash", "longFromHash", "fromBits", "ProtocolError", "fieldMap", "longs", "enums", "encoding", "allocUnsafe", "seenFirstField", "oneofProp", "invalid", "genVerifyKey", "genVerifyValue", "messageName", "Op", "noop", "State", "tail", "states", "writeByte", "VarintOp", "writeVarint64", "writeFixed32", "_push", "writeBytes", "reset", "BufferWriter_", "writeStringBuffer", "writeBytesBuffer", "copy", "byteLength", "$require", "$module", "amd", "isLong"], "mappings": ";;;;;;CAAA,SAAAA,gBAAA,IAAAC,EAAAC,EAAAC,EAcAC,EAdAH,EAiCA,CAAAI,EAAA,CAAA,SAAAC,EAAAC,EAAAC,GChCAD,EAAAC,QAmBA,SAAAC,EAAAC,GACA,IAAAC,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAC,EAAA,EACAC,GAAA,EACA,KAAAD,EAAAH,UAAAC,QACAH,EAAAI,KAAAF,UAAAG,KACA,OAAA,IAAAE,QAAA,SAAAC,EAAAC,GACAT,EAAAI,GAAA,SAAAM,GACA,GAAAJ,EAEA,GADAA,GAAA,EACAI,EACAD,EAAAC,OACA,CAGA,IAFA,IAAAV,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAA,EAAAJ,EAAAG,QACAH,EAAAI,KAAAF,UAAAE,GACAI,EAAAG,MAAA,KAAAX,KAIA,IACAF,EAAAa,MAAAZ,GAAA,KAAAC,GACA,MAAAU,GACAJ,IACAA,GAAA,EACAG,EAAAC,S,uBCjCAE,EAAAT,OAAA,SAAAU,GACA,IAAAC,EAAAD,EAAAV,OACA,IAAAW,EACA,OAAA,EAEA,IADA,IAAAC,EAAA,EACA,IAAAD,EAAA,GAAA,MAAAD,EAAAA,EAAAC,IAAAD,OACAE,EACA,OAAAC,KAAAC,KAAA,EAAAJ,EAAAV,QAAA,EAAAY,GAUA,IANA,IAAAG,EAAAjB,MAAA,IAGAkB,EAAAlB,MAAA,KAGAmB,EAAA,EAAAA,EAAA,IACAD,EAAAD,EAAAE,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,EAAAA,EAAA,GAAA,IAAAA,IASAR,EAAAS,OAAA,SAAAC,EAAAC,EAAAC,GAMA,IALA,IAIAC,EAJAC,EAAA,KACAC,EAAA,GACAP,EAAA,EACAQ,EAAA,EAEAL,EAAAC,GAAA,CACA,IAAAK,EAAAP,EAAAC,KACA,OAAAK,GACA,KAAA,EACAD,EAAAP,KAAAF,EAAAW,GAAA,GACAJ,GAAA,EAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAJ,GAAA,GAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAF,EAAAP,KAAAF,EAAA,GAAAW,GACAD,EAAA,EAGA,KAAAR,KACAM,EAAAA,GAAA,IAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,IACAP,EAAA,GASA,OANAQ,IACAD,EAAAP,KAAAF,EAAAO,GACAE,EAAAP,KAAA,GACA,IAAAQ,IACAD,EAAAP,KAAA,KAEAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,KAGA,IAAAe,EAAA,mBAUAvB,EAAAwB,OAAA,SAAAvB,EAAAS,EAAAlB,GAIA,IAHA,IAEAqB,EAFAF,EAAAnB,EACAwB,EAAA,EAEAR,EAAA,EAAAA,EAAAP,EAAAV,QAAA,CACA,IAAAkC,EAAAxB,EAAAyB,WAAAlB,KACA,GAAA,IAAAiB,GAAA,EAAAT,EACA,MACA,IAAAS,EAAAlB,EAAAkB,MAAAhD,EACA,MAAAkD,MAAAJ,GACA,OAAAP,GACA,KAAA,EACAH,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,KAAAqB,GAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,MAAA,GAAAqB,IAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,MAAA,EAAAqB,IAAA,EAAAY,EACAT,EAAA,GAIA,GAAA,IAAAA,EACA,MAAAW,MAAAJ,GACA,OAAA/B,EAAAmB,GAQAX,EAAA4B,KAAA,SAAA3B,GACA,MAAA,mEAAA2B,KAAA3B,K,uBC/HA,SAAA4B,EAAAC,EAAAC,GAGA,iBAAAD,IACAC,EAAAD,EACAA,EAAArD,GAGA,IAAAuD,EAAA,GAYA,SAAAC,EAAAC,GAIA,GAAA,iBAAAA,EAAA,CACA,IAAAC,EAAAC,IAIA,GAHAP,EAAAQ,SACAC,QAAAC,IAAA,YAAAJ,GACAA,EAAA,UAAAA,EACAD,EAAA,CAKA,IAJA,IAAAM,EAAAC,OAAAC,KAAAR,GACAS,EAAAtD,MAAAmD,EAAAjD,OAAA,GACAqD,EAAAvD,MAAAmD,EAAAjD,QACAsD,EAAA,EACAA,EAAAL,EAAAjD,QACAoD,EAAAE,GAAAL,EAAAK,GACAD,EAAAC,GAAAX,EAAAM,EAAAK,MAGA,OADAF,EAAAE,GAAAV,EACAW,SAAA/C,MAAA,KAAA4C,GAAA5C,MAAA,KAAA6C,GAEA,OAAAE,SAAAX,EAAAW,GAMA,IAFA,IAAAC,EAAA1D,MAAAC,UAAAC,OAAA,GACAyD,EAAA,EACAA,EAAAD,EAAAxD,QACAwD,EAAAC,GAAA1D,YAAA0D,GAYA,GAXAA,EAAA,EACAd,EAAAA,EAAAe,QAAA,eAAA,SAAAC,EAAAC,GACA,IAAAC,EAAAL,EAAAC,KACA,OAAAG,GACA,IAAA,IAAA,IAAA,IAAA,MAAAhC,MAAAkC,GAAAD,GACA,IAAA,IAAA,MAAAjC,GAAAf,KAAAkD,MAAAF,GACA,IAAA,IAAA,OAAAG,KAAAC,UAAAJ,GACA,IAAA,IAAA,MAAAjC,GAAAiC,EAEA,MAAA,MAEAJ,IAAAD,EAAAxD,OACA,MAAAoC,MAAA,4BAEA,OADAK,EAAAd,KAAAgB,GACAD,EAGA,SAAAG,EAAAqB,GACA,MAAA,aAAAA,GAAA1B,GAAA,IAAA,KAAAD,GAAAA,EAAAR,KAAA,MAAA,IAAA,SAAAU,EAAAV,KAAA,QAAA,MAIA,OADAW,EAAAG,SAAAA,EACAH,GAhFAjD,EAAAC,QAAA4C,GAiGAQ,SAAA,G,uBCzFA,SAAAqB,IAOAC,KAAAC,EAAA,IAfA5E,EAAAC,QAAAyE,GAyBAG,UAAAC,GAAA,SAAAC,EAAA7E,EAAAC,GAKA,OAJAwE,KAAAC,EAAAG,KAAAJ,KAAAC,EAAAG,GAAA,KAAA7C,KAAA,CACAhC,GAAAA,EACAC,IAAAA,GAAAwE,OAEAA,MASAD,EAAAG,UAAAG,IAAA,SAAAD,EAAA7E,GACA,GAAA6E,IAAAtF,EACAkF,KAAAC,EAAA,QAEA,GAAA1E,IAAAT,EACAkF,KAAAC,EAAAG,GAAA,QAGA,IADA,IAAAE,EAAAN,KAAAC,EAAAG,GACAvD,EAAA,EAAAA,EAAAyD,EAAA1E,QACA0E,EAAAzD,GAAAtB,KAAAA,EACA+E,EAAAC,OAAA1D,EAAA,KAEAA,EAGA,OAAAmD,MASAD,EAAAG,UAAAM,KAAA,SAAAJ,GACA,IAAAE,EAAAN,KAAAC,EAAAG,GACA,GAAAE,EAAA,CAGA,IAFA,IAAAG,EAAA,GACA5D,EAAA,EACAA,EAAAlB,UAAAC,QACA6E,EAAAlD,KAAA5B,UAAAkB,MACA,IAAAA,EAAA,EAAAA,EAAAyD,EAAA1E,QACA0E,EAAAzD,GAAAtB,GAAAa,MAAAkE,EAAAzD,KAAArB,IAAAiF,GAEA,OAAAT,O,uBCzEA3E,EAAAC,QAAAoF,EAEA,IAAAC,EAAAvF,EAAA,GAGAwF,EAFAxF,EAAA,EAEAyF,CAAA,MA2BA,SAAAH,EAAAI,EAAAC,EAAAC,GAOA,OAJAD,EAFA,mBAAAA,GACAC,EAAAD,EACA,IACAA,GACA,GAEAC,GAIAD,EAAAE,KAAAL,GAAAA,EAAAM,SACAN,EAAAM,SAAAJ,EAAA,SAAA3E,EAAAgF,GACA,OAAAhF,GAAA,oBAAAiF,eACAV,EAAAO,IAAAH,EAAAC,EAAAC,GACA7E,EACA6E,EAAA7E,GACA6E,EAAA,KAAAD,EAAAM,OAAAF,EAAAA,EAAA1C,SAAA,WAIAiC,EAAAO,IAAAH,EAAAC,EAAAC,GAbAL,EAAAD,EAAAV,KAAAc,EAAAC,GAqCAL,EAAAO,IAAA,SAAAH,EAAAC,EAAAC,GACA,IAAAC,EAAA,IAAAG,eACAH,EAAAK,mBAAA,WAEA,GAAA,IAAAL,EAAAM,WACA,OAAAzG,EAKA,GAAA,IAAAmG,EAAAO,QAAA,MAAAP,EAAAO,OACA,OAAAR,EAAAhD,MAAA,UAAAiD,EAAAO,SAIA,GAAAT,EAAAM,OAAA,CAEA,KADAtE,EAAAkE,EAAAQ,UAGA,IAAA,IADA1E,EAAA,GACAF,EAAA,EAAAA,EAAAoE,EAAAS,aAAA9F,SAAAiB,EACAE,EAAAQ,KAAA,IAAA0D,EAAAS,aAAA3D,WAAAlB,IAEA,OAAAmE,EAAA,KAAA,oBAAAW,WAAA,IAAAA,WAAA5E,GAAAA,GAEA,OAAAiE,EAAA,KAAAC,EAAAS,eAGAX,EAAAM,SAEA,qBAAAJ,GACAA,EAAAW,iBAAA,sCACAX,EAAAY,aAAA,eAGAZ,EAAAa,KAAA,MAAAhB,GACAG,EAAAc,S,8BC1BA,SAAAC,EAAA1G,GAsDA,SAAA2G,EAAAC,EAAAC,EAAAC,EAAAC,GACA,IAAAC,EAAAH,EAAA,EAAA,EAAA,EAIAD,EADA,KADAC,EADAG,GACAH,EACAA,GACA,EAAA,EAAAA,EAAA,EAAA,WACAI,MAAAJ,GACA,WACA,qBAAAA,GACAG,GAAA,GAAA,cAAA,EACAH,EAAA,uBACAG,GAAA,GAAA7F,KAAA+F,MAAAL,EAAA,yBAAA,GAIAG,GAAA,GAAA,KAFAG,EAAAhG,KAAAkD,MAAAlD,KAAAmC,IAAAuD,GAAA1F,KAAAiG,OAEA,GADA,QAAAjG,KAAA+F,MAAAL,EAAA1F,KAAAkG,IAAA,GAAAF,GAAA,YACA,EAVAL,EAAAC,GAiBA,SAAAO,EAAAC,EAAAT,EAAAC,GACAS,EAAAD,EAAAT,EAAAC,GACAC,EAAA,GAAAQ,GAAA,IAAA,EACAL,EAAAK,IAAA,GAAA,IACAC,GAAA,QACA,OAAA,KAAAN,EACAM,EACAC,IACAC,EAAAA,EAAAX,EACA,GAAAG,EACA,qBAAAH,EAAAS,EACAT,EAAA7F,KAAAkG,IAAA,EAAAF,EAAA,MAAA,QAAAM,GA9EA,SAAAG,EAAAf,EAAAC,EAAAC,GACAc,EAAA,GAAAhB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GAGA,SAAAC,EAAAlB,EAAAC,EAAAC,GACAc,EAAA,GAAAhB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GAQA,SAAAE,EAAAlB,EAAAC,GAKA,OAJAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAc,EAAA,GAGA,SAAAI,EAAAnB,EAAAC,GAKA,OAJAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAc,EAAA,GAxCA,IAEAA,EACAC,EA4FAI,EACAJ,EACAK,EA+DA,SAAAC,EAAAxB,EAAAyB,EAAAC,EAAAzB,EAAAC,EAAAC,GACA,IAaAU,EAbAT,EAAAH,EAAA,EAAA,EAAA,EAGA,KADAA,EADAG,GACAH,EACAA,IACAD,EAAA,EAAAE,EAAAC,EAAAsB,GACAzB,EAAA,EAAA,EAAAC,EAAA,EAAA,WAAAC,EAAAC,EAAAuB,IACArB,MAAAJ,IACAD,EAAA,EAAAE,EAAAC,EAAAsB,GACAzB,EAAA,WAAAE,EAAAC,EAAAuB,IACA,sBAAAzB,GACAD,EAAA,EAAAE,EAAAC,EAAAsB,GACAzB,GAAAI,GAAA,GAAA,cAAA,EAAAF,EAAAC,EAAAuB,IAGAzB,EAAA,wBAEAD,GADAa,EAAAZ,EAAA,UACA,EAAAC,EAAAC,EAAAsB,GACAzB,GAAAI,GAAA,GAAAS,EAAA,cAAA,EAAAX,EAAAC,EAAAuB,KAMA1B,EAAA,kBADAa,EAAAZ,EAAA1F,KAAAkG,IAAA,IADAF,EADA,QADAA,EAAAhG,KAAAkD,MAAAlD,KAAAmC,IAAAuD,GAAA1F,KAAAiG,MAEA,KACAD,OACA,EAAAL,EAAAC,EAAAsB,GACAzB,GAAAI,GAAA,GAAAG,EAAA,MAAA,GAAA,QAAAM,EAAA,WAAA,EAAAX,EAAAC,EAAAuB,IAQA,SAAAC,EAAAhB,EAAAc,EAAAC,EAAAxB,EAAAC,GACAyB,EAAAjB,EAAAT,EAAAC,EAAAsB,GACAI,EAAAlB,EAAAT,EAAAC,EAAAuB,GACAtB,EAAA,GAAAyB,GAAA,IAAA,EACAtB,EAAAsB,IAAA,GAAA,KACAhB,EAAA,YAAA,QAAAgB,GAAAD,EACA,OAAA,MAAArB,EACAM,EACAC,IACAC,EAAAA,EAAAX,EACA,GAAAG,EACA,OAAAH,EAAAS,EACAT,EAAA7F,KAAAkG,IAAA,EAAAF,EAAA,OAAAM,EAAA,kBA1GA,SAAAiB,EAAA7B,EAAAC,EAAAC,GACAmB,EAAA,GAAArB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GAGA,SAAAa,EAAA9B,EAAAC,EAAAC,GACAmB,EAAA,GAAArB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GAQA,SAAAc,EAAA9B,EAAAC,GASA,OARAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAmB,EAAA,GAGA,SAAAW,EAAA/B,EAAAC,GASA,OARAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAmB,EAAA,GAgEA,MArNA,oBAAAY,cAEAjB,EAAA,IAAAiB,aAAA,EAAA,IACAhB,EAAA,IAAAzB,WAAAwB,EAAApG,QACA0G,EAAA,MAAAL,EAAA,GAmBA9H,EAAA+I,aAAAZ,EAAAP,EAAAG,EAEA/H,EAAAgJ,aAAAb,EAAAJ,EAAAH,EAmBA5H,EAAAiJ,YAAAd,EAAAH,EAAAC,EAEAjI,EAAAkJ,YAAAf,EAAAF,EAAAD,IAwBAhI,EAAA+I,aAAApC,EAAAwC,KAAA,KAAAC,GACApJ,EAAAgJ,aAAArC,EAAAwC,KAAA,KAAAE,GAgBArJ,EAAAiJ,YAAA3B,EAAA6B,KAAA,KAAAG,GACAtJ,EAAAkJ,YAAA5B,EAAA6B,KAAA,KAAAI,IAKA,oBAAAC,cAEAtB,EAAA,IAAAsB,aAAA,EAAA,IACA1B,EAAA,IAAAzB,WAAA6B,EAAAzG,QACA0G,EAAA,MAAAL,EAAA,GA2BA9H,EAAAyJ,cAAAtB,EAAAO,EAAAC,EAEA3I,EAAA0J,cAAAvB,EAAAQ,EAAAD,EA2BA1I,EAAA2J,aAAAxB,EAAAS,EAAAC,EAEA7I,EAAA4J,aAAAzB,EAAAU,EAAAD,IAmCA5I,EAAAyJ,cAAArB,EAAAe,KAAA,KAAAC,EAAA,EAAA,GACApJ,EAAA0J,cAAAtB,EAAAe,KAAA,KAAAE,EAAA,EAAA,GAiBArJ,EAAA2J,aAAApB,EAAAY,KAAA,KAAAG,EAAA,EAAA,GACAtJ,EAAA4J,aAAArB,EAAAY,KAAA,KAAAI,EAAA,EAAA,IAIAvJ,EAKA,SAAAoJ,EAAAvC,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAGA,SAAAwC,EAAAxC,EAAAC,EAAAC,GACAD,EAAAC,GAAAF,IAAA,GACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAA,IAAAF,EAGA,SAAAyC,EAAAxC,EAAAC,GACA,OAAAD,EAAAC,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,MAAA,EAGA,SAAAwC,EAAAzC,EAAAC,GACA,OAAAD,EAAAC,IAAA,GACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,MAAA,EA3UAhH,EAAAC,QAAA0G,EAAAA,I,uBCOA,SAAAnB,EAAAsE,GACA,IACA,IAAAC,EAAAC,KAAA,UAAAA,CAAAF,GACA,GAAAC,IAAAA,EAAAxJ,QAAAkD,OAAAC,KAAAqG,GAAAxJ,QACA,OAAAwJ,EACA,MAAAE,IACA,OAAA,KAdAjK,EAAAC,QAAAuF,G,uBCMA,IAAA0E,EAAAjK,EAEAkK,EAMAD,EAAAC,WAAA,SAAAD,GACA,MAAA,eAAAtH,KAAAsH,IAGAE,EAMAF,EAAAE,UAAA,SAAAF,GAGA,IAAApI,GAFAoI,EAAAA,EAAAjG,QAAA,MAAA,KACAA,QAAA,UAAA,MACAoG,MAAA,KACAC,EAAAH,EAAAD,GACAK,EAAA,GACAD,IACAC,EAAAzI,EAAA0I,QAAA,KACA,IAAA,IAAAhJ,EAAA,EAAAA,EAAAM,EAAAvB,QACA,OAAAuB,EAAAN,GACA,EAAAA,GAAA,OAAAM,EAAAN,EAAA,GACAM,EAAAoD,SAAA1D,EAAA,GACA8I,EACAxI,EAAAoD,OAAA1D,EAAA,KAEAA,EACA,MAAAM,EAAAN,GACAM,EAAAoD,OAAA1D,EAAA,KAEAA,EAEA,OAAA+I,EAAAzI,EAAAQ,KAAA,MAUA4H,EAAAtJ,QAAA,SAAA6J,EAAAC,EAAAC,GAGA,OAFAA,IACAD,EAAAN,EAAAM,KACAP,EAAAO,KAIAD,GADAA,GADAE,EACAP,EAAAK,GACAA,GAAAxG,QAAA,iBAAA,KAAA1D,OAAA6J,EAAAK,EAAA,IAAAC,GAHAA,I,uBC3DA1K,EAAAC,QA6BA,SAAA2K,EAAAvI,EAAAwI,GACA,IAAAC,EAAAD,GAAA,KACAE,EAAAD,IAAA,EACAE,EAAA,KACAxK,EAAAsK,EACA,OAAA,SAAAD,GACA,GAAAA,EAAA,GAAAE,EAAAF,EACA,OAAAD,EAAAC,GACAC,EAAAtK,EAAAqK,IACAG,EAAAJ,EAAAE,GACAtK,EAAA,GAEAuG,EAAA1E,EAAA4I,KAAAD,EAAAxK,EAAAA,GAAAqK,GAGA,OAFA,EAAArK,IACAA,EAAA,GAAA,EAAAA,IACAuG,K,wBC/BAmE,EAAA3K,OAAA,SAAAU,GAGA,IAFA,IACAwB,EADA0I,EAAA,EAEA3J,EAAA,EAAAA,EAAAP,EAAAV,SAAAiB,GACAiB,EAAAxB,EAAAyB,WAAAlB,IACA,IACA2J,GAAA,EACA1I,EAAA,KACA0I,GAAA,EACA,QAAA,MAAA1I,IAAA,QAAA,MAAAxB,EAAAyB,WAAAlB,EAAA,OACAA,EACA2J,GAAA,GAEAA,GAAA,EAEA,OAAAA,GAUAD,EAAAE,KAAA,SAAA1J,EAAAC,EAAAC,GAEA,GADAA,EAAAD,EACA,EACA,MAAA,GAKA,IAJA,IAGAE,EAHAC,EAAA,KACAC,EAAA,GACAP,EAAA,EAEAG,EAAAC,IACAC,EAAAH,EAAAC,MACA,IACAI,EAAAP,KAAAK,EACA,IAAAA,GAAAA,EAAA,IACAE,EAAAP,MAAA,GAAAK,IAAA,EAAA,GAAAH,EAAAC,KACA,IAAAE,GAAAA,EAAA,KACAA,IAAA,EAAAA,IAAA,IAAA,GAAAH,EAAAC,OAAA,IAAA,GAAAD,EAAAC,OAAA,EAAA,GAAAD,EAAAC,MAAA,MACAI,EAAAP,KAAA,OAAAK,GAAA,IACAE,EAAAP,KAAA,OAAA,KAAAK,IAEAE,EAAAP,MAAA,GAAAK,IAAA,IAAA,GAAAH,EAAAC,OAAA,EAAA,GAAAD,EAAAC,KACA,KAAAH,KACAM,EAAAA,GAAA,IAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,IACAP,EAAA,GAGA,OAAAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,KAUA0J,EAAAG,MAAA,SAAApK,EAAAS,EAAAlB,GAIA,IAHA,IACA8K,EACAC,EAFA5J,EAAAnB,EAGAgB,EAAA,EAAAA,EAAAP,EAAAV,SAAAiB,GACA8J,EAAArK,EAAAyB,WAAAlB,IACA,IACAE,EAAAlB,KAAA8K,GACAA,EAAA,KACA5J,EAAAlB,KAAA8K,GAAA,EAAA,KAEA,QAAA,MAAAA,IAAA,QAAA,OAAAC,EAAAtK,EAAAyB,WAAAlB,EAAA,QAEAA,EACAE,EAAAlB,MAFA8K,EAAA,QAAA,KAAAA,IAAA,KAAA,KAAAC,KAEA,GAAA,IACA7J,EAAAlB,KAAA8K,GAAA,GAAA,GAAA,KAIA5J,EAAAlB,KAAA8K,GAAA,GAAA,IAHA5J,EAAAlB,KAAA8K,GAAA,EAAA,GAAA,KANA5J,EAAAlB,KAAA,GAAA8K,EAAA,KAcA,OAAA9K,EAAAmB,I,wBCtGA3B,EAAAC,QAAAuL,EAEA,IAAAC,EAAA,QAsBA,SAAAD,EAAAE,EAAAC,GACAF,EAAA7I,KAAA8I,KACAA,EAAA,mBAAAA,EAAA,SACAC,EAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAD,OAAA,CAAA/L,SAAA,CAAA+L,OAAAD,QAEAH,EAAAE,GAAAC,EAYAH,EAAA,MAAA,CAUAM,IAAA,CACAC,OAAA,CACAC,SAAA,CACAC,KAAA,SACAC,GAAA,GAEA9H,MAAA,CACA6H,KAAA,QACAC,GAAA,OAQAV,EAAA,WAAA,CAUAW,SAAAC,EAAA,CACAL,OAAA,CACAM,QAAA,CACAJ,KAAA,QACAC,GAAA,GAEAI,MAAA,CACAL,KAAA,QACAC,GAAA,OAMAV,EAAA,YAAA,CAUAe,UAAAH,IAGAZ,EAAA,QAAA,CAOAgB,MAAA,CACAT,OAAA,MAIAP,EAAA,SAAA,CASAiB,OAAA,CACAV,OAAA,CACAA,OAAA,CACAW,QAAA,SACAT,KAAA,QACAC,GAAA,KAkBAS,MAAA,CACAC,OAAA,CACAC,KAAA,CACAC,MAAA,CACA,YACA,cACA,cACA,YACA,cACA,eAIAf,OAAA,CACAgB,UAAA,CACAd,KAAA,YACAC,GAAA,GAEAc,YAAA,CACAf,KAAA,SACAC,GAAA,GAEAe,YAAA,CACAhB,KAAA,SACAC,GAAA,GAEAgB,UAAA,CACAjB,KAAA,OACAC,GAAA,GAEAiB,YAAA,CACAlB,KAAA,SACAC,GAAA,GAEAkB,UAAA,CACAnB,KAAA,YACAC,GAAA,KAKAmB,UAAA,CACAC,OAAA,CACAC,WAAA,IAWAC,UAAA,CACAzB,OAAA,CACAuB,OAAA,CACAG,KAAA,WACAxB,KAAA,QACAC,GAAA,OAMAV,EAAA,WAAA,CASAkC,YAAA,CACA3B,OAAA,CACA3H,MAAA,CACA6H,KAAA,SACAC,GAAA,KAYAyB,WAAA,CACA5B,OAAA,CACA3H,MAAA,CACA6H,KAAA,QACAC,GAAA,KAYA0B,WAAA,CACA7B,OAAA,CACA3H,MAAA,CACA6H,KAAA,QACAC,GAAA,KAYA2B,YAAA,CACA9B,OAAA,CACA3H,MAAA,CACA6H,KAAA,SACAC,GAAA,KAYA4B,WAAA,CACA/B,OAAA,CACA3H,MAAA,CACA6H,KAAA,QACAC,GAAA,KAYA6B,YAAA,CACAhC,OAAA,CACA3H,MAAA,CACA6H,KAAA,SACAC,GAAA,KAYA8B,UAAA,CACAjC,OAAA,CACA3H,MAAA,CACA6H,KAAA,OACAC,GAAA,KAYA+B,YAAA,CACAlC,OAAA,CACA3H,MAAA,CACA6H,KAAA,SACAC,GAAA,KAYAgC,WAAA,CACAnC,OAAA,CACA3H,MAAA,CACA6H,KAAA,QACAC,GAAA,OAMAV,EAAA,aAAA,CASA2C,UAAA,CACApC,OAAA,CACAqC,MAAA,CACAX,KAAA,WACAxB,KAAA,SACAC,GAAA,OAqBAV,EAAA6C,IAAA,SAAAC,GACA,OAAA9C,EAAA8C,IAAA,O,wBCxYA,IAAAC,EAAAtO,EAEAuO,EAAAzO,EAAA,IACA0O,EAAA1O,EAAA,IAWA,SAAA2O,EAAAC,EAAAC,EAAAC,EAAAC,GAEA,GAAAF,EAAAG,aACA,GAAAH,EAAAG,wBAAAP,EAAA,CAAAG,EACA,eAAAG,GACA,IAAA,IAAAxB,EAAAsB,EAAAG,aAAAzB,OAAA5J,EAAAD,OAAAC,KAAA4J,GAAA9L,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACAoN,EAAAI,UAAA1B,EAAA5J,EAAAlC,MAAAoN,EAAAK,aAAAN,EACA,YACAA,EACA,UAAAjL,EAAAlC,GADAmN,CAEA,WAAArB,EAAA5J,EAAAlC,IAFAmN,CAGA,SAAAG,EAAAxB,EAAA5J,EAAAlC,IAHAmN,CAIA,SACAA,EACA,UACAA,EACA,4BAAAG,EADAH,CAEA,sBAAAC,EAAAM,SAAA,oBAFAP,CAGA,gCAAAG,EAAAD,EAAAC,OACA,CACA,IAAAK,GAAA,EACA,OAAAP,EAAA3C,MACA,IAAA,SACA,IAAA,QAAA0C,EACA,kBAAAG,EAAAA,GACA,MACA,IAAA,SACA,IAAA,UAAAH,EACA,cAAAG,EAAAA,GACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,WAAAH,EACA,YAAAG,EAAAA,GACA,MACA,IAAA,SACAK,GAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAR,EACA,gBADAA,CAEA,6CAAAG,EAAAA,EAAAK,EAFAR,CAGA,iCAAAG,EAHAH,CAIA,uBAAAG,EAAAA,EAJAH,CAKA,iCAAAG,EALAH,CAMA,UAAAG,EAAAA,EANAH,CAOA,iCAAAG,EAPAH,CAQA,+DAAAG,EAAAA,EAAAA,EAAAK,EAAA,OAAA,IACA,MACA,IAAA,QAAAR,EACA,4BAAAG,EADAH,CAEA,wEAAAG,EAAAA,EAAAA,EAFAH,CAGA,sBAAAG,EAHAH,CAIA,UAAAG,EAAAA,GACA,MACA,IAAA,SAAAH,EACA,kBAAAG,EAAAA,GACA,MACA,IAAA,OAAAH,EACA,mBAAAG,EAAAA,IAOA,OAAAH,EAmEA,SAAAS,EAAAT,EAAAC,EAAAC,EAAAC,GAEA,GAAAF,EAAAG,aACAH,EAAAG,wBAAAP,EAAAG,EACA,iDAAAG,EAAAD,EAAAC,EAAAA,GACAH,EACA,gCAAAG,EAAAD,EAAAC,OACA,CACA,IAAAK,GAAA,EACA,OAAAP,EAAA3C,MACA,IAAA,SACA,IAAA,QAAA0C,EACA,6CAAAG,EAAAA,EAAAA,EAAAA,GACA,MACA,IAAA,SACAK,GAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAR,EACA,4BAAAG,EADAH,CAEA,uCAAAG,EAAAA,EAAAA,EAFAH,CAGA,OAHAA,CAIA,4IAAAG,EAAAA,EAAAA,EAAAA,EAAAK,EAAA,OAAA,GAAAL,GACA,MACA,IAAA,QAAAH,EACA,gHAAAG,EAAAA,EAAAA,EAAAA,EAAAA,GACA,MACA,QAAAH,EACA,UAAAG,EAAAA,IAIA,OAAAH,EA5FAJ,EAAAc,WAAA,SAAAC,GAEA,IAAAvD,EAAAuD,EAAAC,YACAZ,EAAAF,EAAA5L,QAAA,CAAA,KAAAyM,EAAA5D,KAAA,cAAA+C,CACA,6BADAA,CAEA,YACA,IAAA1C,EAAAxL,OAAA,OAAAoO,EACA,wBACAA,EACA,uBACA,IAAA,IAAAnN,EAAA,EAAAA,EAAAuK,EAAAxL,SAAAiB,EAAA,CACA,IAAAoN,EAAA7C,EAAAvK,GAAAZ,UACAkO,EAAAL,EAAAe,SAAAZ,EAAAlD,MAGAkD,EAAAa,KAAAd,EACA,WAAAG,EADAH,CAEA,4BAAAG,EAFAH,CAGA,sBAAAC,EAAAM,SAAA,oBAHAP,CAIA,SAAAG,EAJAH,CAKA,oDAAAG,GACAJ,EAAAC,EAAAC,EAAApN,EAAAsN,EAAA,UAAAJ,CACA,IADAA,CAEA,MAGAE,EAAAI,UAAAL,EACA,WAAAG,EADAH,CAEA,0BAAAG,EAFAH,CAGA,sBAAAC,EAAAM,SAAA,mBAHAP,CAIA,SAAAG,EAJAH,CAKA,iCAAAG,GACAJ,EAAAC,EAAAC,EAAApN,EAAAsN,EAAA,MAAAJ,CACA,IADAA,CAEA,OAIAE,EAAAG,wBAAAP,GAAAG,EACA,iBAAAG,GACAJ,EAAAC,EAAAC,EAAApN,EAAAsN,GACAF,EAAAG,wBAAAP,GAAAG,EACA,MAEA,OAAAA,EACA,aAwDAJ,EAAAmB,SAAA,SAAAJ,GAEA,IAAAvD,EAAAuD,EAAAC,YAAAlN,QAAAsN,KAAAlB,EAAAmB,mBACA,IAAA7D,EAAAxL,OACA,OAAAkO,EAAA5L,SAAA4L,CAAA,aAUA,IATA,IAAAE,EAAAF,EAAA5L,QAAA,CAAA,IAAA,KAAAyM,EAAA5D,KAAA,YAAA+C,CACA,SADAA,CAEA,OAFAA,CAGA,YAEAoB,EAAA,GACAC,EAAA,GACAC,EAAA,GACAvO,EAAA,EACAA,EAAAuK,EAAAxL,SAAAiB,EACAuK,EAAAvK,GAAAwO,SACAjE,EAAAvK,GAAAZ,UAAAoO,SAAAa,EACA9D,EAAAvK,GAAAiO,IAAAK,EACAC,GAAA7N,KAAA6J,EAAAvK,IAEA,GAAAqO,EAAAtP,OAAA,CAEA,IAFAoO,EACA,6BACAnN,EAAA,EAAAA,EAAAqO,EAAAtP,SAAAiB,EAAAmN,EACA,SAAAF,EAAAe,SAAAK,EAAArO,GAAAkK,OACAiD,EACA,KAGA,GAAAmB,EAAAvP,OAAA,CAEA,IAFAoO,EACA,8BACAnN,EAAA,EAAAA,EAAAsO,EAAAvP,SAAAiB,EAAAmN,EACA,SAAAF,EAAAe,SAAAM,EAAAtO,GAAAkK,OACAiD,EACA,KAGA,GAAAoB,EAAAxP,OAAA,CAEA,IAFAoO,EACA,mBACAnN,EAAA,EAAAA,EAAAuO,EAAAxP,SAAAiB,EAAA,CACA,IAWAyO,EAXArB,EAAAmB,EAAAvO,GACAsN,EAAAL,EAAAe,SAAAZ,EAAAlD,MACAkD,EAAAG,wBAAAP,EAAAG,EACA,6BAAAG,EAAAF,EAAAG,aAAAmB,WAAAtB,EAAAK,aAAAL,EAAAK,aACAL,EAAAuB,KAAAxB,EACA,iBADAA,CAEA,gCAAAC,EAAAK,YAAAmB,IAAAxB,EAAAK,YAAAoB,KAAAzB,EAAAK,YAAAqB,SAFA3B,CAGA,oEAAAG,EAHAH,CAIA,QAJAA,CAKA,6BAAAG,EAAAF,EAAAK,YAAA7L,WAAAwL,EAAAK,YAAAsB,YACA3B,EAAA4B,OACAP,EAAA,IAAA5P,MAAAwE,UAAAxC,MAAA4I,KAAA2D,EAAAK,aAAA3M,KAAA,KAAA,IACAqM,EACA,6BAAAG,EAAA3M,OAAAC,aAAArB,MAAAoB,OAAAyM,EAAAK,aADAN,CAEA,QAFAA,CAGA,SAAAG,EAAAmB,EAHAtB,CAIA,6CAAAG,EAAAA,EAJAH,CAKA,MACAA,EACA,SAAAG,EAAAF,EAAAK,aACAN,EACA,KAGA,IADA,IAAA8B,GAAA,EACAjP,EAAA,EAAAA,EAAAuK,EAAAxL,SAAAiB,EAAA,CACA,IAAAoN,EAAA7C,EAAAvK,GACAf,EAAA6O,EAAAoB,EAAAC,QAAA/B,GACAE,EAAAL,EAAAe,SAAAZ,EAAAlD,MACAkD,EAAAa,KACAgB,IAAAA,GAAA,EAAA9B,EACA,YACAA,EACA,0CAAAG,EAAAA,EADAH,CAEA,SAAAG,EAFAH,CAGA,kCACAS,EAAAT,EAAAC,EAAAnO,EAAAqO,EAAA,WAAAM,CACA,MACAR,EAAAI,UAAAL,EACA,uBAAAG,EAAAA,EADAH,CAEA,SAAAG,EAFAH,CAGA,iCAAAG,GACAM,EAAAT,EAAAC,EAAAnO,EAAAqO,EAAA,MAAAM,CACA,OACAT,EACA,uCAAAG,EAAAF,EAAAlD,MACA0D,EAAAT,EAAAC,EAAAnO,EAAAqO,GACAF,EAAAoB,QAAArB,EACA,eADAA,CAEA,SAAAF,EAAAe,SAAAZ,EAAAoB,OAAAtE,MAAAkD,EAAAlD,OAEAiD,EACA,KAEA,OAAAA,EACA,c,mCCjSA3O,EAAAC,QAeA,SAAAqP,GAEA,IAAAX,EAAAF,EAAA5L,QAAA,CAAA,IAAA,KAAAyM,EAAA5D,KAAA,UAAA+C,CACA,6BADAA,CAEA,qBAFAA,CAGA,qDAAAa,EAAAC,YAAAqB,OAAA,SAAAhC,GAAA,OAAAA,EAAAa,MAAAlP,OAAA,WAAA,IAHAkO,CAIA,kBAJAA,CAKA,oBACAa,EAAAuB,OAAAlC,EACA,gBADAA,CAEA,SACAA,EACA,kBAGA,IADA,IAAAnN,EAAA,EACAA,EAAA8N,EAAAC,YAAAhP,SAAAiB,EAAA,CACA,IAAAoN,EAAAU,EAAAoB,EAAAlP,GAAAZ,UACAqL,EAAA2C,EAAAG,wBAAAP,EAAA,QAAAI,EAAA3C,KACA6E,EAAA,IAAArC,EAAAe,SAAAZ,EAAAlD,MAAAiD,EACA,WAAAC,EAAA1C,IAGA0C,EAAAa,KAAAd,EACA,4BAAAmC,EADAnC,CAEA,QAAAmC,EAFAnC,CAGA,6BAEAoC,EAAAC,SAAApC,EAAAlC,WAAAjN,EAAAkP,EACA,OAAAoC,EAAAC,SAAApC,EAAAlC,UACAiC,EACA,UAEAoC,EAAAC,SAAA/E,KAAAxM,EAAAkP,EACA,WAAAoC,EAAAC,SAAA/E,IACA0C,EACA,cAEAA,EACA,mBADAA,CAEA,sBAFAA,CAGA,oBAHAA,CAIA,0BAAAC,EAAAlC,QAJAiC,CAKA,WAEAoC,EAAAE,MAAAhF,KAAAxM,EAAAkP,EACA,uCAAAnN,GACAmN,EACA,eAAA1C,GAEA0C,EACA,QADAA,CAEA,WAFAA,CAGA,qBAHAA,CAIA,QAJAA,CAKA,IALAA,CAMA,KAEAoC,EAAAZ,KAAAvB,EAAAlC,WAAAjN,EAAAkP,EACA,qDAAAmC,GACAnC,EACA,cAAAmC,IAGAlC,EAAAI,UAAAL,EAEA,uBAAAmC,EAAAA,EAFAnC,CAGA,QAAAmC,GAGAC,EAAAG,OAAAjF,KAAAxM,GAAAkP,EACA,iBADAA,CAEA,0BAFAA,CAGA,kBAHAA,CAIA,kBAAAmC,EAAA7E,EAJA0C,CAKA,SAGAoC,EAAAE,MAAAhF,KAAAxM,EAAAkP,EAAAC,EAAAG,aAAA8B,MACA,+BACA,0CAAAC,EAAAtP,GACAmN,EACA,kBAAAmC,EAAA7E,IAGA8E,EAAAE,MAAAhF,KAAAxM,EAAAkP,EAAAC,EAAAG,aAAA8B,MACA,yBACA,oCAAAC,EAAAtP,GACAmN,EACA,YAAAmC,EAAA7E,GACA0C,EACA,SAWA,IATAA,EACA,WADAA,CAEA,kBAFAA,CAGA,QAHAA,CAKA,IALAA,CAMA,KAGAnN,EAAA,EAAAA,EAAA8N,EAAAoB,EAAAnQ,SAAAiB,EAAA,CACA,IAAA2P,EAAA7B,EAAAoB,EAAAlP,GACA2P,EAAAC,UAAAzC,EACA,4BAAAwC,EAAAzF,KADAiD,CAEA,4CAjHA,qBAiHAwC,EAjHAzF,KAAA,KAoHA,OAAAiD,EACA,aA1HA,IAAAH,EAAAzO,EAAA,IACAgR,EAAAhR,EAAA,IACA0O,EAAA1O,EAAA,K,yCCJAC,EAAAC,QA0BA,SAAAqP,GAWA,IATA,IAIAwB,EAJAnC,EAAAF,EAAA5L,QAAA,CAAA,IAAA,KAAAyM,EAAA5D,KAAA,UAAA+C,CACA,SADAA,CAEA,qBAKA1C,EAAAuD,EAAAC,YAAAlN,QAAAsN,KAAAlB,EAAAmB,mBAEApO,EAAA,EAAAA,EAAAuK,EAAAxL,SAAAiB,EAAA,CACA,IAAAoN,EAAA7C,EAAAvK,GAAAZ,UACAH,EAAA6O,EAAAoB,EAAAC,QAAA/B,GACA3C,EAAA2C,EAAAG,wBAAAP,EAAA,QAAAI,EAAA3C,KACAoF,EAAAN,EAAAE,MAAAhF,GACA6E,EAAA,IAAArC,EAAAe,SAAAZ,EAAAlD,MAGAkD,EAAAa,KACAd,EACA,kDAAAmC,EAAAlC,EAAAlD,KADAiD,CAEA,mDAAAmC,EAFAnC,CAGA,4CAAAC,EAAA1C,IAAA,EAAA,KAAA,EAAA,EAAA6E,EAAAO,OAAA1C,EAAAlC,SAAAkC,EAAAlC,SACA2E,IAAA5R,EAAAkP,EACA,oEAAAlO,EAAAqQ,GACAnC,EACA,qCAAA,GAAA0C,EAAApF,EAAA6E,GACAnC,EACA,IADAA,CAEA,MAGAC,EAAAI,UAAAL,EACA,2BAAAmC,EAAAA,GAGAlC,EAAAsC,QAAAH,EAAAG,OAAAjF,KAAAxM,EAAAkP,EAEA,uBAAAC,EAAA1C,IAAA,EAAA,KAAA,EAFAyC,CAGA,+BAAAmC,EAHAnC,CAIA,cAAA1C,EAAA6E,EAJAnC,CAKA,eAGAA,EAEA,+BAAAmC,GACAO,IAAA5R,EACA8R,EAAA5C,EAAAC,EAAAnO,EAAAqQ,EAAA,OACAnC,EACA,0BAAAC,EAAA1C,IAAA,EAAAmF,KAAA,EAAApF,EAAA6E,IAEAnC,EACA,OAIAC,EAAA4C,UAAA7C,EACA,iDAAAmC,EAAAlC,EAAAlD,MAEA2F,IAAA5R,EACA8R,EAAA5C,EAAAC,EAAAnO,EAAAqQ,GACAnC,EACA,uBAAAC,EAAA1C,IAAA,EAAAmF,KAAA,EAAApF,EAAA6E,IAKA,OAAAnC,EACA,aA9FA,IAAAH,EAAAzO,EAAA,IACAgR,EAAAhR,EAAA,IACA0O,EAAA1O,EAAA,IAWA,SAAAwR,EAAA5C,EAAAC,EAAAC,EAAAiC,GACA,OAAAlC,EAAAG,aAAA8B,MACAlC,EAAA,+CAAAE,EAAAiC,GAAAlC,EAAA1C,IAAA,EAAA,KAAA,GAAA0C,EAAA1C,IAAA,EAAA,KAAA,GACAyC,EAAA,oDAAAE,EAAAiC,GAAAlC,EAAA1C,IAAA,EAAA,KAAA,K,yCClBAlM,EAAAC,QAAAuO,EAGA,IAAAiD,EAAA1R,EAAA,MACAyO,EAAA3J,UAAApB,OAAAiO,OAAAD,EAAA5M,YAAA8M,YAAAnD,GAAAoD,UAAA,OAEA,IAAAC,EAAA9R,EAAA,IACA0O,EAAA1O,EAAA,IAaA,SAAAyO,EAAA9C,EAAA4B,EAAA5H,EAAAoM,EAAAC,GAGA,GAFAN,EAAAxG,KAAAtG,KAAA+G,EAAAhG,GAEA4H,GAAA,iBAAAA,EACA,MAAA0E,UAAA,4BAoCA,GA9BArN,KAAAuL,WAAA,GAMAvL,KAAA2I,OAAA7J,OAAAiO,OAAA/M,KAAAuL,YAMAvL,KAAAmN,QAAAA,EAMAnN,KAAAoN,SAAAA,GAAA,GAMApN,KAAAsN,SAAAxS,EAMA6N,EACA,IAAA,IAAA5J,EAAAD,OAAAC,KAAA4J,GAAA9L,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACA,iBAAA8L,EAAA5J,EAAAlC,MACAmD,KAAAuL,WAAAvL,KAAA2I,OAAA5J,EAAAlC,IAAA8L,EAAA5J,EAAAlC,KAAAkC,EAAAlC,IAiBAgN,EAAA0D,SAAA,SAAAxG,EAAAC,GACAwG,EAAA,IAAA3D,EAAA9C,EAAAC,EAAA2B,OAAA3B,EAAAjG,QAAAiG,EAAAmG,QAAAnG,EAAAoG,UAEA,OADAI,EAAAF,SAAAtG,EAAAsG,SACAE,GAQA3D,EAAA3J,UAAAuN,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA7D,EAAAiB,SAAA,CACA,UAAA/K,KAAAe,QACA,SAAAf,KAAA2I,OACA,WAAA3I,KAAAsN,UAAAtN,KAAAsN,SAAA1R,OAAAoE,KAAAsN,SAAAxS,EACA,UAAA6S,EAAA3N,KAAAmN,QAAArS,EACA,WAAA6S,EAAA3N,KAAAoN,SAAAtS,KAaA+O,EAAA3J,UAAA0N,IAAA,SAAA7G,EAAAQ,EAAA4F,GAGA,IAAArD,EAAA+D,SAAA9G,GACA,MAAAsG,UAAA,yBAEA,IAAAvD,EAAAgE,UAAAvG,GACA,MAAA8F,UAAA,yBAEA,GAAArN,KAAA2I,OAAA5B,KAAAjM,EACA,MAAAkD,MAAA,mBAAA+I,EAAA,QAAA/G,MAEA,GAAAA,KAAA+N,aAAAxG,GACA,MAAAvJ,MAAA,MAAAuJ,EAAA,mBAAAvH,MAEA,GAAAA,KAAAgO,eAAAjH,GACA,MAAA/I,MAAA,SAAA+I,EAAA,oBAAA/G,MAEA,GAAAA,KAAAuL,WAAAhE,KAAAzM,EAAA,CACA,IAAAkF,KAAAe,UAAAf,KAAAe,QAAAkN,YACA,MAAAjQ,MAAA,gBAAAuJ,EAAA,OAAAvH,MACAA,KAAA2I,OAAA5B,GAAAQ,OAEAvH,KAAAuL,WAAAvL,KAAA2I,OAAA5B,GAAAQ,GAAAR,EAGA,OADA/G,KAAAoN,SAAArG,GAAAoG,GAAA,KACAnN,MAUA6J,EAAA3J,UAAAgO,OAAA,SAAAnH,GAEA,IAAA+C,EAAA+D,SAAA9G,GACA,MAAAsG,UAAA,yBAEA,IAAAlL,EAAAnC,KAAA2I,OAAA5B,GACA,GAAA,MAAA5E,EACA,MAAAnE,MAAA,SAAA+I,EAAA,uBAAA/G,MAMA,cAJAA,KAAAuL,WAAApJ,UACAnC,KAAA2I,OAAA5B,UACA/G,KAAAoN,SAAArG,GAEA/G,MAQA6J,EAAA3J,UAAA6N,aAAA,SAAAxG,GACA,OAAA2F,EAAAa,aAAA/N,KAAAsN,SAAA/F,IAQAsC,EAAA3J,UAAA8N,eAAA,SAAAjH,GACA,OAAAmG,EAAAc,eAAAhO,KAAAsN,SAAAvG,K,yCClLA1L,EAAAC,QAAA6S,EAGA,IAAArB,EAAA1R,EAAA,MACA+S,EAAAjO,UAAApB,OAAAiO,OAAAD,EAAA5M,YAAA8M,YAAAmB,GAAAlB,UAAA,QAEA,IAIAmB,EAJAvE,EAAAzO,EAAA,IACAgR,EAAAhR,EAAA,IACA0O,EAAA1O,EAAA,IAIAiT,EAAA,+BAyCA,SAAAF,EAAApH,EAAAQ,EAAAD,EAAAwB,EAAAwF,EAAAvN,EAAAoM,GAcA,GAZArD,EAAAyE,SAAAzF,IACAqE,EAAAmB,EACAvN,EAAA+H,EACAA,EAAAwF,EAAAxT,GACAgP,EAAAyE,SAAAD,KACAnB,EAAApM,EACAA,EAAAuN,EACAA,EAAAxT,GAGAgS,EAAAxG,KAAAtG,KAAA+G,EAAAhG,IAEA+I,EAAAgE,UAAAvG,IAAAA,EAAA,EACA,MAAA8F,UAAA,qCAEA,IAAAvD,EAAA+D,SAAAvG,GACA,MAAA+F,UAAA,yBAEA,GAAAvE,IAAAhO,IAAAuT,EAAApQ,KAAA6K,EAAAA,EAAArK,WAAA+P,eACA,MAAAnB,UAAA,8BAEA,GAAAiB,IAAAxT,IAAAgP,EAAA+D,SAAAS,GACA,MAAAjB,UAAA,2BASArN,KAAA8I,MAFAA,EADA,oBAAAA,EACA,WAEAA,IAAA,aAAAA,EAAAA,EAAAhO,EAMAkF,KAAAsH,KAAAA,EAMAtH,KAAAuH,GAAAA,EAMAvH,KAAAsO,OAAAA,GAAAxT,EAMAkF,KAAAyM,SAAA,aAAA3D,EAMA9I,KAAA6M,UAAA7M,KAAAyM,SAMAzM,KAAAqK,SAAA,aAAAvB,EAMA9I,KAAA8K,KAAA,EAMA9K,KAAAyO,QAAA,KAMAzO,KAAAqL,OAAA,KAMArL,KAAAsK,YAAA,KAMAtK,KAAA0O,aAAA,KAMA1O,KAAAwL,OAAA1B,EAAA6E,MAAAvC,EAAAZ,KAAAlE,KAAAxM,EAMAkF,KAAA6L,MAAA,UAAAvE,EAMAtH,KAAAoK,aAAA,KAMApK,KAAA4O,eAAA,KAMA5O,KAAA6O,eAAA,KAOA7O,KAAA8O,EAAA,KAMA9O,KAAAmN,QAAAA,EAhKAgB,EAAAZ,SAAA,SAAAxG,EAAAC,GACA,OAAA,IAAAmH,EAAApH,EAAAC,EAAAO,GAAAP,EAAAM,KAAAN,EAAA8B,KAAA9B,EAAAsH,OAAAtH,EAAAjG,QAAAiG,EAAAmG,UAwKArO,OAAAiQ,eAAAZ,EAAAjO,UAAA,SAAA,CACAwJ,IAAA,WAIA,OAFA,OAAA1J,KAAA8O,IACA9O,KAAA8O,GAAA,IAAA9O,KAAAgP,UAAA,WACAhP,KAAA8O,KAOAX,EAAAjO,UAAA+O,UAAA,SAAAlI,EAAAtH,EAAAyP,GAGA,MAFA,WAAAnI,IACA/G,KAAA8O,EAAA,MACAhC,EAAA5M,UAAA+O,UAAA3I,KAAAtG,KAAA+G,EAAAtH,EAAAyP,IAwBAf,EAAAjO,UAAAuN,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA7D,EAAAiB,SAAA,CACA,OAAA,aAAA/K,KAAA8I,MAAA9I,KAAA8I,MAAAhO,EACA,OAAAkF,KAAAsH,KACA,KAAAtH,KAAAuH,GACA,SAAAvH,KAAAsO,OACA,UAAAtO,KAAAe,QACA,UAAA4M,EAAA3N,KAAAmN,QAAArS,KASAqT,EAAAjO,UAAAjE,QAAA,WAEA,OAAA+D,KAAAmP,SACAnP,OAEAA,KAAAsK,YAAA8B,EAAAC,SAAArM,KAAAsH,SAAAxM,IACAkF,KAAAoK,cAAApK,KAAA6O,gBAAA7O,MAAAoP,OAAAC,iBAAArP,KAAAsH,MACAtH,KAAAoK,wBAAAgE,EACApO,KAAAsK,YAAA,KAEAtK,KAAAsK,YAAAtK,KAAAoK,aAAAzB,OAAA7J,OAAAC,KAAAiB,KAAAoK,aAAAzB,QAAA,KAIA3I,KAAAe,SAAA,MAAAf,KAAAe,QAAA,UACAf,KAAAsK,YAAAtK,KAAAe,QAAA,QACAf,KAAAoK,wBAAAP,GAAA,iBAAA7J,KAAAsK,cACAtK,KAAAsK,YAAAtK,KAAAoK,aAAAzB,OAAA3I,KAAAsK,eAIAtK,KAAAe,WACA,IAAAf,KAAAe,QAAAwL,SAAAvM,KAAAe,QAAAwL,SAAAzR,IAAAkF,KAAAoK,cAAApK,KAAAoK,wBAAAP,WACA7J,KAAAe,QAAAwL,OACAzN,OAAAC,KAAAiB,KAAAe,SAAAnF,SACAoE,KAAAe,QAAAjG,IAIAkF,KAAAwL,MACAxL,KAAAsK,YAAAR,EAAA6E,KAAAW,WAAAtP,KAAAsK,YAAA,MAAAtK,KAAAsH,KAAA,IAAAtH,KAGAlB,OAAAyQ,QACAzQ,OAAAyQ,OAAAvP,KAAAsK,cAEAtK,KAAA6L,OAAA,iBAAA7L,KAAAsK,cAEAR,EAAAzN,OAAA4B,KAAA+B,KAAAsK,aACAR,EAAAzN,OAAAwB,OAAAmC,KAAAsK,YAAAlI,EAAA0H,EAAA0F,UAAA1F,EAAAzN,OAAAT,OAAAoE,KAAAsK,cAAA,GAEAR,EAAAvD,KAAAG,MAAA1G,KAAAsK,YAAAlI,EAAA0H,EAAA0F,UAAA1F,EAAAvD,KAAA3K,OAAAoE,KAAAsK,cAAA,GACAtK,KAAAsK,YAAAlI,GAIApC,KAAA8K,IACA9K,KAAA0O,aAAA5E,EAAA2F,YACAzP,KAAAqK,SACArK,KAAA0O,aAAA5E,EAAA4F,WAEA1P,KAAA0O,aAAA1O,KAAAsK,YAGAtK,KAAAoP,kBAAAhB,IACApO,KAAAoP,OAAAO,KAAAzP,UAAAF,KAAA+G,MAAA/G,KAAA0O,cAEA5B,EAAA5M,UAAAjE,QAAAqK,KAAAtG,OA5BA,IAQAoC,GA2CA+L,EAAAyB,EAAA,SAAAC,EAAAC,EAAAC,EAAArB,GAUA,MAPA,mBAAAoB,EACAA,EAAAhG,EAAAkG,aAAAF,GAAA/I,KAGA+I,GAAA,iBAAAA,IACAA,EAAAhG,EAAAmG,aAAAH,GAAA/I,MAEA,SAAA7G,EAAAgQ,GACApG,EAAAkG,aAAA9P,EAAA8M,aACAY,IAAA,IAAAO,EAAA+B,EAAAL,EAAAC,EAAAC,EAAA,CAAAI,QAAAzB,OAkBAP,EAAAiC,EAAA,SAAAC,GACAjC,EAAAiC,I,+CCnXA,IAAAnV,EAAAG,EAAAC,QAAAF,EAAA,IAEAF,EAAAoV,MAAA,QAoDApV,EAAAqV,KAjCA,SAAAzP,EAAA0P,EAAAxP,GAMA,OAHAwP,EAFA,mBAAAA,GACAxP,EAAAwP,EACA,IAAAtV,EAAAuV,MACAD,GACA,IAAAtV,EAAAuV,MACAF,KAAAzP,EAAAE,IA2CA9F,EAAAwV,SANA,SAAA5P,EAAA0P,GAGA,OADAA,EADAA,GACA,IAAAtV,EAAAuV,MACAC,SAAA5P,IAMA5F,EAAAyV,QAAAvV,EAAA,IACAF,EAAA0V,QAAAxV,EAAA,IACAF,EAAA2V,SAAAzV,EAAA,IACAF,EAAA0O,UAAAxO,EAAA,IAGAF,EAAA4R,iBAAA1R,EAAA,IACAF,EAAAgS,UAAA9R,EAAA,IACAF,EAAAuV,KAAArV,EAAA,IACAF,EAAA2O,KAAAzO,EAAA,IACAF,EAAAkT,KAAAhT,EAAA,IACAF,EAAAiT,MAAA/S,EAAA,IACAF,EAAA4V,MAAA1V,EAAA,IACAF,EAAA6V,SAAA3V,EAAA,IACAF,EAAA8V,QAAA5V,EAAA,IACAF,EAAA+V,OAAA7V,EAAA,IAGAF,EAAAgW,QAAA9V,EAAA,IACAF,EAAAiW,SAAA/V,EAAA,IAGAF,EAAAkR,MAAAhR,EAAA,IACAF,EAAA4O,KAAA1O,EAAA,IAGAF,EAAA4R,iBAAAsD,EAAAlV,EAAAuV,MACAvV,EAAAgS,UAAAkD,EAAAlV,EAAAkT,KAAAlT,EAAA8V,QAAA9V,EAAA2O,MACA3O,EAAAuV,KAAAL,EAAAlV,EAAAkT,MACAlT,EAAAiT,MAAAiC,EAAAlV,EAAAkT,O,yICtGA,IAAAlT,EAAAI,EA2BA,SAAA8V,IACAlW,EAAA4O,KAAAsG,IACAlV,EAAAmW,OAAAjB,EAAAlV,EAAAoW,cACApW,EAAAqW,OAAAnB,EAAAlV,EAAAsW,cAtBAtW,EAAAoV,MAAA,UAGApV,EAAAmW,OAAAjW,EAAA,IACAF,EAAAoW,aAAAlW,EAAA,IACAF,EAAAqW,OAAAnW,EAAA,IACAF,EAAAsW,aAAApW,EAAA,IAGAF,EAAA4O,KAAA1O,EAAA,IACAF,EAAAuW,IAAArW,EAAA,IACAF,EAAAwW,MAAAtW,EAAA,IACAF,EAAAkW,UAAAA,EAcAA,K,iEClCAlW,EAAAG,EAAAC,QAAAF,EAAA,IAEAF,EAAAoV,MAAA,OAGApV,EAAAyW,SAAAvW,EAAA,IACAF,EAAA0W,MAAAxW,EAAA,IACAF,EAAA2L,OAAAzL,EAAA,IAGAF,EAAAuV,KAAAL,EAAAlV,EAAAkT,KAAAlT,EAAA0W,MAAA1W,EAAA2L,S,+CCVAxL,EAAAC,QAAAyV,EAGA,IAAA5C,EAAA/S,EAAA,MACA2V,EAAA7Q,UAAApB,OAAAiO,OAAAoB,EAAAjO,YAAA8M,YAAA+D,GAAA9D,UAAA,WAEA,IAAAb,EAAAhR,EAAA,IACA0O,EAAA1O,EAAA,IAcA,SAAA2V,EAAAhK,EAAAQ,EAAAQ,EAAAT,EAAAvG,EAAAoM,GAIA,GAHAgB,EAAA7H,KAAAtG,KAAA+G,EAAAQ,EAAAD,EAAAxM,EAAAA,EAAAiG,EAAAoM,IAGArD,EAAA+D,SAAA9F,GACA,MAAAsF,UAAA,4BAMArN,KAAA+H,QAAAA,EAMA/H,KAAA6R,gBAAA,KAGA7R,KAAA8K,KAAA,EAwBAiG,EAAAxD,SAAA,SAAAxG,EAAAC,GACA,OAAA,IAAA+J,EAAAhK,EAAAC,EAAAO,GAAAP,EAAAe,QAAAf,EAAAM,KAAAN,EAAAjG,QAAAiG,EAAAmG,UAQA4D,EAAA7Q,UAAAuN,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA7D,EAAAiB,SAAA,CACA,UAAA/K,KAAA+H,QACA,OAAA/H,KAAAsH,KACA,KAAAtH,KAAAuH,GACA,SAAAvH,KAAAsO,OACA,UAAAtO,KAAAe,QACA,UAAA4M,EAAA3N,KAAAmN,QAAArS,KAOAiW,EAAA7Q,UAAAjE,QAAA,WACA,GAAA+D,KAAAmP,SACA,OAAAnP,KAGA,GAAAoM,EAAAO,OAAA3M,KAAA+H,WAAAjN,EACA,MAAAkD,MAAA,qBAAAgC,KAAA+H,SAEA,OAAAoG,EAAAjO,UAAAjE,QAAAqK,KAAAtG,OAaA+Q,EAAAnB,EAAA,SAAAC,EAAAiC,EAAAC,GAUA,MAPA,mBAAAA,EACAA,EAAAjI,EAAAkG,aAAA+B,GAAAhL,KAGAgL,GAAA,iBAAAA,IACAA,EAAAjI,EAAAmG,aAAA8B,GAAAhL,MAEA,SAAA7G,EAAAgQ,GACApG,EAAAkG,aAAA9P,EAAA8M,aACAY,IAAA,IAAAmD,EAAAb,EAAAL,EAAAiC,EAAAC,O,yCC1HA1W,EAAAC,QAAA4V,EAEA,IAAApH,EAAA1O,EAAA,IASA,SAAA8V,EAAAc,GAEA,GAAAA,EACA,IAAA,IAAAjT,EAAAD,OAAAC,KAAAiT,GAAAnV,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACAmD,KAAAjB,EAAAlC,IAAAmV,EAAAjT,EAAAlC,IA0BAqU,EAAAnE,OAAA,SAAAiF,GACA,OAAAhS,KAAAiS,MAAAlF,OAAAiF,IAWAd,EAAApU,OAAA,SAAA2R,EAAAyD,GACA,OAAAlS,KAAAiS,MAAAnV,OAAA2R,EAAAyD,IAWAhB,EAAAiB,gBAAA,SAAA1D,EAAAyD,GACA,OAAAlS,KAAAiS,MAAAE,gBAAA1D,EAAAyD,IAYAhB,EAAArT,OAAA,SAAAuU,GACA,OAAApS,KAAAiS,MAAApU,OAAAuU,IAYAlB,EAAAmB,gBAAA,SAAAD,GACA,OAAApS,KAAAiS,MAAAI,gBAAAD,IAUAlB,EAAAoB,OAAA,SAAA7D,GACA,OAAAzO,KAAAiS,MAAAK,OAAA7D,IAUAyC,EAAAxG,WAAA,SAAA6H,GACA,OAAAvS,KAAAiS,MAAAvH,WAAA6H,IAWArB,EAAAnG,SAAA,SAAA0D,EAAA1N,GACA,OAAAf,KAAAiS,MAAAlH,SAAA0D,EAAA1N,IAOAmQ,EAAAhR,UAAAuN,OAAA,WACA,OAAAzN,KAAAiS,MAAAlH,SAAA/K,KAAA8J,EAAA4D,iB,6BCtIArS,EAAAC,QAAA2V,EAGA,IAAAnE,EAAA1R,EAAA,MACA6V,EAAA/Q,UAAApB,OAAAiO,OAAAD,EAAA5M,YAAA8M,YAAAiE,GAAAhE,UAAA,SAEA,IAAAnD,EAAA1O,EAAA,IAiBA,SAAA6V,EAAAlK,EAAAO,EAAAkL,EAAA3Q,EAAA4Q,EAAAC,EAAA3R,EAAAoM,EAAAwF,GAYA,GATA7I,EAAAyE,SAAAkE,IACA1R,EAAA0R,EACAA,EAAAC,EAAA5X,GACAgP,EAAAyE,SAAAmE,KACA3R,EAAA2R,EACAA,EAAA5X,GAIAwM,IAAAxM,IAAAgP,EAAA+D,SAAAvG,GACA,MAAA+F,UAAA,yBAGA,IAAAvD,EAAA+D,SAAA2E,GACA,MAAAnF,UAAA,gCAGA,IAAAvD,EAAA+D,SAAAhM,GACA,MAAAwL,UAAA,iCAEAP,EAAAxG,KAAAtG,KAAA+G,EAAAhG,GAMAf,KAAAsH,KAAAA,GAAA,MAMAtH,KAAAwS,YAAAA,EAMAxS,KAAAyS,gBAAAA,GAAA3X,EAMAkF,KAAA6B,aAAAA,EAMA7B,KAAA0S,iBAAAA,GAAA5X,EAMAkF,KAAA4S,oBAAA,KAMA5S,KAAA6S,qBAAA,KAMA7S,KAAAmN,QAAAA,EAKAnN,KAAA2S,cAAAA,EAuBA1B,EAAA1D,SAAA,SAAAxG,EAAAC,GACA,OAAA,IAAAiK,EAAAlK,EAAAC,EAAAM,KAAAN,EAAAwL,YAAAxL,EAAAnF,aAAAmF,EAAAyL,cAAAzL,EAAA0L,eAAA1L,EAAAjG,QAAAiG,EAAAmG,QAAAnG,EAAA2L,gBAQA1B,EAAA/Q,UAAAuN,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA7D,EAAAiB,SAAA,CACA,OAAA,QAAA/K,KAAAsH,MAAAtH,KAAAsH,MAAAxM,EACA,cAAAkF,KAAAwS,YACA,gBAAAxS,KAAAyS,cACA,eAAAzS,KAAA6B,aACA,iBAAA7B,KAAA0S,eACA,UAAA1S,KAAAe,QACA,UAAA4M,EAAA3N,KAAAmN,QAAArS,EACA,gBAAAkF,KAAA2S,iBAOA1B,EAAA/Q,UAAAjE,QAAA,WAGA,OAAA+D,KAAAmP,SACAnP,MAEAA,KAAA4S,oBAAA5S,KAAAoP,OAAA0D,WAAA9S,KAAAwS,aACAxS,KAAA6S,qBAAA7S,KAAAoP,OAAA0D,WAAA9S,KAAA6B,cAEAiL,EAAA5M,UAAAjE,QAAAqK,KAAAtG,S,mCC7JA3E,EAAAC,QAAA4R,EAGA,IAAAJ,EAAA1R,EAAA,MACA8R,EAAAhN,UAAApB,OAAAiO,OAAAD,EAAA5M,YAAA8M,YAAAE,GAAAD,UAAA,YAEA,IAGAmB,EACA4C,EACAnH,EALAsE,EAAA/S,EAAA,IACA0O,EAAA1O,EAAA,IAoCA,SAAA2X,EAAAC,EAAAtF,GACA,IAAAsF,IAAAA,EAAApX,OACA,OAAAd,EAEA,IADA,IAAAmY,EAAA,GACApW,EAAA,EAAAA,EAAAmW,EAAApX,SAAAiB,EACAoW,EAAAD,EAAAnW,GAAAkK,MAAAiM,EAAAnW,GAAA4Q,OAAAC,GACA,OAAAuF,EA4CA,SAAA/F,EAAAnG,EAAAhG,GACA+L,EAAAxG,KAAAtG,KAAA+G,EAAAhG,GAMAf,KAAAiH,OAAAnM,EAOAkF,KAAAkT,EAAA,KAGA,SAAAC,EAAAC,GAEA,OADAA,EAAAF,EAAA,KACAE,EAhFAlG,EAAAK,SAAA,SAAAxG,EAAAC,GACA,OAAA,IAAAkG,EAAAnG,EAAAC,EAAAjG,SAAAsS,QAAArM,EAAAC,SAmBAiG,EAAA6F,YAAAA,EAQA7F,EAAAa,aAAA,SAAAT,EAAA/F,GACA,GAAA+F,EACA,IAAA,IAAAzQ,EAAA,EAAAA,EAAAyQ,EAAA1R,SAAAiB,EACA,GAAA,iBAAAyQ,EAAAzQ,IAAAyQ,EAAAzQ,GAAA,IAAA0K,GAAA+F,EAAAzQ,GAAA,GAAA0K,EACA,OAAA,EACA,OAAA,GASA2F,EAAAc,eAAA,SAAAV,EAAAvG,GACA,GAAAuG,EACA,IAAA,IAAAzQ,EAAA,EAAAA,EAAAyQ,EAAA1R,SAAAiB,EACA,GAAAyQ,EAAAzQ,KAAAkK,EACA,OAAA,EACA,OAAA,GA0CAjI,OAAAiQ,eAAA7B,EAAAhN,UAAA,cAAA,CACAwJ,IAAA,WACA,OAAA1J,KAAAkT,IAAAlT,KAAAkT,EAAApJ,EAAAwJ,QAAAtT,KAAAiH,YA6BAiG,EAAAhN,UAAAuN,OAAA,SAAAC,GACA,OAAA5D,EAAAiB,SAAA,CACA,UAAA/K,KAAAe,QACA,SAAAgS,EAAA/S,KAAAuT,YAAA7F,MASAR,EAAAhN,UAAAmT,QAAA,SAAAG,GAGA,GAAAA,EACA,IAAA,IAAAvM,EAAAwM,EAAA3U,OAAAC,KAAAyU,GAAA3W,EAAA,EAAAA,EAAA4W,EAAA7X,SAAAiB,EACAoK,EAAAuM,EAAAC,EAAA5W,IAJAmD,KAKA4N,KACA3G,EAAAG,SAAAtM,EACAsT,EACAnH,EAAA0B,SAAA7N,EACA+O,EACA5C,EAAAyM,UAAA5Y,EACAkW,EACA/J,EAAAM,KAAAzM,EACAqT,EACAjB,GAPAK,SAOAkG,EAAA5W,GAAAoK,IAIA,OAAAjH,MAQAkN,EAAAhN,UAAAwJ,IAAA,SAAA3C,GACA,OAAA/G,KAAAiH,QAAAjH,KAAAiH,OAAAF,IACA,MAUAmG,EAAAhN,UAAAyT,QAAA,SAAA5M,GACA,GAAA/G,KAAAiH,QAAAjH,KAAAiH,OAAAF,aAAA8C,EACA,OAAA7J,KAAAiH,OAAAF,GAAA4B,OACA,MAAA3K,MAAA,iBAAA+I,IAUAmG,EAAAhN,UAAA0N,IAAA,SAAA2E,GAEA,KAAAA,aAAApE,GAAAoE,EAAAjE,SAAAxT,GAAAyX,aAAAnE,GAAAmE,aAAA1I,GAAA0I,aAAAvB,GAAAuB,aAAArF,GACA,MAAAG,UAAA,wCAEA,GAAArN,KAAAiH,OAEA,CACA,IAAA2M,EAAA5T,KAAA0J,IAAA6I,EAAAxL,MACA,GAAA6M,EAAA,CACA,KAAAA,aAAA1G,GAAAqF,aAAArF,IAAA0G,aAAAxF,GAAAwF,aAAA5C,EAWA,MAAAhT,MAAA,mBAAAuU,EAAAxL,KAAA,QAAA/G,MARA,IADA,IAAAiH,EAAA2M,EAAAL,YACA1W,EAAA,EAAAA,EAAAoK,EAAArL,SAAAiB,EACA0V,EAAA3E,IAAA3G,EAAApK,IACAmD,KAAAkO,OAAA0F,GACA5T,KAAAiH,SACAjH,KAAAiH,OAAA,IACAsL,EAAAsB,WAAAD,EAAA7S,SAAA,SAZAf,KAAAiH,OAAA,GAoBA,OAFAjH,KAAAiH,OAAAsL,EAAAxL,MAAAwL,GACAuB,MAAA9T,MACAmT,EAAAnT,OAUAkN,EAAAhN,UAAAgO,OAAA,SAAAqE,GAEA,KAAAA,aAAAzF,GACA,MAAAO,UAAA,qCACA,GAAAkF,EAAAnD,SAAApP,KACA,MAAAhC,MAAAuU,EAAA,uBAAAvS,MAOA,cALAA,KAAAiH,OAAAsL,EAAAxL,MACAjI,OAAAC,KAAAiB,KAAAiH,QAAArL,SACAoE,KAAAiH,OAAAnM,GAEAyX,EAAAwB,SAAA/T,MACAmT,EAAAnT,OASAkN,EAAAhN,UAAA8T,OAAA,SAAAzO,EAAAyB,GAEA,GAAA8C,EAAA+D,SAAAtI,GACAA,EAAAA,EAAAG,MAAA,UACA,IAAAhK,MAAAuY,QAAA1O,GACA,MAAA8H,UAAA,gBACA,GAAA9H,GAAAA,EAAA3J,QAAA,KAAA2J,EAAA,GACA,MAAAvH,MAAA,yBAGA,IADA,IAAAkW,EAAAlU,KACA,EAAAuF,EAAA3J,QAAA,CACA,IAAAuY,EAAA5O,EAAAM,QACA,GAAAqO,EAAAjN,QAAAiN,EAAAjN,OAAAkN,IAEA,MADAD,EAAAA,EAAAjN,OAAAkN,cACAjH,GACA,MAAAlP,MAAA,kDAEAkW,EAAAtG,IAAAsG,EAAA,IAAAhH,EAAAiH,IAIA,OAFAnN,GACAkN,EAAAb,QAAArM,GACAkN,GAOAhH,EAAAhN,UAAAkU,WAAA,WAEA,IADA,IAAAnN,EAAAjH,KAAAuT,YAAA1W,EAAA,EACAA,EAAAoK,EAAArL,QACAqL,EAAApK,aAAAqQ,EACAjG,EAAApK,KAAAuX,aAEAnN,EAAApK,KAAAZ,UACA,OAAA+D,KAAA/D,WAUAiR,EAAAhN,UAAAmU,OAAA,SAAA9O,EAAA+O,EAAAC,GASA,GANA,kBAAAD,GACAC,EAAAD,EACAA,EAAAxZ,GACAwZ,IAAA5Y,MAAAuY,QAAAK,KACAA,EAAA,CAAAA,IAEAxK,EAAA+D,SAAAtI,IAAAA,EAAA3J,OAAA,CACA,GAAA,MAAA2J,EACA,OAAAvF,KAAAwQ,KACAjL,EAAAA,EAAAG,MAAA,UACA,IAAAH,EAAA3J,OACA,OAAAoE,KAGA,GAAA,KAAAuF,EAAA,GACA,OAAAvF,KAAAwQ,KAAA6D,OAAA9O,EAAA7H,MAAA,GAAA4W,GAGA,IAAAE,EAAAxU,KAAA0J,IAAAnE,EAAA,IACA,GAAAiP,GACA,GAAA,IAAAjP,EAAA3J,QACA,IAAA0Y,IAAAA,EAAAtI,QAAAwI,EAAAxH,aACA,OAAAwH,OACA,GAAAA,aAAAtH,IAAAsH,EAAAA,EAAAH,OAAA9O,EAAA7H,MAAA,GAAA4W,GAAA,IACA,OAAAE,OAIA,IAAA,IAAA3X,EAAA,EAAAA,EAAAmD,KAAAuT,YAAA3X,SAAAiB,EACA,GAAAmD,KAAAkT,EAAArW,aAAAqQ,IAAAsH,EAAAxU,KAAAkT,EAAArW,GAAAwX,OAAA9O,EAAA+O,GAAA,IACA,OAAAE,EAGA,OAAA,OAAAxU,KAAAoP,QAAAmF,EACA,KACAvU,KAAAoP,OAAAiF,OAAA9O,EAAA+O,IAqBApH,EAAAhN,UAAA4S,WAAA,SAAAvN,GACA,IAAAiP,EAAAxU,KAAAqU,OAAA9O,EAAA,CAAA6I,IACA,IAAAoG,EACA,MAAAxW,MAAA,iBAAAuH,GACA,OAAAiP,GAUAtH,EAAAhN,UAAAuU,WAAA,SAAAlP,GACA,IAAAiP,EAAAxU,KAAAqU,OAAA9O,EAAA,CAAAsE,IACA,IAAA2K,EACA,MAAAxW,MAAA,iBAAAuH,EAAA,QAAAvF,MACA,OAAAwU,GAUAtH,EAAAhN,UAAAmP,iBAAA,SAAA9J,GACA,IAAAiP,EAAAxU,KAAAqU,OAAA9O,EAAA,CAAA6I,EAAAvE,IACA,IAAA2K,EACA,MAAAxW,MAAA,yBAAAuH,EAAA,QAAAvF,MACA,OAAAwU,GAUAtH,EAAAhN,UAAAwU,cAAA,SAAAnP,GACA,IAAAiP,EAAAxU,KAAAqU,OAAA9O,EAAA,CAAAyL,IACA,IAAAwD,EACA,MAAAxW,MAAA,oBAAAuH,EAAA,QAAAvF,MACA,OAAAwU,GAIAtH,EAAAkD,EAAA,SAAAC,EAAAsE,EAAAC,GACAxG,EAAAiC,EACAW,EAAA2D,EACA9K,EAAA+K,I,0CC9aAvZ,EAAAC,QAAAwR,GAEAG,UAAA,mBAEA,IAEAwD,EAFA3G,EAAA1O,EAAA,IAYA,SAAA0R,EAAA/F,EAAAhG,GAEA,IAAA+I,EAAA+D,SAAA9G,GACA,MAAAsG,UAAA,yBAEA,GAAAtM,IAAA+I,EAAAyE,SAAAxN,GACA,MAAAsM,UAAA,6BAMArN,KAAAe,QAAAA,EAMAf,KAAA2S,cAAA,KAMA3S,KAAA+G,KAAAA,EAMA/G,KAAAoP,OAAA,KAMApP,KAAAmP,UAAA,EAMAnP,KAAAmN,QAAA,KAMAnN,KAAAc,SAAA,KAGAhC,OAAA+V,iBAAA/H,EAAA5M,UAAA,CAQAsQ,KAAA,CACA9G,IAAA,WAEA,IADA,IAAAwK,EAAAlU,KACA,OAAAkU,EAAA9E,QACA8E,EAAAA,EAAA9E,OACA,OAAA8E,IAUA3J,SAAA,CACAb,IAAA,WAGA,IAFA,IAAAnE,EAAA,CAAAvF,KAAA+G,MACAmN,EAAAlU,KAAAoP,OACA8E,GACA3O,EAAAuP,QAAAZ,EAAAnN,MACAmN,EAAAA,EAAA9E,OAEA,OAAA7J,EAAA5H,KAAA,SAUAmP,EAAA5M,UAAAuN,OAAA,WACA,MAAAzP,SAQA8O,EAAA5M,UAAA4T,MAAA,SAAA1E,GACApP,KAAAoP,QAAApP,KAAAoP,SAAAA,GACApP,KAAAoP,OAAAlB,OAAAlO,MACAA,KAAAoP,OAAAA,EACApP,KAAAmP,UAAA,EACAqB,EAAApB,EAAAoB,KACAA,aAAAC,GACAD,EAAAuE,EAAA/U,OAQA8M,EAAA5M,UAAA6T,SAAA,SAAA3E,GACAoB,EAAApB,EAAAoB,KACAA,aAAAC,GACAD,EAAAwE,EAAAhV,MACAA,KAAAoP,OAAA,KACApP,KAAAmP,UAAA,GAOArC,EAAA5M,UAAAjE,QAAA,WACA,OAAA+D,KAAAmP,UAEAnP,KAAAwQ,gBAAAC,IACAzQ,KAAAmP,UAAA,GAFAnP,MAWA8M,EAAA5M,UAAA8O,UAAA,SAAAjI,GACA,OAAA/G,KAAAe,QACAf,KAAAe,QAAAgG,GACAjM,GAUAgS,EAAA5M,UAAA+O,UAAA,SAAAlI,EAAAtH,EAAAyP,GAGA,OAFAA,GAAAlP,KAAAe,SAAAf,KAAAe,QAAAgG,KAAAjM,KACAkF,KAAAe,UAAAf,KAAAe,QAAA,KAAAgG,GAAAtH,GACAO,MAUA8M,EAAA5M,UAAA+U,gBAAA,SAAAlO,EAAAtH,EAAAyV,GACAlV,KAAA2S,gBACA3S,KAAA2S,cAAA,IAEA,IASAwC,EAUAC,EAnBAzC,EAAA3S,KAAA2S,cAuBA,OAtBAuC,GAGAG,EAAA1C,EAAA2C,KAAA,SAAAD,GACA,OAAAvW,OAAAoB,UAAAqV,eAAAjP,KAAA+O,EAAAtO,OAIAoO,EAAAE,EAAAtO,GACA+C,EAAA0L,YAAAL,EAAAD,EAAAzV,MAGA4V,EAAA,IACAtO,GAAA+C,EAAA0L,YAAA,GAAAN,EAAAzV,GACAkT,EAAApV,KAAA8X,MAIAD,EAAA,IACArO,GAAAtH,EACAkT,EAAApV,KAAA6X,IAEApV,MASA8M,EAAA5M,UAAA2T,WAAA,SAAA9S,EAAAmO,GACA,GAAAnO,EACA,IAAA,IAAAhC,EAAAD,OAAAC,KAAAgC,GAAAlE,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACAmD,KAAAiP,UAAAlQ,EAAAlC,GAAAkE,EAAAhC,EAAAlC,IAAAqS,GACA,OAAAlP,MAOA8M,EAAA5M,UAAAzB,SAAA,WACA,IAAAwO,EAAAjN,KAAAgN,YAAAC,UACA1C,EAAAvK,KAAAuK,SACA,OAAAA,EAAA3O,OACAqR,EAAA,IAAA1C,EACA0C,GAIAH,EAAAsD,EAAA,SAAAqF,GACAhF,EAAAgF,I,6BChPApa,EAAAC,QAAAwV,EAGA,IAAAhE,EAAA1R,EAAA,MACA0V,EAAA5Q,UAAApB,OAAAiO,OAAAD,EAAA5M,YAAA8M,YAAA8D,GAAA7D,UAAA,QAEA,IAAAkB,EAAA/S,EAAA,IACA0O,EAAA1O,EAAA,IAYA,SAAA0V,EAAA/J,EAAA2O,EAAA3U,EAAAoM,GAQA,GAPAzR,MAAAuY,QAAAyB,KACA3U,EAAA2U,EACAA,EAAA5a,GAEAgS,EAAAxG,KAAAtG,KAAA+G,EAAAhG,GAGA2U,IAAA5a,IAAAY,MAAAuY,QAAAyB,GACA,MAAArI,UAAA,+BAMArN,KAAAmI,MAAAuN,GAAA,GAOA1V,KAAA4K,YAAA,GAMA5K,KAAAmN,QAAAA,EA0CA,SAAAwI,EAAAxN,GACA,GAAAA,EAAAiH,OACA,IAAA,IAAAvS,EAAA,EAAAA,EAAAsL,EAAAyC,YAAAhP,SAAAiB,EACAsL,EAAAyC,YAAA/N,GAAAuS,QACAjH,EAAAiH,OAAAxB,IAAAzF,EAAAyC,YAAA/N,IA7BAiU,EAAAvD,SAAA,SAAAxG,EAAAC,GACA,OAAA,IAAA8J,EAAA/J,EAAAC,EAAAmB,MAAAnB,EAAAjG,QAAAiG,EAAAmG,UAQA2D,EAAA5Q,UAAAuN,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA7D,EAAAiB,SAAA,CACA,UAAA/K,KAAAe,QACA,QAAAf,KAAAmI,MACA,UAAAwF,EAAA3N,KAAAmN,QAAArS,KAuBAgW,EAAA5Q,UAAA0N,IAAA,SAAA3D,GAGA,KAAAA,aAAAkE,GACA,MAAAd,UAAA,yBAQA,OANApD,EAAAmF,QAAAnF,EAAAmF,SAAApP,KAAAoP,QACAnF,EAAAmF,OAAAlB,OAAAjE,GACAjK,KAAAmI,MAAA5K,KAAA0M,EAAAlD,MACA/G,KAAA4K,YAAArN,KAAA0M,GAEA0L,EADA1L,EAAAoB,OAAArL,MAEAA,MAQA8Q,EAAA5Q,UAAAgO,OAAA,SAAAjE,GAGA,KAAAA,aAAAkE,GACA,MAAAd,UAAA,yBAEA,IAAAvR,EAAAkE,KAAA4K,YAAAoB,QAAA/B,GAGA,GAAAnO,EAAA,EACA,MAAAkC,MAAAiM,EAAA,uBAAAjK,MAUA,OARAA,KAAA4K,YAAArK,OAAAzE,EAAA,IAIA,GAHAA,EAAAkE,KAAAmI,MAAA6D,QAAA/B,EAAAlD,QAIA/G,KAAAmI,MAAA5H,OAAAzE,EAAA,GAEAmO,EAAAoB,OAAA,KACArL,MAMA8Q,EAAA5Q,UAAA4T,MAAA,SAAA1E,GACAtC,EAAA5M,UAAA4T,MAAAxN,KAAAtG,KAAAoP,GAGA,IAFA,IAEAvS,EAAA,EAAAA,EAAAmD,KAAAmI,MAAAvM,SAAAiB,EAAA,CACA,IAAAoN,EAAAmF,EAAA1F,IAAA1J,KAAAmI,MAAAtL,IACAoN,IAAAA,EAAAoB,SACApB,EAAAoB,OALArL,MAMA4K,YAAArN,KAAA0M,GAIA0L,EAAA3V,OAMA8Q,EAAA5Q,UAAA6T,SAAA,SAAA3E,GACA,IAAA,IAAAnF,EAAApN,EAAA,EAAAA,EAAAmD,KAAA4K,YAAAhP,SAAAiB,GACAoN,EAAAjK,KAAA4K,YAAA/N,IAAAuS,QACAnF,EAAAmF,OAAAlB,OAAAjE,GACA6C,EAAA5M,UAAA6T,SAAAzN,KAAAtG,KAAAoP,IAmBA0B,EAAAlB,EAAA,WAGA,IAFA,IAAA8F,EAAAha,MAAAC,UAAAC,QACAE,EAAA,EACAA,EAAAH,UAAAC,QACA8Z,EAAA5Z,GAAAH,UAAAG,KACA,OAAA,SAAAoE,EAAA0V,GACA9L,EAAAkG,aAAA9P,EAAA8M,aACAY,IAAA,IAAAkD,EAAA8E,EAAAF,IACA5W,OAAAiQ,eAAA7O,EAAA0V,EAAA,CACAlM,IAAAI,EAAA+L,YAAAH,GACAI,IAAAhM,EAAAiM,YAAAL,Q,0CCtMAra,EAAAC,QAAAsW,GAEA9Q,SAAA,KACA8Q,EAAAvF,SAAA,CAAA2J,UAAA,GAEA,IAAArE,EAAAvW,EAAA,IACAqV,EAAArV,EAAA,IACAgT,EAAAhT,EAAA,IACA+S,EAAA/S,EAAA,IACA2V,EAAA3V,EAAA,IACA0V,EAAA1V,EAAA,IACAyO,EAAAzO,EAAA,IACA4V,EAAA5V,EAAA,IACA6V,EAAA7V,EAAA,IACAgR,EAAAhR,EAAA,IACA0O,EAAA1O,EAAA,IAEA6a,EAAA,gBACAC,EAAA,kBACAC,EAAA,qBACAC,EAAA,uBACAC,EAAA,YACAC,EAAA,cACAC,EAAA,oDACAC,EAAA,2BACAC,EAAA,+DACAC,EAAA,kCAmCA,SAAA9E,EAAApT,EAAAgS,EAAAzP,GAEAyP,aAAAC,IACA1P,EAAAyP,EACAA,EAAA,IAAAC,GAKA,IASAkG,EACAC,EACAC,EACAC,EA0pBAC,EAtqBAC,GAFAjW,EADAA,GACA6Q,EAAAvF,UAEA2K,wBAAA,EACAC,EAAAtF,EAAAnT,EAAAuC,EAAAmW,uBAAA,GACAC,EAAAF,EAAAE,KACA5Z,EAAA0Z,EAAA1Z,KACA6Z,EAAAH,EAAAG,KACAC,EAAAJ,EAAAI,KACAC,EAAAL,EAAAK,KAEAC,GAAA,EAKAC,GAAA,EAEAtD,EAAA1D,EAEAiH,EAAA1W,EAAAiV,SAAA,SAAAjP,GAAA,OAAAA,GAAA+C,EAAA4N,UAGA,SAAAC,EAAAZ,EAAAhQ,EAAA6Q,GACA,IAAA9W,EAAA8Q,EAAA9Q,SAGA,OAFA8W,IACAhG,EAAA9Q,SAAA,MACA9C,MAAA,YAAA+I,GAAA,SAAA,KAAAgQ,EAAA,OAAAjW,EAAAA,EAAA,KAAA,IAAA,QAAAmW,EAAAY,KAAA,KAGA,SAAAC,IACA,IACAf,EADApO,EAAA,GAEA,GAEA,GAAA,OAAAoO,EAAAI,MAAA,MAAAJ,EACA,MAAAY,EAAAZ,SAEApO,EAAApL,KAAA4Z,KACAE,EAAAN,GAEA,OADAA,EAAAK,MACA,MAAAL,GACA,OAAApO,EAAAhL,KAAA,IAGA,SAAAoa,EAAAC,GACA,IAAAjB,EAAAI,IACA,OAAAJ,GACA,IAAA,IACA,IAAA,IAEA,OADAxZ,EAAAwZ,GACAe,IACA,IAAA,OAAA,IAAA,OACA,OAAA,EACA,IAAA,QAAA,IAAA,QACA,OAAA,EAEA,IACA,OAuBA,SAAAf,EAAAa,GACA,IAAAtV,EAAA,EACA,MAAAyU,EAAA,IAAAA,MACAzU,GAAA,EACAyU,EAAAA,EAAAkB,UAAA,IAEA,OAAAlB,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAAzU,GAAAW,EAAAA,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAAD,IACA,IAAA,IACA,OAAA,EAEA,GAAAiT,EAAAhY,KAAA8Y,GACA,OAAAzU,EAAA4V,SAAAnB,EAAA,IACA,GAAAZ,EAAAlY,KAAA8Y,GACA,OAAAzU,EAAA4V,SAAAnB,EAAA,IACA,GAAAV,EAAApY,KAAA8Y,GACA,OAAAzU,EAAA4V,SAAAnB,EAAA,GAGA,GAAAR,EAAAtY,KAAA8Y,GACA,OAAAzU,EAAA6V,WAAApB,GAGA,MAAAY,EAAAZ,EAAA,SAAAa,GAjDAQ,CAAArB,GAAA,GACA,MAAAzR,GAGA,GAAA0S,GAAAvB,EAAAxY,KAAA8Y,GACA,OAAAA,EAGA,MAAAY,EAAAZ,EAAA,UAIA,SAAAsB,EAAAC,EAAAC,GAEA,IADA,IAAAvb,GAEAub,GAAA,OAAAxB,EAAAK,MAAA,MAAAL,EAGAuB,EAAA/a,KAAA,CAAAP,EAAAwb,EAAArB,KAAAE,EAAA,MAAA,GAAAmB,EAAArB,KAAAna,IAFAsb,EAAA/a,KAAAua,KAGAT,EAAA,KAAA,KACAA,EAAA,KAgCA,SAAAmB,EAAAzB,EAAA0B,GACA,OAAA1B,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAA,UACA,IAAA,IACA,OAAA,EAIA,IAAA0B,GAAA,MAAA1B,EAAA,IAAAA,IACA,MAAAY,EAAAZ,EAAA,MAEA,GAAAb,EAAAjY,KAAA8Y,GACA,OAAAmB,SAAAnB,EAAA,IACA,GAAAX,EAAAnY,KAAA8Y,GACA,OAAAmB,SAAAnB,EAAA,IAGA,GAAAT,EAAArY,KAAA8Y,GACA,OAAAmB,SAAAnB,EAAA,GAGA,MAAAY,EAAAZ,EAAA,MAmDA,SAAA2B,EAAAtJ,EAAA2H,GACA,OAAAA,GAEA,IAAA,SAGA,OAFA4B,EAAAvJ,EAAA2H,GACAM,EAAA,KACA,EAEA,IAAA,UAEA,OAuCA,SAAAjI,EAAA2H,GAGA,IAAAP,EAAAvY,KAAA8Y,EAAAI,KACA,MAAAQ,EAAAZ,EAAA,aAEA,IAAAzP,EAAA,IAAA8G,EAAA2I,GACA6B,EAAAtR,EAAA,SAAAyP,GACA,IAAA2B,EAAApR,EAAAyP,GAGA,OAAAA,GAEA,IAAA,OA6IA,SAAA3H,GACAiI,EAAA,KACA,IAAAtP,EAAAoP,IAGA,GAAA/K,EAAAO,OAAA5E,KAAAjN,EACA,MAAA6c,EAAA5P,EAAA,QAEAsP,EAAA,KACA,IAAAwB,EAAA1B,IAGA,IAAAV,EAAAxY,KAAA4a,GACA,MAAAlB,EAAAkB,EAAA,QAEAxB,EAAA,KACA,IAAAtQ,EAAAoQ,IAGA,IAAAX,EAAAvY,KAAA8I,GACA,MAAA4Q,EAAA5Q,EAAA,QAEAsQ,EAAA,KACA,IAAApN,EAAA,IAAA8G,EAAA0G,EAAA1Q,GAAAyR,EAAArB,KAAApP,EAAA8Q,GACAD,EAAA3O,EAAA,SAAA8M,GAGA,GAAA,WAAAA,EAIA,MAAAY,EAAAZ,GAHA4B,EAAA1O,EAAA8M,GACAM,EAAA,MAIA,WACAyB,EAAA7O,KAEAmF,EAAAxB,IAAA3D,GAhLA8O,CAAAzR,GACA,MAEA,IAAA,WACA,IAAA,WACA0R,EAAA1R,EAAAyP,GACA,MAEA,IAAA,WAGAiC,EAAA1R,EADAkQ,EACA,kBAEA,YAEA,MAEA,IAAA,SAkKA,SAAApI,EAAA2H,GAGA,IAAAP,EAAAvY,KAAA8Y,EAAAI,KACA,MAAAQ,EAAAZ,EAAA,QAEA,IAAA5O,EAAA,IAAA2I,EAAA2G,EAAAV,IACA6B,EAAAzQ,EAAA,SAAA4O,GACA,WAAAA,GACA4B,EAAAxQ,EAAA4O,GACAM,EAAA,OAEA9Z,EAAAwZ,GACAiC,EAAA7Q,EAAA,eAGAiH,EAAAxB,IAAAzF,GAjLA8Q,CAAA3R,EAAAyP,GACA,MAEA,IAAA,aACAsB,EAAA/Q,EAAA4R,aAAA5R,EAAA4R,WAAA,KACA,MAEA,IAAA,WACAb,EAAA/Q,EAAAgG,WAAAhG,EAAAgG,SAAA,KAAA,GACA,MAEA,QAEA,IAAAkK,IAAAf,EAAAxY,KAAA8Y,GACA,MAAAY,EAAAZ,GAEAxZ,EAAAwZ,GACAiC,EAAA1R,EAAA,eAIA8H,EAAAxB,IAAAtG,GA7FA6R,CAAA/J,EAAA2H,GACA,EAEA,IAAA,OAEA,OAuPA,SAAA3H,EAAA2H,GAGA,IAAAP,EAAAvY,KAAA8Y,EAAAI,KACA,MAAAQ,EAAAZ,EAAA,QAEA,IAAAvJ,EAAA,IAAA3D,EAAAkN,GACA6B,EAAApL,EAAA,SAAAuJ,GACA,OAAAA,GACA,IAAA,SACA4B,EAAAnL,EAAAuJ,GACAM,EAAA,KACA,MAEA,IAAA,WACAgB,EAAA7K,EAAAF,WAAAE,EAAAF,SAAA,KAAA,GACA,MAEA,SAOA,SAAA8B,EAAA2H,GAGA,IAAAP,EAAAvY,KAAA8Y,GACA,MAAAY,EAAAZ,EAAA,QAEAM,EAAA,KACA,IAAA5X,EAAA+Y,EAAArB,KAAA,GACAiC,EAAA,GACAR,EAAAQ,EAAA,SAAArC,GAGA,GAAA,WAAAA,EAIA,MAAAY,EAAAZ,GAHA4B,EAAAS,EAAArC,GACAM,EAAA,MAIA,WACAyB,EAAAM,KAEAhK,EAAAxB,IAAAmJ,EAAAtX,EAAA2Z,EAAAjM,SA3BAkM,CAAA7L,EAAAuJ,MAGA3H,EAAAxB,IAAAJ,GA9QA8L,CAAAlK,EAAA2H,GACA,EAEA,IAAA,UAEA,OAuXA,SAAA3H,EAAA2H,GAGA,IAAAP,EAAAvY,KAAA8Y,EAAAI,KACA,MAAAQ,EAAAZ,EAAA,gBAEA,IAAAwC,EAAA,IAAAvI,EAAA+F,GACA6B,EAAAW,EAAA,SAAAxC,GACA,IAAA2B,EAAAa,EAAAxC,GAAA,CAIA,GAAA,QAAAA,EAGA,MAAAY,EAAAZ,IAKA,SAAA3H,EAAA2H,GAGA,IAAAyC,EAAAlC,IAEAhQ,EAAAyP,EAGA,IAAAP,EAAAvY,KAAA8Y,EAAAI,KACA,MAAAQ,EAAAZ,EAAA,QAEA,IACAvE,EAAAC,EACAC,EAFA3L,EAAAgQ,EAIAM,EAAA,KACAA,EAAA,UAAA,KACA5E,GAAA,GAGA,IAAAgE,EAAAxY,KAAA8Y,EAAAI,KACA,MAAAQ,EAAAZ,GAEAvE,EAAAuE,EACAM,EAAA,KAAAA,EAAA,WAAAA,EAAA,KACAA,EAAA,UAAA,KACA3E,GAAA,GAGA,IAAA+D,EAAAxY,KAAA8Y,EAAAI,KACA,MAAAQ,EAAAZ,GAEAlV,EAAAkV,EACAM,EAAA,KAEA,IAAAoC,EAAA,IAAAxI,EAAAlK,EAAAO,EAAAkL,EAAA3Q,EAAA4Q,EAAAC,GACA+G,EAAAtM,QAAAqM,EACAZ,EAAAa,EAAA,SAAA1C,GAGA,GAAA,WAAAA,EAIA,MAAAY,EAAAZ,GAHA4B,EAAAc,EAAA1C,GACAM,EAAA,OAKAjI,EAAAxB,IAAA6L,GAtDAC,CAAAH,EAAAxC,MAIA3H,EAAAxB,IAAA2L,GAzYAI,CAAAvK,EAAA2H,GACA,EAEA,IAAA,SAEA,OAybA,SAAA3H,EAAA2H,GAGA,IAAAN,EAAAxY,KAAA8Y,EAAAI,KACA,MAAAQ,EAAAZ,EAAA,aAEA,IAAA6C,EAAA7C,EACA6B,EAAA,KAAA,SAAA7B,GACA,OAAAA,GAEA,IAAA,WACA,IAAA,WACAiC,EAAA5J,EAAA2H,EAAA6C,GACA,MAEA,IAAA,WAGAZ,EAAA5J,EADAoI,EACA,kBAEA,WAFAoC,GAIA,MAEA,QAEA,IAAApC,IAAAf,EAAAxY,KAAA8Y,GACA,MAAAY,EAAAZ,GACAxZ,EAAAwZ,GACAiC,EAAA5J,EAAA,WAAAwK,MAvdAC,CAAAzK,EAAA2H,GACA,GAKA,SAAA6B,EAAA3F,EAAA6G,EAAAC,GACA,IAQAhD,EARAiD,EAAA/C,EAAAY,KAOA,GANA5E,IACA,iBAAAA,EAAA9F,UACA8F,EAAA9F,QAAAmK,KAEArE,EAAAnS,SAAA8Q,EAAA9Q,UAEAuW,EAAA,KAAA,GAAA,CAEA,KAAA,OAAAN,EAAAI,MACA2C,EAAA/C,GACAM,EAAA,KAAA,QAEA0C,GACAA,IACA1C,EAAA,KACApE,IAAA,iBAAAA,EAAA9F,SAAA6J,KACA/D,EAAA9F,QAAAmK,EAAA0C,IAAA/G,EAAA9F,SA4DA,SAAA6L,EAAA5J,EAAAtG,EAAAwF,GACA,IAAAhH,EAAA6P,IACA,GAAA,UAAA7P,EAAA,CAMA,IAAAmP,EAAAxY,KAAAqJ,GACA,MAAAqQ,EAAArQ,EAAA,QAEA,IAAAP,EAAAoQ,IAGA,IAAAX,EAAAvY,KAAA8I,GACA,MAAA4Q,EAAA5Q,EAAA,QAEAA,EAAA0Q,EAAA1Q,GACAsQ,EAAA,KAEA,IAAApN,EAAA,IAAAkE,EAAApH,EAAAyR,EAAArB,KAAA7P,EAAAwB,EAAAwF,GACAsK,EAAA3O,EAAA,SAAA8M,GAGA,GAAA,WAAAA,EAIA,MAAAY,EAAAZ,GAHA4B,EAAA1O,EAAA8M,GACAM,EAAA,MAIA,WACAyB,EAAA7O,KAGA,oBAAAnB,GAEAX,EAAA,IAAA2I,EAAA,IAAA/J,GACAkD,EAAAgF,UAAA,mBAAA,GACA9G,EAAAyF,IAAA3D,GACAmF,EAAAxB,IAAAzF,IAEAiH,EAAAxB,IAAA3D,GAMAuN,IAAAvN,EAAAI,UAAA+B,EAAAG,OAAAjF,KAAAxM,GAAAsR,EAAAE,MAAAhF,KAAAxM,GACAmP,EAAAgF,UAAA,UAAA,GAAA,QAGA,SAAAG,EAAAtG,GACA,IAAA/B,EAAAoQ,IAGA,IAAAX,EAAAvY,KAAA8I,GACA,MAAA4Q,EAAA5Q,EAAA,QAEA,IAAAmJ,EAAApG,EAAAmQ,QAAAlT,GACAA,IAAAmJ,IACAnJ,EAAA+C,EAAAoQ,QAAAnT,IACAsQ,EAAA,KACA,IAAA9P,EAAAiR,EAAArB,KACA7P,EAAA,IAAA8G,EAAArH,GACAO,EAAA4E,OAAA,EACAjC,EAAA,IAAAkE,EAAA+B,EAAA3I,EAAAR,EAAA+B,GACAmB,EAAAnJ,SAAA8Q,EAAA9Q,SACA8X,EAAAtR,EAAA,SAAAyP,GACA,OAAAA,GAEA,IAAA,SACA4B,EAAArR,EAAAyP,GACAM,EAAA,KACA,MAEA,IAAA,WACA,IAAA,WACA2B,EAAA1R,EAAAyP,GACA,MAEA,IAAA,WAGAiC,EAAA1R,EADAkQ,EACA,kBAEA,YAEA,MAGA,QACA,MAAAG,EAAAZ,MAGA3H,EAAAxB,IAAAtG,GACAsG,IAAA3D,GA5FAkQ,CAAA/K,EAAAtG,GA0MA,SAAA6P,EAAAvJ,EAAA2H,GACA,IAAAqD,EAAA/C,EAAA,KAAA,GAGA,IAAAZ,EAAAxY,KAAA8Y,EAAAI,KACA,MAAAQ,EAAAZ,EAAA,QAEA,IAAAhQ,EAAAgQ,EACAsD,EAAAtT,EAGAqT,IACA/C,EAAA,KAEAgD,EADAtT,EAAA,IAAAA,EAAA,IAEAgQ,EAAAK,IACAV,EAAAzY,KAAA8Y,KACA7B,EAAA6B,EAAAuD,OAAA,GACAvT,GAAAgQ,EACAI,MAGAE,EAAA,KACA,IA6CAnC,EA7CAqF,EAIA,SAAAC,EAAApL,EAAArI,GACA,GAAAsQ,EAAA,KAAA,GAAA,CAEA,IADA,IAAAoD,EAAA,IACApD,EAAA,KAAA,IAAA,CAEA,IAAAb,EAAAvY,KAAA8Y,EAAAI,KACA,MAAAQ,EAAAZ,EAAA,QAEA,IAAAtX,EACAyV,EAAA6B,EACA,MAAAK,IACA3X,EAAA+a,EAAApL,EAAArI,EAAA,IAAAgQ,IAEAM,EAAA,KACA,MAAAD,IACA3X,EAAA+a,EAAApL,EAAArI,EAAA,IAAAgQ,IAEAtX,EAAAsY,GAAA,GACA9I,EAAAG,EAAArI,EAAA,IAAAgQ,EAAAtX,KAGA,IAAAib,EAAAD,EAAAvF,GACAwF,IACAjb,EAAA,GAAAkb,OAAAD,GAAAC,OAAAlb,IACAgb,EAAAvF,GAAAzV,EACA4X,EAAA,KAAA,GAEA,OAAAoD,EAGA,IAAAG,EAAA7C,GAAA,GACA9I,EAAAG,EAAArI,EAAA6T,GACA,OAAAA,EApCAJ,CAAApL,EAAArI,GA6CAA,EA5CAsT,EA4CA5a,EA5CA8a,EA4CArF,EA5CAA,GA4CA9F,EA5CAA,GA6CA6F,iBACA7F,EAAA6F,gBAAAlO,EAAAtH,EAAAyV,GAPA,SAAAjG,EAAAG,EAAArI,EAAAtH,GACA2P,EAAAH,WACAG,EAAAH,UAAAlI,EAAAtH,GAQA,SAAAqZ,EAAA1J,GACA,GAAAiI,EAAA,KAAA,GAAA,CACA,KACAsB,EAAAvJ,EAAA,UACAiI,EAAA,KAAA,KACAA,EAAA,KAEA,OAAAjI,EA6GA,KAAA,QAAA2H,EAAAI,MACA,OAAAJ,GAEA,IAAA,UAGA,IAAAQ,EACA,MAAAI,EAAAZ,IA1iBA,WAGA,GAAAJ,IAAA7b,EACA,MAAA6c,EAAA,WAKA,GAHAhB,EAAAQ,KAGAV,EAAAxY,KAAA0Y,GACA,MAAAgB,EAAAhB,EAAA,QAEAzC,EAAAA,EAAAF,OAAA2C,GACAU,EAAA,KA+hBAwD,GACA,MAEA,IAAA,SAGA,IAAAtD,EACA,MAAAI,EAAAZ,IAniBA,WACA,IACA+D,EADA/D,EAAAK,IAEA,OAAAL,GACA,IAAA,OACA+D,EAAAjE,EAAAA,GAAA,GACAM,IACA,MACA,IAAA,SACAA,IAEA,QACA2D,EAAAlE,EAAAA,GAAA,GAGAG,EAAAe,IACAT,EAAA,KACAyD,EAAAvd,KAAAwZ,GAohBAgE,GACA,MAEA,IAAA,SAGA,IAAAxD,EACA,MAAAI,EAAAZ,IAxhBA,WAMA,GALAM,EAAA,KACAP,EAAAgB,MACAN,EAAA,WAAAV,IAGA,WAAAA,EACA,MAAAa,EAAAb,EAAA,UAEAO,EAAA,KAihBA2D,GACA,MAEA,IAAA,SAEArC,EAAAzE,EAAA6C,GACAM,EAAA,KACA,MAEA,QAGA,GAAAqB,EAAAxE,EAAA6C,GAAA,CACAQ,GAAA,EACA,SAIA,MAAAI,EAAAZ,GAKA,OADAnF,EAAA9Q,SAAA,KACA,CACAma,QAAAtE,EACAC,QAAAA,EACAC,YAAAA,EACAC,OAAAA,EACAtG,KAAAA,K,yFCpyBAnV,EAAAC,QAAAiW,EAEA,IAEAC,EAFA1H,EAAA1O,EAAA,IAIA8f,EAAApR,EAAAoR,SACA3U,EAAAuD,EAAAvD,KAGA,SAAA4U,EAAA/I,EAAAgJ,GACA,OAAAC,WAAA,uBAAAjJ,EAAA/P,IAAA,OAAA+Y,GAAA,GAAA,MAAAhJ,EAAA5L,KASA,SAAA+K,EAAAxU,GAMAiD,KAAAoC,IAAArF,EAMAiD,KAAAqC,IAAA,EAMArC,KAAAwG,IAAAzJ,EAAAnB,OAgBA,SAAAmR,IACA,OAAAjD,EAAAwR,OACA,SAAAve,GACA,OAAAwU,EAAAxE,OAAA,SAAAhQ,GACA,OAAA+M,EAAAwR,OAAAC,SAAAxe,GACA,IAAAyU,EAAAzU,GAEAye,EAAAze,KACAA,IAGAye,EAxBA,IA4CA/b,EA5CA+b,EAAA,oBAAA7Z,WACA,SAAA5E,GACA,GAAAA,aAAA4E,YAAAjG,MAAAuY,QAAAlX,GACA,OAAA,IAAAwU,EAAAxU,GACA,MAAAiB,MAAA,mBAGA,SAAAjB,GACA,GAAArB,MAAAuY,QAAAlX,GACA,OAAA,IAAAwU,EAAAxU,GACA,MAAAiB,MAAA,mBAsEA,SAAAyd,IAEA,IAAAC,EAAA,IAAAR,EAAA,EAAA,GACAre,EAAA,EACA,KAAA,EAAAmD,KAAAwG,IAAAxG,KAAAqC,KAaA,CACA,KAAAxF,EAAA,IAAAA,EAAA,CAEA,GAAAmD,KAAAqC,KAAArC,KAAAwG,IACA,MAAA2U,EAAAnb,MAGA,GADA0b,EAAA5X,IAAA4X,EAAA5X,IAAA,IAAA9D,KAAAoC,IAAApC,KAAAqC,OAAA,EAAAxF,KAAA,EACAmD,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAAqZ,EAIA,OADAA,EAAA5X,IAAA4X,EAAA5X,IAAA,IAAA9D,KAAAoC,IAAApC,KAAAqC,SAAA,EAAAxF,KAAA,EACA6e,EAxBA,KAAA7e,EAAA,IAAAA,EAGA,GADA6e,EAAA5X,IAAA4X,EAAA5X,IAAA,IAAA9D,KAAAoC,IAAApC,KAAAqC,OAAA,EAAAxF,KAAA,EACAmD,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAAqZ,EAKA,GAFAA,EAAA5X,IAAA4X,EAAA5X,IAAA,IAAA9D,KAAAoC,IAAApC,KAAAqC,OAAA,MAAA,EACAqZ,EAAA3X,IAAA2X,EAAA3X,IAAA,IAAA/D,KAAAoC,IAAApC,KAAAqC,OAAA,KAAA,EACArC,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAAqZ,EAgBA,GAfA7e,EAAA,EAeA,EAAAmD,KAAAwG,IAAAxG,KAAAqC,KACA,KAAAxF,EAAA,IAAAA,EAGA,GADA6e,EAAA3X,IAAA2X,EAAA3X,IAAA,IAAA/D,KAAAoC,IAAApC,KAAAqC,OAAA,EAAAxF,EAAA,KAAA,EACAmD,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAAqZ,OAGA,KAAA7e,EAAA,IAAAA,EAAA,CAEA,GAAAmD,KAAAqC,KAAArC,KAAAwG,IACA,MAAA2U,EAAAnb,MAGA,GADA0b,EAAA3X,IAAA2X,EAAA3X,IAAA,IAAA/D,KAAAoC,IAAApC,KAAAqC,OAAA,EAAAxF,EAAA,KAAA,EACAmD,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAAqZ,EAIA,MAAA1d,MAAA,2BAkCA,SAAA2d,EAAAvZ,EAAAnF,GACA,OAAAmF,EAAAnF,EAAA,GACAmF,EAAAnF,EAAA,IAAA,EACAmF,EAAAnF,EAAA,IAAA,GACAmF,EAAAnF,EAAA,IAAA,MAAA,EA+BA,SAAA2e,IAGA,GAAA5b,KAAAqC,IAAA,EAAArC,KAAAwG,IACA,MAAA2U,EAAAnb,KAAA,GAEA,OAAA,IAAAkb,EAAAS,EAAA3b,KAAAoC,IAAApC,KAAAqC,KAAA,GAAAsZ,EAAA3b,KAAAoC,IAAApC,KAAAqC,KAAA,IA3KAkP,EAAAxE,OAAAA,IAEAwE,EAAArR,UAAA2b,EAAA/R,EAAApO,MAAAwE,UAAA4b,UAAAhS,EAAApO,MAAAwE,UAAAxC,MAOA6T,EAAArR,UAAA6b,QACAtc,EAAA,WACA,WACA,GAAAA,GAAA,IAAAO,KAAAoC,IAAApC,KAAAqC,QAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EACA,GAAAA,GAAAA,GAAA,IAAAO,KAAAoC,IAAApC,KAAAqC,OAAA,KAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EACA,GAAAA,GAAAA,GAAA,IAAAO,KAAAoC,IAAApC,KAAAqC,OAAA,MAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EACA,GAAAA,GAAAA,GAAA,IAAAO,KAAAoC,IAAApC,KAAAqC,OAAA,MAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EACA,GAAAA,GAAAA,GAAA,GAAAO,KAAAoC,IAAApC,KAAAqC,OAAA,MAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EAGA,IAAAO,KAAAqC,KAAA,GAAArC,KAAAwG,IAEA,MADAxG,KAAAqC,IAAArC,KAAAwG,IACA2U,EAAAnb,KAAA,IAEA,OAAAP,IAQA8R,EAAArR,UAAA8b,MAAA,WACA,OAAA,EAAAhc,KAAA+b,UAOAxK,EAAArR,UAAA+b,OAAA,WACA,IAAAxc,EAAAO,KAAA+b,SACA,OAAAtc,IAAA,IAAA,EAAAA,GAAA,GAqFA8R,EAAArR,UAAAgc,KAAA,WACA,OAAA,IAAAlc,KAAA+b,UAcAxK,EAAArR,UAAAic,QAAA,WAGA,GAAAnc,KAAAqC,IAAA,EAAArC,KAAAwG,IACA,MAAA2U,EAAAnb,KAAA,GAEA,OAAA2b,EAAA3b,KAAAoC,IAAApC,KAAAqC,KAAA,IAOAkP,EAAArR,UAAAkc,SAAA,WAGA,GAAApc,KAAAqC,IAAA,EAAArC,KAAAwG,IACA,MAAA2U,EAAAnb,KAAA,GAEA,OAAA,EAAA2b,EAAA3b,KAAAoC,IAAApC,KAAAqC,KAAA,IAmCAkP,EAAArR,UAAAmc,MAAA,WAGA,GAAArc,KAAAqC,IAAA,EAAArC,KAAAwG,IACA,MAAA2U,EAAAnb,KAAA,GAEA,IAAAP,EAAAqK,EAAAuS,MAAA9X,YAAAvE,KAAAoC,IAAApC,KAAAqC,KAEA,OADArC,KAAAqC,KAAA,EACA5C,GAQA8R,EAAArR,UAAAoc,OAAA,WAGA,GAAAtc,KAAAqC,IAAA,EAAArC,KAAAwG,IACA,MAAA2U,EAAAnb,KAAA,GAEA,IAAAP,EAAAqK,EAAAuS,MAAApX,aAAAjF,KAAAoC,IAAApC,KAAAqC,KAEA,OADArC,KAAAqC,KAAA,EACA5C,GAOA8R,EAAArR,UAAA2L,MAAA,WACA,IAAAjQ,EAAAoE,KAAA+b,SACA/e,EAAAgD,KAAAqC,IACApF,EAAA+C,KAAAqC,IAAAzG,EAGA,GAAAqB,EAAA+C,KAAAwG,IACA,MAAA2U,EAAAnb,KAAApE,GAGA,OADAoE,KAAAqC,KAAAzG,EACAF,MAAAuY,QAAAjU,KAAAoC,KACApC,KAAAoC,IAAA1E,MAAAV,EAAAC,GACAD,IAAAC,EACA,IAAA+C,KAAAoC,IAAA4K,YAAA,GACAhN,KAAA6b,EAAAvV,KAAAtG,KAAAoC,IAAApF,EAAAC,IAOAsU,EAAArR,UAAA5D,OAAA,WACA,IAAAuP,EAAA7L,KAAA6L,QACA,OAAAtF,EAAAE,KAAAoF,EAAA,EAAAA,EAAAjQ,SAQA2V,EAAArR,UAAAmX,KAAA,SAAAzb,GACA,GAAA,iBAAAA,EAAA,CAEA,GAAAoE,KAAAqC,IAAAzG,EAAAoE,KAAAwG,IACA,MAAA2U,EAAAnb,KAAApE,GACAoE,KAAAqC,KAAAzG,OAEA,GAEA,GAAAoE,KAAAqC,KAAArC,KAAAwG,IACA,MAAA2U,EAAAnb,YACA,IAAAA,KAAAoC,IAAApC,KAAAqC,QAEA,OAAArC,MAQAuR,EAAArR,UAAAqc,SAAA,SAAA7P,GACA,OAAAA,GACA,KAAA,EACA1M,KAAAqX,OACA,MACA,KAAA,EACArX,KAAAqX,KAAA,GACA,MACA,KAAA,EACArX,KAAAqX,KAAArX,KAAA+b,UACA,MACA,KAAA,EACA,KAAA,IAAArP,EAAA,EAAA1M,KAAA+b,WACA/b,KAAAuc,SAAA7P,GAEA,MACA,KAAA,EACA1M,KAAAqX,KAAA,GACA,MAGA,QACA,MAAArZ,MAAA,qBAAA0O,EAAA,cAAA1M,KAAAqC,KAEA,OAAArC,MAGAuR,EAAAnB,EAAA,SAAAoM,GACAhL,EAAAgL,EACAjL,EAAAxE,OAAAA,IACAyE,EAAApB,IAEA,IAAA7U,EAAAuO,EAAA6E,KAAA,SAAA,WACA7E,EAAA2S,MAAAlL,EAAArR,UAAA,CAEAwc,MAAA,WACA,OAAAjB,EAAAnV,KAAAtG,MAAAzE,IAAA,IAGAohB,OAAA,WACA,OAAAlB,EAAAnV,KAAAtG,MAAAzE,IAAA,IAGAqhB,OAAA,WACA,OAAAnB,EAAAnV,KAAAtG,MAAA6c,WAAAthB,IAAA,IAGAuhB,QAAA,WACA,OAAAlB,EAAAtV,KAAAtG,MAAAzE,IAAA,IAGAwhB,SAAA,WACA,OAAAnB,EAAAtV,KAAAtG,MAAAzE,IAAA,Q,6BCrZAF,EAAAC,QAAAkW,EAGA,IAAAD,EAAAnW,EAAA,KACAoW,EAAAtR,UAAApB,OAAAiO,OAAAwE,EAAArR,YAAA8M,YAAAwE,EAEA,IAAA1H,EAAA1O,EAAA,IASA,SAAAoW,EAAAzU,GACAwU,EAAAjL,KAAAtG,KAAAjD,GASAyU,EAAApB,EAAA,WAEAtG,EAAAwR,SACA9J,EAAAtR,UAAA2b,EAAA/R,EAAAwR,OAAApb,UAAAxC,QAOA8T,EAAAtR,UAAA5D,OAAA,WACA,IAAAkK,EAAAxG,KAAA+b,SACA,OAAA/b,KAAAoC,IAAA4a,UACAhd,KAAAoC,IAAA4a,UAAAhd,KAAAqC,IAAArC,KAAAqC,IAAA5F,KAAAwgB,IAAAjd,KAAAqC,IAAAmE,EAAAxG,KAAAwG,MACAxG,KAAAoC,IAAA3D,SAAA,QAAAuB,KAAAqC,IAAArC,KAAAqC,IAAA5F,KAAAwgB,IAAAjd,KAAAqC,IAAAmE,EAAAxG,KAAAwG,OAUAgL,EAAApB,K,mCCjDA/U,EAAAC,QAAAmV,EAGA,IAAAvD,EAAA9R,EAAA,MACAqV,EAAAvQ,UAAApB,OAAAiO,OAAAG,EAAAhN,YAAA8M,YAAAyD,GAAAxD,UAAA,OAEA,IAKAmB,EACAwD,EACA/K,EAPAsH,EAAA/S,EAAA,IACAyO,EAAAzO,EAAA,IACA0V,EAAA1V,EAAA,IACA0O,EAAA1O,EAAA,IAaA,SAAAqV,EAAA1P,GACAmM,EAAA5G,KAAAtG,KAAA,GAAAe,GAMAf,KAAAkd,SAAA,GAMAld,KAAAmd,MAAA,GAuCA,SAAAC,KA9BA3M,EAAAlD,SAAA,SAAAvG,EAAAwJ,GAKA,OAHAA,EADAA,GACA,IAAAC,EACAzJ,EAAAjG,SACAyP,EAAAqD,WAAA7M,EAAAjG,SACAyP,EAAA6C,QAAArM,EAAAC,SAWAwJ,EAAAvQ,UAAAmd,YAAAvT,EAAAvE,KAAAtJ,QAUAwU,EAAAvQ,UAAAQ,MAAAoJ,EAAApJ,MAaA+P,EAAAvQ,UAAAqQ,KAAA,SAAAA,EAAAzP,EAAAC,EAAAC,GACA,mBAAAD,IACAC,EAAAD,EACAA,EAAAjG,GAEA,IAAAwiB,EAAAtd,KACA,IAAAgB,EACA,OAAA8I,EAAAnJ,UAAA4P,EAAA+M,EAAAxc,EAAAC,GAEA,IAAAwc,EAAAvc,IAAAoc,EAGA,SAAAI,EAAArhB,EAAAqU,GAEA,GAAAxP,EAAA,CAEA,IAAAyc,EAAAzc,EAEA,GADAA,EAAA,KACAuc,EACA,MAAAphB,EACAshB,EAAAthB,EAAAqU,IAIA,SAAAkN,EAAA5c,GACA,IAAA6c,EAAA7c,EAAA8c,YAAA,oBACA,IAAA,EAAAD,EAAA,CACAE,EAAA/c,EAAAmX,UAAA0F,GACA,GAAAE,KAAAhX,EAAA,OAAAgX,EAEA,OAAA,KAIA,SAAAC,EAAAhd,EAAAtC,GACA,IAGA,GAFAsL,EAAA+D,SAAArP,IAAA,MAAAA,EAAA,IAAAA,MACAA,EAAAoB,KAAAgS,MAAApT,IACAsL,EAAA+D,SAAArP,GAEA,CACAoT,EAAA9Q,SAAAA,EACA,IACAqO,EADA4O,EAAAnM,EAAApT,EAAA8e,EAAAvc,GAEAlE,EAAA,EACA,GAAAkhB,EAAAnH,QACA,KAAA/Z,EAAAkhB,EAAAnH,QAAAhb,SAAAiB,GACAsS,EAAAuO,EAAAK,EAAAnH,QAAA/Z,KAAAygB,EAAAD,YAAAvc,EAAAid,EAAAnH,QAAA/Z,MACA6D,EAAAyO,GACA,GAAA4O,EAAAlH,YACA,IAAAha,EAAA,EAAAA,EAAAkhB,EAAAlH,YAAAjb,SAAAiB,GACAsS,EAAAuO,EAAAK,EAAAlH,YAAAha,KAAAygB,EAAAD,YAAAvc,EAAAid,EAAAlH,YAAAha,MACA6D,EAAAyO,GAAA,QAbAmO,EAAAzJ,WAAArV,EAAAuC,SAAAsS,QAAA7U,EAAAyI,QAeA,MAAA9K,GACAqhB,EAAArhB,GAEAohB,GAAAS,GACAR,EAAA,KAAAF,GAIA,SAAA5c,EAAAI,EAAAmd,GAGA,KAAAX,EAAAH,MAAAnR,QAAAlL,GAKA,GAHAwc,EAAAH,MAAA5f,KAAAuD,GAGAA,KAAA+F,EACA0W,EACAO,EAAAhd,EAAA+F,EAAA/F,OAEAkd,EACAE,WAAA,aACAF,EACAF,EAAAhd,EAAA+F,EAAA/F,YAOA,GAAAyc,EAAA,CACA,IAAA/e,EACA,IACAA,EAAAsL,EAAAlJ,GAAAud,aAAArd,GAAArC,SAAA,QACA,MAAAtC,GAGA,YAFA8hB,GACAT,EAAArhB,IAGA2hB,EAAAhd,EAAAtC,SAEAwf,EACAV,EAAA5c,MAAAI,EAAA,SAAA3E,EAAAqC,KACAwf,EAEAhd,IAEA7E,EAEA8hB,EAEAD,GACAR,EAAA,KAAAF,GAFAE,EAAArhB,GAKA2hB,EAAAhd,EAAAtC,MAIA,IAAAwf,EAAA,EAIAlU,EAAA+D,SAAA/M,KACAA,EAAA,CAAAA,IACA,IAAA,IAAAqO,EAAAtS,EAAA,EAAAA,EAAAiE,EAAAlF,SAAAiB,GACAsS,EAAAmO,EAAAD,YAAA,GAAAvc,EAAAjE,MACA6D,EAAAyO,GAEA,OAAAoO,EACAD,GACAU,GACAR,EAAA,KAAAF,GACAxiB,IAgCA2V,EAAAvQ,UAAAwQ,SAAA,SAAA5P,EAAAC,GACA,IAAA+I,EAAAsU,OACA,MAAApgB,MAAA,iBACA,OAAAgC,KAAAuQ,KAAAzP,EAAAC,EAAAqc,IAMA3M,EAAAvQ,UAAAkU,WAAA,WACA,GAAApU,KAAAkd,SAAAthB,OACA,MAAAoC,MAAA,4BAAAgC,KAAAkd,SAAApS,IAAA,SAAAb,GACA,MAAA,WAAAA,EAAAqE,OAAA,QAAArE,EAAAmF,OAAA7E,WACA5M,KAAA,OACA,OAAAuP,EAAAhN,UAAAkU,WAAA9N,KAAAtG,OAIA,IAAAqe,EAAA,SAUA,SAAAC,EAAA9N,EAAAvG,GACA,IAAAsU,EAAAtU,EAAAmF,OAAAiF,OAAApK,EAAAqE,QACA,GAAAiQ,EAAA,CACA,IAAAC,EAAA,IAAArQ,EAAAlE,EAAAM,SAAAN,EAAA1C,GAAA0C,EAAA3C,KAAA2C,EAAAnB,KAAAhO,EAAAmP,EAAAlJ,SAIA,OAHAyd,EAAA3P,eAAA5E,GACA2E,eAAA4P,EACAD,EAAA3Q,IAAA4Q,GACA,GAWA/N,EAAAvQ,UAAA6U,EAAA,SAAAxC,GACA,GAAAA,aAAApE,EAEAoE,EAAAjE,SAAAxT,GAAAyX,EAAA3D,gBACA0P,EAAAte,EAAAuS,IACAvS,KAAAkd,SAAA3f,KAAAgV,QAEA,GAAAA,aAAA1I,EAEAwU,EAAApgB,KAAAsU,EAAAxL,QACAwL,EAAAnD,OAAAmD,EAAAxL,MAAAwL,EAAA5J,aAEA,KAAA4J,aAAAzB,GAAA,CAEA,GAAAyB,aAAAnE,EACA,IAAA,IAAAvR,EAAA,EAAAA,EAAAmD,KAAAkd,SAAAthB,QACA0iB,EAAAte,EAAAA,KAAAkd,SAAArgB,IACAmD,KAAAkd,SAAA3c,OAAA1D,EAAA,KAEAA,EACA,IAAA,IAAAQ,EAAA,EAAAA,EAAAkV,EAAAgB,YAAA3X,SAAAyB,EACA2C,KAAA+U,EAAAxC,EAAAW,EAAA7V,IACAghB,EAAApgB,KAAAsU,EAAAxL,QACAwL,EAAAnD,OAAAmD,EAAAxL,MAAAwL,KAcA9B,EAAAvQ,UAAA8U,EAAA,SAAAzC,GAGA,IAKAzW,EAPA,GAAAyW,aAAApE,EAEAoE,EAAAjE,SAAAxT,IACAyX,EAAA3D,gBACA2D,EAAA3D,eAAAQ,OAAAlB,OAAAqE,EAAA3D,gBACA2D,EAAA3D,eAAA,OAIA,GAFA9S,EAAAkE,KAAAkd,SAAAlR,QAAAuG,KAGAvS,KAAAkd,SAAA3c,OAAAzE,EAAA,SAIA,GAAAyW,aAAA1I,EAEAwU,EAAApgB,KAAAsU,EAAAxL,cACAwL,EAAAnD,OAAAmD,EAAAxL,WAEA,GAAAwL,aAAArF,EAAA,CAEA,IAAA,IAAArQ,EAAA,EAAAA,EAAA0V,EAAAgB,YAAA3X,SAAAiB,EACAmD,KAAAgV,EAAAzC,EAAAW,EAAArW,IAEAwhB,EAAApgB,KAAAsU,EAAAxL,cACAwL,EAAAnD,OAAAmD,EAAAxL,QAMA0J,EAAAL,EAAA,SAAAC,EAAAoO,EAAAC,GACAtQ,EAAAiC,EACAuB,EAAA6M,EACA5X,EAAA6X,I,qDCxWArjB,EAAAC,QAAA,I,wBCKAA,EA6BA0V,QAAA5V,EAAA,K,6BClCAC,EAAAC,QAAA0V,EAEA,IAAAlH,EAAA1O,EAAA,IAsCA,SAAA4V,EAAA2N,EAAAC,EAAAC,GAEA,GAAA,mBAAAF,EACA,MAAAtR,UAAA,8BAEAvD,EAAA/J,aAAAuG,KAAAtG,MAMAA,KAAA2e,QAAAA,EAMA3e,KAAA4e,mBAAAA,EAMA5e,KAAA6e,oBAAAA,IA1DA7N,EAAA9Q,UAAApB,OAAAiO,OAAAjD,EAAA/J,aAAAG,YAAA8M,YAAAgE,GAwEA9Q,UAAA4e,QAAA,SAAAA,EAAArF,EAAAsF,EAAAC,EAAAC,EAAAje,GAEA,IAAAie,EACA,MAAA5R,UAAA,6BAEA,IAAAiQ,EAAAtd,KACA,IAAAgB,EACA,OAAA8I,EAAAnJ,UAAAme,EAAAxB,EAAA7D,EAAAsF,EAAAC,EAAAC,GAEA,IAAA3B,EAAAqB,QAEA,OADAT,WAAA,WAAAld,EAAAhD,MAAA,mBAAA,GACAlD,EAGA,IACA,OAAAwiB,EAAAqB,QACAlF,EACAsF,EAAAzB,EAAAsB,iBAAA,kBAAA,UAAAK,GAAAzB,SACA,SAAArhB,EAAAsF,GAEA,GAAAtF,EAEA,OADAmhB,EAAA9c,KAAA,QAAArE,EAAAsd,GACAzY,EAAA7E,GAGA,GAAA,OAAAsF,EAEA,OADA6b,EAAArgB,KAAA,GACAnC,EAGA,KAAA2G,aAAAud,GACA,IACAvd,EAAAud,EAAA1B,EAAAuB,kBAAA,kBAAA,UAAApd,GACA,MAAAtF,GAEA,OADAmhB,EAAA9c,KAAA,QAAArE,EAAAsd,GACAzY,EAAA7E,GAKA,OADAmhB,EAAA9c,KAAA,OAAAiB,EAAAgY,GACAzY,EAAA,KAAAS,KAGA,MAAAtF,GAGA,OAFAmhB,EAAA9c,KAAA,QAAArE,EAAAsd,GACAyE,WAAA,WAAAld,EAAA7E,IAAA,GACArB,IASAkW,EAAA9Q,UAAAjD,IAAA,SAAAiiB,GAOA,OANAlf,KAAA2e,UACAO,GACAlf,KAAA2e,QAAA,KAAA,KAAA,MACA3e,KAAA2e,QAAA,KACA3e,KAAAQ,KAAA,OAAAH,OAEAL,O,6BC3IA3E,EAAAC,QAAA0V,EAGA,IAAA9D,EAAA9R,EAAA,MACA4V,EAAA9Q,UAAApB,OAAAiO,OAAAG,EAAAhN,YAAA8M,YAAAgE,GAAA/D,UAAA,UAEA,IAAAgE,EAAA7V,EAAA,IACA0O,EAAA1O,EAAA,IACAqW,EAAArW,EAAA,IAWA,SAAA4V,EAAAjK,EAAAhG,GACAmM,EAAA5G,KAAAtG,KAAA+G,EAAAhG,GAMAf,KAAA0T,QAAA,GAOA1T,KAAAmf,EAAA,KAyDA,SAAAhM,EAAAoG,GAEA,OADAA,EAAA4F,EAAA,KACA5F,EA1CAvI,EAAAzD,SAAA,SAAAxG,EAAAC,GACA,IAAAuS,EAAA,IAAAvI,EAAAjK,EAAAC,EAAAjG,SAEA,GAAAiG,EAAA0M,QACA,IAAA,IAAAD,EAAA3U,OAAAC,KAAAiI,EAAA0M,SAAA7W,EAAA,EAAAA,EAAA4W,EAAA7X,SAAAiB,EACA0c,EAAA3L,IAAAqD,EAAA1D,SAAAkG,EAAA5W,GAAAmK,EAAA0M,QAAAD,EAAA5W,MAIA,OAHAmK,EAAAC,QACAsS,EAAAlG,QAAArM,EAAAC,QACAsS,EAAApM,QAAAnG,EAAAmG,QACAoM,GAQAvI,EAAA9Q,UAAAuN,OAAA,SAAAC,GACA,IAAA0R,EAAAlS,EAAAhN,UAAAuN,OAAAnH,KAAAtG,KAAA0N,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA7D,EAAAiB,SAAA,CACA,UAAAqU,GAAAA,EAAAre,SAAAjG,EACA,UAAAoS,EAAA6F,YAAA/S,KAAAqf,aAAA3R,IAAA,GACA,SAAA0R,GAAAA,EAAAnY,QAAAnM,EACA,UAAA6S,EAAA3N,KAAAmN,QAAArS,KAUAgE,OAAAiQ,eAAAiC,EAAA9Q,UAAA,eAAA,CACAwJ,IAAA,WACA,OAAA1J,KAAAmf,IAAAnf,KAAAmf,EAAArV,EAAAwJ,QAAAtT,KAAA0T,aAYA1C,EAAA9Q,UAAAwJ,IAAA,SAAA3C,GACA,OAAA/G,KAAA0T,QAAA3M,IACAmG,EAAAhN,UAAAwJ,IAAApD,KAAAtG,KAAA+G,IAMAiK,EAAA9Q,UAAAkU,WAAA,WAEA,IADA,IAAAV,EAAA1T,KAAAqf,aACAxiB,EAAA,EAAAA,EAAA6W,EAAA9X,SAAAiB,EACA6W,EAAA7W,GAAAZ,UACA,OAAAiR,EAAAhN,UAAAjE,QAAAqK,KAAAtG,OAMAgR,EAAA9Q,UAAA0N,IAAA,SAAA2E,GAGA,GAAAvS,KAAA0J,IAAA6I,EAAAxL,MACA,MAAA/I,MAAA,mBAAAuU,EAAAxL,KAAA,QAAA/G,MAEA,OAAAuS,aAAAtB,EAGAkC,GAFAnT,KAAA0T,QAAAnB,EAAAxL,MAAAwL,GACAnD,OAAApP,MAGAkN,EAAAhN,UAAA0N,IAAAtH,KAAAtG,KAAAuS,IAMAvB,EAAA9Q,UAAAgO,OAAA,SAAAqE,GACA,GAAAA,aAAAtB,EAAA,CAGA,GAAAjR,KAAA0T,QAAAnB,EAAAxL,QAAAwL,EACA,MAAAvU,MAAAuU,EAAA,uBAAAvS,MAIA,cAFAA,KAAA0T,QAAAnB,EAAAxL,MACAwL,EAAAnD,OAAA,KACA+D,EAAAnT,MAEA,OAAAkN,EAAAhN,UAAAgO,OAAA5H,KAAAtG,KAAAuS,IAUAvB,EAAA9Q,UAAA6M,OAAA,SAAA4R,EAAAC,EAAAC,GAEA,IADA,IACApF,EADA6F,EAAA,IAAA7N,EAAAT,QAAA2N,EAAAC,EAAAC,GACAhiB,EAAA,EAAAA,EAAAmD,KAAAqf,aAAAzjB,SAAAiB,EAAA,CACA,IAAA0iB,EAAAzV,EAAAmQ,SAAAR,EAAAzZ,KAAAmf,EAAAtiB,IAAAZ,UAAA8K,MAAAzH,QAAA,WAAA,IACAggB,EAAAC,GAAAzV,EAAA5L,QAAA,CAAA,IAAA,KAAA4L,EAAA0V,WAAAD,GAAAA,EAAA,IAAAA,EAAAzV,CAAA,iCAAAA,CAAA,CACA2V,EAAAhG,EACAiG,EAAAjG,EAAA7G,oBAAAjD,KACAgQ,EAAAlG,EAAA5G,qBAAAlD,OAGA,OAAA2P,I,+CCpKAjkB,EAAAC,QAAAqW,EAEA,IAAAiO,EAAA,uBACAC,EAAA,kCACAC,EAAA,kCAEAC,EAAA,aACAC,EAAA,aACAC,EAAA,MACAC,EAAA,KACAC,EAAA,UAEAC,EAAA,CACAC,EAAA,KACAC,EAAA,KACA9jB,EAAA,KACAU,EAAA,MAUA,SAAAqjB,EAAAC,GACA,OAAAA,EAAAlhB,QAAA6gB,EAAA,SAAA5gB,EAAAC,GACA,OAAAA,GACA,IAAA,KACA,IAAA,GACA,OAAAA,EACA,QACA,OAAA4gB,EAAA5gB,IAAA,MAgEA,SAAAmS,EAAAnT,EAAA0Y,GAEA1Y,EAAAA,EAAAC,WAEA,IAAA5C,EAAA,EACAD,EAAA4C,EAAA5C,OACAic,EAAA,EACA4I,EAAA,KACAjH,EAAA,KACAkH,EAAA,EACAC,GAAA,EACAC,GAAA,EAEAC,EAAA,GAEAC,EAAA,KASA,SAAAnJ,EAAAoJ,GACA,OAAA/iB,MAAA,WAAA+iB,EAAA,UAAAlJ,EAAA,KA0BA,SAAAmJ,EAAA3e,GACA,OAAA7D,EAAAA,EAAA6D,IAAA7D,GAWA,SAAAyiB,EAAAjkB,EAAAC,EAAAikB,GACAT,EAAAjiB,EAAAA,EAAAxB,MAAAwB,GACAkiB,EAAA7I,EACA8I,GAAA,EACAC,EAAAM,EAOA,IACApjB,EADAqjB,EAAAnkB,GALAka,EACA,EAEA,GAIA,GACA,KAAAiK,EAAA,GACA,OAAArjB,EAAAU,EAAAA,EAAA2iB,IAAA3iB,IAAA,CACAmiB,GAAA,EACA,aAEA,MAAA7iB,GAAA,OAAAA,GAIA,IAHA,IAAAsjB,EAAA5iB,EACAyZ,UAAAjb,EAAAC,GACAyI,MAAAua,GACApjB,EAAA,EAAAA,EAAAukB,EAAAxlB,SAAAiB,EACAukB,EAAAvkB,GAAAukB,EAAAvkB,GACAyC,QAAA4X,EAAA8I,EAAAD,EAAA,IACAsB,OACA7H,EAAA4H,EACAzjB,KAAA,MACA0jB,OAGA,SAAAC,EAAAC,GACA,IAAAC,EAAAC,EAAAF,GAGAG,EAAAljB,EAAAyZ,UAAAsJ,EAAAC,GAIA,MADA,cAAAvjB,KAAAyjB,GAIA,SAAAD,EAAAE,GAGA,IADA,IAAAH,EAAAG,EACAH,EAAA5lB,GAAA,OAAAolB,EAAAQ,IACAA,IAEA,OAAAA,EAQA,SAAArK,IACA,GAAA,EAAA0J,EAAAjlB,OACA,OAAAilB,EAAAhb,QACA,GAAAib,EACA,OA3FA,WACA,IAAAc,EAAA,MAAAd,EAAAhB,EAAAD,EACA+B,EAAAC,UAAAhmB,EAAA,EACA,IAAAimB,EAAAF,EAAAG,KAAAvjB,GACA,IAAAsjB,EACA,MAAAnK,EAAA,UAIA,OAHA9b,EAAA+lB,EAAAC,UACAtkB,EAAAujB,GACAA,EAAA,KACAP,EAAAuB,EAAA,IAkFAhK,GACA,IAAAkK,EACApO,EACAqO,EACAjlB,EACAklB,EACAC,EAAA,IAAAtmB,EACA,EAAA,CACA,GAAAA,IAAAD,EACA,OAAA,KAEA,IADAomB,GAAA,EACA9B,EAAAjiB,KAAAgkB,EAAAjB,EAAAnlB,KAKA,GAJA,OAAAomB,IACAE,GAAA,IACAtK,KAEAhc,IAAAD,EACA,OAAA,KAGA,GAAA,MAAAolB,EAAAnlB,GAAA,CACA,KAAAA,IAAAD,EACA,MAAA+b,EAAA,WAEA,GAAA,MAAAqJ,EAAAnlB,GACA,GAAAqb,EAeA,CAIA,GADAgL,GAAA,EACAZ,EAFAtkB,EAAAnB,GAIA,IADAqmB,GAAA,GAEArmB,EAAA4lB,EAAA5lB,MACAD,GAIA0lB,IADAzlB,UAGAA,EAAAY,KAAAwgB,IAAArhB,EAAA6lB,EAAA5lB,GAAA,GAEAqmB,GACAjB,EAAAjkB,EAAAnB,EAAAsmB,GAEAtK,IACAmK,GAAA,MAnCA,CAIA,IAFAE,EAAA,MAAAlB,EAAAhkB,EAAAnB,EAAA,GAEA,OAAAmlB,IAAAnlB,IACA,GAAAA,IAAAD,EACA,OAAA,OAGAC,EACAqmB,GACAjB,EAAAjkB,EAAAnB,EAAA,EAAAsmB,KAEAtK,EACAmK,GAAA,MAuBA,CAAA,GAAA,OAAAC,EAAAjB,EAAAnlB,IAoBA,MAAA,IAlBAmB,EAAAnB,EAAA,EACAqmB,EAAAhL,GAAA,MAAA8J,EAAAhkB,GACA,GAIA,GAHA,OAAAilB,KACApK,IAEAhc,IAAAD,EACA,MAAA+b,EAAA,iBAEA/D,EAAAqO,EACAA,EAAAjB,EAAAnlB,GACA,MAAA+X,GAAA,MAAAqO,KACApmB,EACAqmB,GACAjB,EAAAjkB,EAAAnB,EAAA,EAAAsmB,GAEAH,GAAA,UAKAA,GAIA,IAAA/kB,EAAApB,EAGA,GAFA+jB,EAAAiC,UAAA,GACAjC,EAAA3hB,KAAA+iB,EAAA/jB,MAEA,KAAAA,EAAArB,IAAAgkB,EAAA3hB,KAAA+iB,EAAA/jB,OACAA,EACA,IAAA8Z,EAAAvY,EAAAyZ,UAAApc,EAAAA,EAAAoB,GAGA,MAFA,KAAA8Z,GAAA,KAAAA,IACA+J,EAAA/J,GACAA,EASA,SAAAxZ,EAAAwZ,GACA8J,EAAAtjB,KAAAwZ,GAQA,SAAAK,IACA,IAAAyJ,EAAAjlB,OAAA,CACA,IAAAmb,EAAAI,IACA,GAAA,OAAAJ,EACA,OAAA,KACAxZ,EAAAwZ,GAEA,OAAA8J,EAAA,GA+CA,OAAA/hB,OAAAiQ,eAAA,CACAoI,KAAAA,EACAC,KAAAA,EACA7Z,KAAAA,EACA8Z,KAxCA,SAAA+K,EAAAvV,GACA,IAAAwV,EAAAjL,IAEA,GADAiL,IAAAD,EAGA,OADAjL,KACA,EAEA,IAAAtK,EACA,MAAA8K,EAAA,UAAA0K,EAAA,OAAAD,EAAA,cACA,OAAA,GAgCA9K,KAvBA,SAAA0C,GACA,IAAAsI,EAAA,KAcA,OAbAtI,IAAAlf,EACA4lB,IAAA7I,EAAA,IAAAX,GAAA,MAAAuJ,GAAAE,KACA2B,EAAA1B,EAAApH,EAAA,OAIAkH,EAAA1G,GACA5C,IAEAsJ,IAAA1G,GAAA2G,IAAAzJ,GAAA,MAAAuJ,IACA6B,EAAA1B,EAAA,KAAApH,IAGA8I,IASA,OAAA,CACA5Y,IAAA,WAAA,OAAAmO,KAxWAlG,EAAA4O,SAAAA,G,wBCtCAllB,EAAAC,QAAA8S,EAGA,IAAAlB,EAAA9R,EAAA,MACAgT,EAAAlO,UAAApB,OAAAiO,OAAAG,EAAAhN,YAAA8M,YAAAoB,GAAAnB,UAAA,OAEA,IAAApD,EAAAzO,EAAA,IACA0V,EAAA1V,EAAA,IACA+S,EAAA/S,EAAA,IACA2V,EAAA3V,EAAA,IACA4V,EAAA5V,EAAA,IACA8V,EAAA9V,EAAA,IACAmW,EAAAnW,EAAA,IACAiW,EAAAjW,EAAA,IACA0O,EAAA1O,EAAA,IACAuV,EAAAvV,EAAA,IACAwV,EAAAxV,EAAA,IACAyV,EAAAzV,EAAA,IACAwO,EAAAxO,EAAA,IACA+V,EAAA/V,EAAA,IAUA,SAAAgT,EAAArH,EAAAhG,GACAmM,EAAA5G,KAAAtG,KAAA+G,EAAAhG,GAMAf,KAAAoH,OAAA,GAMApH,KAAAiI,OAAAnN,EAMAkF,KAAAkZ,WAAApe,EAMAkF,KAAAsN,SAAAxS,EAMAkF,KAAAkM,MAAApR,EAOAkF,KAAAuiB,EAAA,KAOAviB,KAAA+L,EAAA,KAOA/L,KAAAwiB,EAAA,KAOAxiB,KAAAyiB,EAAA,KA0HA,SAAAtP,EAAA7L,GAKA,OAJAA,EAAAib,EAAAjb,EAAAyE,EAAAzE,EAAAkb,EAAA,YACAlb,EAAAxK,cACAwK,EAAAzJ,cACAyJ,EAAAgL,OACAhL,EA5HAxI,OAAA+V,iBAAAzG,EAAAlO,UAAA,CAQAwiB,WAAA,CACAhZ,IAAA,WAGA,GAAA1J,KAAAuiB,EACA,OAAAviB,KAAAuiB,EAEAviB,KAAAuiB,EAAA,GACA,IAAA,IAAA9O,EAAA3U,OAAAC,KAAAiB,KAAAoH,QAAAvK,EAAA,EAAAA,EAAA4W,EAAA7X,SAAAiB,EAAA,CACA,IAAAoN,EAAAjK,KAAAoH,OAAAqM,EAAA5W,IACA0K,EAAA0C,EAAA1C,GAGA,GAAAvH,KAAAuiB,EAAAhb,GACA,MAAAvJ,MAAA,gBAAAuJ,EAAA,OAAAvH,MAEAA,KAAAuiB,EAAAhb,GAAA0C,EAEA,OAAAjK,KAAAuiB,IAUA3X,YAAA,CACAlB,IAAA,WACA,OAAA1J,KAAA+L,IAAA/L,KAAA+L,EAAAjC,EAAAwJ,QAAAtT,KAAAoH,WAUAub,YAAA,CACAjZ,IAAA,WACA,OAAA1J,KAAAwiB,IAAAxiB,KAAAwiB,EAAA1Y,EAAAwJ,QAAAtT,KAAAiI,WAUA0H,KAAA,CACAjG,IAAA,WACA,OAAA1J,KAAAyiB,IAAAziB,KAAA2P,KAAAvB,EAAAwU,oBAAA5iB,KAAAoO,KAEA0H,IAAA,SAAAnG,GAGA,IAAAzP,EAAAyP,EAAAzP,UACAA,aAAAgR,KACAvB,EAAAzP,UAAA,IAAAgR,GAAAlE,YAAA2C,EACA7F,EAAA2S,MAAA9M,EAAAzP,UAAAA,IAIAyP,EAAAsC,MAAAtC,EAAAzP,UAAA+R,MAAAjS,KAGA8J,EAAA2S,MAAA9M,EAAAuB,GAAA,GAEAlR,KAAAyiB,EAAA9S,EAIA,IADA,IAAA9S,EAAA,EACAA,EAAAmD,KAAA4K,YAAAhP,SAAAiB,EACAmD,KAAA+L,EAAAlP,GAAAZ,UAIA,IADA,IAAA4mB,EAAA,GACAhmB,EAAA,EAAAA,EAAAmD,KAAA2iB,YAAA/mB,SAAAiB,EACAgmB,EAAA7iB,KAAAwiB,EAAA3lB,GAAAZ,UAAA8K,MAAA,CACA2C,IAAAI,EAAA+L,YAAA7V,KAAAwiB,EAAA3lB,GAAAsL,OACA2N,IAAAhM,EAAAiM,YAAA/V,KAAAwiB,EAAA3lB,GAAAsL,QAEAtL,GACAiC,OAAA+V,iBAAAlF,EAAAzP,UAAA2iB,OAUAzU,EAAAwU,oBAAA,SAAAjY,GAIA,IAFA,IAEAV,EAFAD,EAAAF,EAAA5L,QAAA,CAAA,KAAAyM,EAAA5D,MAEAlK,EAAA,EAAAA,EAAA8N,EAAAC,YAAAhP,SAAAiB,GACAoN,EAAAU,EAAAoB,EAAAlP,IAAAiO,IAAAd,EACA,YAAAF,EAAAe,SAAAZ,EAAAlD,OACAkD,EAAAI,UAAAL,EACA,YAAAF,EAAAe,SAAAZ,EAAAlD,OACA,OAAAiD,EACA,wEADAA,CAEA,yBA6BAoE,EAAAb,SAAA,SAAAxG,EAAAC,GACA,IAAAM,EAAA,IAAA8G,EAAArH,EAAAC,EAAAjG,SACAuG,EAAA4R,WAAAlS,EAAAkS,WACA5R,EAAAgG,SAAAtG,EAAAsG,SAGA,IAFA,IAAAmG,EAAA3U,OAAAC,KAAAiI,EAAAI,QACAvK,EAAA,EACAA,EAAA4W,EAAA7X,SAAAiB,EACAyK,EAAAsG,UACA,IAAA5G,EAAAI,OAAAqM,EAAA5W,IAAAkL,QACAgJ,EACA5C,GADAZ,SACAkG,EAAA5W,GAAAmK,EAAAI,OAAAqM,EAAA5W,MAEA,GAAAmK,EAAAiB,OACA,IAAAwL,EAAA3U,OAAAC,KAAAiI,EAAAiB,QAAApL,EAAA,EAAAA,EAAA4W,EAAA7X,SAAAiB,EACAyK,EAAAsG,IAAAkD,EAAAvD,SAAAkG,EAAA5W,GAAAmK,EAAAiB,OAAAwL,EAAA5W,MACA,GAAAmK,EAAAC,OACA,IAAAwM,EAAA3U,OAAAC,KAAAiI,EAAAC,QAAApK,EAAA,EAAAA,EAAA4W,EAAA7X,SAAAiB,EAAA,CACA,IAAAoK,EAAAD,EAAAC,OAAAwM,EAAA5W,IACAyK,EAAAsG,KACA3G,EAAAM,KAAAzM,EACAqT,EACAlH,EAAAG,SAAAtM,EACAsT,EACAnH,EAAA0B,SAAA7N,EACA+O,EACA5C,EAAAyM,UAAA5Y,EACAkW,EACA9D,GAPAK,SAOAkG,EAAA5W,GAAAoK,IAWA,OARAD,EAAAkS,YAAAlS,EAAAkS,WAAAtd,SACA0L,EAAA4R,WAAAlS,EAAAkS,YACAlS,EAAAsG,UAAAtG,EAAAsG,SAAA1R,SACA0L,EAAAgG,SAAAtG,EAAAsG,UACAtG,EAAAkF,QACA5E,EAAA4E,OAAA,GACAlF,EAAAmG,UACA7F,EAAA6F,QAAAnG,EAAAmG,SACA7F,GAQA8G,EAAAlO,UAAAuN,OAAA,SAAAC,GACA,IAAA0R,EAAAlS,EAAAhN,UAAAuN,OAAAnH,KAAAtG,KAAA0N,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA7D,EAAAiB,SAAA,CACA,UAAAqU,GAAAA,EAAAre,SAAAjG,EACA,SAAAoS,EAAA6F,YAAA/S,KAAA2iB,YAAAjV,GACA,SAAAR,EAAA6F,YAAA/S,KAAA4K,YAAAqB,OAAA,SAAAgH,GAAA,OAAAA,EAAApE,iBAAAnB,IAAA,GACA,aAAA1N,KAAAkZ,YAAAlZ,KAAAkZ,WAAAtd,OAAAoE,KAAAkZ,WAAApe,EACA,WAAAkF,KAAAsN,UAAAtN,KAAAsN,SAAA1R,OAAAoE,KAAAsN,SAAAxS,EACA,QAAAkF,KAAAkM,OAAApR,EACA,SAAAskB,GAAAA,EAAAnY,QAAAnM,EACA,UAAA6S,EAAA3N,KAAAmN,QAAArS,KAOAsT,EAAAlO,UAAAkU,WAAA,WAEA,IADA,IAAAhN,EAAApH,KAAA4K,YAAA/N,EAAA,EACAA,EAAAuK,EAAAxL,QACAwL,EAAAvK,KAAAZ,UAEA,IADA,IAAAgM,EAAAjI,KAAA2iB,YAAA9lB,EAAA,EACAA,EAAAoL,EAAArM,QACAqM,EAAApL,KAAAZ,UACA,OAAAiR,EAAAhN,UAAAkU,WAAA9N,KAAAtG,OAMAoO,EAAAlO,UAAAwJ,IAAA,SAAA3C,GACA,OAAA/G,KAAAoH,OAAAL,IACA/G,KAAAiI,QAAAjI,KAAAiI,OAAAlB,IACA/G,KAAAiH,QAAAjH,KAAAiH,OAAAF,IACA,MAUAqH,EAAAlO,UAAA0N,IAAA,SAAA2E,GAEA,GAAAvS,KAAA0J,IAAA6I,EAAAxL,MACA,MAAA/I,MAAA,mBAAAuU,EAAAxL,KAAA,QAAA/G,MAEA,GAAAuS,aAAApE,GAAAoE,EAAAjE,SAAAxT,EAAA,CAMA,IAAAkF,KAAAuiB,GAAAviB,KAAA0iB,YAAAnQ,EAAAhL,IACA,MAAAvJ,MAAA,gBAAAuU,EAAAhL,GAAA,OAAAvH,MACA,GAAAA,KAAA+N,aAAAwE,EAAAhL,IACA,MAAAvJ,MAAA,MAAAuU,EAAAhL,GAAA,mBAAAvH,MACA,GAAAA,KAAAgO,eAAAuE,EAAAxL,MACA,MAAA/I,MAAA,SAAAuU,EAAAxL,KAAA,oBAAA/G,MAOA,OALAuS,EAAAnD,QACAmD,EAAAnD,OAAAlB,OAAAqE,IACAvS,KAAAoH,OAAAmL,EAAAxL,MAAAwL,GACA9D,QAAAzO,KACAuS,EAAAuB,MAAA9T,MACAmT,EAAAnT,MAEA,OAAAuS,aAAAzB,GACA9Q,KAAAiI,SACAjI,KAAAiI,OAAA,KACAjI,KAAAiI,OAAAsK,EAAAxL,MAAAwL,GACAuB,MAAA9T,MACAmT,EAAAnT,OAEAkN,EAAAhN,UAAA0N,IAAAtH,KAAAtG,KAAAuS,IAUAnE,EAAAlO,UAAAgO,OAAA,SAAAqE,GACA,GAAAA,aAAApE,GAAAoE,EAAAjE,SAAAxT,EAAA,CAIA,IAAAkF,KAAAoH,QAAApH,KAAAoH,OAAAmL,EAAAxL,QAAAwL,EACA,MAAAvU,MAAAuU,EAAA,uBAAAvS,MAKA,cAHAA,KAAAoH,OAAAmL,EAAAxL,MACAwL,EAAAnD,OAAA,KACAmD,EAAAwB,SAAA/T,MACAmT,EAAAnT,MAEA,GAAAuS,aAAAzB,EAAA,CAGA,IAAA9Q,KAAAiI,QAAAjI,KAAAiI,OAAAsK,EAAAxL,QAAAwL,EACA,MAAAvU,MAAAuU,EAAA,uBAAAvS,MAKA,cAHAA,KAAAiI,OAAAsK,EAAAxL,MACAwL,EAAAnD,OAAA,KACAmD,EAAAwB,SAAA/T,MACAmT,EAAAnT,MAEA,OAAAkN,EAAAhN,UAAAgO,OAAA5H,KAAAtG,KAAAuS,IAQAnE,EAAAlO,UAAA6N,aAAA,SAAAxG,GACA,OAAA2F,EAAAa,aAAA/N,KAAAsN,SAAA/F,IAQA6G,EAAAlO,UAAA8N,eAAA,SAAAjH,GACA,OAAAmG,EAAAc,eAAAhO,KAAAsN,SAAAvG,IAQAqH,EAAAlO,UAAA6M,OAAA,SAAAiF,GACA,OAAA,IAAAhS,KAAA2P,KAAAqC,IAOA5D,EAAAlO,UAAA4iB,MAAA,WAMA,IAFA,IAAAvY,EAAAvK,KAAAuK,SACA6B,EAAA,GACAvP,EAAA,EAAAA,EAAAmD,KAAA4K,YAAAhP,SAAAiB,EACAuP,EAAA7O,KAAAyC,KAAA+L,EAAAlP,GAAAZ,UAAAmO,cAGApK,KAAAlD,OAAA6T,EAAA3Q,KAAA2Q,CAAA,CACAU,OAAAA,EACAjF,MAAAA,EACAtC,KAAAA,IAEA9J,KAAAnC,OAAA+S,EAAA5Q,KAAA4Q,CAAA,CACAW,OAAAA,EACAnF,MAAAA,EACAtC,KAAAA,IAEA9J,KAAAsS,OAAAzB,EAAA7Q,KAAA6Q,CAAA,CACAzE,MAAAA,EACAtC,KAAAA,IAEA9J,KAAA0K,WAAAd,EAAAc,WAAA1K,KAAA4J,CAAA,CACAwC,MAAAA,EACAtC,KAAAA,IAEA9J,KAAA+K,SAAAnB,EAAAmB,SAAA/K,KAAA4J,CAAA,CACAwC,MAAAA,EACAtC,KAAAA,IAIA,IAAAiZ,EAAA5R,EAAA5G,GAaA,OAZAwY,KACAC,EAAAlkB,OAAAiO,OAAA/M,OAEA0K,WAAA1K,KAAA0K,WACA1K,KAAA0K,WAAAqY,EAAArY,WAAAjG,KAAAue,GAGAA,EAAAjY,SAAA/K,KAAA+K,SACA/K,KAAA+K,SAAAgY,EAAAhY,SAAAtG,KAAAue,IAIAhjB,MASAoO,EAAAlO,UAAApD,OAAA,SAAA2R,EAAAyD,GACA,OAAAlS,KAAA8iB,QAAAhmB,OAAA2R,EAAAyD,IASA9D,EAAAlO,UAAAiS,gBAAA,SAAA1D,EAAAyD,GACA,OAAAlS,KAAAlD,OAAA2R,EAAAyD,GAAAA,EAAA1L,IAAA0L,EAAA+Q,OAAA/Q,GAAAgR,UAWA9U,EAAAlO,UAAArC,OAAA,SAAAuU,EAAAxW,GACA,OAAAoE,KAAA8iB,QAAAjlB,OAAAuU,EAAAxW,IAUAwS,EAAAlO,UAAAmS,gBAAA,SAAAD,GAGA,OAFAA,aAAAb,IACAa,EAAAb,EAAAxE,OAAAqF,IACApS,KAAAnC,OAAAuU,EAAAA,EAAA2J,WAQA3N,EAAAlO,UAAAoS,OAAA,SAAA7D,GACA,OAAAzO,KAAA8iB,QAAAxQ,OAAA7D,IAQAL,EAAAlO,UAAAwK,WAAA,SAAA6H,GACA,OAAAvS,KAAA8iB,QAAApY,WAAA6H,IA4BAnE,EAAAlO,UAAA6K,SAAA,SAAA0D,EAAA1N,GACA,OAAAf,KAAA8iB,QAAA/X,SAAA0D,EAAA1N,IAkBAqN,EAAAwB,EAAA,SAAAuT,GACA,OAAA,SAAA7K,GACAxO,EAAAkG,aAAAsI,EAAA6K,M,iHCpkBA,IAAA/W,EAAA9Q,EAEAwO,EAAA1O,EAAA,IAEAukB,EAAA,CACA,SACA,QACA,QACA,SACA,SACA,UACA,WACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,SAGA,SAAAyD,EAAAza,EAAA9M,GACA,IAAAgB,EAAA,EAAAwmB,EAAA,GAEA,IADAxnB,GAAA,EACAgB,EAAA8L,EAAA/M,QAAAynB,EAAA1D,EAAA9iB,EAAAhB,IAAA8M,EAAA9L,KACA,OAAAwmB,EAuBAjX,EAAAE,MAAA8W,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,IAwBAhX,EAAAC,SAAA+W,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,EACA,GACAtZ,EAAA4F,WACA,OAaAtD,EAAAZ,KAAA4X,EAAA,CACA,EACA,EACA,EACA,EACA,GACA,GAmBAhX,EAAAO,OAAAyW,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,GAoBAhX,EAAAG,OAAA6W,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,K,6BC5LA,IAIAhV,EACAvE,EALAC,EAAAzO,EAAAC,QAAAF,EAAA,IAEAsW,EAAAtW,EAAA,IAKA0O,EAAA5L,QAAA9C,EAAA,GACA0O,EAAApJ,MAAAtF,EAAA,GACA0O,EAAAvE,KAAAnK,EAAA,GAMA0O,EAAAlJ,GAAAkJ,EAAAjJ,QAAA,MAOAiJ,EAAAwJ,QAAA,SAAAf,GACA,GAAAA,EAAA,CAIA,IAHA,IAAAxT,EAAAD,OAAAC,KAAAwT,GACAS,EAAAtX,MAAAqD,EAAAnD,QACAE,EAAA,EACAA,EAAAiD,EAAAnD,QACAoX,EAAAlX,GAAAyW,EAAAxT,EAAAjD,MACA,OAAAkX,EAEA,MAAA,IAQAlJ,EAAAiB,SAAA,SAAAiI,GAGA,IAFA,IAAAT,EAAA,GACAzW,EAAA,EACAA,EAAAkX,EAAApX,QAAA,CACA,IAAA0nB,EAAAtQ,EAAAlX,KACAqG,EAAA6Q,EAAAlX,KACAqG,IAAArH,IACAyX,EAAA+Q,GAAAnhB,GAEA,OAAAoQ,GAGA,IAAAgR,EAAA,MACAC,EAAA,KAOA1Z,EAAA0V,WAAA,SAAAzY,GACA,MAAA,uTAAA9I,KAAA8I,IAQA+C,EAAAe,SAAA,SAAAV,GACA,OAAA,YAAAlM,KAAAkM,IAAAL,EAAA0V,WAAArV,GACA,KAAAA,EAAA7K,QAAAikB,EAAA,QAAAjkB,QAAAkkB,EAAA,OAAA,KACA,IAAArZ,GAQAL,EAAAoQ,QAAA,SAAAsG,GACA,OAAAA,EAAA,IAAAA,IAAAiD,cAAAjD,EAAAvI,UAAA,IAGA,IAAAyL,EAAA,YAOA5Z,EAAA4N,UAAA,SAAA8I,GACA,OAAAA,EAAAvI,UAAA,EAAA,GACAuI,EAAAvI,UAAA,GACA3Y,QAAAokB,EAAA,SAAAnkB,EAAAC,GAAA,OAAAA,EAAAikB,iBASA3Z,EAAAmB,kBAAA,SAAA0Y,EAAArmB,GACA,OAAAqmB,EAAApc,GAAAjK,EAAAiK,IAWAuC,EAAAkG,aAAA,SAAAL,EAAAwT,GAGA,GAAAxT,EAAAsC,MAMA,OALAkR,GAAAxT,EAAAsC,MAAAlL,OAAAoc,IACArZ,EAAA8Z,aAAA1V,OAAAyB,EAAAsC,OACAtC,EAAAsC,MAAAlL,KAAAoc,EACArZ,EAAA8Z,aAAAhW,IAAA+B,EAAAsC,QAEAtC,EAAAsC,MAOA3K,EAAA,IAFA8G,EADAA,GACAhT,EAAA,KAEA+nB,GAAAxT,EAAA5I,MAKA,OAJA+C,EAAA8Z,aAAAhW,IAAAtG,GACAA,EAAAqI,KAAAA,EACA7Q,OAAAiQ,eAAAY,EAAA,QAAA,CAAAlQ,MAAA6H,EAAAuc,YAAA,IACA/kB,OAAAiQ,eAAAY,EAAAzP,UAAA,QAAA,CAAAT,MAAA6H,EAAAuc,YAAA,IACAvc,GAGA,IAAAwc,EAAA,EAOAha,EAAAmG,aAAA,SAAAsC,GAGA,GAAAA,EAAAN,MACA,OAAAM,EAAAN,MAMA,IAAAzE,EAAA,IAFA3D,EADAA,GACAzO,EAAA,KAEA,OAAA0oB,IAAAvR,GAGA,OAFAzI,EAAA8Z,aAAAhW,IAAAJ,GACA1O,OAAAiQ,eAAAwD,EAAA,QAAA,CAAA9S,MAAA+N,EAAAqW,YAAA,IACArW,GAWA1D,EAAA0L,YAAA,SAAAuO,EAAAxe,EAAA9F,GAcA,GAAA,iBAAAskB,EACA,MAAA1W,UAAA,yBACA,IAAA9H,EACA,MAAA8H,UAAA,0BAGA,OAnBA,SAAA2W,EAAAD,EAAAxe,EAAA9F,GACA,IAAA0U,EAAA5O,EAAAM,QASA,OARA,EAAAN,EAAA3J,OACAmoB,EAAA5P,GAAA6P,EAAAD,EAAA5P,IAAA,GAAA5O,EAAA9F,KAEAib,EAAAqJ,EAAA5P,MAEA1U,EAAA,GAAAkb,OAAAD,GAAAC,OAAAlb,IACAskB,EAAA5P,GAAA1U,GAEAskB,EASAC,CAAAD,EADAxe,EAAAA,EAAAG,MAAA,KACAjG,IASAX,OAAAiQ,eAAAjF,EAAA,eAAA,CACAJ,IAAA,WACA,OAAAgI,EAAA,YAAAA,EAAA,UAAA,IAAAtW,EAAA,U,iEC7MAC,EAAAC,QAAA4f,EAEA,IAAApR,EAAA1O,EAAA,IAUA,SAAA8f,EAAApX,EAAAC,GASA/D,KAAA8D,GAAAA,IAAA,EAMA9D,KAAA+D,GAAAA,IAAA,EAQA,IAAAkgB,EAAA/I,EAAA+I,KAAA,IAAA/I,EAAA,EAAA,GAEA+I,EAAArY,SAAA,WAAA,OAAA,GACAqY,EAAAC,SAAAD,EAAApH,SAAA,WAAA,OAAA7c,MACAikB,EAAAroB,OAAA,WAAA,OAAA,GAOAsf,EAAAiJ,SAAA,mBAOAjJ,EAAA5L,WAAA,SAAA7P,GACA,GAAA,IAAAA,EACA,OAAAwkB,EACA,IAAA3hB,EAAA7C,EAAA,EAGAqE,GADArE,EADA6C,GACA7C,EACAA,KAAA,EACAsE,GAAAtE,EAAAqE,GAAA,aAAA,EAUA,OATAxB,IACAyB,GAAAA,IAAA,EACAD,GAAAA,IAAA,EACA,aAAAA,IACAA,EAAA,EACA,aAAAC,IACAA,EAAA,KAGA,IAAAmX,EAAApX,EAAAC,IAQAmX,EAAAkJ,KAAA,SAAA3kB,GACA,GAAA,iBAAAA,EACA,OAAAyb,EAAA5L,WAAA7P,GACA,GAAAqK,EAAA+D,SAAApO,GAAA,CAEA,IAAAqK,EAAA6E,KAGA,OAAAuM,EAAA5L,WAAA4I,SAAAzY,EAAA,KAFAA,EAAAqK,EAAA6E,KAAA0V,WAAA5kB,GAIA,OAAAA,EAAAgM,KAAAhM,EAAAiM,KAAA,IAAAwP,EAAAzb,EAAAgM,MAAA,EAAAhM,EAAAiM,OAAA,GAAAuY,GAQA/I,EAAAhb,UAAA0L,SAAA,SAAAD,GACA,IAAAA,GAAA3L,KAAA+D,KAAA,GAAA,CACA,IAAAD,EAAA,GAAA9D,KAAA8D,KAAA,EACAC,GAAA/D,KAAA+D,KAAA,EAGA,QAAAD,EAAA,YADAC,GADAD,EACAC,EAAA,IAAA,EACAA,IAEA,OAAA/D,KAAA8D,GAAA,WAAA9D,KAAA+D,IAQAmX,EAAAhb,UAAAokB,OAAA,SAAA3Y,GACA,OAAA7B,EAAA6E,KACA,IAAA7E,EAAA6E,KAAA,EAAA3O,KAAA8D,GAAA,EAAA9D,KAAA+D,KAAA4H,GAEA,CAAAF,IAAA,EAAAzL,KAAA8D,GAAA4H,KAAA,EAAA1L,KAAA+D,GAAA4H,WAAAA,IAGA,IAAA5N,EAAAP,OAAA0C,UAAAnC,WAOAmd,EAAAqJ,SAAA,SAAAC,GACA,MAjFAtJ,qBAiFAsJ,EACAP,EACA,IAAA/I,GACAnd,EAAAuI,KAAAke,EAAA,GACAzmB,EAAAuI,KAAAke,EAAA,IAAA,EACAzmB,EAAAuI,KAAAke,EAAA,IAAA,GACAzmB,EAAAuI,KAAAke,EAAA,IAAA,MAAA,GAEAzmB,EAAAuI,KAAAke,EAAA,GACAzmB,EAAAuI,KAAAke,EAAA,IAAA,EACAzmB,EAAAuI,KAAAke,EAAA,IAAA,GACAzmB,EAAAuI,KAAAke,EAAA,IAAA,MAAA,IAQAtJ,EAAAhb,UAAAukB,OAAA,WACA,OAAAjnB,OAAAC,aACA,IAAAuC,KAAA8D,GACA9D,KAAA8D,KAAA,EAAA,IACA9D,KAAA8D,KAAA,GAAA,IACA9D,KAAA8D,KAAA,GACA,IAAA9D,KAAA+D,GACA/D,KAAA+D,KAAA,EAAA,IACA/D,KAAA+D,KAAA,GAAA,IACA/D,KAAA+D,KAAA,KAQAmX,EAAAhb,UAAAgkB,SAAA,WACA,IAAAQ,EAAA1kB,KAAA+D,IAAA,GAGA,OAFA/D,KAAA+D,KAAA/D,KAAA+D,IAAA,EAAA/D,KAAA8D,KAAA,IAAA4gB,KAAA,EACA1kB,KAAA8D,IAAA9D,KAAA8D,IAAA,EAAA4gB,KAAA,EACA1kB,MAOAkb,EAAAhb,UAAA2c,SAAA,WACA,IAAA6H,IAAA,EAAA1kB,KAAA8D,IAGA,OAFA9D,KAAA8D,KAAA9D,KAAA8D,KAAA,EAAA9D,KAAA+D,IAAA,IAAA2gB,KAAA,EACA1kB,KAAA+D,IAAA/D,KAAA+D,KAAA,EAAA2gB,KAAA,EACA1kB,MAOAkb,EAAAhb,UAAAtE,OAAA,WACA,IAAA+oB,EAAA3kB,KAAA8D,GACA8gB,GAAA5kB,KAAA8D,KAAA,GAAA9D,KAAA+D,IAAA,KAAA,EACA8gB,EAAA7kB,KAAA+D,KAAA,GACA,OAAA,GAAA8gB,EACA,GAAAD,EACAD,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,IAAA,EAAA,K,6BCrMA,IAAA/a,EAAAxO,EA2OA,SAAAmhB,EAAAsH,EAAAe,EAAA5V,GACA,IAAA,IAAAnQ,EAAAD,OAAAC,KAAA+lB,GAAAjoB,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACAknB,EAAAhlB,EAAAlC,MAAA/B,GAAAoU,IACA6U,EAAAhlB,EAAAlC,IAAAioB,EAAA/lB,EAAAlC,KACA,OAAAknB,EAoBA,SAAAgB,EAAAhe,GAEA,SAAAie,EAAAvW,EAAAuD,GAEA,KAAAhS,gBAAAglB,GACA,OAAA,IAAAA,EAAAvW,EAAAuD,GAKAlT,OAAAiQ,eAAA/O,KAAA,UAAA,CAAA0J,IAAA,WAAA,OAAA+E,KAGAzQ,MAAAinB,kBACAjnB,MAAAinB,kBAAAjlB,KAAAglB,GAEAlmB,OAAAiQ,eAAA/O,KAAA,QAAA,CAAAP,MAAAzB,QAAA6iB,OAAA,KAEA7O,GACAyK,EAAAzc,KAAAgS,GAWA,OARAgT,EAAA9kB,UAAApB,OAAAiO,OAAA/O,MAAAkC,YAAA8M,YAAAgY,EAEAlmB,OAAAiQ,eAAAiW,EAAA9kB,UAAA,OAAA,CAAAwJ,IAAA,WAAA,OAAA3C,KAEAie,EAAA9kB,UAAAzB,SAAA,WACA,OAAAuB,KAAA+G,KAAA,KAAA/G,KAAAyO,SAGAuW,EA9RAlb,EAAAnJ,UAAAvF,EAAA,GAGA0O,EAAAzN,OAAAjB,EAAA,GAGA0O,EAAA/J,aAAA3E,EAAA,GAGA0O,EAAAuS,MAAAjhB,EAAA,GAGA0O,EAAAjJ,QAAAzF,EAAA,GAGA0O,EAAAvD,KAAAnL,EAAA,IAGA0O,EAAAob,KAAA9pB,EAAA,GAGA0O,EAAAoR,SAAA9f,EAAA,IAOA0O,EAAAsU,UAAA,oBAAA+G,QACAA,QACAA,OAAArH,SACAqH,OAAArH,QAAAsH,UACAD,OAAArH,QAAAsH,SAAAC,MAOAvb,EAAAqb,OAAArb,EAAAsU,QAAA+G,QACA,oBAAAG,QAAAA,QACA,oBAAAhI,MAAAA,MACAtd,KAQA8J,EAAA4F,WAAA5Q,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,IAAA,GAOAzF,EAAA2F,YAAA3Q,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,IAAA,GAQAzF,EAAAgE,UAAApO,OAAAoO,WAAA,SAAArO,GACA,MAAA,iBAAAA,GAAA8lB,SAAA9lB,IAAAhD,KAAAkD,MAAAF,KAAAA,GAQAqK,EAAA+D,SAAA,SAAApO,GACA,MAAA,iBAAAA,GAAAA,aAAAjC,QAQAsM,EAAAyE,SAAA,SAAA9O,GACA,OAAAA,GAAA,iBAAAA,GAWAqK,EAAA0b,MAQA1b,EAAA2b,MAAA,SAAAxS,EAAA9I,GACA,IAAA1K,EAAAwT,EAAA9I,GACA,OAAA,MAAA1K,GAAAwT,EAAAsC,eAAApL,KACA,iBAAA1K,GAAA,GAAA/D,MAAAuY,QAAAxU,GAAAA,EAAAX,OAAAC,KAAAU,IAAA7D,SAeAkO,EAAAwR,OAAA,WACA,IACA,IAAAA,EAAAxR,EAAAjJ,QAAA,UAAAya,OAEA,OAAAA,EAAApb,UAAAwlB,UAAApK,EAAA,KACA,MAAAhW,GAEA,OAAA,MAPA,GAYAwE,EAAA6b,EAAA,KAGA7b,EAAA8b,EAAA,KAOA9b,EAAA0F,UAAA,SAAAqW,GAEA,MAAA,iBAAAA,EACA/b,EAAAwR,OACAxR,EAAA8b,EAAAC,GACA,IAAA/b,EAAApO,MAAAmqB,GACA/b,EAAAwR,OACAxR,EAAA6b,EAAAE,GACA,oBAAAlkB,WACAkkB,EACA,IAAAlkB,WAAAkkB,IAOA/b,EAAApO,MAAA,oBAAAiG,WAAAA,WAAAjG,MAeAoO,EAAA6E,KAAA7E,EAAAqb,OAAAW,SAAAhc,EAAAqb,OAAAW,QAAAnX,MACA7E,EAAAqb,OAAAxW,MACA7E,EAAAjJ,QAAA,QAOAiJ,EAAAic,OAAA,mBAOAjc,EAAAkc,QAAA,wBAOAlc,EAAAmc,QAAA,6CAOAnc,EAAAoc,WAAA,SAAAzmB,GACA,OAAAA,EACAqK,EAAAoR,SAAAkJ,KAAA3kB,GAAAglB,SACA3a,EAAAoR,SAAAiJ,UASAra,EAAAqc,aAAA,SAAA3B,EAAA7Y,GACA+P,EAAA5R,EAAAoR,SAAAqJ,SAAAC,GACA,OAAA1a,EAAA6E,KACA7E,EAAA6E,KAAAyX,SAAA1K,EAAA5X,GAAA4X,EAAA3X,GAAA4H,GACA+P,EAAA9P,WAAAD,IAkBA7B,EAAA2S,MAAAA,EAOA3S,EAAAmQ,QAAA,SAAAuG,GACA,OAAAA,EAAA,IAAAA,IAAAhS,cAAAgS,EAAAvI,UAAA,IA0CAnO,EAAAib,SAAAA,EAmBAjb,EAAAuc,cAAAtB,EAAA,iBAoBAjb,EAAA+L,YAAA,SAAAH,GAEA,IADA,IAAA4Q,EAAA,GACAzpB,EAAA,EAAAA,EAAA6Y,EAAA9Z,SAAAiB,EACAypB,EAAA5Q,EAAA7Y,IAAA,EAOA,OAAA,WACA,IAAA,IAAAkC,EAAAD,OAAAC,KAAAiB,MAAAnD,EAAAkC,EAAAnD,OAAA,GAAA,EAAAiB,IAAAA,EACA,GAAA,IAAAypB,EAAAvnB,EAAAlC,KAAAmD,KAAAjB,EAAAlC,MAAA/B,GAAA,OAAAkF,KAAAjB,EAAAlC,IACA,OAAAkC,EAAAlC,KAiBAiN,EAAAiM,YAAA,SAAAL,GAQA,OAAA,SAAA3O,GACA,IAAA,IAAAlK,EAAA,EAAAA,EAAA6Y,EAAA9Z,SAAAiB,EACA6Y,EAAA7Y,KAAAkK,UACA/G,KAAA0V,EAAA7Y,MAoBAiN,EAAA4D,cAAA,CACA6Y,MAAA/oB,OACAgpB,MAAAhpB,OACAqO,MAAArO,OACAwJ,MAAA,GAIA8C,EAAAsG,EAAA,WACA,IAAAkL,EAAAxR,EAAAwR,OAEAA,GAMAxR,EAAA6b,EAAArK,EAAA8I,OAAAziB,WAAAyiB,MAAA9I,EAAA8I,MAEA,SAAA3kB,EAAAgnB,GACA,OAAA,IAAAnL,EAAA7b,EAAAgnB,IAEA3c,EAAA8b,EAAAtK,EAAAoL,aAEA,SAAAxgB,GACA,OAAA,IAAAoV,EAAApV,KAbA4D,EAAA6b,EAAA7b,EAAA8b,EAAA,O,2DCpZAvqB,EAAAC,QAwHA,SAAAqP,GAGA,IAAAX,EAAAF,EAAA5L,QAAA,CAAA,KAAAyM,EAAA5D,KAAA,UAAA+C,CACA,oCADAA,CAEA,WAAA,mBACA7B,EAAA0C,EAAAgY,YACAgE,EAAA,GACA1e,EAAArM,QAAAoO,EACA,YAEA,IAAA,IAAAnN,EAAA,EAAAA,EAAA8N,EAAAC,YAAAhP,SAAAiB,EAAA,CACA,IA2BA+pB,EA3BA3c,EAAAU,EAAAoB,EAAAlP,GAAAZ,UACAkQ,EAAA,IAAArC,EAAAe,SAAAZ,EAAAlD,MAEAkD,EAAA4C,UAAA7C,EACA,sCAAAmC,EAAAlC,EAAAlD,MAGAkD,EAAAa,KAAAd,EACA,yBAAAmC,EADAnC,CAEA,WAAA6c,EAAA5c,EAAA,UAFAD,CAGA,wBAAAmC,EAHAnC,CAIA,gCAxDA,SAAAA,EAAAC,EAAAkC,GAEA,OAAAlC,EAAAlC,SACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAiC,EACA,6BAAAmC,EADAnC,CAEA,WAAA6c,EAAA5c,EAAA,gBACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,6BAAAmC,EADAnC,CAEA,WAAA6c,EAAA5c,EAAA,qBACA,MACA,IAAA,OAAAD,EACA,4BAAAmC,EADAnC,CAEA,WAAA6c,EAAA5c,EAAA,iBAoCA6c,CAAA9c,EAAAC,EAAA,QACA8c,EAAA/c,EAAAC,EAAApN,EAAAsP,EAAA,SAAA4a,CACA,MAGA9c,EAAAI,UAAAL,EACA,yBAAAmC,EADAnC,CAEA,WAAA6c,EAAA5c,EAAA,SAFAD,CAGA,gCAAAmC,GACA4a,EAAA/c,EAAAC,EAAApN,EAAAsP,EAAA,MAAA4a,CACA,OAIA9c,EAAAoB,SACAub,EAAA9c,EAAAe,SAAAZ,EAAAoB,OAAAtE,MACA,IAAA4f,EAAA1c,EAAAoB,OAAAtE,OAAAiD,EACA,cAAA4c,EADA5c,CAEA,WAAAC,EAAAoB,OAAAtE,KAAA,qBACA4f,EAAA1c,EAAAoB,OAAAtE,MAAA,EACAiD,EACA,QAAA4c,IAEAG,EAAA/c,EAAAC,EAAApN,EAAAsP,IAEAlC,EAAA4C,UAAA7C,EACA,KAEA,OAAAA,EACA,gBA3KA,IAAAH,EAAAzO,EAAA,IACA0O,EAAA1O,EAAA,IAEA,SAAAyrB,EAAA5c,EAAAmY,GACA,OAAAnY,EAAAlD,KAAA,KAAAqb,GAAAnY,EAAAI,UAAA,UAAA+X,EAAA,KAAAnY,EAAAa,KAAA,WAAAsX,EAAA,MAAAnY,EAAAlC,QAAA,IAAA,IAAA,YAYA,SAAAgf,EAAA/c,EAAAC,EAAAC,EAAAiC,GAEA,GAAAlC,EAAAG,aACA,GAAAH,EAAAG,wBAAAP,EAAA,CAAAG,EACA,cAAAmC,EADAnC,CAEA,WAFAA,CAGA,WAAA6c,EAAA5c,EAAA,eACA,IAAA,IAAAlL,EAAAD,OAAAC,KAAAkL,EAAAG,aAAAzB,QAAAtL,EAAA,EAAAA,EAAA0B,EAAAnD,SAAAyB,EAAA2M,EACA,WAAAC,EAAAG,aAAAzB,OAAA5J,EAAA1B,KACA2M,EACA,QADAA,CAEA,UAEAA,EACA,IADAA,CAEA,8BAAAE,EAAAiC,EAFAnC,CAGA,QAHAA,CAIA,aAAAC,EAAAlD,KAAA,IAJAiD,CAKA,UAGA,OAAAC,EAAA3C,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAA0C,EACA,0BAAAmC,EADAnC,CAEA,WAAA6c,EAAA5c,EAAA,YACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,kFAAAmC,EAAAA,EAAAA,EAAAA,EADAnC,CAEA,WAAA6c,EAAA5c,EAAA,iBACA,MACA,IAAA,QACA,IAAA,SAAAD,EACA,2BAAAmC,EADAnC,CAEA,WAAA6c,EAAA5c,EAAA,WACA,MACA,IAAA,OAAAD,EACA,4BAAAmC,EADAnC,CAEA,WAAA6c,EAAA5c,EAAA,YACA,MACA,IAAA,SAAAD,EACA,yBAAAmC,EADAnC,CAEA,WAAA6c,EAAA5c,EAAA,WACA,MACA,IAAA,QAAAD,EACA,4DAAAmC,EAAAA,EAAAA,EADAnC,CAEA,WAAA6c,EAAA5c,EAAA,WAIA,OAAAD,I,mCCrEA,IAAAmH,EAAA7V,EAEA4V,EAAA9V,EAAA,IA6BA+V,EAAA,wBAAA,CAEAzG,WAAA,SAAA6H,GAGA,GAAAA,GAAAA,EAAA,SAAA,CAEA,IAAAxL,EAAAwL,EAAA,SAAA0F,UAAA,EAAA1F,EAAA,SAAAqL,YAAA,MACAtW,EAAAtH,KAAAqU,OAAAtN,GAEA,GAAAO,EAAA,CAEAD,EAAA,MAAAkL,EAAA,SAAA,IAAAA,IACAA,EAAA,SAAA+H,OAAA,GAAA/H,EAAA,SAKA,OAHAlL,EAAA2E,QAAA,OACA3E,EAAA,IAAAA,GAEArH,KAAA+M,OAAA,CACA1F,SAAAA,EACA5H,MAAA6H,EAAAxK,OAAAwK,EAAAoD,WAAA6H,IAAAiL,YAKA,OAAAxd,KAAA0K,WAAA6H,IAGAxH,SAAA,SAAA0D,EAAA1N,GAGA,IAUAuG,EATA1B,EAAA,GACAmB,EAAA,GAeA,GAZAhG,GAAAA,EAAAiG,MAAAyH,EAAApH,UAAAoH,EAAAhP,QAEAsH,EAAA0H,EAAApH,SAAA4Q,UAAA,EAAAxJ,EAAApH,SAAAuW,YAAA,MAEAhY,EAAA6I,EAAApH,SAAA4Q,UAAA,EAAA,EAAAxJ,EAAApH,SAAAuW,YAAA,OACAtW,EAAAtH,KAAAqU,OAAAtN,MAGA0H,EAAAnH,EAAAzJ,OAAA4Q,EAAAhP,SAIAgP,aAAAzO,KAAA2P,QAAAlB,aAAAyC,GAaA,OAAAlR,KAAA+K,SAAA0D,EAAA1N,GAZAwR,EAAA9D,EAAAwD,MAAAlH,SAAA0D,EAAA1N,GACAimB,EAAA,MAAAvY,EAAAwD,MAAA1H,SAAA,GACAkE,EAAAwD,MAAA1H,SAAA+P,OAAA,GAAA7L,EAAAwD,MAAA1H,SAOA,OADAgI,EAAA,SADAxL,GAFAnB,EADA,KAAAA,EAtBA,uBAyBAA,GAAAohB,EAEAzU,K,6BC/FAlX,EAAAC,QAAA+V,EAEA,IAEAC,EAFAxH,EAAA1O,EAAA,IAIA8f,EAAApR,EAAAoR,SACA7e,EAAAyN,EAAAzN,OACAkK,EAAAuD,EAAAvD,KAWA,SAAA0gB,EAAA1rB,EAAAiL,EAAArE,GAMAnC,KAAAzE,GAAAA,EAMAyE,KAAAwG,IAAAA,EAMAxG,KAAAmX,KAAArc,EAMAkF,KAAAmC,IAAAA,EAIA,SAAA+kB,KAUA,SAAAC,EAAAjV,GAMAlS,KAAAuX,KAAArF,EAAAqF,KAMAvX,KAAAonB,KAAAlV,EAAAkV,KAMApnB,KAAAwG,IAAA0L,EAAA1L,IAMAxG,KAAAmX,KAAAjF,EAAAmV,OAQA,SAAAhW,IAMArR,KAAAwG,IAAA,EAMAxG,KAAAuX,KAAA,IAAA0P,EAAAC,EAAA,EAAA,GAMAlnB,KAAAonB,KAAApnB,KAAAuX,KAMAvX,KAAAqnB,OAAA,KASA,SAAAta,IACA,OAAAjD,EAAAwR,OACA,WACA,OAAAjK,EAAAtE,OAAA,WACA,OAAA,IAAAuE,OAIA,WACA,OAAA,IAAAD,GAuCA,SAAAiW,EAAAnlB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EAoBA,SAAAolB,EAAA/gB,EAAArE,GACAnC,KAAAwG,IAAAA,EACAxG,KAAAmX,KAAArc,EACAkF,KAAAmC,IAAAA,EA8CA,SAAAqlB,EAAArlB,EAAAC,EAAAC,GACA,KAAAF,EAAA4B,IACA3B,EAAAC,KAAA,IAAAF,EAAA2B,GAAA,IACA3B,EAAA2B,IAAA3B,EAAA2B,KAAA,EAAA3B,EAAA4B,IAAA,MAAA,EACA5B,EAAA4B,MAAA,EAEA,KAAA,IAAA5B,EAAA2B,IACA1B,EAAAC,KAAA,IAAAF,EAAA2B,GAAA,IACA3B,EAAA2B,GAAA3B,EAAA2B,KAAA,EAEA1B,EAAAC,KAAAF,EAAA2B,GA2CA,SAAA2jB,EAAAtlB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GA7JAkP,EAAAtE,OAAAA,IAOAsE,EAAApL,MAAA,SAAAC,GACA,OAAA,IAAA4D,EAAApO,MAAAwK,IAKA4D,EAAApO,QAAAA,QACA2V,EAAApL,MAAA6D,EAAAob,KAAA7T,EAAApL,MAAA6D,EAAApO,MAAAwE,UAAA4b,WAUAzK,EAAAnR,UAAAwnB,EAAA,SAAAnsB,EAAAiL,EAAArE,GAGA,OAFAnC,KAAAonB,KAAApnB,KAAAonB,KAAAjQ,KAAA,IAAA8P,EAAA1rB,EAAAiL,EAAArE,GACAnC,KAAAwG,KAAAA,EACAxG,OA8BAunB,EAAArnB,UAAApB,OAAAiO,OAAAka,EAAA/mB,YACA3E,GAxBA,SAAA4G,EAAAC,EAAAC,GACA,KAAA,IAAAF,GACAC,EAAAC,KAAA,IAAAF,EAAA,IACAA,KAAA,EAEAC,EAAAC,GAAAF,GA0BAkP,EAAAnR,UAAA6b,OAAA,SAAAtc,GAWA,OARAO,KAAAwG,MAAAxG,KAAAonB,KAAApnB,KAAAonB,KAAAjQ,KAAA,IAAAoQ,GACA9nB,KAAA,GACA,IAAA,EACAA,EAAA,MAAA,EACAA,EAAA,QAAA,EACAA,EAAA,UAAA,EACA,EACAA,IAAA+G,IACAxG,MASAqR,EAAAnR,UAAA8b,MAAA,SAAAvc,GACA,OAAAA,EAAA,EACAO,KAAA0nB,EAAAF,EAAA,GAAAtM,EAAA5L,WAAA7P,IACAO,KAAA+b,OAAAtc,IAQA4R,EAAAnR,UAAA+b,OAAA,SAAAxc,GACA,OAAAO,KAAA+b,QAAAtc,GAAA,EAAAA,GAAA,MAAA,IAkCA4R,EAAAnR,UAAAwc,MAZArL,EAAAnR,UAAAyc,OAAA,SAAAld,GACAic,EAAAR,EAAAkJ,KAAA3kB,GACA,OAAAO,KAAA0nB,EAAAF,EAAA9L,EAAA9f,SAAA8f,IAkBArK,EAAAnR,UAAA0c,OAAA,SAAAnd,GACAic,EAAAR,EAAAkJ,KAAA3kB,GAAAykB,WACA,OAAAlkB,KAAA0nB,EAAAF,EAAA9L,EAAA9f,SAAA8f,IAQArK,EAAAnR,UAAAgc,KAAA,SAAAzc,GACA,OAAAO,KAAA0nB,EAAAJ,EAAA,EAAA7nB,EAAA,EAAA,IAyBA4R,EAAAnR,UAAAkc,SAVA/K,EAAAnR,UAAAic,QAAA,SAAA1c,GACA,OAAAO,KAAA0nB,EAAAD,EAAA,EAAAhoB,IAAA,IA6BA4R,EAAAnR,UAAA6c,SAZA1L,EAAAnR,UAAA4c,QAAA,SAAArd,GACAic,EAAAR,EAAAkJ,KAAA3kB,GACA,OAAAO,KAAA0nB,EAAAD,EAAA,EAAA/L,EAAA5X,IAAA4jB,EAAAD,EAAA,EAAA/L,EAAA3X,KAkBAsN,EAAAnR,UAAAmc,MAAA,SAAA5c,GACA,OAAAO,KAAA0nB,EAAA5d,EAAAuS,MAAAhY,aAAA,EAAA5E,IASA4R,EAAAnR,UAAAoc,OAAA,SAAA7c,GACA,OAAAO,KAAA0nB,EAAA5d,EAAAuS,MAAAtX,cAAA,EAAAtF,IAGA,IAAAkoB,EAAA7d,EAAApO,MAAAwE,UAAA4V,IACA,SAAA3T,EAAAC,EAAAC,GACAD,EAAA0T,IAAA3T,EAAAE,IAGA,SAAAF,EAAAC,EAAAC,GACA,IAAA,IAAAxF,EAAA,EAAAA,EAAAsF,EAAAvG,SAAAiB,EACAuF,EAAAC,EAAAxF,GAAAsF,EAAAtF,IAQAwU,EAAAnR,UAAA2L,MAAA,SAAApM,GACA,IAIA2C,EAJAoE,EAAA/G,EAAA7D,SAAA,EACA,OAAA4K,GAEAsD,EAAA+D,SAAApO,KACA2C,EAAAiP,EAAApL,MAAAO,EAAAnK,EAAAT,OAAA6D,IACApD,EAAAwB,OAAA4B,EAAA2C,EAAA,GACA3C,EAAA2C,GAEApC,KAAA+b,OAAAvV,GAAAkhB,EAAAC,EAAAnhB,EAAA/G,IANAO,KAAA0nB,EAAAJ,EAAA,EAAA,IAcAjW,EAAAnR,UAAA5D,OAAA,SAAAmD,GACA,IAAA+G,EAAAD,EAAA3K,OAAA6D,GACA,OAAA+G,EACAxG,KAAA+b,OAAAvV,GAAAkhB,EAAAnhB,EAAAG,MAAAF,EAAA/G,GACAO,KAAA0nB,EAAAJ,EAAA,EAAA,IAQAjW,EAAAnR,UAAA+iB,KAAA,WAIA,OAHAjjB,KAAAqnB,OAAA,IAAAF,EAAAnnB,MACAA,KAAAuX,KAAAvX,KAAAonB,KAAA,IAAAH,EAAAC,EAAA,EAAA,GACAlnB,KAAAwG,IAAA,EACAxG,MAOAqR,EAAAnR,UAAA0nB,MAAA,WAUA,OATA5nB,KAAAqnB,QACArnB,KAAAuX,KAAAvX,KAAAqnB,OAAA9P,KACAvX,KAAAonB,KAAApnB,KAAAqnB,OAAAD,KACApnB,KAAAwG,IAAAxG,KAAAqnB,OAAA7gB,IACAxG,KAAAqnB,OAAArnB,KAAAqnB,OAAAlQ,OAEAnX,KAAAuX,KAAAvX,KAAAonB,KAAA,IAAAH,EAAAC,EAAA,EAAA,GACAlnB,KAAAwG,IAAA,GAEAxG,MAOAqR,EAAAnR,UAAAgjB,OAAA,WACA,IAAA3L,EAAAvX,KAAAuX,KACA6P,EAAApnB,KAAAonB,KACA5gB,EAAAxG,KAAAwG,IAOA,OANAxG,KAAA4nB,QAAA7L,OAAAvV,GACAA,IACAxG,KAAAonB,KAAAjQ,KAAAI,EAAAJ,KACAnX,KAAAonB,KAAAA,EACApnB,KAAAwG,KAAAA,GAEAxG,MAOAqR,EAAAnR,UAAAsd,OAAA,WAIA,IAHA,IAAAjG,EAAAvX,KAAAuX,KAAAJ,KACA/U,EAAApC,KAAAgN,YAAA/G,MAAAjG,KAAAwG,KACAnE,EAAA,EACAkV,GACAA,EAAAhc,GAAAgc,EAAApV,IAAAC,EAAAC,GACAA,GAAAkV,EAAA/Q,IACA+Q,EAAAA,EAAAJ,KAGA,OAAA/U,GAGAiP,EAAAjB,EAAA,SAAAyX,GACAvW,EAAAuW,EACAxW,EAAAtE,OAAAA,IACAuE,EAAAlB,M,6BC9cA/U,EAAAC,QAAAgW,EAGA,IAAAD,EAAAjW,EAAA,KACAkW,EAAApR,UAAApB,OAAAiO,OAAAsE,EAAAnR,YAAA8M,YAAAsE,EAEA,IAAAxH,EAAA1O,EAAA,IAQA,SAAAkW,IACAD,EAAA/K,KAAAtG,MAwCA,SAAA8nB,EAAA3lB,EAAAC,EAAAC,GACAF,EAAAvG,OAAA,GACAkO,EAAAvD,KAAAG,MAAAvE,EAAAC,EAAAC,GACAD,EAAAsjB,UACAtjB,EAAAsjB,UAAAvjB,EAAAE,GAEAD,EAAAsE,MAAAvE,EAAAE,GA3CAiP,EAAAlB,EAAA,WAOAkB,EAAArL,MAAA6D,EAAA8b,EAEAtU,EAAAyW,iBAAAje,EAAAwR,QAAAxR,EAAAwR,OAAApb,qBAAAyB,YAAA,QAAAmI,EAAAwR,OAAApb,UAAA4V,IAAA/O,KACA,SAAA5E,EAAAC,EAAAC,GACAD,EAAA0T,IAAA3T,EAAAE,IAIA,SAAAF,EAAAC,EAAAC,GACA,GAAAF,EAAA6lB,KACA7lB,EAAA6lB,KAAA5lB,EAAAC,EAAA,EAAAF,EAAAvG,aACA,IAAA,IAAAiB,EAAA,EAAAA,EAAAsF,EAAAvG,QACAwG,EAAAC,KAAAF,EAAAtF,OAQAyU,EAAApR,UAAA2L,MAAA,SAAApM,GAGA,IAAA+G,GADA/G,EADAqK,EAAA+D,SAAApO,GACAqK,EAAA6b,EAAAlmB,EAAA,UACAA,GAAA7D,SAAA,EAIA,OAHAoE,KAAA+b,OAAAvV,GACAA,GACAxG,KAAA0nB,EAAApW,EAAAyW,iBAAAvhB,EAAA/G,GACAO,MAeAsR,EAAApR,UAAA5D,OAAA,SAAAmD,GACA,IAAA+G,EAAAsD,EAAAwR,OAAA2M,WAAAxoB,GAIA,OAHAO,KAAA+b,OAAAvV,GACAA,GACAxG,KAAA0nB,EAAAI,EAAAthB,EAAA/G,GACAO,MAWAsR,EAAAlB,qB3CpFApV,KAAAC,OAcAC,EAPA,SAAAgtB,EAAAnhB,GACA,IAAAohB,EAAAntB,EAAA+L,GAGA,OAFAohB,GACAptB,EAAAgM,GAAA,GAAAT,KAAA6hB,EAAAntB,EAAA+L,GAAA,CAAAzL,QAAA,IAAA4sB,EAAAC,EAAAA,EAAA7sB,SACA6sB,EAAA7sB,QAGA4sB,CAAAjtB,EAAA,IAGAC,EAAA4O,KAAAqb,OAAAjqB,SAAAA,EAGA,mBAAA8Y,QAAAA,OAAAoU,KACApU,OAAA,CAAA,QAAA,SAAArF,GAKA,OAJAA,GAAAA,EAAA0Z,SACAntB,EAAA4O,KAAA6E,KAAAA,EACAzT,EAAAkW,aAEAlW,IAIA,iBAAAG,QAAAA,QAAAA,OAAAC,UACAD,OAAAC,QAAAJ,GA/BA", "file": "protobuf.min.js", "sourcesContent": ["(function prelude(modules, cache, entries) {\n\n    // This is the prelude used to bundle protobuf.js for the browser. Wraps up the CommonJS\n    // sources through a conflict-free require shim and is again wrapped within an iife that\n    // provides a minification-friendly `undefined` var plus a global \"use strict\" directive\n    // so that minification can remove the directives of each module.\n\n    function $require(name) {\n        var $module = cache[name];\n        if (!$module)\n            modules[name][0].call($module = cache[name] = { exports: {} }, $require, $module, $module.exports);\n        return $module.exports;\n    }\n\n    var protobuf = $require(entries[0]);\n\n    // Expose globally\n    protobuf.util.global.protobuf = protobuf;\n\n    // Be nice to AMD\n    if (typeof define === \"function\" && define.amd)\n        define([\"long\"], function(Long) {\n            if (Long && Long.isLong) {\n                protobuf.util.Long = Long;\n                protobuf.configure();\n            }\n            return protobuf;\n        });\n\n    // Be nice to CommonJS\n    if (typeof module === \"object\" && module && module.exports)\n        module.exports = protobuf;\n\n})/* end of prelude */", "\"use strict\";\r\nmodule.exports = asPromise;\r\n\r\n/**\r\n * Callback as used by {@link util.asPromise}.\r\n * @typedef asPromiseCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {...*} params Additional arguments\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Returns a promise from a node-style callback function.\r\n * @memberof util\r\n * @param {asPromiseCallback} fn Function to call\r\n * @param {*} ctx Function context\r\n * @param {...*} params Function arguments\r\n * @returns {Promise<*>} Promisified function\r\n */\r\nfunction asPromise(fn, ctx/*, varargs */) {\r\n    var params  = new Array(arguments.length - 1),\r\n        offset  = 0,\r\n        index   = 2,\r\n        pending = true;\r\n    while (index < arguments.length)\r\n        params[offset++] = arguments[index++];\r\n    return new Promise(function executor(resolve, reject) {\r\n        params[offset] = function callback(err/*, varargs */) {\r\n            if (pending) {\r\n                pending = false;\r\n                if (err)\r\n                    reject(err);\r\n                else {\r\n                    var params = new Array(arguments.length - 1),\r\n                        offset = 0;\r\n                    while (offset < params.length)\r\n                        params[offset++] = arguments[offset];\r\n                    resolve.apply(null, params);\r\n                }\r\n            }\r\n        };\r\n        try {\r\n            fn.apply(ctx || null, params);\r\n        } catch (err) {\r\n            if (pending) {\r\n                pending = false;\r\n                reject(err);\r\n            }\r\n        }\r\n    });\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal base64 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar base64 = exports;\r\n\r\n/**\r\n * Calculates the byte length of a base64 encoded string.\r\n * @param {string} string Base64 encoded string\r\n * @returns {number} Byte length\r\n */\r\nbase64.length = function length(string) {\r\n    var p = string.length;\r\n    if (!p)\r\n        return 0;\r\n    var n = 0;\r\n    while (--p % 4 > 1 && string.charAt(p) === \"=\")\r\n        ++n;\r\n    return Math.ceil(string.length * 3) / 4 - n;\r\n};\r\n\r\n// Base64 encoding table\r\nvar b64 = new Array(64);\r\n\r\n// Base64 decoding table\r\nvar s64 = new Array(123);\r\n\r\n// 65..90, 97..122, 48..57, 43, 47\r\nfor (var i = 0; i < 64;)\r\n    s64[b64[i] = i < 26 ? i + 65 : i < 52 ? i + 71 : i < 62 ? i - 4 : i - 59 | 43] = i++;\r\n\r\n/**\r\n * Encodes a buffer to a base64 encoded string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} Base64 encoded string\r\n */\r\nbase64.encode = function encode(buffer, start, end) {\r\n    var parts = null,\r\n        chunk = [];\r\n    var i = 0, // output index\r\n        j = 0, // goto index\r\n        t;     // temporary\r\n    while (start < end) {\r\n        var b = buffer[start++];\r\n        switch (j) {\r\n            case 0:\r\n                chunk[i++] = b64[b >> 2];\r\n                t = (b & 3) << 4;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                chunk[i++] = b64[t | b >> 4];\r\n                t = (b & 15) << 2;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                chunk[i++] = b64[t | b >> 6];\r\n                chunk[i++] = b64[b & 63];\r\n                j = 0;\r\n                break;\r\n        }\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (j) {\r\n        chunk[i++] = b64[t];\r\n        chunk[i++] = 61;\r\n        if (j === 1)\r\n            chunk[i++] = 61;\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\nvar invalidEncoding = \"invalid encoding\";\r\n\r\n/**\r\n * Decodes a base64 encoded string to a buffer.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Number of bytes written\r\n * @throws {Error} If encoding is invalid\r\n */\r\nbase64.decode = function decode(string, buffer, offset) {\r\n    var start = offset;\r\n    var j = 0, // goto index\r\n        t;     // temporary\r\n    for (var i = 0; i < string.length;) {\r\n        var c = string.charCodeAt(i++);\r\n        if (c === 61 && j > 1)\r\n            break;\r\n        if ((c = s64[c]) === undefined)\r\n            throw Error(invalidEncoding);\r\n        switch (j) {\r\n            case 0:\r\n                t = c;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                buffer[offset++] = t << 2 | (c & 48) >> 4;\r\n                t = c;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                buffer[offset++] = (t & 15) << 4 | (c & 60) >> 2;\r\n                t = c;\r\n                j = 3;\r\n                break;\r\n            case 3:\r\n                buffer[offset++] = (t & 3) << 6 | c;\r\n                j = 0;\r\n                break;\r\n        }\r\n    }\r\n    if (j === 1)\r\n        throw Error(invalidEncoding);\r\n    return offset - start;\r\n};\r\n\r\n/**\r\n * Tests if the specified string appears to be base64 encoded.\r\n * @param {string} string String to test\r\n * @returns {boolean} `true` if probably base64 encoded, otherwise false\r\n */\r\nbase64.test = function test(string) {\r\n    return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(string);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = codegen;\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @param {string[]} functionParams Function parameter names\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n */\r\nfunction codegen(functionParams, functionName) {\r\n\r\n    /* istanbul ignore if */\r\n    if (typeof functionParams === \"string\") {\r\n        functionName = functionParams;\r\n        functionParams = undefined;\r\n    }\r\n\r\n    var body = [];\r\n\r\n    /**\r\n     * Appends code to the function's body or finishes generation.\r\n     * @typedef Codegen\r\n     * @type {function}\r\n     * @param {string|Object.<string,*>} [formatStringOrScope] Format string or, to finish the function, an object of additional scope variables, if any\r\n     * @param {...*} [formatParams] Format parameters\r\n     * @returns {Codegen|Function} Itself or the generated function if finished\r\n     * @throws {Error} If format parameter counts do not match\r\n     */\r\n\r\n    function Codegen(formatStringOrScope) {\r\n        // note that explicit array handling below makes this ~50% faster\r\n\r\n        // finish the function\r\n        if (typeof formatStringOrScope !== \"string\") {\r\n            var source = toString();\r\n            if (codegen.verbose)\r\n                console.log(\"codegen: \" + source); // eslint-disable-line no-console\r\n            source = \"return \" + source;\r\n            if (formatStringOrScope) {\r\n                var scopeKeys   = Object.keys(formatStringOrScope),\r\n                    scopeParams = new Array(scopeKeys.length + 1),\r\n                    scopeValues = new Array(scopeKeys.length),\r\n                    scopeOffset = 0;\r\n                while (scopeOffset < scopeKeys.length) {\r\n                    scopeParams[scopeOffset] = scopeKeys[scopeOffset];\r\n                    scopeValues[scopeOffset] = formatStringOrScope[scopeKeys[scopeOffset++]];\r\n                }\r\n                scopeParams[scopeOffset] = source;\r\n                return Function.apply(null, scopeParams).apply(null, scopeValues); // eslint-disable-line no-new-func\r\n            }\r\n            return Function(source)(); // eslint-disable-line no-new-func\r\n        }\r\n\r\n        // otherwise append to body\r\n        var formatParams = new Array(arguments.length - 1),\r\n            formatOffset = 0;\r\n        while (formatOffset < formatParams.length)\r\n            formatParams[formatOffset] = arguments[++formatOffset];\r\n        formatOffset = 0;\r\n        formatStringOrScope = formatStringOrScope.replace(/%([%dfijs])/g, function replace($0, $1) {\r\n            var value = formatParams[formatOffset++];\r\n            switch ($1) {\r\n                case \"d\": case \"f\": return String(Number(value));\r\n                case \"i\": return String(Math.floor(value));\r\n                case \"j\": return JSON.stringify(value);\r\n                case \"s\": return String(value);\r\n            }\r\n            return \"%\";\r\n        });\r\n        if (formatOffset !== formatParams.length)\r\n            throw Error(\"parameter count mismatch\");\r\n        body.push(formatStringOrScope);\r\n        return Codegen;\r\n    }\r\n\r\n    function toString(functionNameOverride) {\r\n        return \"function \" + (functionNameOverride || functionName || \"\") + \"(\" + (functionParams && functionParams.join(\",\") || \"\") + \"){\\n  \" + body.join(\"\\n  \") + \"\\n}\";\r\n    }\r\n\r\n    Codegen.toString = toString;\r\n    return Codegen;\r\n}\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @function codegen\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * When set to `true`, codegen will log generated code to console. Useful for debugging.\r\n * @name util.codegen.verbose\r\n * @type {boolean}\r\n */\r\ncodegen.verbose = false;\r\n", "\"use strict\";\r\nmodule.exports = EventEmitter;\r\n\r\n/**\r\n * Constructs a new event emitter instance.\r\n * @classdesc A minimal event emitter.\r\n * @memberof util\r\n * @constructor\r\n */\r\nfunction EventEmitter() {\r\n\r\n    /**\r\n     * Registered listeners.\r\n     * @type {Object.<string,*>}\r\n     * @private\r\n     */\r\n    this._listeners = {};\r\n}\r\n\r\n/**\r\n * Registers an event listener.\r\n * @param {string} evt Event name\r\n * @param {function} fn Listener\r\n * @param {*} [ctx] Listener context\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.on = function on(evt, fn, ctx) {\r\n    (this._listeners[evt] || (this._listeners[evt] = [])).push({\r\n        fn  : fn,\r\n        ctx : ctx || this\r\n    });\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes an event listener or any matching listeners if arguments are omitted.\r\n * @param {string} [evt] Event name. Removes all listeners if omitted.\r\n * @param {function} [fn] Listener to remove. Removes all listeners of `evt` if omitted.\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.off = function off(evt, fn) {\r\n    if (evt === undefined)\r\n        this._listeners = {};\r\n    else {\r\n        if (fn === undefined)\r\n            this._listeners[evt] = [];\r\n        else {\r\n            var listeners = this._listeners[evt];\r\n            for (var i = 0; i < listeners.length;)\r\n                if (listeners[i].fn === fn)\r\n                    listeners.splice(i, 1);\r\n                else\r\n                    ++i;\r\n        }\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Emits an event by calling its listeners with the specified arguments.\r\n * @param {string} evt Event name\r\n * @param {...*} args Arguments\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.emit = function emit(evt) {\r\n    var listeners = this._listeners[evt];\r\n    if (listeners) {\r\n        var args = [],\r\n            i = 1;\r\n        for (; i < arguments.length;)\r\n            args.push(arguments[i++]);\r\n        for (i = 0; i < listeners.length;)\r\n            listeners[i].fn.apply(listeners[i++].ctx, args);\r\n    }\r\n    return this;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = fetch;\r\n\r\nvar asPromise = require(1),\r\n    inquire   = require(7);\r\n\r\nvar fs = inquire(\"fs\");\r\n\r\n/**\r\n * Node-style callback as used by {@link util.fetch}.\r\n * @typedef FetchCallback\r\n * @type {function}\r\n * @param {?Error} error Error, if any, otherwise `null`\r\n * @param {string} [contents] File contents, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Options as used by {@link util.fetch}.\r\n * @typedef FetchOptions\r\n * @type {Object}\r\n * @property {boolean} [binary=false] Whether expecting a binary response\r\n * @property {boolean} [xhr=false] If `true`, forces the use of XMLHttpRequest\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @memberof util\r\n * @param {string} filename File path or url\r\n * @param {FetchOptions} options Fetch options\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n */\r\nfunction fetch(filename, options, callback) {\r\n    if (typeof options === \"function\") {\r\n        callback = options;\r\n        options = {};\r\n    } else if (!options)\r\n        options = {};\r\n\r\n    if (!callback)\r\n        return asPromise(fetch, this, filename, options); // eslint-disable-line no-invalid-this\r\n\r\n    // if a node-like filesystem is present, try it first but fall back to XHR if nothing is found.\r\n    if (!options.xhr && fs && fs.readFile)\r\n        return fs.readFile(filename, function fetchReadFileCallback(err, contents) {\r\n            return err && typeof XMLHttpRequest !== \"undefined\"\r\n                ? fetch.xhr(filename, options, callback)\r\n                : err\r\n                ? callback(err)\r\n                : callback(null, options.binary ? contents : contents.toString(\"utf8\"));\r\n        });\r\n\r\n    // use the XHR version otherwise.\r\n    return fetch.xhr(filename, options, callback);\r\n}\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchOptions} [options] Fetch options\r\n * @returns {Promise<string|Uint8Array>} Promise\r\n * @variation 3\r\n */\r\n\r\n/**/\r\nfetch.xhr = function fetch_xhr(filename, options, callback) {\r\n    var xhr = new XMLHttpRequest();\r\n    xhr.onreadystatechange /* works everywhere */ = function fetchOnReadyStateChange() {\r\n\r\n        if (xhr.readyState !== 4)\r\n            return undefined;\r\n\r\n        // local cors security errors return status 0 / empty string, too. afaik this cannot be\r\n        // reliably distinguished from an actually empty file for security reasons. feel free\r\n        // to send a pull request if you are aware of a solution.\r\n        if (xhr.status !== 0 && xhr.status !== 200)\r\n            return callback(Error(\"status \" + xhr.status));\r\n\r\n        // if binary data is expected, make sure that some sort of array is returned, even if\r\n        // ArrayBuffers are not supported. the binary string fallback, however, is unsafe.\r\n        if (options.binary) {\r\n            var buffer = xhr.response;\r\n            if (!buffer) {\r\n                buffer = [];\r\n                for (var i = 0; i < xhr.responseText.length; ++i)\r\n                    buffer.push(xhr.responseText.charCodeAt(i) & 255);\r\n            }\r\n            return callback(null, typeof Uint8Array !== \"undefined\" ? new Uint8Array(buffer) : buffer);\r\n        }\r\n        return callback(null, xhr.responseText);\r\n    };\r\n\r\n    if (options.binary) {\r\n        // ref: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Sending_and_Receiving_Binary_Data#Receiving_binary_data_in_older_browsers\r\n        if (\"overrideMimeType\" in xhr)\r\n            xhr.overrideMimeType(\"text/plain; charset=x-user-defined\");\r\n        xhr.responseType = \"arraybuffer\";\r\n    }\r\n\r\n    xhr.open(\"GET\", filename);\r\n    xhr.send();\r\n};\r\n", "\"use strict\";\r\n\r\nmodule.exports = factory(factory);\r\n\r\n/**\r\n * Reads / writes floats / doubles from / to buffers.\r\n * @name util.float\r\n * @namespace\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using little endian byte order.\r\n * @name util.float.writeFloatLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using big endian byte order.\r\n * @name util.float.writeFloatBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using little endian byte order.\r\n * @name util.float.readFloatLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using big endian byte order.\r\n * @name util.float.readFloatBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using little endian byte order.\r\n * @name util.float.writeDoubleLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using big endian byte order.\r\n * @name util.float.writeDoubleBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using little endian byte order.\r\n * @name util.float.readDoubleLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using big endian byte order.\r\n * @name util.float.readDoubleBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n// Factory function for the purpose of node-based testing in modified global environments\r\nfunction factory(exports) {\r\n\r\n    // float: typed array\r\n    if (typeof Float32Array !== \"undefined\") (function() {\r\n\r\n        var f32 = new Float32Array([ -0 ]),\r\n            f8b = new Uint8Array(f32.buffer),\r\n            le  = f8b[3] === 128;\r\n\r\n        function writeFloat_f32_cpy(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n        }\r\n\r\n        function writeFloat_f32_rev(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[3];\r\n            buf[pos + 1] = f8b[2];\r\n            buf[pos + 2] = f8b[1];\r\n            buf[pos + 3] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeFloatLE = le ? writeFloat_f32_cpy : writeFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeFloatBE = le ? writeFloat_f32_rev : writeFloat_f32_cpy;\r\n\r\n        function readFloat_f32_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        function readFloat_f32_rev(buf, pos) {\r\n            f8b[3] = buf[pos    ];\r\n            f8b[2] = buf[pos + 1];\r\n            f8b[1] = buf[pos + 2];\r\n            f8b[0] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readFloatLE = le ? readFloat_f32_cpy : readFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.readFloatBE = le ? readFloat_f32_rev : readFloat_f32_cpy;\r\n\r\n    // float: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeFloat_ieee754(writeUint, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0)\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos);\r\n            else if (isNaN(val))\r\n                writeUint(2143289344, buf, pos);\r\n            else if (val > 3.4028234663852886e+38) // +-Infinity\r\n                writeUint((sign << 31 | 2139095040) >>> 0, buf, pos);\r\n            else if (val < 1.1754943508222875e-38) // denormal\r\n                writeUint((sign << 31 | Math.round(val / 1.401298464324817e-45)) >>> 0, buf, pos);\r\n            else {\r\n                var exponent = Math.floor(Math.log(val) / Math.LN2),\r\n                    mantissa = Math.round(val * Math.pow(2, -exponent) * 8388608) & 8388607;\r\n                writeUint((sign << 31 | exponent + 127 << 23 | mantissa) >>> 0, buf, pos);\r\n            }\r\n        }\r\n\r\n        exports.writeFloatLE = writeFloat_ieee754.bind(null, writeUintLE);\r\n        exports.writeFloatBE = writeFloat_ieee754.bind(null, writeUintBE);\r\n\r\n        function readFloat_ieee754(readUint, buf, pos) {\r\n            var uint = readUint(buf, pos),\r\n                sign = (uint >> 31) * 2 + 1,\r\n                exponent = uint >>> 23 & 255,\r\n                mantissa = uint & 8388607;\r\n            return exponent === 255\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 1.401298464324817e-45 * mantissa\r\n                : sign * Math.pow(2, exponent - 150) * (mantissa + 8388608);\r\n        }\r\n\r\n        exports.readFloatLE = readFloat_ieee754.bind(null, readUintLE);\r\n        exports.readFloatBE = readFloat_ieee754.bind(null, readUintBE);\r\n\r\n    })();\r\n\r\n    // double: typed array\r\n    if (typeof Float64Array !== \"undefined\") (function() {\r\n\r\n        var f64 = new Float64Array([-0]),\r\n            f8b = new Uint8Array(f64.buffer),\r\n            le  = f8b[7] === 128;\r\n\r\n        function writeDouble_f64_cpy(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n            buf[pos + 4] = f8b[4];\r\n            buf[pos + 5] = f8b[5];\r\n            buf[pos + 6] = f8b[6];\r\n            buf[pos + 7] = f8b[7];\r\n        }\r\n\r\n        function writeDouble_f64_rev(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[7];\r\n            buf[pos + 1] = f8b[6];\r\n            buf[pos + 2] = f8b[5];\r\n            buf[pos + 3] = f8b[4];\r\n            buf[pos + 4] = f8b[3];\r\n            buf[pos + 5] = f8b[2];\r\n            buf[pos + 6] = f8b[1];\r\n            buf[pos + 7] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleLE = le ? writeDouble_f64_cpy : writeDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleBE = le ? writeDouble_f64_rev : writeDouble_f64_cpy;\r\n\r\n        function readDouble_f64_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            f8b[4] = buf[pos + 4];\r\n            f8b[5] = buf[pos + 5];\r\n            f8b[6] = buf[pos + 6];\r\n            f8b[7] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        function readDouble_f64_rev(buf, pos) {\r\n            f8b[7] = buf[pos    ];\r\n            f8b[6] = buf[pos + 1];\r\n            f8b[5] = buf[pos + 2];\r\n            f8b[4] = buf[pos + 3];\r\n            f8b[3] = buf[pos + 4];\r\n            f8b[2] = buf[pos + 5];\r\n            f8b[1] = buf[pos + 6];\r\n            f8b[0] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readDoubleLE = le ? readDouble_f64_cpy : readDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.readDoubleBE = le ? readDouble_f64_rev : readDouble_f64_cpy;\r\n\r\n    // double: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeDouble_ieee754(writeUint, off0, off1, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos + off1);\r\n            } else if (isNaN(val)) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(2146959360, buf, pos + off1);\r\n            } else if (val > 1.7976931348623157e+308) { // +-Infinity\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint((sign << 31 | 2146435072) >>> 0, buf, pos + off1);\r\n            } else {\r\n                var mantissa;\r\n                if (val < 2.2250738585072014e-308) { // denormal\r\n                    mantissa = val / 5e-324;\r\n                    writeUint(mantissa >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | mantissa / 4294967296) >>> 0, buf, pos + off1);\r\n                } else {\r\n                    var exponent = Math.floor(Math.log(val) / Math.LN2);\r\n                    if (exponent === 1024)\r\n                        exponent = 1023;\r\n                    mantissa = val * Math.pow(2, -exponent);\r\n                    writeUint(mantissa * 4503599627370496 >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | exponent + 1023 << 20 | mantissa * 1048576 & 1048575) >>> 0, buf, pos + off1);\r\n                }\r\n            }\r\n        }\r\n\r\n        exports.writeDoubleLE = writeDouble_ieee754.bind(null, writeUintLE, 0, 4);\r\n        exports.writeDoubleBE = writeDouble_ieee754.bind(null, writeUintBE, 4, 0);\r\n\r\n        function readDouble_ieee754(readUint, off0, off1, buf, pos) {\r\n            var lo = readUint(buf, pos + off0),\r\n                hi = readUint(buf, pos + off1);\r\n            var sign = (hi >> 31) * 2 + 1,\r\n                exponent = hi >>> 20 & 2047,\r\n                mantissa = 4294967296 * (hi & 1048575) + lo;\r\n            return exponent === 2047\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 5e-324 * mantissa\r\n                : sign * Math.pow(2, exponent - 1075) * (mantissa + 4503599627370496);\r\n        }\r\n\r\n        exports.readDoubleLE = readDouble_ieee754.bind(null, readUintLE, 0, 4);\r\n        exports.readDoubleBE = readDouble_ieee754.bind(null, readUintBE, 4, 0);\r\n\r\n    })();\r\n\r\n    return exports;\r\n}\r\n\r\n// uint helpers\r\n\r\nfunction writeUintLE(val, buf, pos) {\r\n    buf[pos    ] =  val        & 255;\r\n    buf[pos + 1] =  val >>> 8  & 255;\r\n    buf[pos + 2] =  val >>> 16 & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\nfunction writeUintBE(val, buf, pos) {\r\n    buf[pos    ] =  val >>> 24;\r\n    buf[pos + 1] =  val >>> 16 & 255;\r\n    buf[pos + 2] =  val >>> 8  & 255;\r\n    buf[pos + 3] =  val        & 255;\r\n}\r\n\r\nfunction readUintLE(buf, pos) {\r\n    return (buf[pos    ]\r\n          | buf[pos + 1] << 8\r\n          | buf[pos + 2] << 16\r\n          | buf[pos + 3] << 24) >>> 0;\r\n}\r\n\r\nfunction readUintBE(buf, pos) {\r\n    return (buf[pos    ] << 24\r\n          | buf[pos + 1] << 16\r\n          | buf[pos + 2] << 8\r\n          | buf[pos + 3]) >>> 0;\r\n}\r\n", "\"use strict\";\r\nmodule.exports = inquire;\r\n\r\n/**\r\n * Requires a module only if available.\r\n * @memberof util\r\n * @param {string} moduleName Module to require\r\n * @returns {?Object} Required module if available and not empty, otherwise `null`\r\n */\r\nfunction inquire(moduleName) {\r\n    try {\r\n        var mod = eval(\"quire\".replace(/^/,\"re\"))(moduleName); // eslint-disable-line no-eval\r\n        if (mod && (mod.length || Object.keys(mod).length))\r\n            return mod;\r\n    } catch (e) {} // eslint-disable-line no-empty\r\n    return null;\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal path module to resolve Unix, Windows and URL paths alike.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar path = exports;\r\n\r\nvar isAbsolute =\r\n/**\r\n * Tests if the specified path is absolute.\r\n * @param {string} path Path to test\r\n * @returns {boolean} `true` if path is absolute\r\n */\r\npath.isAbsolute = function isAbsolute(path) {\r\n    return /^(?:\\/|\\w+:)/.test(path);\r\n};\r\n\r\nvar normalize =\r\n/**\r\n * Normalizes the specified path.\r\n * @param {string} path Path to normalize\r\n * @returns {string} Normalized path\r\n */\r\npath.normalize = function normalize(path) {\r\n    path = path.replace(/\\\\/g, \"/\")\r\n               .replace(/\\/{2,}/g, \"/\");\r\n    var parts    = path.split(\"/\"),\r\n        absolute = isAbsolute(path),\r\n        prefix   = \"\";\r\n    if (absolute)\r\n        prefix = parts.shift() + \"/\";\r\n    for (var i = 0; i < parts.length;) {\r\n        if (parts[i] === \"..\") {\r\n            if (i > 0 && parts[i - 1] !== \"..\")\r\n                parts.splice(--i, 2);\r\n            else if (absolute)\r\n                parts.splice(i, 1);\r\n            else\r\n                ++i;\r\n        } else if (parts[i] === \".\")\r\n            parts.splice(i, 1);\r\n        else\r\n            ++i;\r\n    }\r\n    return prefix + parts.join(\"/\");\r\n};\r\n\r\n/**\r\n * Resolves the specified include path against the specified origin path.\r\n * @param {string} originPath Path to the origin file\r\n * @param {string} includePath Include path relative to origin path\r\n * @param {boolean} [alreadyNormalized=false] `true` if both paths are already known to be normalized\r\n * @returns {string} Path to the include file\r\n */\r\npath.resolve = function resolve(originPath, includePath, alreadyNormalized) {\r\n    if (!alreadyNormalized)\r\n        includePath = normalize(includePath);\r\n    if (isAbsolute(includePath))\r\n        return includePath;\r\n    if (!alreadyNormalized)\r\n        originPath = normalize(originPath);\r\n    return (originPath = originPath.replace(/(?:\\/|^)[^/]+$/, \"\")).length ? normalize(originPath + \"/\" + includePath) : includePath;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = pool;\r\n\r\n/**\r\n * An allocator as used by {@link util.pool}.\r\n * @typedef PoolAllocator\r\n * @type {function}\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\n\r\n/**\r\n * A slicer as used by {@link util.pool}.\r\n * @typedef PoolSlicer\r\n * @type {function}\r\n * @param {number} start Start offset\r\n * @param {number} end End offset\r\n * @returns {Uint8Array} Buffer slice\r\n * @this {Uint8Array}\r\n */\r\n\r\n/**\r\n * A general purpose buffer pool.\r\n * @memberof util\r\n * @function\r\n * @param {PoolAllocator} alloc Allocator\r\n * @param {PoolSlicer} slice Slicer\r\n * @param {number} [size=8192] Slab size\r\n * @returns {PoolAllocator} Pooled allocator\r\n */\r\nfunction pool(alloc, slice, size) {\r\n    var SIZE   = size || 8192;\r\n    var MAX    = SIZE >>> 1;\r\n    var slab   = null;\r\n    var offset = SIZE;\r\n    return function pool_alloc(size) {\r\n        if (size < 1 || size > MAX)\r\n            return alloc(size);\r\n        if (offset + size > SIZE) {\r\n            slab = alloc(SIZE);\r\n            offset = 0;\r\n        }\r\n        var buf = slice.call(slab, offset, offset += size);\r\n        if (offset & 7) // align to 32 bit\r\n            offset = (offset | 7) + 1;\r\n        return buf;\r\n    };\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal UTF8 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar utf8 = exports;\r\n\r\n/**\r\n * Calculates the UTF8 byte length of a string.\r\n * @param {string} string String\r\n * @returns {number} Byte length\r\n */\r\nutf8.length = function utf8_length(string) {\r\n    var len = 0,\r\n        c = 0;\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c = string.charCodeAt(i);\r\n        if (c < 128)\r\n            len += 1;\r\n        else if (c < 2048)\r\n            len += 2;\r\n        else if ((c & 0xFC00) === 0xD800 && (string.charCodeAt(i + 1) & 0xFC00) === 0xDC00) {\r\n            ++i;\r\n            len += 4;\r\n        } else\r\n            len += 3;\r\n    }\r\n    return len;\r\n};\r\n\r\n/**\r\n * Reads UTF8 bytes as a string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} String read\r\n */\r\nutf8.read = function utf8_read(buffer, start, end) {\r\n    var len = end - start;\r\n    if (len < 1)\r\n        return \"\";\r\n    var parts = null,\r\n        chunk = [],\r\n        i = 0, // char offset\r\n        t;     // temporary\r\n    while (start < end) {\r\n        t = buffer[start++];\r\n        if (t < 128)\r\n            chunk[i++] = t;\r\n        else if (t > 191 && t < 224)\r\n            chunk[i++] = (t & 31) << 6 | buffer[start++] & 63;\r\n        else if (t > 239 && t < 365) {\r\n            t = ((t & 7) << 18 | (buffer[start++] & 63) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63) - 0x10000;\r\n            chunk[i++] = 0xD800 + (t >> 10);\r\n            chunk[i++] = 0xDC00 + (t & 1023);\r\n        } else\r\n            chunk[i++] = (t & 15) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63;\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\n/**\r\n * Writes a string as UTF8 bytes.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Bytes written\r\n */\r\nutf8.write = function utf8_write(string, buffer, offset) {\r\n    var start = offset,\r\n        c1, // character 1\r\n        c2; // character 2\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c1 = string.charCodeAt(i);\r\n        if (c1 < 128) {\r\n            buffer[offset++] = c1;\r\n        } else if (c1 < 2048) {\r\n            buffer[offset++] = c1 >> 6       | 192;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else if ((c1 & 0xFC00) === 0xD800 && ((c2 = string.charCodeAt(i + 1)) & 0xFC00) === 0xDC00) {\r\n            c1 = 0x10000 + ((c1 & 0x03FF) << 10) + (c2 & 0x03FF);\r\n            ++i;\r\n            buffer[offset++] = c1 >> 18      | 240;\r\n            buffer[offset++] = c1 >> 12 & 63 | 128;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else {\r\n            buffer[offset++] = c1 >> 12      | 224;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        }\r\n    }\r\n    return offset - start;\r\n};\r\n", "\"use strict\";\nmodule.exports = common;\n\nvar commonRe = /\\/|\\./;\n\n/**\n * Provides common type definitions.\n * Can also be used to provide additional google types or your own custom types.\n * @param {string} name Short name as in `google/protobuf/[name].proto` or full file name\n * @param {Object.<string,*>} json JSON definition within `google.protobuf` if a short name, otherwise the file's root definition\n * @returns {undefined}\n * @property {INamespace} google/protobuf/any.proto Any\n * @property {INamespace} google/protobuf/duration.proto Duration\n * @property {INamespace} google/protobuf/empty.proto Empty\n * @property {INamespace} google/protobuf/field_mask.proto FieldMask\n * @property {INamespace} google/protobuf/struct.proto Struct, Value, NullValue and ListValue\n * @property {INamespace} google/protobuf/timestamp.proto Timestamp\n * @property {INamespace} google/protobuf/wrappers.proto Wrappers\n * @example\n * // manually provides descriptor.proto (assumes google/protobuf/ namespace and .proto extension)\n * protobuf.common(\"descriptor\", descriptorJson);\n *\n * // manually provides a custom definition (uses my.foo namespace)\n * protobuf.common(\"my/foo/bar.proto\", myFooBarJson);\n */\nfunction common(name, json) {\n    if (!commonRe.test(name)) {\n        name = \"google/protobuf/\" + name + \".proto\";\n        json = { nested: { google: { nested: { protobuf: { nested: json } } } } };\n    }\n    common[name] = json;\n}\n\n// Not provided because of limited use (feel free to discuss or to provide yourself):\n//\n// google/protobuf/descriptor.proto\n// google/protobuf/source_context.proto\n// google/protobuf/type.proto\n//\n// Stripped and pre-parsed versions of these non-bundled files are instead available as part of\n// the repository or package within the google/protobuf directory.\n\ncommon(\"any\", {\n\n    /**\n     * Properties of a google.protobuf.Any message.\n     * @interface IAny\n     * @type {Object}\n     * @property {string} [typeUrl]\n     * @property {Uint8Array} [bytes]\n     * @memberof common\n     */\n    Any: {\n        fields: {\n            type_url: {\n                type: \"string\",\n                id: 1\n            },\n            value: {\n                type: \"bytes\",\n                id: 2\n            }\n        }\n    }\n});\n\nvar timeType;\n\ncommon(\"duration\", {\n\n    /**\n     * Properties of a google.protobuf.Duration message.\n     * @interface IDuration\n     * @type {Object}\n     * @property {number|Long} [seconds]\n     * @property {number} [nanos]\n     * @memberof common\n     */\n    Duration: timeType = {\n        fields: {\n            seconds: {\n                type: \"int64\",\n                id: 1\n            },\n            nanos: {\n                type: \"int32\",\n                id: 2\n            }\n        }\n    }\n});\n\ncommon(\"timestamp\", {\n\n    /**\n     * Properties of a google.protobuf.Timestamp message.\n     * @interface ITimestamp\n     * @type {Object}\n     * @property {number|Long} [seconds]\n     * @property {number} [nanos]\n     * @memberof common\n     */\n    Timestamp: timeType\n});\n\ncommon(\"empty\", {\n\n    /**\n     * Properties of a google.protobuf.Empty message.\n     * @interface IEmpty\n     * @memberof common\n     */\n    Empty: {\n        fields: {}\n    }\n});\n\ncommon(\"struct\", {\n\n    /**\n     * Properties of a google.protobuf.Struct message.\n     * @interface IStruct\n     * @type {Object}\n     * @property {Object.<string,IValue>} [fields]\n     * @memberof common\n     */\n    Struct: {\n        fields: {\n            fields: {\n                keyType: \"string\",\n                type: \"Value\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Value message.\n     * @interface IValue\n     * @type {Object}\n     * @property {string} [kind]\n     * @property {0} [nullValue]\n     * @property {number} [numberValue]\n     * @property {string} [stringValue]\n     * @property {boolean} [boolValue]\n     * @property {IStruct} [structValue]\n     * @property {IListValue} [listValue]\n     * @memberof common\n     */\n    Value: {\n        oneofs: {\n            kind: {\n                oneof: [\n                    \"nullValue\",\n                    \"numberValue\",\n                    \"stringValue\",\n                    \"boolValue\",\n                    \"structValue\",\n                    \"listValue\"\n                ]\n            }\n        },\n        fields: {\n            nullValue: {\n                type: \"NullValue\",\n                id: 1\n            },\n            numberValue: {\n                type: \"double\",\n                id: 2\n            },\n            stringValue: {\n                type: \"string\",\n                id: 3\n            },\n            boolValue: {\n                type: \"bool\",\n                id: 4\n            },\n            structValue: {\n                type: \"Struct\",\n                id: 5\n            },\n            listValue: {\n                type: \"ListValue\",\n                id: 6\n            }\n        }\n    },\n\n    NullValue: {\n        values: {\n            NULL_VALUE: 0\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.ListValue message.\n     * @interface IListValue\n     * @type {Object}\n     * @property {Array.<IValue>} [values]\n     * @memberof common\n     */\n    ListValue: {\n        fields: {\n            values: {\n                rule: \"repeated\",\n                type: \"Value\",\n                id: 1\n            }\n        }\n    }\n});\n\ncommon(\"wrappers\", {\n\n    /**\n     * Properties of a google.protobuf.DoubleValue message.\n     * @interface IDoubleValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    DoubleValue: {\n        fields: {\n            value: {\n                type: \"double\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.FloatValue message.\n     * @interface IFloatValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    FloatValue: {\n        fields: {\n            value: {\n                type: \"float\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Int64Value message.\n     * @interface IInt64Value\n     * @type {Object}\n     * @property {number|Long} [value]\n     * @memberof common\n     */\n    Int64Value: {\n        fields: {\n            value: {\n                type: \"int64\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.UInt64Value message.\n     * @interface IUInt64Value\n     * @type {Object}\n     * @property {number|Long} [value]\n     * @memberof common\n     */\n    UInt64Value: {\n        fields: {\n            value: {\n                type: \"uint64\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Int32Value message.\n     * @interface IInt32Value\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    Int32Value: {\n        fields: {\n            value: {\n                type: \"int32\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.UInt32Value message.\n     * @interface IUInt32Value\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    UInt32Value: {\n        fields: {\n            value: {\n                type: \"uint32\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.BoolValue message.\n     * @interface IBoolValue\n     * @type {Object}\n     * @property {boolean} [value]\n     * @memberof common\n     */\n    BoolValue: {\n        fields: {\n            value: {\n                type: \"bool\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.StringValue message.\n     * @interface IStringValue\n     * @type {Object}\n     * @property {string} [value]\n     * @memberof common\n     */\n    StringValue: {\n        fields: {\n            value: {\n                type: \"string\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.BytesValue message.\n     * @interface IBytesValue\n     * @type {Object}\n     * @property {Uint8Array} [value]\n     * @memberof common\n     */\n    BytesValue: {\n        fields: {\n            value: {\n                type: \"bytes\",\n                id: 1\n            }\n        }\n    }\n});\n\ncommon(\"field_mask\", {\n\n    /**\n     * Properties of a google.protobuf.FieldMask message.\n     * @interface IDoubleValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    FieldMask: {\n        fields: {\n            paths: {\n                rule: \"repeated\",\n                type: \"string\",\n                id: 1\n            }\n        }\n    }\n});\n\n/**\n * Gets the root definition of the specified common proto file.\n *\n * Bundled definitions are:\n * - google/protobuf/any.proto\n * - google/protobuf/duration.proto\n * - google/protobuf/empty.proto\n * - google/protobuf/field_mask.proto\n * - google/protobuf/struct.proto\n * - google/protobuf/timestamp.proto\n * - google/protobuf/wrappers.proto\n *\n * @param {string} file Proto file name\n * @returns {INamespace|null} Root definition or `null` if not defined\n */\ncommon.get = function get(file) {\n    return common[file] || null;\n};\n", "\"use strict\";\n/**\n * Runtime message from/to plain object converters.\n * @namespace\n */\nvar converter = exports;\n\nvar Enum = require(15),\n    util = require(37);\n\n/**\n * Generates a partial value fromObject conveter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_fromObject(gen, field, fieldIndex, prop) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(d%s){\", prop);\n            for (var values = field.resolvedType.values, keys = Object.keys(values), i = 0; i < keys.length; ++i) {\n                if (field.repeated && values[keys[i]] === field.typeDefault) gen\n                (\"default:\");\n                gen\n                (\"case%j:\", keys[i])\n                (\"case %i:\", values[keys[i]])\n                    (\"m%s=%j\", prop, values[keys[i]])\n                    (\"break\");\n            } gen\n            (\"}\");\n        } else gen\n            (\"if(typeof d%s!==\\\"object\\\")\", prop)\n                (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n            (\"m%s=types[%i].fromObject(d%s)\", prop, fieldIndex, prop);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n                (\"m%s=Number(d%s)\", prop, prop); // also catches \"NaN\", \"Infinity\"\n                break;\n            case \"uint32\":\n            case \"fixed32\": gen\n                (\"m%s=d%s>>>0\", prop, prop);\n                break;\n            case \"int32\":\n            case \"sint32\":\n            case \"sfixed32\": gen\n                (\"m%s=d%s|0\", prop, prop);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(util.Long)\")\n                    (\"(m%s=util.Long.fromValue(d%s)).unsigned=%j\", prop, prop, isUnsigned)\n                (\"else if(typeof d%s===\\\"string\\\")\", prop)\n                    (\"m%s=parseInt(d%s,10)\", prop, prop)\n                (\"else if(typeof d%s===\\\"number\\\")\", prop)\n                    (\"m%s=d%s\", prop, prop)\n                (\"else if(typeof d%s===\\\"object\\\")\", prop)\n                    (\"m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)\", prop, prop, prop, isUnsigned ? \"true\" : \"\");\n                break;\n            case \"bytes\": gen\n                (\"if(typeof d%s===\\\"string\\\")\", prop)\n                    (\"util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)\", prop, prop, prop)\n                (\"else if(d%s.length)\", prop)\n                    (\"m%s=d%s\", prop, prop);\n                break;\n            case \"string\": gen\n                (\"m%s=String(d%s)\", prop, prop);\n                break;\n            case \"bool\": gen\n                (\"m%s=Boolean(d%s)\", prop, prop);\n                break;\n            /* default: gen\n                (\"m%s=d%s\", prop, prop);\n                break; */\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a plain object to runtime message converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.fromObject = function fromObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray;\n    var gen = util.codegen([\"d\"], mtype.name + \"$fromObject\")\n    (\"if(d instanceof this.ctor)\")\n        (\"return d\");\n    if (!fields.length) return gen\n    (\"return new this.ctor\");\n    gen\n    (\"var m=new this.ctor\");\n    for (var i = 0; i < fields.length; ++i) {\n        var field  = fields[i].resolve(),\n            prop   = util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) { gen\n    (\"if(d%s){\", prop)\n        (\"if(typeof d%s!==\\\"object\\\")\", prop)\n            (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n        (\"m%s={}\", prop)\n        (\"for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){\", prop);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[ks[i]]\")\n        (\"}\")\n    (\"}\");\n\n        // Repeated fields\n        } else if (field.repeated) { gen\n    (\"if(d%s){\", prop)\n        (\"if(!Array.isArray(d%s))\", prop)\n            (\"throw TypeError(%j)\", field.fullName + \": array expected\")\n        (\"m%s=[]\", prop)\n        (\"for(var i=0;i<d%s.length;++i){\", prop);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[i]\")\n        (\"}\")\n    (\"}\");\n\n        // Non-repeated fields\n        } else {\n            if (!(field.resolvedType instanceof Enum)) gen // no need to test for null/undefined if an enum (uses switch)\n    (\"if(d%s!=null){\", prop); // !== undefined && !== null\n        genValuePartial_fromObject(gen, field, /* not sorted */ i, prop);\n            if (!(field.resolvedType instanceof Enum)) gen\n    (\"}\");\n        }\n    } return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n\n/**\n * Generates a partial value toObject converter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_toObject(gen, field, fieldIndex, prop) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) gen\n            (\"d%s=o.enums===String?types[%i].values[m%s]:m%s\", prop, fieldIndex, prop, prop);\n        else gen\n            (\"d%s=types[%i].toObject(m%s,o)\", prop, fieldIndex, prop);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n            (\"d%s=o.json&&!isFinite(m%s)?String(m%s):m%s\", prop, prop, prop, prop);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n            (\"if(typeof m%s===\\\"number\\\")\", prop)\n                (\"d%s=o.longs===String?String(m%s):m%s\", prop, prop, prop)\n            (\"else\") // Long-like\n                (\"d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s\", prop, prop, prop, prop, isUnsigned ? \"true\": \"\", prop);\n                break;\n            case \"bytes\": gen\n            (\"d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s\", prop, prop, prop, prop, prop);\n                break;\n            default: gen\n            (\"d%s=m%s\", prop, prop);\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a runtime message to plain object converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.toObject = function toObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray.slice().sort(util.compareFieldsById);\n    if (!fields.length)\n        return util.codegen()(\"return {}\");\n    var gen = util.codegen([\"m\", \"o\"], mtype.name + \"$toObject\")\n    (\"if(!o)\")\n        (\"o={}\")\n    (\"var d={}\");\n\n    var repeatedFields = [],\n        mapFields = [],\n        normalFields = [],\n        i = 0;\n    for (; i < fields.length; ++i)\n        if (!fields[i].partOf)\n            ( fields[i].resolve().repeated ? repeatedFields\n            : fields[i].map ? mapFields\n            : normalFields).push(fields[i]);\n\n    if (repeatedFields.length) { gen\n    (\"if(o.arrays||o.defaults){\");\n        for (i = 0; i < repeatedFields.length; ++i) gen\n        (\"d%s=[]\", util.safeProp(repeatedFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (mapFields.length) { gen\n    (\"if(o.objects||o.defaults){\");\n        for (i = 0; i < mapFields.length; ++i) gen\n        (\"d%s={}\", util.safeProp(mapFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (normalFields.length) { gen\n    (\"if(o.defaults){\");\n        for (i = 0; i < normalFields.length; ++i) {\n            var field = normalFields[i],\n                prop  = util.safeProp(field.name);\n            if (field.resolvedType instanceof Enum) gen\n        (\"d%s=o.enums===String?%j:%j\", prop, field.resolvedType.valuesById[field.typeDefault], field.typeDefault);\n            else if (field.long) gen\n        (\"if(util.Long){\")\n            (\"var n=new util.Long(%i,%i,%j)\", field.typeDefault.low, field.typeDefault.high, field.typeDefault.unsigned)\n            (\"d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n\", prop)\n        (\"}else\")\n            (\"d%s=o.longs===String?%j:%i\", prop, field.typeDefault.toString(), field.typeDefault.toNumber());\n            else if (field.bytes) {\n                var arrayDefault = \"[\" + Array.prototype.slice.call(field.typeDefault).join(\",\") + \"]\";\n                gen\n        (\"if(o.bytes===String)d%s=%j\", prop, String.fromCharCode.apply(String, field.typeDefault))\n        (\"else{\")\n            (\"d%s=%s\", prop, arrayDefault)\n            (\"if(o.bytes!==Array)d%s=util.newBuffer(d%s)\", prop, prop)\n        (\"}\");\n            } else gen\n        (\"d%s=%j\", prop, field.typeDefault); // also messages (=null)\n        } gen\n    (\"}\");\n    }\n    var hasKs2 = false;\n    for (i = 0; i < fields.length; ++i) {\n        var field = fields[i],\n            index = mtype._fieldsArray.indexOf(field),\n            prop  = util.safeProp(field.name);\n        if (field.map) {\n            if (!hasKs2) { hasKs2 = true; gen\n    (\"var ks2\");\n            } gen\n    (\"if(m%s&&(ks2=Object.keys(m%s)).length){\", prop, prop)\n        (\"d%s={}\", prop)\n        (\"for(var j=0;j<ks2.length;++j){\");\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[ks2[j]]\")\n        (\"}\");\n        } else if (field.repeated) { gen\n    (\"if(m%s&&m%s.length){\", prop, prop)\n        (\"d%s=[]\", prop)\n        (\"for(var j=0;j<m%s.length;++j){\", prop);\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[j]\")\n        (\"}\");\n        } else { gen\n    (\"if(m%s!=null&&m.hasOwnProperty(%j)){\", prop, field.name); // !== undefined && !== null\n        genValuePartial_toObject(gen, field, /* sorted */ index, prop);\n        if (field.partOf) gen\n        (\"if(o.oneofs)\")\n            (\"d%s=%j\", util.safeProp(field.partOf.name), field.name);\n        }\n        gen\n    (\"}\");\n    }\n    return gen\n    (\"return d\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n", "\"use strict\";\nmodule.exports = decoder;\n\nvar Enum    = require(15),\n    types   = require(36),\n    util    = require(37);\n\nfunction missing(field) {\n    return \"missing required '\" + field.name + \"'\";\n}\n\n/**\n * Generates a decoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction decoder(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"r\", \"l\"], mtype.name + \"$decode\")\n    (\"if(!(r instanceof Reader))\")\n        (\"r=Reader.create(r)\")\n    (\"var c=l===undefined?r.len:r.pos+l,m=new this.ctor\" + (mtype.fieldsArray.filter(function(field) { return field.map; }).length ? \",k,value\" : \"\"))\n    (\"while(r.pos<c){\")\n        (\"var t=r.uint32()\");\n    if (mtype.group) gen\n        (\"if((t&7)===4)\")\n            (\"break\");\n    gen\n        (\"switch(t>>>3){\");\n\n    var i = 0;\n    for (; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            type  = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            ref   = \"m\" + util.safeProp(field.name); gen\n            (\"case %i:\", field.id);\n\n        // Map fields\n        if (field.map) { gen\n                (\"if(%s===util.emptyObject)\", ref)\n                    (\"%s={}\", ref)\n                (\"var c2 = r.uint32()+r.pos\");\n\n            if (types.defaults[field.keyType] !== undefined) gen\n                (\"k=%j\", types.defaults[field.keyType]);\n            else gen\n                (\"k=null\");\n\n            if (types.defaults[type] !== undefined) gen\n                (\"value=%j\", types.defaults[type]);\n            else gen\n                (\"value=null\");\n\n            gen\n                (\"while(r.pos<c2){\")\n                    (\"var tag2=r.uint32()\")\n                    (\"switch(tag2>>>3){\")\n                        (\"case 1: k=r.%s(); break\", field.keyType)\n                        (\"case 2:\");\n\n            if (types.basic[type] === undefined) gen\n                            (\"value=types[%i].decode(r,r.uint32())\", i); // can't be groups\n            else gen\n                            (\"value=r.%s()\", type);\n\n            gen\n                            (\"break\")\n                        (\"default:\")\n                            (\"r.skipType(tag2&7)\")\n                            (\"break\")\n                    (\"}\")\n                (\"}\");\n\n            if (types.long[field.keyType] !== undefined) gen\n                (\"%s[typeof k===\\\"object\\\"?util.longToHash(k):k]=value\", ref);\n            else gen\n                (\"%s[k]=value\", ref);\n\n        // Repeated fields\n        } else if (field.repeated) { gen\n\n                (\"if(!(%s&&%s.length))\", ref, ref)\n                    (\"%s=[]\", ref);\n\n            // Packable (always check for forward and backward compatiblity)\n            if (types.packed[type] !== undefined) gen\n                (\"if((t&7)===2){\")\n                    (\"var c2=r.uint32()+r.pos\")\n                    (\"while(r.pos<c2)\")\n                        (\"%s.push(r.%s())\", ref, type)\n                (\"}else\");\n\n            // Non-packed\n            if (types.basic[type] === undefined) gen(field.resolvedType.group\n                    ? \"%s.push(types[%i].decode(r))\"\n                    : \"%s.push(types[%i].decode(r,r.uint32()))\", ref, i);\n            else gen\n                    (\"%s.push(r.%s())\", ref, type);\n\n        // Non-repeated\n        } else if (types.basic[type] === undefined) gen(field.resolvedType.group\n                ? \"%s=types[%i].decode(r)\"\n                : \"%s=types[%i].decode(r,r.uint32())\", ref, i);\n        else gen\n                (\"%s=r.%s()\", ref, type);\n        gen\n                (\"break\");\n    // Unknown fields\n    } gen\n            (\"default:\")\n                (\"r.skipType(t&7)\")\n                (\"break\")\n\n        (\"}\")\n    (\"}\");\n\n    // Field presence\n    for (i = 0; i < mtype._fieldsArray.length; ++i) {\n        var rfield = mtype._fieldsArray[i];\n        if (rfield.required) gen\n    (\"if(!m.hasOwnProperty(%j))\", rfield.name)\n        (\"throw util.ProtocolError(%j,{instance:m})\", missing(rfield));\n    }\n\n    return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline */\n}\n", "\"use strict\";\nmodule.exports = encoder;\n\nvar Enum     = require(15),\n    types    = require(36),\n    util     = require(37);\n\n/**\n * Generates a partial message type encoder.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genTypePartial(gen, field, fieldIndex, ref) {\n    return field.resolvedType.group\n        ? gen(\"types[%i].encode(%s,w.uint32(%i)).uint32(%i)\", fieldIndex, ref, (field.id << 3 | 3) >>> 0, (field.id << 3 | 4) >>> 0)\n        : gen(\"types[%i].encode(%s,w.uint32(%i).fork()).ldelim()\", fieldIndex, ref, (field.id << 3 | 2) >>> 0);\n}\n\n/**\n * Generates an encoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction encoder(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var gen = util.codegen([\"m\", \"w\"], mtype.name + \"$encode\")\n    (\"if(!w)\")\n        (\"w=Writer.create()\");\n\n    var i, ref;\n\n    // \"when a message is serialized its known fields should be written sequentially by field number\"\n    var fields = /* initializes */ mtype.fieldsArray.slice().sort(util.compareFieldsById);\n\n    for (var i = 0; i < fields.length; ++i) {\n        var field    = fields[i].resolve(),\n            index    = mtype._fieldsArray.indexOf(field),\n            type     = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            wireType = types.basic[type];\n            ref      = \"m\" + util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) {\n            gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j)){\", ref, field.name) // !== undefined && !== null\n        (\"for(var ks=Object.keys(%s),i=0;i<ks.length;++i){\", ref)\n            (\"w.uint32(%i).fork().uint32(%i).%s(ks[i])\", (field.id << 3 | 2) >>> 0, 8 | types.mapKey[field.keyType], field.keyType);\n            if (wireType === undefined) gen\n            (\"types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()\", index, ref); // can't be groups\n            else gen\n            (\".uint32(%i).%s(%s[ks[i]]).ldelim()\", 16 | wireType, type, ref);\n            gen\n        (\"}\")\n    (\"}\");\n\n            // Repeated fields\n        } else if (field.repeated) { gen\n    (\"if(%s!=null&&%s.length){\", ref, ref); // !== undefined && !== null\n\n            // Packed repeated\n            if (field.packed && types.packed[type] !== undefined) { gen\n\n        (\"w.uint32(%i).fork()\", (field.id << 3 | 2) >>> 0)\n        (\"for(var i=0;i<%s.length;++i)\", ref)\n            (\"w.%s(%s[i])\", type, ref)\n        (\"w.ldelim()\");\n\n            // Non-packed\n            } else { gen\n\n        (\"for(var i=0;i<%s.length;++i)\", ref);\n                if (wireType === undefined)\n            genTypePartial(gen, field, index, ref + \"[i]\");\n                else gen\n            (\"w.uint32(%i).%s(%s[i])\", (field.id << 3 | wireType) >>> 0, type, ref);\n\n            } gen\n    (\"}\");\n\n        // Non-repeated\n        } else {\n            if (field.optional) gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j))\", ref, field.name); // !== undefined && !== null\n\n            if (wireType === undefined)\n        genTypePartial(gen, field, index, ref);\n            else gen\n        (\"w.uint32(%i).%s(%s)\", (field.id << 3 | wireType) >>> 0, type, ref);\n\n        }\n    }\n\n    return gen\n    (\"return w\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n", "\"use strict\";\nmodule.exports = Enum;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Enum.prototype = Object.create(ReflectionObject.prototype)).constructor = Enum).className = \"Enum\";\n\nvar Namespace = require(23),\n    util = require(37);\n\n/**\n * Constructs a new enum instance.\n * @classdesc Reflected enum.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {Object.<string,number>} [values] Enum values as an object, by name\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this enum\n * @param {Object.<string,string>} [comments] The value comments for this enum\n */\nfunction Enum(name, values, options, comment, comments) {\n    ReflectionObject.call(this, name, options);\n\n    if (values && typeof values !== \"object\")\n        throw TypeError(\"values must be an object\");\n\n    /**\n     * Enum values by id.\n     * @type {Object.<number,string>}\n     */\n    this.valuesById = {};\n\n    /**\n     * Enum values by name.\n     * @type {Object.<string,number>}\n     */\n    this.values = Object.create(this.valuesById); // toJSON, marker\n\n    /**\n     * Enum comment text.\n     * @type {string|null}\n     */\n    this.comment = comment;\n\n    /**\n     * Value comment texts, if any.\n     * @type {Object.<string,string>}\n     */\n    this.comments = comments || {};\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    // Note that values inherit valuesById on their prototype which makes them a TypeScript-\n    // compatible enum. This is used by pbts to write actual enum definitions that work for\n    // static and reflection code alike instead of emitting generic object definitions.\n\n    if (values)\n        for (var keys = Object.keys(values), i = 0; i < keys.length; ++i)\n            if (typeof values[keys[i]] === \"number\") // use forward entries only\n                this.valuesById[ this.values[keys[i]] = values[keys[i]] ] = keys[i];\n}\n\n/**\n * Enum descriptor.\n * @interface IEnum\n * @property {Object.<string,number>} values Enum values\n * @property {Object.<string,*>} [options] Enum options\n */\n\n/**\n * Constructs an enum from an enum descriptor.\n * @param {string} name Enum name\n * @param {IEnum} json Enum descriptor\n * @returns {Enum} Created enum\n * @throws {TypeError} If arguments are invalid\n */\nEnum.fromJSON = function fromJSON(name, json) {\n    var enm = new Enum(name, json.values, json.options, json.comment, json.comments);\n    enm.reserved = json.reserved;\n    return enm;\n};\n\n/**\n * Converts this enum to an enum descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IEnum} Enum descriptor\n */\nEnum.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\"  , this.options,\n        \"values\"   , this.values,\n        \"reserved\" , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"comment\"  , keepComments ? this.comment : undefined,\n        \"comments\" , keepComments ? this.comments : undefined\n    ]);\n};\n\n/**\n * Adds a value to this enum.\n * @param {string} name Value name\n * @param {number} id Value id\n * @param {string} [comment] Comment, if any\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a value with this name or id\n */\nEnum.prototype.add = function add(name, id, comment) {\n    // utilized by the parser but not by .fromJSON\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (!util.isInteger(id))\n        throw TypeError(\"id must be an integer\");\n\n    if (this.values[name] !== undefined)\n        throw Error(\"duplicate name '\" + name + \"' in \" + this);\n\n    if (this.isReservedId(id))\n        throw Error(\"id \" + id + \" is reserved in \" + this);\n\n    if (this.isReservedName(name))\n        throw Error(\"name '\" + name + \"' is reserved in \" + this);\n\n    if (this.valuesById[id] !== undefined) {\n        if (!(this.options && this.options.allow_alias))\n            throw Error(\"duplicate id \" + id + \" in \" + this);\n        this.values[name] = id;\n    } else\n        this.valuesById[this.values[name] = id] = name;\n\n    this.comments[name] = comment || null;\n    return this;\n};\n\n/**\n * Removes a value from this enum\n * @param {string} name Value name\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `name` is not a name of this enum\n */\nEnum.prototype.remove = function remove(name) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    var val = this.values[name];\n    if (val == null)\n        throw Error(\"name '\" + name + \"' does not exist in \" + this);\n\n    delete this.valuesById[val];\n    delete this.values[name];\n    delete this.comments[name];\n\n    return this;\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n", "\"use strict\";\nmodule.exports = Field;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Field.prototype = Object.create(ReflectionObject.prototype)).constructor = Field).className = \"Field\";\n\nvar Enum  = require(15),\n    types = require(36),\n    util  = require(37);\n\nvar Type; // cyclic\n\nvar ruleRe = /^required|optional|repeated$/;\n\n/**\n * Constructs a new message field instance. Note that {@link MapField|map fields} have their own class.\n * @name Field\n * @classdesc Reflected message field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a field from a field descriptor.\n * @param {string} name Field name\n * @param {IField} json Field descriptor\n * @returns {Field} Created field\n * @throws {TypeError} If arguments are invalid\n */\nField.fromJSON = function fromJSON(name, json) {\n    return new Field(name, json.id, json.type, json.rule, json.extend, json.options, json.comment);\n};\n\n/**\n * Not an actual constructor. Use {@link Field} instead.\n * @classdesc Base class of all reflected message fields. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports FieldBase\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction Field(name, id, type, rule, extend, options, comment) {\n\n    if (util.isObject(rule)) {\n        comment = extend;\n        options = rule;\n        rule = extend = undefined;\n    } else if (util.isObject(extend)) {\n        comment = options;\n        options = extend;\n        extend = undefined;\n    }\n\n    ReflectionObject.call(this, name, options);\n\n    if (!util.isInteger(id) || id < 0)\n        throw TypeError(\"id must be a non-negative integer\");\n\n    if (!util.isString(type))\n        throw TypeError(\"type must be a string\");\n\n    if (rule !== undefined && !ruleRe.test(rule = rule.toString().toLowerCase()))\n        throw TypeError(\"rule must be a string rule\");\n\n    if (extend !== undefined && !util.isString(extend))\n        throw TypeError(\"extend must be a string\");\n\n    /**\n     * Field rule, if any.\n     * @type {string|undefined}\n     */\n    if (rule === \"proto3_optional\") {\n        rule = \"optional\";\n    }\n    this.rule = rule && rule !== \"optional\" ? rule : undefined; // toJSON\n\n    /**\n     * Field type.\n     * @type {string}\n     */\n    this.type = type; // toJSON\n\n    /**\n     * Unique field id.\n     * @type {number}\n     */\n    this.id = id; // toJSON, marker\n\n    /**\n     * Extended type if different from parent.\n     * @type {string|undefined}\n     */\n    this.extend = extend || undefined; // toJSON\n\n    /**\n     * Whether this field is required.\n     * @type {boolean}\n     */\n    this.required = rule === \"required\";\n\n    /**\n     * Whether this field is optional.\n     * @type {boolean}\n     */\n    this.optional = !this.required;\n\n    /**\n     * Whether this field is repeated.\n     * @type {boolean}\n     */\n    this.repeated = rule === \"repeated\";\n\n    /**\n     * Whether this field is a map or not.\n     * @type {boolean}\n     */\n    this.map = false;\n\n    /**\n     * Message this field belongs to.\n     * @type {Type|null}\n     */\n    this.message = null;\n\n    /**\n     * OneOf this field belongs to, if any,\n     * @type {OneOf|null}\n     */\n    this.partOf = null;\n\n    /**\n     * The field type's default value.\n     * @type {*}\n     */\n    this.typeDefault = null;\n\n    /**\n     * The field's default value on prototypes.\n     * @type {*}\n     */\n    this.defaultValue = null;\n\n    /**\n     * Whether this field's value should be treated as a long.\n     * @type {boolean}\n     */\n    this.long = util.Long ? types.long[type] !== undefined : /* istanbul ignore next */ false;\n\n    /**\n     * Whether this field's value is a buffer.\n     * @type {boolean}\n     */\n    this.bytes = type === \"bytes\";\n\n    /**\n     * Resolved type if not a basic type.\n     * @type {Type|Enum|null}\n     */\n    this.resolvedType = null;\n\n    /**\n     * Sister-field within the extended type if a declaring extension field.\n     * @type {Field|null}\n     */\n    this.extensionField = null;\n\n    /**\n     * Sister-field within the declaring namespace if an extended field.\n     * @type {Field|null}\n     */\n    this.declaringField = null;\n\n    /**\n     * Internally remembers whether this field is packed.\n     * @type {boolean|null}\n     * @private\n     */\n    this._packed = null;\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Determines whether this field is packed. Only relevant when repeated and working with proto2.\n * @name Field#packed\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"packed\", {\n    get: function() {\n        // defaults to packed=true if not explicity set to false\n        if (this._packed === null)\n            this._packed = this.getOption(\"packed\") !== false;\n        return this._packed;\n    }\n});\n\n/**\n * @override\n */\nField.prototype.setOption = function setOption(name, value, ifNotSet) {\n    if (name === \"packed\") // clear cached before setting\n        this._packed = null;\n    return ReflectionObject.prototype.setOption.call(this, name, value, ifNotSet);\n};\n\n/**\n * Field descriptor.\n * @interface IField\n * @property {string} [rule=\"optional\"] Field rule\n * @property {string} type Field type\n * @property {number} id Field id\n * @property {Object.<string,*>} [options] Field options\n */\n\n/**\n * Extension field descriptor.\n * @interface IExtensionField\n * @extends IField\n * @property {string} extend Extended type\n */\n\n/**\n * Converts this field to a field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IField} Field descriptor\n */\nField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"rule\"    , this.rule !== \"optional\" && this.rule || undefined,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Resolves this field's type references.\n * @returns {Field} `this`\n * @throws {Error} If any reference cannot be resolved\n */\nField.prototype.resolve = function resolve() {\n\n    if (this.resolved)\n        return this;\n\n    if ((this.typeDefault = types.defaults[this.type]) === undefined) { // if not a basic type, resolve it\n        this.resolvedType = (this.declaringField ? this.declaringField.parent : this.parent).lookupTypeOrEnum(this.type);\n        if (this.resolvedType instanceof Type)\n            this.typeDefault = null;\n        else // instanceof Enum\n            this.typeDefault = this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]; // first defined\n    }\n\n    // use explicitly set default value if present\n    if (this.options && this.options[\"default\"] != null) {\n        this.typeDefault = this.options[\"default\"];\n        if (this.resolvedType instanceof Enum && typeof this.typeDefault === \"string\")\n            this.typeDefault = this.resolvedType.values[this.typeDefault];\n    }\n\n    // remove unnecessary options\n    if (this.options) {\n        if (this.options.packed === true || this.options.packed !== undefined && this.resolvedType && !(this.resolvedType instanceof Enum))\n            delete this.options.packed;\n        if (!Object.keys(this.options).length)\n            this.options = undefined;\n    }\n\n    // convert to internal data type if necesssary\n    if (this.long) {\n        this.typeDefault = util.Long.fromNumber(this.typeDefault, this.type.charAt(0) === \"u\");\n\n        /* istanbul ignore else */\n        if (Object.freeze)\n            Object.freeze(this.typeDefault); // long instances are meant to be immutable anyway (i.e. use small int cache that even requires it)\n\n    } else if (this.bytes && typeof this.typeDefault === \"string\") {\n        var buf;\n        if (util.base64.test(this.typeDefault))\n            util.base64.decode(this.typeDefault, buf = util.newBuffer(util.base64.length(this.typeDefault)), 0);\n        else\n            util.utf8.write(this.typeDefault, buf = util.newBuffer(util.utf8.length(this.typeDefault)), 0);\n        this.typeDefault = buf;\n    }\n\n    // take special care of maps and repeated fields\n    if (this.map)\n        this.defaultValue = util.emptyObject;\n    else if (this.repeated)\n        this.defaultValue = util.emptyArray;\n    else\n        this.defaultValue = this.typeDefault;\n\n    // ensure proper value on prototype\n    if (this.parent instanceof Type)\n        this.parent.ctor.prototype[this.name] = this.defaultValue;\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n\n/**\n * Decorator function as returned by {@link Field.d} and {@link MapField.d} (TypeScript).\n * @typedef FieldDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} fieldName Field name\n * @returns {undefined}\n */\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"string\"|\"bool\"|\"bytes\"|Object} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @param {T} [defaultValue] Default value\n * @returns {FieldDecorator} Decorator function\n * @template T extends number | number[] | Long | Long[] | string | string[] | boolean | boolean[] | Uint8Array | Uint8Array[] | Buffer | Buffer[]\n */\nField.d = function decorateField(fieldId, fieldType, fieldRule, defaultValue) {\n\n    // submessage: decorate the submessage and use its name as the type\n    if (typeof fieldType === \"function\")\n        fieldType = util.decorateType(fieldType).name;\n\n    // enum reference: create a reflected copy of the enum and keep reuseing it\n    else if (fieldType && typeof fieldType === \"object\")\n        fieldType = util.decorateEnum(fieldType).name;\n\n    return function fieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new Field(fieldName, fieldId, fieldType, fieldRule, { \"default\": defaultValue }));\n    };\n};\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {Constructor<T>|string} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @returns {FieldDecorator} Decorator function\n * @template T extends Message<T>\n * @variation 2\n */\n// like Field.d but without a default value\n\n// Sets up cyclic dependencies (called in index-light)\nField._configure = function configure(Type_) {\n    Type = Type_;\n};\n", "\"use strict\";\nvar protobuf = module.exports = require(18);\n\nprotobuf.build = \"light\";\n\n/**\n * A node-style callback as used by {@link load} and {@link Root#load}.\n * @typedef LoadCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Root} [root] Root, if there hasn't been an error\n * @returns {undefined}\n */\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} root Root namespace, defaults to create a new one if omitted.\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n */\nfunction load(filename, root, callback) {\n    if (typeof root === \"function\") {\n        callback = root;\n        root = new protobuf.Root();\n    } else if (!root)\n        root = new protobuf.Root();\n    return root.load(filename, callback);\n}\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and returns a promise.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Promise<Root>} Promise\n * @see {@link Root#load}\n * @variation 3\n */\n// function load(filename:string, [root:Root]):Promise<Root>\n\nprotobuf.load = load;\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into a common root namespace (node only).\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n * @see {@link Root#loadSync}\n */\nfunction loadSync(filename, root) {\n    if (!root)\n        root = new protobuf.Root();\n    return root.loadSync(filename);\n}\n\nprotobuf.loadSync = loadSync;\n\n// Serialization\nprotobuf.encoder          = require(14);\nprotobuf.decoder          = require(13);\nprotobuf.verifier         = require(40);\nprotobuf.converter        = require(12);\n\n// Reflection\nprotobuf.ReflectionObject = require(24);\nprotobuf.Namespace        = require(23);\nprotobuf.Root             = require(29);\nprotobuf.Enum             = require(15);\nprotobuf.Type             = require(35);\nprotobuf.Field            = require(16);\nprotobuf.OneOf            = require(25);\nprotobuf.MapField         = require(20);\nprotobuf.Service          = require(33);\nprotobuf.Method           = require(22);\n\n// Runtime\nprotobuf.Message          = require(21);\nprotobuf.wrappers         = require(41);\n\n// Utility\nprotobuf.types            = require(36);\nprotobuf.util             = require(37);\n\n// Set up possibly cyclic reflection dependencies\nprotobuf.ReflectionObject._configure(protobuf.Root);\nprotobuf.Namespace._configure(protobuf.Type, protobuf.Service, protobuf.Enum);\nprotobuf.Root._configure(protobuf.Type);\nprotobuf.Field._configure(protobuf.Type);\n", "\"use strict\";\nvar protobuf = exports;\n\n/**\n * Build type, one of `\"full\"`, `\"light\"` or `\"minimal\"`.\n * @name build\n * @type {string}\n * @const\n */\nprotobuf.build = \"minimal\";\n\n// Serialization\nprotobuf.Writer       = require(42);\nprotobuf.BufferWriter = require(43);\nprotobuf.Reader       = require(27);\nprotobuf.BufferReader = require(28);\n\n// Utility\nprotobuf.util         = require(39);\nprotobuf.rpc          = require(31);\nprotobuf.roots        = require(30);\nprotobuf.configure    = configure;\n\n/* istanbul ignore next */\n/**\n * Reconfigures the library according to the environment.\n * @returns {undefined}\n */\nfunction configure() {\n    protobuf.util._configure();\n    protobuf.Writer._configure(protobuf.BufferWriter);\n    protobuf.Reader._configure(protobuf.BufferReader);\n}\n\n// Set up buffer utility according to the environment\nconfigure();\n", "\"use strict\";\nvar protobuf = module.exports = require(17);\n\nprotobuf.build = \"full\";\n\n// Parser\nprotobuf.tokenize         = require(34);\nprotobuf.parse            = require(26);\nprotobuf.common           = require(11);\n\n// Configure parser\nprotobuf.Root._configure(protobuf.Type, protobuf.parse, protobuf.common);\n", "\"use strict\";\nmodule.exports = MapField;\n\n// extends Field\nvar Field = require(16);\n((MapField.prototype = Object.create(Field.prototype)).constructor = MapField).className = \"MapField\";\n\nvar types   = require(36),\n    util    = require(37);\n\n/**\n * Constructs a new map field instance.\n * @classdesc Reflected map field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} keyType Key type\n * @param {string} type Value type\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction MapField(name, id, keyType, type, options, comment) {\n    Field.call(this, name, id, type, undefined, undefined, options, comment);\n\n    /* istanbul ignore if */\n    if (!util.isString(keyType))\n        throw TypeError(\"keyType must be a string\");\n\n    /**\n     * Key type.\n     * @type {string}\n     */\n    this.keyType = keyType; // toJSON, marker\n\n    /**\n     * Resolved key type if not a basic type.\n     * @type {ReflectionObject|null}\n     */\n    this.resolvedKeyType = null;\n\n    // Overrides Field#map\n    this.map = true;\n}\n\n/**\n * Map field descriptor.\n * @interface IMapField\n * @extends {IField}\n * @property {string} keyType Key type\n */\n\n/**\n * Extension map field descriptor.\n * @interface IExtensionMapField\n * @extends IMapField\n * @property {string} extend Extended type\n */\n\n/**\n * Constructs a map field from a map field descriptor.\n * @param {string} name Field name\n * @param {IMapField} json Map field descriptor\n * @returns {MapField} Created map field\n * @throws {TypeError} If arguments are invalid\n */\nMapField.fromJSON = function fromJSON(name, json) {\n    return new MapField(name, json.id, json.keyType, json.type, json.options, json.comment);\n};\n\n/**\n * Converts this map field to a map field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMapField} Map field descriptor\n */\nMapField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"keyType\" , this.keyType,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nMapField.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n\n    // Besides a value type, map fields have a key type that may be \"any scalar type except for floating point types and bytes\"\n    if (types.mapKey[this.keyType] === undefined)\n        throw Error(\"invalid key type: \" + this.keyType);\n\n    return Field.prototype.resolve.call(this);\n};\n\n/**\n * Map field decorator (TypeScript).\n * @name MapField.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"} fieldKeyType Field key type\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"|\"bytes\"|Object|Constructor<{}>} fieldValueType Field value type\n * @returns {FieldDecorator} Decorator function\n * @template T extends { [key: string]: number | Long | string | boolean | Uint8Array | Buffer | number[] | Message<{}> }\n */\nMapField.d = function decorateMapField(fieldId, fieldKeyType, fieldValueType) {\n\n    // submessage value: decorate the submessage and use its name as the type\n    if (typeof fieldValueType === \"function\")\n        fieldValueType = util.decorateType(fieldValueType).name;\n\n    // enum reference value: create a reflected copy of the enum and keep reuseing it\n    else if (fieldValueType && typeof fieldValueType === \"object\")\n        fieldValueType = util.decorateEnum(fieldValueType).name;\n\n    return function mapFieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new MapField(fieldName, fieldId, fieldKeyType, fieldValueType));\n    };\n};\n", "\"use strict\";\nmodule.exports = Message;\n\nvar util = require(39);\n\n/**\n * Constructs a new message instance.\n * @classdesc Abstract runtime message.\n * @constructor\n * @param {Properties<T>} [properties] Properties to set\n * @template T extends object = object\n */\nfunction Message(properties) {\n    // not used internally\n    if (properties)\n        for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n            this[keys[i]] = properties[keys[i]];\n}\n\n/**\n * Reference to the reflected type.\n * @name Message.$type\n * @type {Type}\n * @readonly\n */\n\n/**\n * Reference to the reflected type.\n * @name Message#$type\n * @type {Type}\n * @readonly\n */\n\n/*eslint-disable valid-jsdoc*/\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<T>} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.create = function create(properties) {\n    return this.$type.create(properties);\n};\n\n/**\n * Encodes a message of this type.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encode = function encode(message, writer) {\n    return this.$type.encode(message, writer);\n};\n\n/**\n * Encodes a message of this type preceeded by its length as a varint.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.$type.encodeDelimited(message, writer);\n};\n\n/**\n * Decodes a message of this type.\n * @name Message.decode\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decode = function decode(reader) {\n    return this.$type.decode(reader);\n};\n\n/**\n * Decodes a message of this type preceeded by its length as a varint.\n * @name Message.decodeDelimited\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decodeDelimited = function decodeDelimited(reader) {\n    return this.$type.decodeDelimited(reader);\n};\n\n/**\n * Verifies a message of this type.\n * @name Message.verify\n * @function\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {string|null} `null` if valid, otherwise the reason why it is not\n */\nMessage.verify = function verify(message) {\n    return this.$type.verify(message);\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object\n * @returns {T} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.fromObject = function fromObject(object) {\n    return this.$type.fromObject(object);\n};\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {T} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.toObject = function toObject(message, options) {\n    return this.$type.toObject(message, options);\n};\n\n/**\n * Converts this message to JSON.\n * @returns {Object.<string,*>} JSON object\n */\nMessage.prototype.toJSON = function toJSON() {\n    return this.$type.toObject(this, util.toJSONOptions);\n};\n\n/*eslint-enable valid-jsdoc*/", "\"use strict\";\nmodule.exports = Method;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Method.prototype = Object.create(ReflectionObject.prototype)).constructor = Method).className = \"Method\";\n\nvar util = require(37);\n\n/**\n * Constructs a new service method instance.\n * @classdesc Reflected service method.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Method name\n * @param {string|undefined} type Method type, usually `\"rpc\"`\n * @param {string} requestType Request message type\n * @param {string} responseType Response message type\n * @param {boolean|Object.<string,*>} [requestStream] Whether the request is streamed\n * @param {boolean|Object.<string,*>} [responseStream] Whether the response is streamed\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this method\n * @param {Object.<string,*>} [parsedOptions] Declared options, properly parsed into an object\n */\nfunction Method(name, type, requestType, responseType, requestStream, responseStream, options, comment, parsedOptions) {\n\n    /* istanbul ignore next */\n    if (util.isObject(requestStream)) {\n        options = requestStream;\n        requestStream = responseStream = undefined;\n    } else if (util.isObject(responseStream)) {\n        options = responseStream;\n        responseStream = undefined;\n    }\n\n    /* istanbul ignore if */\n    if (!(type === undefined || util.isString(type)))\n        throw TypeError(\"type must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(requestType))\n        throw TypeError(\"requestType must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(responseType))\n        throw TypeError(\"responseType must be a string\");\n\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Method type.\n     * @type {string}\n     */\n    this.type = type || \"rpc\"; // toJSON\n\n    /**\n     * Request type.\n     * @type {string}\n     */\n    this.requestType = requestType; // toJSON, marker\n\n    /**\n     * Whether requests are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.requestStream = requestStream ? true : undefined; // toJSON\n\n    /**\n     * Response type.\n     * @type {string}\n     */\n    this.responseType = responseType; // toJSON\n\n    /**\n     * Whether responses are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.responseStream = responseStream ? true : undefined; // toJSON\n\n    /**\n     * Resolved request type.\n     * @type {Type|null}\n     */\n    this.resolvedRequestType = null;\n\n    /**\n     * Resolved response type.\n     * @type {Type|null}\n     */\n    this.resolvedResponseType = null;\n\n    /**\n     * Comment for this method\n     * @type {string|null}\n     */\n    this.comment = comment;\n\n    /**\n     * Options properly parsed into an object\n     */\n    this.parsedOptions = parsedOptions;\n}\n\n/**\n * Method descriptor.\n * @interface IMethod\n * @property {string} [type=\"rpc\"] Method type\n * @property {string} requestType Request type\n * @property {string} responseType Response type\n * @property {boolean} [requestStream=false] Whether requests are streamed\n * @property {boolean} [responseStream=false] Whether responses are streamed\n * @property {Object.<string,*>} [options] Method options\n * @property {string} comment Method comments\n * @property {Object.<string,*>} [parsedOptions] Method options properly parsed into an object\n */\n\n/**\n * Constructs a method from a method descriptor.\n * @param {string} name Method name\n * @param {IMethod} json Method descriptor\n * @returns {Method} Created method\n * @throws {TypeError} If arguments are invalid\n */\nMethod.fromJSON = function fromJSON(name, json) {\n    return new Method(name, json.type, json.requestType, json.responseType, json.requestStream, json.responseStream, json.options, json.comment, json.parsedOptions);\n};\n\n/**\n * Converts this method to a method descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMethod} Method descriptor\n */\nMethod.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"type\"           , this.type !== \"rpc\" && /* istanbul ignore next */ this.type || undefined,\n        \"requestType\"    , this.requestType,\n        \"requestStream\"  , this.requestStream,\n        \"responseType\"   , this.responseType,\n        \"responseStream\" , this.responseStream,\n        \"options\"        , this.options,\n        \"comment\"        , keepComments ? this.comment : undefined,\n        \"parsedOptions\"  , this.parsedOptions,\n    ]);\n};\n\n/**\n * @override\n */\nMethod.prototype.resolve = function resolve() {\n\n    /* istanbul ignore if */\n    if (this.resolved)\n        return this;\n\n    this.resolvedRequestType = this.parent.lookupType(this.requestType);\n    this.resolvedResponseType = this.parent.lookupType(this.responseType);\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n", "\"use strict\";\nmodule.exports = Namespace;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Namespace.prototype = Object.create(ReflectionObject.prototype)).constructor = Namespace).className = \"Namespace\";\n\nvar Field    = require(16),\n    util     = require(37);\n\nvar Type,    // cyclic\n    Service,\n    Enum;\n\n/**\n * Constructs a new namespace instance.\n * @name Namespace\n * @classdesc Reflected namespace.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a namespace from JSON.\n * @memberof Namespace\n * @function\n * @param {string} name Namespace name\n * @param {Object.<string,*>} json JSON object\n * @returns {Namespace} Created namespace\n * @throws {TypeError} If arguments are invalid\n */\nNamespace.fromJSON = function fromJSON(name, json) {\n    return new Namespace(name, json.options).addJSON(json.nested);\n};\n\n/**\n * Converts an array of reflection objects to JSON.\n * @memberof Namespace\n * @param {ReflectionObject[]} array Object array\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {Object.<string,*>|undefined} JSON object or `undefined` when array is empty\n */\nfunction arrayToJSON(array, toJSONOptions) {\n    if (!(array && array.length))\n        return undefined;\n    var obj = {};\n    for (var i = 0; i < array.length; ++i)\n        obj[array[i].name] = array[i].toJSON(toJSONOptions);\n    return obj;\n}\n\nNamespace.arrayToJSON = arrayToJSON;\n\n/**\n * Tests if the specified id is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedId = function isReservedId(reserved, id) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (typeof reserved[i] !== \"string\" && reserved[i][0] <= id && reserved[i][1] > id)\n                return true;\n    return false;\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedName = function isReservedName(reserved, name) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (reserved[i] === name)\n                return true;\n    return false;\n};\n\n/**\n * Not an actual constructor. Use {@link Namespace} instead.\n * @classdesc Base class of all reflection objects containing nested objects. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports NamespaceBase\n * @extends ReflectionObject\n * @abstract\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n * @see {@link Namespace}\n */\nfunction Namespace(name, options) {\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Nested objects by name.\n     * @type {Object.<string,ReflectionObject>|undefined}\n     */\n    this.nested = undefined; // toJSON\n\n    /**\n     * Cached nested objects as an array.\n     * @type {ReflectionObject[]|null}\n     * @private\n     */\n    this._nestedArray = null;\n}\n\nfunction clearCache(namespace) {\n    namespace._nestedArray = null;\n    return namespace;\n}\n\n/**\n * Nested objects of this namespace as an array for iteration.\n * @name NamespaceBase#nestedArray\n * @type {ReflectionObject[]}\n * @readonly\n */\nObject.defineProperty(Namespace.prototype, \"nestedArray\", {\n    get: function() {\n        return this._nestedArray || (this._nestedArray = util.toArray(this.nested));\n    }\n});\n\n/**\n * Namespace descriptor.\n * @interface INamespace\n * @property {Object.<string,*>} [options] Namespace options\n * @property {Object.<string,AnyNestedObject>} [nested] Nested object descriptors\n */\n\n/**\n * Any extension field descriptor.\n * @typedef AnyExtensionField\n * @type {IExtensionField|IExtensionMapField}\n */\n\n/**\n * Any nested object descriptor.\n * @typedef AnyNestedObject\n * @type {IEnum|IType|IService|AnyExtensionField|INamespace}\n */\n// ^ BEWARE: VSCode hangs forever when using more than 5 types (that's why AnyExtensionField exists in the first place)\n\n/**\n * Converts this namespace to a namespace descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {INamespace} Namespace descriptor\n */\nNamespace.prototype.toJSON = function toJSON(toJSONOptions) {\n    return util.toObject([\n        \"options\" , this.options,\n        \"nested\"  , arrayToJSON(this.nestedArray, toJSONOptions)\n    ]);\n};\n\n/**\n * Adds nested objects to this namespace from nested object descriptors.\n * @param {Object.<string,AnyNestedObject>} nestedJson Any nested object descriptors\n * @returns {Namespace} `this`\n */\nNamespace.prototype.addJSON = function addJSON(nestedJson) {\n    var ns = this;\n    /* istanbul ignore else */\n    if (nestedJson) {\n        for (var names = Object.keys(nestedJson), i = 0, nested; i < names.length; ++i) {\n            nested = nestedJson[names[i]];\n            ns.add( // most to least likely\n                ( nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : nested.id !== undefined\n                ? Field.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    }\n    return this;\n};\n\n/**\n * Gets the nested object of the specified name.\n * @param {string} name Nested object name\n * @returns {ReflectionObject|null} The reflection object or `null` if it doesn't exist\n */\nNamespace.prototype.get = function get(name) {\n    return this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Gets the values of the nested {@link Enum|enum} of the specified name.\n * This methods differs from {@link Namespace#get|get} in that it returns an enum's values directly and throws instead of returning `null`.\n * @param {string} name Nested enum name\n * @returns {Object.<string,number>} Enum values\n * @throws {Error} If there is no such enum\n */\nNamespace.prototype.getEnum = function getEnum(name) {\n    if (this.nested && this.nested[name] instanceof Enum)\n        return this.nested[name].values;\n    throw Error(\"no such enum: \" + name);\n};\n\n/**\n * Adds a nested object to this namespace.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name\n */\nNamespace.prototype.add = function add(object) {\n\n    if (!(object instanceof Field && object.extend !== undefined || object instanceof Type || object instanceof Enum || object instanceof Service || object instanceof Namespace))\n        throw TypeError(\"object must be a valid nested object\");\n\n    if (!this.nested)\n        this.nested = {};\n    else {\n        var prev = this.get(object.name);\n        if (prev) {\n            if (prev instanceof Namespace && object instanceof Namespace && !(prev instanceof Type || prev instanceof Service)) {\n                // replace plain namespace but keep existing nested elements and options\n                var nested = prev.nestedArray;\n                for (var i = 0; i < nested.length; ++i)\n                    object.add(nested[i]);\n                this.remove(prev);\n                if (!this.nested)\n                    this.nested = {};\n                object.setOptions(prev.options, true);\n\n            } else\n                throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n        }\n    }\n    this.nested[object.name] = object;\n    object.onAdd(this);\n    return clearCache(this);\n};\n\n/**\n * Removes a nested object from this namespace.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this namespace\n */\nNamespace.prototype.remove = function remove(object) {\n\n    if (!(object instanceof ReflectionObject))\n        throw TypeError(\"object must be a ReflectionObject\");\n    if (object.parent !== this)\n        throw Error(object + \" is not a member of \" + this);\n\n    delete this.nested[object.name];\n    if (!Object.keys(this.nested).length)\n        this.nested = undefined;\n\n    object.onRemove(this);\n    return clearCache(this);\n};\n\n/**\n * Defines additial namespaces within this one if not yet existing.\n * @param {string|string[]} path Path to create\n * @param {*} [json] Nested types to create from JSON\n * @returns {Namespace} Pointer to the last namespace created or `this` if path is empty\n */\nNamespace.prototype.define = function define(path, json) {\n\n    if (util.isString(path))\n        path = path.split(\".\");\n    else if (!Array.isArray(path))\n        throw TypeError(\"illegal path\");\n    if (path && path.length && path[0] === \"\")\n        throw Error(\"path must be relative\");\n\n    var ptr = this;\n    while (path.length > 0) {\n        var part = path.shift();\n        if (ptr.nested && ptr.nested[part]) {\n            ptr = ptr.nested[part];\n            if (!(ptr instanceof Namespace))\n                throw Error(\"path conflicts with non-namespace objects\");\n        } else\n            ptr.add(ptr = new Namespace(part));\n    }\n    if (json)\n        ptr.addJSON(json);\n    return ptr;\n};\n\n/**\n * Resolves this namespace's and all its nested objects' type references. Useful to validate a reflection tree, but comes at a cost.\n * @returns {Namespace} `this`\n */\nNamespace.prototype.resolveAll = function resolveAll() {\n    var nested = this.nestedArray, i = 0;\n    while (i < nested.length)\n        if (nested[i] instanceof Namespace)\n            nested[i++].resolveAll();\n        else\n            nested[i++].resolve();\n    return this.resolve();\n};\n\n/**\n * Recursively looks up the reflection object matching the specified path in the scope of this namespace.\n * @param {string|string[]} path Path to look up\n * @param {*|Array.<*>} filterTypes Filter types, any combination of the constructors of `protobuf.Type`, `protobuf.Enum`, `protobuf.Service` etc.\n * @param {boolean} [parentAlreadyChecked=false] If known, whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n */\nNamespace.prototype.lookup = function lookup(path, filterTypes, parentAlreadyChecked) {\n\n    /* istanbul ignore next */\n    if (typeof filterTypes === \"boolean\") {\n        parentAlreadyChecked = filterTypes;\n        filterTypes = undefined;\n    } else if (filterTypes && !Array.isArray(filterTypes))\n        filterTypes = [ filterTypes ];\n\n    if (util.isString(path) && path.length) {\n        if (path === \".\")\n            return this.root;\n        path = path.split(\".\");\n    } else if (!path.length)\n        return this;\n\n    // Start at root if path is absolute\n    if (path[0] === \"\")\n        return this.root.lookup(path.slice(1), filterTypes);\n\n    // Test if the first part matches any nested object, and if so, traverse if path contains more\n    var found = this.get(path[0]);\n    if (found) {\n        if (path.length === 1) {\n            if (!filterTypes || filterTypes.indexOf(found.constructor) > -1)\n                return found;\n        } else if (found instanceof Namespace && (found = found.lookup(path.slice(1), filterTypes, true)))\n            return found;\n\n    // Otherwise try each nested namespace\n    } else\n        for (var i = 0; i < this.nestedArray.length; ++i)\n            if (this._nestedArray[i] instanceof Namespace && (found = this._nestedArray[i].lookup(path, filterTypes, true)))\n                return found;\n\n    // If there hasn't been a match, try again at the parent\n    if (this.parent === null || parentAlreadyChecked)\n        return null;\n    return this.parent.lookup(path, filterTypes);\n};\n\n/**\n * Looks up the reflection object at the specified path, relative to this namespace.\n * @name NamespaceBase#lookup\n * @function\n * @param {string|string[]} path Path to look up\n * @param {boolean} [parentAlreadyChecked=false] Whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n * @variation 2\n */\n// lookup(path: string, [parentAlreadyChecked: boolean])\n\n/**\n * Looks up the {@link Type|type} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type\n * @throws {Error} If `path` does not point to a type\n */\nNamespace.prototype.lookupType = function lookupType(path) {\n    var found = this.lookup(path, [ Type ]);\n    if (!found)\n        throw Error(\"no such type: \" + path);\n    return found;\n};\n\n/**\n * Looks up the values of the {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Enum} Looked up enum\n * @throws {Error} If `path` does not point to an enum\n */\nNamespace.prototype.lookupEnum = function lookupEnum(path) {\n    var found = this.lookup(path, [ Enum ]);\n    if (!found)\n        throw Error(\"no such Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Type|type} or {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type or enum\n * @throws {Error} If `path` does not point to a type or enum\n */\nNamespace.prototype.lookupTypeOrEnum = function lookupTypeOrEnum(path) {\n    var found = this.lookup(path, [ Type, Enum ]);\n    if (!found)\n        throw Error(\"no such Type or Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Service|service} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Service} Looked up service\n * @throws {Error} If `path` does not point to a service\n */\nNamespace.prototype.lookupService = function lookupService(path) {\n    var found = this.lookup(path, [ Service ]);\n    if (!found)\n        throw Error(\"no such Service '\" + path + \"' in \" + this);\n    return found;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nNamespace._configure = function(Type_, Service_, Enum_) {\n    Type    = Type_;\n    Service = Service_;\n    Enum    = Enum_;\n};\n", "\"use strict\";\nmodule.exports = ReflectionObject;\n\nReflectionObject.className = \"ReflectionObject\";\n\nvar util = require(37);\n\nvar Root; // cyclic\n\n/**\n * Constructs a new reflection object instance.\n * @classdesc Base class of all reflection objects.\n * @constructor\n * @param {string} name Object name\n * @param {Object.<string,*>} [options] Declared options\n * @abstract\n */\nfunction ReflectionObject(name, options) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (options && !util.isObject(options))\n        throw TypeError(\"options must be an object\");\n\n    /**\n     * Options.\n     * @type {Object.<string,*>|undefined}\n     */\n    this.options = options; // toJSON\n\n    /**\n     * Parsed Options.\n     * @type {Array.<Object.<string,*>>|undefined}\n     */\n    this.parsedOptions = null;\n\n    /**\n     * Unique name within its namespace.\n     * @type {string}\n     */\n    this.name = name;\n\n    /**\n     * Parent namespace.\n     * @type {Namespace|null}\n     */\n    this.parent = null;\n\n    /**\n     * Whether already resolved or not.\n     * @type {boolean}\n     */\n    this.resolved = false;\n\n    /**\n     * Comment text, if any.\n     * @type {string|null}\n     */\n    this.comment = null;\n\n    /**\n     * Defining file name.\n     * @type {string|null}\n     */\n    this.filename = null;\n}\n\nObject.defineProperties(ReflectionObject.prototype, {\n\n    /**\n     * Reference to the root namespace.\n     * @name ReflectionObject#root\n     * @type {Root}\n     * @readonly\n     */\n    root: {\n        get: function() {\n            var ptr = this;\n            while (ptr.parent !== null)\n                ptr = ptr.parent;\n            return ptr;\n        }\n    },\n\n    /**\n     * Full name including leading dot.\n     * @name ReflectionObject#fullName\n     * @type {string}\n     * @readonly\n     */\n    fullName: {\n        get: function() {\n            var path = [ this.name ],\n                ptr = this.parent;\n            while (ptr) {\n                path.unshift(ptr.name);\n                ptr = ptr.parent;\n            }\n            return path.join(\".\");\n        }\n    }\n});\n\n/**\n * Converts this reflection object to its descriptor representation.\n * @returns {Object.<string,*>} Descriptor\n * @abstract\n */\nReflectionObject.prototype.toJSON = /* istanbul ignore next */ function toJSON() {\n    throw Error(); // not implemented, shouldn't happen\n};\n\n/**\n * Called when this object is added to a parent.\n * @param {ReflectionObject} parent Parent added to\n * @returns {undefined}\n */\nReflectionObject.prototype.onAdd = function onAdd(parent) {\n    if (this.parent && this.parent !== parent)\n        this.parent.remove(this);\n    this.parent = parent;\n    this.resolved = false;\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleAdd(this);\n};\n\n/**\n * Called when this object is removed from a parent.\n * @param {ReflectionObject} parent Parent removed from\n * @returns {undefined}\n */\nReflectionObject.prototype.onRemove = function onRemove(parent) {\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleRemove(this);\n    this.parent = null;\n    this.resolved = false;\n};\n\n/**\n * Resolves this objects type references.\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n    if (this.root instanceof Root)\n        this.resolved = true; // only if part of a root\n    return this;\n};\n\n/**\n * Gets an option value.\n * @param {string} name Option name\n * @returns {*} Option value or `undefined` if not set\n */\nReflectionObject.prototype.getOption = function getOption(name) {\n    if (this.options)\n        return this.options[name];\n    return undefined;\n};\n\n/**\n * Sets an option.\n * @param {string} name Option name\n * @param {*} value Option value\n * @param {boolean} [ifNotSet] Sets the option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOption = function setOption(name, value, ifNotSet) {\n    if (!ifNotSet || !this.options || this.options[name] === undefined)\n        (this.options || (this.options = {}))[name] = value;\n    return this;\n};\n\n/**\n * Sets a parsed option.\n * @param {string} name parsed Option name\n * @param {*} value Option value\n * @param {string} propName dot '.' delimited full path of property within the option to set. if undefined\\empty, will add a new option with that value\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setParsedOption = function setParsedOption(name, value, propName) {\n    if (!this.parsedOptions) {\n        this.parsedOptions = [];\n    }\n    var parsedOptions = this.parsedOptions;\n    if (propName) {\n        // If setting a sub property of an option then try to merge it\n        // with an existing option\n        var opt = parsedOptions.find(function (opt) {\n            return Object.prototype.hasOwnProperty.call(opt, name);\n        });\n        if (opt) {\n            // If we found an existing option - just merge the property value\n            var newValue = opt[name];\n            util.setProperty(newValue, propName, value);\n        } else {\n            // otherwise, create a new option, set it's property and add it to the list\n            opt = {};\n            opt[name] = util.setProperty({}, propName, value);\n            parsedOptions.push(opt);\n        }\n    } else {\n        // Always create a new option when setting the value of the option itself\n        var newOpt = {};\n        newOpt[name] = value;\n        parsedOptions.push(newOpt);\n    }\n    return this;\n};\n\n/**\n * Sets multiple options.\n * @param {Object.<string,*>} options Options to set\n * @param {boolean} [ifNotSet] Sets an option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOptions = function setOptions(options, ifNotSet) {\n    if (options)\n        for (var keys = Object.keys(options), i = 0; i < keys.length; ++i)\n            this.setOption(keys[i], options[keys[i]], ifNotSet);\n    return this;\n};\n\n/**\n * Converts this instance to its string representation.\n * @returns {string} Class name[, space, full name]\n */\nReflectionObject.prototype.toString = function toString() {\n    var className = this.constructor.className,\n        fullName  = this.fullName;\n    if (fullName.length)\n        return className + \" \" + fullName;\n    return className;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nReflectionObject._configure = function(Root_) {\n    Root = Root_;\n};\n", "\"use strict\";\nmodule.exports = OneOf;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((OneOf.prototype = Object.create(ReflectionObject.prototype)).constructor = OneOf).className = \"OneOf\";\n\nvar Field = require(16),\n    util  = require(37);\n\n/**\n * Constructs a new oneof instance.\n * @classdesc Reflected oneof.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Oneof name\n * @param {string[]|Object.<string,*>} [fieldNames] Field names\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction OneOf(name, fieldNames, options, comment) {\n    if (!Array.isArray(fieldNames)) {\n        options = fieldNames;\n        fieldNames = undefined;\n    }\n    ReflectionObject.call(this, name, options);\n\n    /* istanbul ignore if */\n    if (!(fieldNames === undefined || Array.isArray(fieldNames)))\n        throw TypeError(\"fieldNames must be an Array\");\n\n    /**\n     * Field names that belong to this oneof.\n     * @type {string[]}\n     */\n    this.oneof = fieldNames || []; // toJSON, marker\n\n    /**\n     * Fields that belong to this oneof as an array for iteration.\n     * @type {Field[]}\n     * @readonly\n     */\n    this.fieldsArray = []; // declared readonly for conformance, possibly not yet added to parent\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Oneof descriptor.\n * @interface IOneOf\n * @property {Array.<string>} oneof Oneof field names\n * @property {Object.<string,*>} [options] Oneof options\n */\n\n/**\n * Constructs a oneof from a oneof descriptor.\n * @param {string} name Oneof name\n * @param {IOneOf} json Oneof descriptor\n * @returns {OneOf} Created oneof\n * @throws {TypeError} If arguments are invalid\n */\nOneOf.fromJSON = function fromJSON(name, json) {\n    return new OneOf(name, json.oneof, json.options, json.comment);\n};\n\n/**\n * Converts this oneof to a oneof descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IOneOf} Oneof descriptor\n */\nOneOf.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\" , this.options,\n        \"oneof\"   , this.oneof,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Adds the fields of the specified oneof to the parent if not already done so.\n * @param {OneOf} oneof The oneof\n * @returns {undefined}\n * @inner\n * @ignore\n */\nfunction addFieldsToParent(oneof) {\n    if (oneof.parent)\n        for (var i = 0; i < oneof.fieldsArray.length; ++i)\n            if (!oneof.fieldsArray[i].parent)\n                oneof.parent.add(oneof.fieldsArray[i]);\n}\n\n/**\n * Adds a field to this oneof and removes it from its current parent, if any.\n * @param {Field} field Field to add\n * @returns {OneOf} `this`\n */\nOneOf.prototype.add = function add(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    if (field.parent && field.parent !== this.parent)\n        field.parent.remove(field);\n    this.oneof.push(field.name);\n    this.fieldsArray.push(field);\n    field.partOf = this; // field.parent remains null\n    addFieldsToParent(this);\n    return this;\n};\n\n/**\n * Removes a field from this oneof and puts it back to the oneof's parent.\n * @param {Field} field Field to remove\n * @returns {OneOf} `this`\n */\nOneOf.prototype.remove = function remove(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    var index = this.fieldsArray.indexOf(field);\n\n    /* istanbul ignore if */\n    if (index < 0)\n        throw Error(field + \" is not a member of \" + this);\n\n    this.fieldsArray.splice(index, 1);\n    index = this.oneof.indexOf(field.name);\n\n    /* istanbul ignore else */\n    if (index > -1) // theoretical\n        this.oneof.splice(index, 1);\n\n    field.partOf = null;\n    return this;\n};\n\n/**\n * @override\n */\nOneOf.prototype.onAdd = function onAdd(parent) {\n    ReflectionObject.prototype.onAdd.call(this, parent);\n    var self = this;\n    // Collect present fields\n    for (var i = 0; i < this.oneof.length; ++i) {\n        var field = parent.get(this.oneof[i]);\n        if (field && !field.partOf) {\n            field.partOf = self;\n            self.fieldsArray.push(field);\n        }\n    }\n    // Add not yet present fields\n    addFieldsToParent(this);\n};\n\n/**\n * @override\n */\nOneOf.prototype.onRemove = function onRemove(parent) {\n    for (var i = 0, field; i < this.fieldsArray.length; ++i)\n        if ((field = this.fieldsArray[i]).parent)\n            field.parent.remove(field);\n    ReflectionObject.prototype.onRemove.call(this, parent);\n};\n\n/**\n * Decorator function as returned by {@link OneOf.d} (TypeScript).\n * @typedef OneOfDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} oneofName OneOf name\n * @returns {undefined}\n */\n\n/**\n * OneOf decorator (TypeScript).\n * @function\n * @param {...string} fieldNames Field names\n * @returns {OneOfDecorator} Decorator function\n * @template T extends string\n */\nOneOf.d = function decorateOneOf() {\n    var fieldNames = new Array(arguments.length),\n        index = 0;\n    while (index < arguments.length)\n        fieldNames[index] = arguments[index++];\n    return function oneOfDecorator(prototype, oneofName) {\n        util.decorateType(prototype.constructor)\n            .add(new OneOf(oneofName, fieldNames));\n        Object.defineProperty(prototype, oneofName, {\n            get: util.oneOfGetter(fieldNames),\n            set: util.oneOfSetter(fieldNames)\n        });\n    };\n};\n", "\"use strict\";\nmodule.exports = parse;\n\nparse.filename = null;\nparse.defaults = { keepCase: false };\n\nvar tokenize  = require(34),\n    Root      = require(29),\n    Type      = require(35),\n    Field     = require(16),\n    MapField  = require(20),\n    OneOf     = require(25),\n    Enum      = require(15),\n    Service   = require(33),\n    Method    = require(22),\n    types     = require(36),\n    util      = require(37);\n\nvar base10Re    = /^[1-9][0-9]*$/,\n    base10NegRe = /^-?[1-9][0-9]*$/,\n    base16Re    = /^0[x][0-9a-fA-F]+$/,\n    base16NegRe = /^-?0[x][0-9a-fA-F]+$/,\n    base8Re     = /^0[0-7]+$/,\n    base8NegRe  = /^-?0[0-7]+$/,\n    numberRe    = /^(?![eE])[0-9]*(?:\\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/,\n    nameRe      = /^[a-zA-Z_][a-zA-Z_0-9]*$/,\n    typeRefRe   = /^(?:\\.?[a-zA-Z_][a-zA-Z_0-9]*)(?:\\.[a-zA-Z_][a-zA-Z_0-9]*)*$/,\n    fqTypeRefRe = /^(?:\\.[a-zA-Z_][a-zA-Z_0-9]*)+$/;\n\n/**\n * Result object returned from {@link parse}.\n * @interface IParserResult\n * @property {string|undefined} package Package name, if declared\n * @property {string[]|undefined} imports Imports, if any\n * @property {string[]|undefined} weakImports Weak imports, if any\n * @property {string|undefined} syntax Syntax, if specified (either `\"proto2\"` or `\"proto3\"`)\n * @property {Root} root Populated root instance\n */\n\n/**\n * Options modifying the behavior of {@link parse}.\n * @interface IParseOptions\n * @property {boolean} [keepCase=false] Keeps field casing instead of converting to camel case\n * @property {boolean} [alternateCommentMode=false] Recognize double-slash comments in addition to doc-block comments.\n * @property {boolean} [preferTrailingComment=false] Use trailing comment when both leading comment and trailing comment exist.\n */\n\n/**\n * Options modifying the behavior of JSON serialization.\n * @interface IToJSONOptions\n * @property {boolean} [keepComments=false] Serializes comments.\n */\n\n/**\n * Parses the given .proto source and returns an object with the parsed contents.\n * @param {string} source Source contents\n * @param {Root} root Root to populate\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {IParserResult} Parser result\n * @property {string} filename=null Currently processing file name for error reporting, if known\n * @property {IParseOptions} defaults Default {@link IParseOptions}\n */\nfunction parse(source, root, options) {\n    /* eslint-disable callback-return */\n    if (!(root instanceof Root)) {\n        options = root;\n        root = new Root();\n    }\n    if (!options)\n        options = parse.defaults;\n\n    var preferTrailingComment = options.preferTrailingComment || false;\n    var tn = tokenize(source, options.alternateCommentMode || false),\n        next = tn.next,\n        push = tn.push,\n        peek = tn.peek,\n        skip = tn.skip,\n        cmnt = tn.cmnt;\n\n    var head = true,\n        pkg,\n        imports,\n        weakImports,\n        syntax,\n        isProto3 = false;\n\n    var ptr = root;\n\n    var applyCase = options.keepCase ? function(name) { return name; } : util.camelCase;\n\n    /* istanbul ignore next */\n    function illegal(token, name, insideTryCatch) {\n        var filename = parse.filename;\n        if (!insideTryCatch)\n            parse.filename = null;\n        return Error(\"illegal \" + (name || \"token\") + \" '\" + token + \"' (\" + (filename ? filename + \", \" : \"\") + \"line \" + tn.line + \")\");\n    }\n\n    function readString() {\n        var values = [],\n            token;\n        do {\n            /* istanbul ignore if */\n            if ((token = next()) !== \"\\\"\" && token !== \"'\")\n                throw illegal(token);\n\n            values.push(next());\n            skip(token);\n            token = peek();\n        } while (token === \"\\\"\" || token === \"'\");\n        return values.join(\"\");\n    }\n\n    function readValue(acceptTypeRef) {\n        var token = next();\n        switch (token) {\n            case \"'\":\n            case \"\\\"\":\n                push(token);\n                return readString();\n            case \"true\": case \"TRUE\":\n                return true;\n            case \"false\": case \"FALSE\":\n                return false;\n        }\n        try {\n            return parseNumber(token, /* insideTryCatch */ true);\n        } catch (e) {\n\n            /* istanbul ignore else */\n            if (acceptTypeRef && typeRefRe.test(token))\n                return token;\n\n            /* istanbul ignore next */\n            throw illegal(token, \"value\");\n        }\n    }\n\n    function readRanges(target, acceptStrings) {\n        var token, start;\n        do {\n            if (acceptStrings && ((token = peek()) === \"\\\"\" || token === \"'\"))\n                target.push(readString());\n            else\n                target.push([ start = parseId(next()), skip(\"to\", true) ? parseId(next()) : start ]);\n        } while (skip(\",\", true));\n        skip(\";\");\n    }\n\n    function parseNumber(token, insideTryCatch) {\n        var sign = 1;\n        if (token.charAt(0) === \"-\") {\n            sign = -1;\n            token = token.substring(1);\n        }\n        switch (token) {\n            case \"inf\": case \"INF\": case \"Inf\":\n                return sign * Infinity;\n            case \"nan\": case \"NAN\": case \"Nan\": case \"NaN\":\n                return NaN;\n            case \"0\":\n                return 0;\n        }\n        if (base10Re.test(token))\n            return sign * parseInt(token, 10);\n        if (base16Re.test(token))\n            return sign * parseInt(token, 16);\n        if (base8Re.test(token))\n            return sign * parseInt(token, 8);\n\n        /* istanbul ignore else */\n        if (numberRe.test(token))\n            return sign * parseFloat(token);\n\n        /* istanbul ignore next */\n        throw illegal(token, \"number\", insideTryCatch);\n    }\n\n    function parseId(token, acceptNegative) {\n        switch (token) {\n            case \"max\": case \"MAX\": case \"Max\":\n                return 536870911;\n            case \"0\":\n                return 0;\n        }\n\n        /* istanbul ignore if */\n        if (!acceptNegative && token.charAt(0) === \"-\")\n            throw illegal(token, \"id\");\n\n        if (base10NegRe.test(token))\n            return parseInt(token, 10);\n        if (base16NegRe.test(token))\n            return parseInt(token, 16);\n\n        /* istanbul ignore else */\n        if (base8NegRe.test(token))\n            return parseInt(token, 8);\n\n        /* istanbul ignore next */\n        throw illegal(token, \"id\");\n    }\n\n    function parsePackage() {\n\n        /* istanbul ignore if */\n        if (pkg !== undefined)\n            throw illegal(\"package\");\n\n        pkg = next();\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(pkg))\n            throw illegal(pkg, \"name\");\n\n        ptr = ptr.define(pkg);\n        skip(\";\");\n    }\n\n    function parseImport() {\n        var token = peek();\n        var whichImports;\n        switch (token) {\n            case \"weak\":\n                whichImports = weakImports || (weakImports = []);\n                next();\n                break;\n            case \"public\":\n                next();\n                // eslint-disable-line no-fallthrough\n            default:\n                whichImports = imports || (imports = []);\n                break;\n        }\n        token = readString();\n        skip(\";\");\n        whichImports.push(token);\n    }\n\n    function parseSyntax() {\n        skip(\"=\");\n        syntax = readString();\n        isProto3 = syntax === \"proto3\";\n\n        /* istanbul ignore if */\n        if (!isProto3 && syntax !== \"proto2\")\n            throw illegal(syntax, \"syntax\");\n\n        skip(\";\");\n    }\n\n    function parseCommon(parent, token) {\n        switch (token) {\n\n            case \"option\":\n                parseOption(parent, token);\n                skip(\";\");\n                return true;\n\n            case \"message\":\n                parseType(parent, token);\n                return true;\n\n            case \"enum\":\n                parseEnum(parent, token);\n                return true;\n\n            case \"service\":\n                parseService(parent, token);\n                return true;\n\n            case \"extend\":\n                parseExtension(parent, token);\n                return true;\n        }\n        return false;\n    }\n\n    function ifBlock(obj, fnIf, fnElse) {\n        var trailingLine = tn.line;\n        if (obj) {\n            if(typeof obj.comment !== \"string\") {\n              obj.comment = cmnt(); // try block-type comment\n            }\n            obj.filename = parse.filename;\n        }\n        if (skip(\"{\", true)) {\n            var token;\n            while ((token = next()) !== \"}\")\n                fnIf(token);\n            skip(\";\", true);\n        } else {\n            if (fnElse)\n                fnElse();\n            skip(\";\");\n            if (obj && (typeof obj.comment !== \"string\" || preferTrailingComment))\n                obj.comment = cmnt(trailingLine) || obj.comment; // try line-type comment\n        }\n    }\n\n    function parseType(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"type name\");\n\n        var type = new Type(token);\n        ifBlock(type, function parseType_block(token) {\n            if (parseCommon(type, token))\n                return;\n\n            switch (token) {\n\n                case \"map\":\n                    parseMapField(type, token);\n                    break;\n\n                case \"required\":\n                case \"repeated\":\n                    parseField(type, token);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (isProto3) {\n                        parseField(type, \"proto3_optional\");\n                    } else {\n                        parseField(type, \"optional\");\n                    }\n                    break;\n\n                case \"oneof\":\n                    parseOneOf(type, token);\n                    break;\n\n                case \"extensions\":\n                    readRanges(type.extensions || (type.extensions = []));\n                    break;\n\n                case \"reserved\":\n                    readRanges(type.reserved || (type.reserved = []), true);\n                    break;\n\n                default:\n                    /* istanbul ignore if */\n                    if (!isProto3 || !typeRefRe.test(token))\n                        throw illegal(token);\n\n                    push(token);\n                    parseField(type, \"optional\");\n                    break;\n            }\n        });\n        parent.add(type);\n    }\n\n    function parseField(parent, rule, extend) {\n        var type = next();\n        if (type === \"group\") {\n            parseGroup(parent, rule);\n            return;\n        }\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(type))\n            throw illegal(type, \"type\");\n\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        name = applyCase(name);\n        skip(\"=\");\n\n        var field = new Field(name, parseId(next()), type, rule, extend);\n        ifBlock(field, function parseField_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(field, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseField_line() {\n            parseInlineOptions(field);\n        });\n\n        if (rule === \"proto3_optional\") {\n            // for proto3 optional fields, we create a single-member Oneof to mimic \"optional\" behavior\n            var oneof = new OneOf(\"_\" + name);\n            field.setOption(\"proto3_optional\", true);\n            oneof.add(field);\n            parent.add(oneof);\n        } else {\n            parent.add(field);\n        }\n\n        // JSON defaults to packed=true if not set so we have to set packed=false explicity when\n        // parsing proto2 descriptors without the option, where applicable. This must be done for\n        // all known packable types and anything that could be an enum (= is not a basic type).\n        if (!isProto3 && field.repeated && (types.packed[type] !== undefined || types.basic[type] === undefined))\n            field.setOption(\"packed\", false, /* ifNotSet */ true);\n    }\n\n    function parseGroup(parent, rule) {\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        var fieldName = util.lcFirst(name);\n        if (name === fieldName)\n            name = util.ucFirst(name);\n        skip(\"=\");\n        var id = parseId(next());\n        var type = new Type(name);\n        type.group = true;\n        var field = new Field(fieldName, id, name, rule);\n        field.filename = parse.filename;\n        ifBlock(type, function parseGroup_block(token) {\n            switch (token) {\n\n                case \"option\":\n                    parseOption(type, token);\n                    skip(\";\");\n                    break;\n\n                case \"required\":\n                case \"repeated\":\n                    parseField(type, token);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (isProto3) {\n                        parseField(type, \"proto3_optional\");\n                    } else {\n                        parseField(type, \"optional\");\n                    }\n                    break;\n\n                /* istanbul ignore next */\n                default:\n                    throw illegal(token); // there are no groups with proto3 semantics\n            }\n        });\n        parent.add(type)\n              .add(field);\n    }\n\n    function parseMapField(parent) {\n        skip(\"<\");\n        var keyType = next();\n\n        /* istanbul ignore if */\n        if (types.mapKey[keyType] === undefined)\n            throw illegal(keyType, \"type\");\n\n        skip(\",\");\n        var valueType = next();\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(valueType))\n            throw illegal(valueType, \"type\");\n\n        skip(\">\");\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        skip(\"=\");\n        var field = new MapField(applyCase(name), parseId(next()), keyType, valueType);\n        ifBlock(field, function parseMapField_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(field, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseMapField_line() {\n            parseInlineOptions(field);\n        });\n        parent.add(field);\n    }\n\n    function parseOneOf(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var oneof = new OneOf(applyCase(token));\n        ifBlock(oneof, function parseOneOf_block(token) {\n            if (token === \"option\") {\n                parseOption(oneof, token);\n                skip(\";\");\n            } else {\n                push(token);\n                parseField(oneof, \"optional\");\n            }\n        });\n        parent.add(oneof);\n    }\n\n    function parseEnum(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var enm = new Enum(token);\n        ifBlock(enm, function parseEnum_block(token) {\n          switch(token) {\n            case \"option\":\n              parseOption(enm, token);\n              skip(\";\");\n              break;\n\n            case \"reserved\":\n              readRanges(enm.reserved || (enm.reserved = []), true);\n              break;\n\n            default:\n              parseEnumValue(enm, token);\n          }\n        });\n        parent.add(enm);\n    }\n\n    function parseEnumValue(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token))\n            throw illegal(token, \"name\");\n\n        skip(\"=\");\n        var value = parseId(next(), true),\n            dummy = {};\n        ifBlock(dummy, function parseEnumValue_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(dummy, token); // skip\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseEnumValue_line() {\n            parseInlineOptions(dummy); // skip\n        });\n        parent.add(token, value, dummy.comment);\n    }\n\n    function parseOption(parent, token) {\n        var isCustom = skip(\"(\", true);\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var name = token;\n        var option = name;\n        var propName;\n\n        if (isCustom) {\n            skip(\")\");\n            name = \"(\" + name + \")\";\n            option = name;\n            token = peek();\n            if (fqTypeRefRe.test(token)) {\n                propName = token.substr(1); //remove '.' before property name\n                name += token;\n                next();\n            }\n        }\n        skip(\"=\");\n        var optionValue = parseOptionValue(parent, name);\n        setParsedOption(parent, option, optionValue, propName);\n    }\n\n    function parseOptionValue(parent, name) {\n        if (skip(\"{\", true)) { // { a: \"foo\" b { c: \"bar\" } }\n            var result = {};\n            while (!skip(\"}\", true)) {\n                /* istanbul ignore if */\n                if (!nameRe.test(token = next()))\n                    throw illegal(token, \"name\");\n\n                var value;\n                var propName = token;\n                if (peek() === \"{\")\n                    value = parseOptionValue(parent, name + \".\" + token);\n                else {\n                    skip(\":\");\n                    if (peek() === \"{\")\n                        value = parseOptionValue(parent, name + \".\" + token);\n                    else {\n                        value = readValue(true);\n                        setOption(parent, name + \".\" + token, value);\n                    }\n                }\n                var prevValue = result[propName];\n                if (prevValue)\n                    value = [].concat(prevValue).concat(value);\n                result[propName] = value;\n                skip(\",\", true);\n            }\n            return result;\n        }\n\n        var simpleValue = readValue(true);\n        setOption(parent, name, simpleValue);\n        return simpleValue;\n        // Does not enforce a delimiter to be universal\n    }\n\n    function setOption(parent, name, value) {\n        if (parent.setOption)\n            parent.setOption(name, value);\n    }\n\n    function setParsedOption(parent, name, value, propName) {\n        if (parent.setParsedOption)\n            parent.setParsedOption(name, value, propName);\n    }\n\n    function parseInlineOptions(parent) {\n        if (skip(\"[\", true)) {\n            do {\n                parseOption(parent, \"option\");\n            } while (skip(\",\", true));\n            skip(\"]\");\n        }\n        return parent;\n    }\n\n    function parseService(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"service name\");\n\n        var service = new Service(token);\n        ifBlock(service, function parseService_block(token) {\n            if (parseCommon(service, token))\n                return;\n\n            /* istanbul ignore else */\n            if (token === \"rpc\")\n                parseMethod(service, token);\n            else\n                throw illegal(token);\n        });\n        parent.add(service);\n    }\n\n    function parseMethod(parent, token) {\n        // Get the comment of the preceding line now (if one exists) in case the\n        // method is defined across multiple lines.\n        var commentText = cmnt();\n\n        var type = token;\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var name = token,\n            requestType, requestStream,\n            responseType, responseStream;\n\n        skip(\"(\");\n        if (skip(\"stream\", true))\n            requestStream = true;\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token);\n\n        requestType = token;\n        skip(\")\"); skip(\"returns\"); skip(\"(\");\n        if (skip(\"stream\", true))\n            responseStream = true;\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token);\n\n        responseType = token;\n        skip(\")\");\n\n        var method = new Method(name, type, requestType, responseType, requestStream, responseStream);\n        method.comment = commentText;\n        ifBlock(method, function parseMethod_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(method, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        });\n        parent.add(method);\n    }\n\n    function parseExtension(parent, token) {\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token, \"reference\");\n\n        var reference = token;\n        ifBlock(null, function parseExtension_block(token) {\n            switch (token) {\n\n                case \"required\":\n                case \"repeated\":\n                    parseField(parent, token, reference);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (isProto3) {\n                        parseField(parent, \"proto3_optional\", reference);\n                    } else {\n                        parseField(parent, \"optional\", reference);\n                    }\n                    break;\n\n                default:\n                    /* istanbul ignore if */\n                    if (!isProto3 || !typeRefRe.test(token))\n                        throw illegal(token);\n                    push(token);\n                    parseField(parent, \"optional\", reference);\n                    break;\n            }\n        });\n    }\n\n    var token;\n    while ((token = next()) !== null) {\n        switch (token) {\n\n            case \"package\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parsePackage();\n                break;\n\n            case \"import\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parseImport();\n                break;\n\n            case \"syntax\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parseSyntax();\n                break;\n\n            case \"option\":\n\n                parseOption(ptr, token);\n                skip(\";\");\n                break;\n\n            default:\n\n                /* istanbul ignore else */\n                if (parseCommon(ptr, token)) {\n                    head = false;\n                    continue;\n                }\n\n                /* istanbul ignore next */\n                throw illegal(token);\n        }\n    }\n\n    parse.filename = null;\n    return {\n        \"package\"     : pkg,\n        \"imports\"     : imports,\n         weakImports  : weakImports,\n         syntax       : syntax,\n         root         : root\n    };\n}\n\n/**\n * Parses the given .proto source and returns an object with the parsed contents.\n * @name parse\n * @function\n * @param {string} source Source contents\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {IParserResult} Parser result\n * @property {string} filename=null Currently processing file name for error reporting, if known\n * @property {IParseOptions} defaults Default {@link IParseOptions}\n * @variation 2\n */\n", "\"use strict\";\nmodule.exports = Reader;\n\nvar util      = require(39);\n\nvar BufferReader; // cyclic\n\nvar LongBits  = util.LongBits,\n    utf8      = util.utf8;\n\n/* istanbul ignore next */\nfunction indexOutOfRange(reader, writeLength) {\n    return RangeError(\"index out of range: \" + reader.pos + \" + \" + (writeLength || 1) + \" > \" + reader.len);\n}\n\n/**\n * Constructs a new reader instance using the specified buffer.\n * @classdesc Wire format reader using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n * @param {Uint8Array} buffer Buffer to read from\n */\nfunction Reader(buffer) {\n\n    /**\n     * Read buffer.\n     * @type {Uint8Array}\n     */\n    this.buf = buffer;\n\n    /**\n     * Read buffer position.\n     * @type {number}\n     */\n    this.pos = 0;\n\n    /**\n     * Read buffer length.\n     * @type {number}\n     */\n    this.len = buffer.length;\n}\n\nvar create_array = typeof Uint8Array !== \"undefined\"\n    ? function create_typed_array(buffer) {\n        if (buffer instanceof Uint8Array || Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    }\n    /* istanbul ignore next */\n    : function create_array(buffer) {\n        if (Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    };\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup(buffer) {\n            return (Reader.create = function create_buffer(buffer) {\n                return util.Buffer.isBuffer(buffer)\n                    ? new BufferReader(buffer)\n                    /* istanbul ignore next */\n                    : create_array(buffer);\n            })(buffer);\n        }\n        /* istanbul ignore next */\n        : create_array;\n};\n\n/**\n * Creates a new reader using the specified buffer.\n * @function\n * @param {Uint8Array|Buffer} buffer Buffer to read from\n * @returns {Reader|BufferReader} A {@link BufferReader} if `buffer` is a Buffer, otherwise a {@link Reader}\n * @throws {Error} If `buffer` is not a valid buffer\n */\nReader.create = create();\n\nReader.prototype._slice = util.Array.prototype.subarray || /* istanbul ignore next */ util.Array.prototype.slice;\n\n/**\n * Reads a varint as an unsigned 32 bit value.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.uint32 = (function read_uint32_setup() {\n    var value = 4294967295; // optimizer type-hint, tends to deopt otherwise (?!)\n    return function read_uint32() {\n        value = (         this.buf[this.pos] & 127       ) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) <<  7) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 14) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 21) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] &  15) << 28) >>> 0; if (this.buf[this.pos++] < 128) return value;\n\n        /* istanbul ignore if */\n        if ((this.pos += 5) > this.len) {\n            this.pos = this.len;\n            throw indexOutOfRange(this, 10);\n        }\n        return value;\n    };\n})();\n\n/**\n * Reads a varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.int32 = function read_int32() {\n    return this.uint32() | 0;\n};\n\n/**\n * Reads a zig-zag encoded varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.sint32 = function read_sint32() {\n    var value = this.uint32();\n    return value >>> 1 ^ -(value & 1) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readLongVarint() {\n    // tends to deopt with local vars for octet etc.\n    var bits = new LongBits(0, 0);\n    var i = 0;\n    if (this.len - this.pos > 4) { // fast route (lo)\n        for (; i < 4; ++i) {\n            // 1st..4th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 5th\n        bits.lo = (bits.lo | (this.buf[this.pos] & 127) << 28) >>> 0;\n        bits.hi = (bits.hi | (this.buf[this.pos] & 127) >>  4) >>> 0;\n        if (this.buf[this.pos++] < 128)\n            return bits;\n        i = 0;\n    } else {\n        for (; i < 3; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 1st..3th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 4th\n        bits.lo = (bits.lo | (this.buf[this.pos++] & 127) << i * 7) >>> 0;\n        return bits;\n    }\n    if (this.len - this.pos > 4) { // fast route (hi)\n        for (; i < 5; ++i) {\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    } else {\n        for (; i < 5; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    }\n    /* istanbul ignore next */\n    throw Error(\"invalid varint encoding\");\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads a varint as a signed 64 bit value.\n * @name Reader#int64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as an unsigned 64 bit value.\n * @name Reader#uint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a zig-zag encoded varint as a signed 64 bit value.\n * @name Reader#sint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as a boolean.\n * @returns {boolean} Value read\n */\nReader.prototype.bool = function read_bool() {\n    return this.uint32() !== 0;\n};\n\nfunction readFixed32_end(buf, end) { // note that this uses `end`, not `pos`\n    return (buf[end - 4]\n          | buf[end - 3] << 8\n          | buf[end - 2] << 16\n          | buf[end - 1] << 24) >>> 0;\n}\n\n/**\n * Reads fixed 32 bits as an unsigned 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.fixed32 = function read_fixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4);\n};\n\n/**\n * Reads fixed 32 bits as a signed 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.sfixed32 = function read_sfixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readFixed64(/* this: Reader */) {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 8);\n\n    return new LongBits(readFixed32_end(this.buf, this.pos += 4), readFixed32_end(this.buf, this.pos += 4));\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads fixed 64 bits.\n * @name Reader#fixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads zig-zag encoded fixed 64 bits.\n * @name Reader#sfixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a float (32 bit) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.float = function read_float() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readFloatLE(this.buf, this.pos);\n    this.pos += 4;\n    return value;\n};\n\n/**\n * Reads a double (64 bit float) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.double = function read_double() {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readDoubleLE(this.buf, this.pos);\n    this.pos += 8;\n    return value;\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @returns {Uint8Array} Value read\n */\nReader.prototype.bytes = function read_bytes() {\n    var length = this.uint32(),\n        start  = this.pos,\n        end    = this.pos + length;\n\n    /* istanbul ignore if */\n    if (end > this.len)\n        throw indexOutOfRange(this, length);\n\n    this.pos += length;\n    if (Array.isArray(this.buf)) // plain array\n        return this.buf.slice(start, end);\n    return start === end // fix for IE 10/Win8 and others' subarray returning array of size 1\n        ? new this.buf.constructor(0)\n        : this._slice.call(this.buf, start, end);\n};\n\n/**\n * Reads a string preceeded by its byte length as a varint.\n * @returns {string} Value read\n */\nReader.prototype.string = function read_string() {\n    var bytes = this.bytes();\n    return utf8.read(bytes, 0, bytes.length);\n};\n\n/**\n * Skips the specified number of bytes if specified, otherwise skips a varint.\n * @param {number} [length] Length if known, otherwise a varint is assumed\n * @returns {Reader} `this`\n */\nReader.prototype.skip = function skip(length) {\n    if (typeof length === \"number\") {\n        /* istanbul ignore if */\n        if (this.pos + length > this.len)\n            throw indexOutOfRange(this, length);\n        this.pos += length;\n    } else {\n        do {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n        } while (this.buf[this.pos++] & 128);\n    }\n    return this;\n};\n\n/**\n * Skips the next element of the specified wire type.\n * @param {number} wireType Wire type received\n * @returns {Reader} `this`\n */\nReader.prototype.skipType = function(wireType) {\n    switch (wireType) {\n        case 0:\n            this.skip();\n            break;\n        case 1:\n            this.skip(8);\n            break;\n        case 2:\n            this.skip(this.uint32());\n            break;\n        case 3:\n            while ((wireType = this.uint32() & 7) !== 4) {\n                this.skipType(wireType);\n            }\n            break;\n        case 5:\n            this.skip(4);\n            break;\n\n        /* istanbul ignore next */\n        default:\n            throw Error(\"invalid wire type \" + wireType + \" at offset \" + this.pos);\n    }\n    return this;\n};\n\nReader._configure = function(BufferReader_) {\n    BufferReader = BufferReader_;\n    Reader.create = create();\n    BufferReader._configure();\n\n    var fn = util.Long ? \"toLong\" : /* istanbul ignore next */ \"toNumber\";\n    util.merge(Reader.prototype, {\n\n        int64: function read_int64() {\n            return readLongVarint.call(this)[fn](false);\n        },\n\n        uint64: function read_uint64() {\n            return readLongVarint.call(this)[fn](true);\n        },\n\n        sint64: function read_sint64() {\n            return readLongVarint.call(this).zzDecode()[fn](false);\n        },\n\n        fixed64: function read_fixed64() {\n            return readFixed64.call(this)[fn](true);\n        },\n\n        sfixed64: function read_sfixed64() {\n            return readFixed64.call(this)[fn](false);\n        }\n\n    });\n};\n", "\"use strict\";\nmodule.exports = B<PERSON>erReader;\n\n// extends Reader\nvar Reader = require(27);\n(BufferReader.prototype = Object.create(Reader.prototype)).constructor = BufferReader;\n\nvar util = require(39);\n\n/**\n * Constructs a new buffer reader instance.\n * @classdesc Wire format reader using node buffers.\n * @extends Reader\n * @constructor\n * @param {Buffer} buffer Buffer to read from\n */\nfunction BufferReader(buffer) {\n    Reader.call(this, buffer);\n\n    /**\n     * Read buffer.\n     * @name BufferReader#buf\n     * @type {Buffer}\n     */\n}\n\nBufferReader._configure = function () {\n    /* istanbul ignore else */\n    if (util.Buffer)\n        BufferReader.prototype._slice = util.Buffer.prototype.slice;\n};\n\n\n/**\n * @override\n */\nBufferReader.prototype.string = function read_string_buffer() {\n    var len = this.uint32(); // modifies pos\n    return this.buf.utf8Slice\n        ? this.buf.utf8Slice(this.pos, this.pos = Math.min(this.pos + len, this.len))\n        : this.buf.toString(\"utf-8\", this.pos, this.pos = Math.min(this.pos + len, this.len));\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @name BufferReader#bytes\n * @function\n * @returns {Buffer} Value read\n */\n\nBufferReader._configure();\n", "\"use strict\";\nmodule.exports = Root;\n\n// extends Namespace\nvar Namespace = require(23);\n((Root.prototype = Object.create(Namespace.prototype)).constructor = Root).className = \"Root\";\n\nvar Field   = require(16),\n    Enum    = require(15),\n    OneOf   = require(25),\n    util    = require(37);\n\nvar Type,   // cyclic\n    parse,  // might be excluded\n    common; // \"\n\n/**\n * Constructs a new root namespace instance.\n * @classdesc Root namespace wrapping all types, enums, services, sub-namespaces etc. that belong together.\n * @extends NamespaceBase\n * @constructor\n * @param {Object.<string,*>} [options] Top level options\n */\nfunction Root(options) {\n    Namespace.call(this, \"\", options);\n\n    /**\n     * Deferred extension fields.\n     * @type {Field[]}\n     */\n    this.deferred = [];\n\n    /**\n     * Resolved file names of loaded files.\n     * @type {string[]}\n     */\n    this.files = [];\n}\n\n/**\n * Loads a namespace descriptor into a root namespace.\n * @param {INamespace} json Nameespace descriptor\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted\n * @returns {Root} Root namespace\n */\nRoot.fromJSON = function fromJSON(json, root) {\n    if (!root)\n        root = new Root();\n    if (json.options)\n        root.setOptions(json.options);\n    return root.addJSON(json.nested);\n};\n\n/**\n * Resolves the path of an imported file, relative to the importing origin.\n * This method exists so you can override it with your own logic in case your imports are scattered over multiple directories.\n * @function\n * @param {string} origin The file name of the importing file\n * @param {string} target The file name being imported\n * @returns {string|null} Resolved path to `target` or `null` to skip the file\n */\nRoot.prototype.resolvePath = util.path.resolve;\n\n/**\n * Fetch content from file path or url\n * This method exists so you can override it with your own logic.\n * @function\n * @param {string} path File path or url\n * @param {FetchCallback} callback Callback function\n * @returns {undefined}\n */\nRoot.prototype.fetch = util.fetch;\n\n// A symbol-like function to safely signal synchronous loading\n/* istanbul ignore next */\nfunction SYNC() {} // eslint-disable-line no-empty-function\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} options Parse options\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n */\nRoot.prototype.load = function load(filename, options, callback) {\n    if (typeof options === \"function\") {\n        callback = options;\n        options = undefined;\n    }\n    var self = this;\n    if (!callback)\n        return util.asPromise(load, self, filename, options);\n\n    var sync = callback === SYNC; // undocumented\n\n    // Finishes loading by calling the callback (exactly once)\n    function finish(err, root) {\n        /* istanbul ignore if */\n        if (!callback)\n            return;\n        var cb = callback;\n        callback = null;\n        if (sync)\n            throw err;\n        cb(err, root);\n    }\n\n    // Bundled definition existence checking\n    function getBundledFileName(filename) {\n        var idx = filename.lastIndexOf(\"google/protobuf/\");\n        if (idx > -1) {\n            var altname = filename.substring(idx);\n            if (altname in common) return altname;\n        }\n        return null;\n    }\n\n    // Processes a single file\n    function process(filename, source) {\n        try {\n            if (util.isString(source) && source.charAt(0) === \"{\")\n                source = JSON.parse(source);\n            if (!util.isString(source))\n                self.setOptions(source.options).addJSON(source.nested);\n            else {\n                parse.filename = filename;\n                var parsed = parse(source, self, options),\n                    resolved,\n                    i = 0;\n                if (parsed.imports)\n                    for (; i < parsed.imports.length; ++i)\n                        if (resolved = getBundledFileName(parsed.imports[i]) || self.resolvePath(filename, parsed.imports[i]))\n                            fetch(resolved);\n                if (parsed.weakImports)\n                    for (i = 0; i < parsed.weakImports.length; ++i)\n                        if (resolved = getBundledFileName(parsed.weakImports[i]) || self.resolvePath(filename, parsed.weakImports[i]))\n                            fetch(resolved, true);\n            }\n        } catch (err) {\n            finish(err);\n        }\n        if (!sync && !queued)\n            finish(null, self); // only once anyway\n    }\n\n    // Fetches a single file\n    function fetch(filename, weak) {\n\n        // Skip if already loaded / attempted\n        if (self.files.indexOf(filename) > -1)\n            return;\n        self.files.push(filename);\n\n        // Shortcut bundled definitions\n        if (filename in common) {\n            if (sync)\n                process(filename, common[filename]);\n            else {\n                ++queued;\n                setTimeout(function() {\n                    --queued;\n                    process(filename, common[filename]);\n                });\n            }\n            return;\n        }\n\n        // Otherwise fetch from disk or network\n        if (sync) {\n            var source;\n            try {\n                source = util.fs.readFileSync(filename).toString(\"utf8\");\n            } catch (err) {\n                if (!weak)\n                    finish(err);\n                return;\n            }\n            process(filename, source);\n        } else {\n            ++queued;\n            self.fetch(filename, function(err, source) {\n                --queued;\n                /* istanbul ignore if */\n                if (!callback)\n                    return; // terminated meanwhile\n                if (err) {\n                    /* istanbul ignore else */\n                    if (!weak)\n                        finish(err);\n                    else if (!queued) // can't be covered reliably\n                        finish(null, self);\n                    return;\n                }\n                process(filename, source);\n            });\n        }\n    }\n    var queued = 0;\n\n    // Assembling the root namespace doesn't require working type\n    // references anymore, so we can load everything in parallel\n    if (util.isString(filename))\n        filename = [ filename ];\n    for (var i = 0, resolved; i < filename.length; ++i)\n        if (resolved = self.resolvePath(\"\", filename[i]))\n            fetch(resolved);\n\n    if (sync)\n        return self;\n    if (!queued)\n        finish(null, self);\n    return undefined;\n};\n// function load(filename:string, options:IParseOptions, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and returns a promise.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Promise<Root>} Promise\n * @variation 3\n */\n// function load(filename:string, [options:IParseOptions]):Promise<Root>\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into this root namespace (node only).\n * @function Root#loadSync\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n */\nRoot.prototype.loadSync = function loadSync(filename, options) {\n    if (!util.isNode)\n        throw Error(\"not supported\");\n    return this.load(filename, options, SYNC);\n};\n\n/**\n * @override\n */\nRoot.prototype.resolveAll = function resolveAll() {\n    if (this.deferred.length)\n        throw Error(\"unresolvable extensions: \" + this.deferred.map(function(field) {\n            return \"'extend \" + field.extend + \"' in \" + field.parent.fullName;\n        }).join(\", \"));\n    return Namespace.prototype.resolveAll.call(this);\n};\n\n// only uppercased (and thus conflict-free) children are exposed, see below\nvar exposeRe = /^[A-Z]/;\n\n/**\n * Handles a deferred declaring extension field by creating a sister field to represent it within its extended type.\n * @param {Root} root Root instance\n * @param {Field} field Declaring extension field witin the declaring type\n * @returns {boolean} `true` if successfully added to the extended type, `false` otherwise\n * @inner\n * @ignore\n */\nfunction tryHandleExtension(root, field) {\n    var extendedType = field.parent.lookup(field.extend);\n    if (extendedType) {\n        var sisterField = new Field(field.fullName, field.id, field.type, field.rule, undefined, field.options);\n        sisterField.declaringField = field;\n        field.extensionField = sisterField;\n        extendedType.add(sisterField);\n        return true;\n    }\n    return false;\n}\n\n/**\n * Called when any object is added to this root or its sub-namespaces.\n * @param {ReflectionObject} object Object added\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleAdd = function _handleAdd(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field (implies not part of a oneof) */ object.extend !== undefined && /* not already handled */ !object.extensionField)\n            if (!tryHandleExtension(this, object))\n                this.deferred.push(object);\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object.values; // expose enum values as property of its parent\n\n    } else if (!(object instanceof OneOf)) /* everything else is a namespace */ {\n\n        if (object instanceof Type) // Try to handle any deferred extensions\n            for (var i = 0; i < this.deferred.length;)\n                if (tryHandleExtension(this, this.deferred[i]))\n                    this.deferred.splice(i, 1);\n                else\n                    ++i;\n        for (var j = 0; j < /* initializes */ object.nestedArray.length; ++j) // recurse into the namespace\n            this._handleAdd(object._nestedArray[j]);\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object; // expose namespace as property of its parent\n    }\n\n    // The above also adds uppercased (and thus conflict-free) nested types, services and enums as\n    // properties of namespaces just like static code does. This allows using a .d.ts generated for\n    // a static module with reflection-based solutions where the condition is met.\n};\n\n/**\n * Called when any object is removed from this root or its sub-namespaces.\n * @param {ReflectionObject} object Object removed\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleRemove = function _handleRemove(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field */ object.extend !== undefined) {\n            if (/* already handled */ object.extensionField) { // remove its sister field\n                object.extensionField.parent.remove(object.extensionField);\n                object.extensionField = null;\n            } else { // cancel the extension\n                var index = this.deferred.indexOf(object);\n                /* istanbul ignore else */\n                if (index > -1)\n                    this.deferred.splice(index, 1);\n            }\n        }\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose enum values\n\n    } else if (object instanceof Namespace) {\n\n        for (var i = 0; i < /* initializes */ object.nestedArray.length; ++i) // recurse into the namespace\n            this._handleRemove(object._nestedArray[i]);\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose namespaces\n\n    }\n};\n\n// Sets up cyclic dependencies (called in index-light)\nRoot._configure = function(Type_, parse_, common_) {\n    Type   = Type_;\n    parse  = parse_;\n    common = common_;\n};\n", "\"use strict\";\nmodule.exports = {};\n\n/**\n * Named roots.\n * This is where pbjs stores generated structures (the option `-r, --root` specifies a name).\n * Can also be used manually to make roots available accross modules.\n * @name roots\n * @type {Object.<string,Root>}\n * @example\n * // pbjs -r myroot -o compiled.js ...\n *\n * // in another module:\n * require(\"./compiled.js\");\n *\n * // in any subsequent module:\n * var root = protobuf.roots[\"myroot\"];\n */\n", "\"use strict\";\n\n/**\n * Streaming RPC helpers.\n * @namespace\n */\nvar rpc = exports;\n\n/**\n * RPC implementation passed to {@link Service#create} performing a service request on network level, i.e. by utilizing http requests or websockets.\n * @typedef RPCImpl\n * @type {function}\n * @param {Method|rpc.ServiceMethod<Message<{}>,Message<{}>>} method Reflected or static method being called\n * @param {Uint8Array} requestData Request data\n * @param {RPCImplCallback} callback Callback function\n * @returns {undefined}\n * @example\n * function rpcImpl(method, requestData, callback) {\n *     if (protobuf.util.lcFirst(method.name) !== \"myMethod\") // compatible with static code\n *         throw Error(\"no such method\");\n *     asynchronouslyObtainAResponse(requestData, function(err, responseData) {\n *         callback(err, responseData);\n *     });\n * }\n */\n\n/**\n * Node-style callback as used by {@link RPCImpl}.\n * @typedef RPCImplCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Uint8Array|null} [response] Response data or `null` to signal end of stream, if there hasn't been an error\n * @returns {undefined}\n */\n\nrpc.Service = require(32);\n", "\"use strict\";\nmodule.exports = Service;\n\nvar util = require(39);\n\n// Extends EventEmitter\n(Service.prototype = Object.create(util.EventEmitter.prototype)).constructor = Service;\n\n/**\n * A service method callback as used by {@link rpc.ServiceMethod|ServiceMethod}.\n *\n * Differs from {@link RPCImplCallback} in that it is an actual callback of a service method which may not return `response = null`.\n * @typedef rpc.ServiceMethodCallback\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {Error|null} error Error, if any\n * @param {TRes} [response] Response message\n * @returns {undefined}\n */\n\n/**\n * A service method part of a {@link rpc.Service} as created by {@link Service.create}.\n * @typedef rpc.ServiceMethod\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} [callback] Node-style callback called with the error, if any, and the response message\n * @returns {Promise<Message<TRes>>} Promise if `callback` has been omitted, otherwise `undefined`\n */\n\n/**\n * Constructs a new RPC service instance.\n * @classdesc An RPC service as returned by {@link Service#create}.\n * @exports rpc.Service\n * @extends util.EventEmitter\n * @constructor\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n */\nfunction Service(rpcImpl, requestDelimited, responseDelimited) {\n\n    if (typeof rpcImpl !== \"function\")\n        throw TypeError(\"rpcImpl must be a function\");\n\n    util.EventEmitter.call(this);\n\n    /**\n     * RPC implementation. Becomes `null` once the service is ended.\n     * @type {RPCImpl|null}\n     */\n    this.rpcImpl = rpcImpl;\n\n    /**\n     * Whether requests are length-delimited.\n     * @type {boolean}\n     */\n    this.requestDelimited = Boolean(requestDelimited);\n\n    /**\n     * Whether responses are length-delimited.\n     * @type {boolean}\n     */\n    this.responseDelimited = Boolean(responseDelimited);\n}\n\n/**\n * Calls a service method through {@link rpc.Service#rpcImpl|rpcImpl}.\n * @param {Method|rpc.ServiceMethod<TReq,TRes>} method Reflected or static method\n * @param {Constructor<TReq>} requestCtor Request constructor\n * @param {Constructor<TRes>} responseCtor Response constructor\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} callback Service callback\n * @returns {undefined}\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n */\nService.prototype.rpcCall = function rpcCall(method, requestCtor, responseCtor, request, callback) {\n\n    if (!request)\n        throw TypeError(\"request must be specified\");\n\n    var self = this;\n    if (!callback)\n        return util.asPromise(rpcCall, self, method, requestCtor, responseCtor, request);\n\n    if (!self.rpcImpl) {\n        setTimeout(function() { callback(Error(\"already ended\")); }, 0);\n        return undefined;\n    }\n\n    try {\n        return self.rpcImpl(\n            method,\n            requestCtor[self.requestDelimited ? \"encodeDelimited\" : \"encode\"](request).finish(),\n            function rpcCallback(err, response) {\n\n                if (err) {\n                    self.emit(\"error\", err, method);\n                    return callback(err);\n                }\n\n                if (response === null) {\n                    self.end(/* endedByRPC */ true);\n                    return undefined;\n                }\n\n                if (!(response instanceof responseCtor)) {\n                    try {\n                        response = responseCtor[self.responseDelimited ? \"decodeDelimited\" : \"decode\"](response);\n                    } catch (err) {\n                        self.emit(\"error\", err, method);\n                        return callback(err);\n                    }\n                }\n\n                self.emit(\"data\", response, method);\n                return callback(null, response);\n            }\n        );\n    } catch (err) {\n        self.emit(\"error\", err, method);\n        setTimeout(function() { callback(err); }, 0);\n        return undefined;\n    }\n};\n\n/**\n * Ends this service and emits the `end` event.\n * @param {boolean} [endedByRPC=false] Whether the service has been ended by the RPC implementation.\n * @returns {rpc.Service} `this`\n */\nService.prototype.end = function end(endedByRPC) {\n    if (this.rpcImpl) {\n        if (!endedByRPC) // signal end to rpcImpl\n            this.rpcImpl(null, null, null);\n        this.rpcImpl = null;\n        this.emit(\"end\").off();\n    }\n    return this;\n};\n", "\"use strict\";\nmodule.exports = Service;\n\n// extends Namespace\nvar Namespace = require(23);\n((Service.prototype = Object.create(Namespace.prototype)).constructor = Service).className = \"Service\";\n\nvar Method = require(22),\n    util   = require(37),\n    rpc    = require(31);\n\n/**\n * Constructs a new service instance.\n * @classdesc Reflected service.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Service name\n * @param {Object.<string,*>} [options] Service options\n * @throws {TypeError} If arguments are invalid\n */\nfunction Service(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Service methods.\n     * @type {Object.<string,Method>}\n     */\n    this.methods = {}; // toJSON, marker\n\n    /**\n     * Cached methods as an array.\n     * @type {Method[]|null}\n     * @private\n     */\n    this._methodsArray = null;\n}\n\n/**\n * Service descriptor.\n * @interface IService\n * @extends INamespace\n * @property {Object.<string,IMethod>} methods Method descriptors\n */\n\n/**\n * Constructs a service from a service descriptor.\n * @param {string} name Service name\n * @param {IService} json Service descriptor\n * @returns {Service} Created service\n * @throws {TypeError} If arguments are invalid\n */\nService.fromJSON = function fromJSON(name, json) {\n    var service = new Service(name, json.options);\n    /* istanbul ignore else */\n    if (json.methods)\n        for (var names = Object.keys(json.methods), i = 0; i < names.length; ++i)\n            service.add(Method.fromJSON(names[i], json.methods[names[i]]));\n    if (json.nested)\n        service.addJSON(json.nested);\n    service.comment = json.comment;\n    return service;\n};\n\n/**\n * Converts this service to a service descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IService} Service descriptor\n */\nService.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\" , inherited && inherited.options || undefined,\n        \"methods\" , Namespace.arrayToJSON(this.methodsArray, toJSONOptions) || /* istanbul ignore next */ {},\n        \"nested\"  , inherited && inherited.nested || undefined,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Methods of this service as an array for iteration.\n * @name Service#methodsArray\n * @type {Method[]}\n * @readonly\n */\nObject.defineProperty(Service.prototype, \"methodsArray\", {\n    get: function() {\n        return this._methodsArray || (this._methodsArray = util.toArray(this.methods));\n    }\n});\n\nfunction clearCache(service) {\n    service._methodsArray = null;\n    return service;\n}\n\n/**\n * @override\n */\nService.prototype.get = function get(name) {\n    return this.methods[name]\n        || Namespace.prototype.get.call(this, name);\n};\n\n/**\n * @override\n */\nService.prototype.resolveAll = function resolveAll() {\n    var methods = this.methodsArray;\n    for (var i = 0; i < methods.length; ++i)\n        methods[i].resolve();\n    return Namespace.prototype.resolve.call(this);\n};\n\n/**\n * @override\n */\nService.prototype.add = function add(object) {\n\n    /* istanbul ignore if */\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Method) {\n        this.methods[object.name] = object;\n        object.parent = this;\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * @override\n */\nService.prototype.remove = function remove(object) {\n    if (object instanceof Method) {\n\n        /* istanbul ignore if */\n        if (this.methods[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.methods[object.name];\n        object.parent = null;\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Creates a runtime service using the specified rpc implementation.\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n * @returns {rpc.Service} RPC service. Useful where requests and/or responses are streamed.\n */\nService.prototype.create = function create(rpcImpl, requestDelimited, responseDelimited) {\n    var rpcService = new rpc.Service(rpcImpl, requestDelimited, responseDelimited);\n    for (var i = 0, method; i < /* initializes */ this.methodsArray.length; ++i) {\n        var methodName = util.lcFirst((method = this._methodsArray[i]).resolve().name).replace(/[^$\\w_]/g, \"\");\n        rpcService[methodName] = util.codegen([\"r\",\"c\"], util.isReserved(methodName) ? methodName + \"_\" : methodName)(\"return this.rpcCall(m,q,s,r,c)\")({\n            m: method,\n            q: method.resolvedRequestType.ctor,\n            s: method.resolvedResponseType.ctor\n        });\n    }\n    return rpcService;\n};\n", "\"use strict\";\nmodule.exports = tokenize;\n\nvar delimRe        = /[\\s{}=;:[\\],'\"()<>]/g,\n    stringDoubleRe = /(?:\"([^\"\\\\]*(?:\\\\.[^\"\\\\]*)*)\")/g,\n    stringSingleRe = /(?:'([^'\\\\]*(?:\\\\.[^'\\\\]*)*)')/g;\n\nvar setCommentRe = /^ *[*/]+ */,\n    setCommentAltRe = /^\\s*\\*?\\/*/,\n    setCommentSplitRe = /\\n/g,\n    whitespaceRe = /\\s/,\n    unescapeRe = /\\\\(.?)/g;\n\nvar unescapeMap = {\n    \"0\": \"\\0\",\n    \"r\": \"\\r\",\n    \"n\": \"\\n\",\n    \"t\": \"\\t\"\n};\n\n/**\n * Unescapes a string.\n * @param {string} str String to unescape\n * @returns {string} Unescaped string\n * @property {Object.<string,string>} map Special characters map\n * @memberof tokenize\n */\nfunction unescape(str) {\n    return str.replace(unescapeRe, function($0, $1) {\n        switch ($1) {\n            case \"\\\\\":\n            case \"\":\n                return $1;\n            default:\n                return unescapeMap[$1] || \"\";\n        }\n    });\n}\n\ntokenize.unescape = unescape;\n\n/**\n * Gets the next token and advances.\n * @typedef TokenizerHandleNext\n * @type {function}\n * @returns {string|null} Next token or `null` on eof\n */\n\n/**\n * Peeks for the next token.\n * @typedef TokenizerHandlePeek\n * @type {function}\n * @returns {string|null} Next token or `null` on eof\n */\n\n/**\n * Pushes a token back to the stack.\n * @typedef TokenizerHandlePush\n * @type {function}\n * @param {string} token Token\n * @returns {undefined}\n */\n\n/**\n * Skips the next token.\n * @typedef TokenizerHandleSkip\n * @type {function}\n * @param {string} expected Expected token\n * @param {boolean} [optional=false] If optional\n * @returns {boolean} Whether the token matched\n * @throws {Error} If the token didn't match and is not optional\n */\n\n/**\n * Gets the comment on the previous line or, alternatively, the line comment on the specified line.\n * @typedef TokenizerHandleCmnt\n * @type {function}\n * @param {number} [line] Line number\n * @returns {string|null} Comment text or `null` if none\n */\n\n/**\n * Handle object returned from {@link tokenize}.\n * @interface ITokenizerHandle\n * @property {TokenizerHandleNext} next Gets the next token and advances (`null` on eof)\n * @property {TokenizerHandlePeek} peek Peeks for the next token (`null` on eof)\n * @property {TokenizerHandlePush} push Pushes a token back to the stack\n * @property {TokenizerHandleSkip} skip Skips a token, returns its presence and advances or, if non-optional and not present, throws\n * @property {TokenizerHandleCmnt} cmnt Gets the comment on the previous line or the line comment on the specified line, if any\n * @property {number} line Current line number\n */\n\n/**\n * Tokenizes the given .proto source and returns an object with useful utility functions.\n * @param {string} source Source contents\n * @param {boolean} alternateCommentMode Whether we should activate alternate comment parsing mode.\n * @returns {ITokenizerHandle} Tokenizer handle\n */\nfunction tokenize(source, alternateCommentMode) {\n    /* eslint-disable callback-return */\n    source = source.toString();\n\n    var offset = 0,\n        length = source.length,\n        line = 1,\n        commentType = null,\n        commentText = null,\n        commentLine = 0,\n        commentLineEmpty = false,\n        commentIsLeading = false;\n\n    var stack = [];\n\n    var stringDelim = null;\n\n    /* istanbul ignore next */\n    /**\n     * Creates an error for illegal syntax.\n     * @param {string} subject Subject\n     * @returns {Error} Error created\n     * @inner\n     */\n    function illegal(subject) {\n        return Error(\"illegal \" + subject + \" (line \" + line + \")\");\n    }\n\n    /**\n     * Reads a string till its end.\n     * @returns {string} String read\n     * @inner\n     */\n    function readString() {\n        var re = stringDelim === \"'\" ? stringSingleRe : stringDoubleRe;\n        re.lastIndex = offset - 1;\n        var match = re.exec(source);\n        if (!match)\n            throw illegal(\"string\");\n        offset = re.lastIndex;\n        push(stringDelim);\n        stringDelim = null;\n        return unescape(match[1]);\n    }\n\n    /**\n     * Gets the character at `pos` within the source.\n     * @param {number} pos Position\n     * @returns {string} Character\n     * @inner\n     */\n    function charAt(pos) {\n        return source.charAt(pos);\n    }\n\n    /**\n     * Sets the current comment text.\n     * @param {number} start Start offset\n     * @param {number} end End offset\n     * @param {boolean} isLeading set if a leading comment\n     * @returns {undefined}\n     * @inner\n     */\n    function setComment(start, end, isLeading) {\n        commentType = source.charAt(start++);\n        commentLine = line;\n        commentLineEmpty = false;\n        commentIsLeading = isLeading;\n        var lookback;\n        if (alternateCommentMode) {\n            lookback = 2;  // alternate comment parsing: \"//\" or \"/*\"\n        } else {\n            lookback = 3;  // \"///\" or \"/**\"\n        }\n        var commentOffset = start - lookback,\n            c;\n        do {\n            if (--commentOffset < 0 ||\n                    (c = source.charAt(commentOffset)) === \"\\n\") {\n                commentLineEmpty = true;\n                break;\n            }\n        } while (c === \" \" || c === \"\\t\");\n        var lines = source\n            .substring(start, end)\n            .split(setCommentSplitRe);\n        for (var i = 0; i < lines.length; ++i)\n            lines[i] = lines[i]\n                .replace(alternateCommentMode ? setCommentAltRe : setCommentRe, \"\")\n                .trim();\n        commentText = lines\n            .join(\"\\n\")\n            .trim();\n    }\n\n    function isDoubleSlashCommentLine(startOffset) {\n        var endOffset = findEndOfLine(startOffset);\n\n        // see if remaining line matches comment pattern\n        var lineText = source.substring(startOffset, endOffset);\n        // look for 1 or 2 slashes since startOffset would already point past\n        // the first slash that started the comment.\n        var isComment = /^\\s*\\/{1,2}/.test(lineText);\n        return isComment;\n    }\n\n    function findEndOfLine(cursor) {\n        // find end of cursor's line\n        var endOffset = cursor;\n        while (endOffset < length && charAt(endOffset) !== \"\\n\") {\n            endOffset++;\n        }\n        return endOffset;\n    }\n\n    /**\n     * Obtains the next token.\n     * @returns {string|null} Next token or `null` on eof\n     * @inner\n     */\n    function next() {\n        if (stack.length > 0)\n            return stack.shift();\n        if (stringDelim)\n            return readString();\n        var repeat,\n            prev,\n            curr,\n            start,\n            isDoc,\n            isLeadingComment = offset === 0;\n        do {\n            if (offset === length)\n                return null;\n            repeat = false;\n            while (whitespaceRe.test(curr = charAt(offset))) {\n                if (curr === \"\\n\") {\n                    isLeadingComment = true;\n                    ++line;\n                }\n                if (++offset === length)\n                    return null;\n            }\n\n            if (charAt(offset) === \"/\") {\n                if (++offset === length) {\n                    throw illegal(\"comment\");\n                }\n                if (charAt(offset) === \"/\") { // Line\n                    if (!alternateCommentMode) {\n                        // check for triple-slash comment\n                        isDoc = charAt(start = offset + 1) === \"/\";\n\n                        while (charAt(++offset) !== \"\\n\") {\n                            if (offset === length) {\n                                return null;\n                            }\n                        }\n                        ++offset;\n                        if (isDoc) {\n                            setComment(start, offset - 1, isLeadingComment);\n                        }\n                        ++line;\n                        repeat = true;\n                    } else {\n                        // check for double-slash comments, consolidating consecutive lines\n                        start = offset;\n                        isDoc = false;\n                        if (isDoubleSlashCommentLine(offset)) {\n                            isDoc = true;\n                            do {\n                                offset = findEndOfLine(offset);\n                                if (offset === length) {\n                                    break;\n                                }\n                                offset++;\n                            } while (isDoubleSlashCommentLine(offset));\n                        } else {\n                            offset = Math.min(length, findEndOfLine(offset) + 1);\n                        }\n                        if (isDoc) {\n                            setComment(start, offset, isLeadingComment);\n                        }\n                        line++;\n                        repeat = true;\n                    }\n                } else if ((curr = charAt(offset)) === \"*\") { /* Block */\n                    // check for /** (regular comment mode) or /* (alternate comment mode)\n                    start = offset + 1;\n                    isDoc = alternateCommentMode || charAt(start) === \"*\";\n                    do {\n                        if (curr === \"\\n\") {\n                            ++line;\n                        }\n                        if (++offset === length) {\n                            throw illegal(\"comment\");\n                        }\n                        prev = curr;\n                        curr = charAt(offset);\n                    } while (prev !== \"*\" || curr !== \"/\");\n                    ++offset;\n                    if (isDoc) {\n                        setComment(start, offset - 2, isLeadingComment);\n                    }\n                    repeat = true;\n                } else {\n                    return \"/\";\n                }\n            }\n        } while (repeat);\n\n        // offset !== length if we got here\n\n        var end = offset;\n        delimRe.lastIndex = 0;\n        var delim = delimRe.test(charAt(end++));\n        if (!delim)\n            while (end < length && !delimRe.test(charAt(end)))\n                ++end;\n        var token = source.substring(offset, offset = end);\n        if (token === \"\\\"\" || token === \"'\")\n            stringDelim = token;\n        return token;\n    }\n\n    /**\n     * Pushes a token back to the stack.\n     * @param {string} token Token\n     * @returns {undefined}\n     * @inner\n     */\n    function push(token) {\n        stack.push(token);\n    }\n\n    /**\n     * Peeks for the next token.\n     * @returns {string|null} Token or `null` on eof\n     * @inner\n     */\n    function peek() {\n        if (!stack.length) {\n            var token = next();\n            if (token === null)\n                return null;\n            push(token);\n        }\n        return stack[0];\n    }\n\n    /**\n     * Skips a token.\n     * @param {string} expected Expected token\n     * @param {boolean} [optional=false] Whether the token is optional\n     * @returns {boolean} `true` when skipped, `false` if not\n     * @throws {Error} When a required token is not present\n     * @inner\n     */\n    function skip(expected, optional) {\n        var actual = peek(),\n            equals = actual === expected;\n        if (equals) {\n            next();\n            return true;\n        }\n        if (!optional)\n            throw illegal(\"token '\" + actual + \"', '\" + expected + \"' expected\");\n        return false;\n    }\n\n    /**\n     * Gets a comment.\n     * @param {number} [trailingLine] Line number if looking for a trailing comment\n     * @returns {string|null} Comment text\n     * @inner\n     */\n    function cmnt(trailingLine) {\n        var ret = null;\n        if (trailingLine === undefined) {\n            if (commentLine === line - 1 && (alternateCommentMode || commentType === \"*\" || commentLineEmpty)) {\n                ret = commentIsLeading ? commentText : null;\n            }\n        } else {\n            /* istanbul ignore else */\n            if (commentLine < trailingLine) {\n                peek();\n            }\n            if (commentLine === trailingLine && !commentLineEmpty && (alternateCommentMode || commentType === \"/\")) {\n                ret = commentIsLeading ? null : commentText;\n            }\n        }\n        return ret;\n    }\n\n    return Object.defineProperty({\n        next: next,\n        peek: peek,\n        push: push,\n        skip: skip,\n        cmnt: cmnt\n    }, \"line\", {\n        get: function() { return line; }\n    });\n    /* eslint-enable callback-return */\n}\n", "\"use strict\";\nmodule.exports = Type;\n\n// extends Namespace\nvar Namespace = require(23);\n((Type.prototype = Object.create(Namespace.prototype)).constructor = Type).className = \"Type\";\n\nvar Enum      = require(15),\n    OneOf     = require(25),\n    Field     = require(16),\n    MapField  = require(20),\n    Service   = require(33),\n    Message   = require(21),\n    Reader    = require(27),\n    Writer    = require(42),\n    util      = require(37),\n    encoder   = require(14),\n    decoder   = require(13),\n    verifier  = require(40),\n    converter = require(12),\n    wrappers  = require(41);\n\n/**\n * Constructs a new reflected message type instance.\n * @classdesc Reflected message type.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Message name\n * @param {Object.<string,*>} [options] Declared options\n */\nfunction Type(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Message fields.\n     * @type {Object.<string,Field>}\n     */\n    this.fields = {};  // toJSON, marker\n\n    /**\n     * Oneofs declared within this namespace, if any.\n     * @type {Object.<string,OneOf>}\n     */\n    this.oneofs = undefined; // toJSON\n\n    /**\n     * Extension ranges, if any.\n     * @type {number[][]}\n     */\n    this.extensions = undefined; // toJSON\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    /*?\n     * Whether this type is a legacy group.\n     * @type {boolean|undefined}\n     */\n    this.group = undefined; // toJSON\n\n    /**\n     * Cached fields by id.\n     * @type {Object.<number,Field>|null}\n     * @private\n     */\n    this._fieldsById = null;\n\n    /**\n     * Cached fields as an array.\n     * @type {Field[]|null}\n     * @private\n     */\n    this._fieldsArray = null;\n\n    /**\n     * Cached oneofs as an array.\n     * @type {OneOf[]|null}\n     * @private\n     */\n    this._oneofsArray = null;\n\n    /**\n     * Cached constructor.\n     * @type {Constructor<{}>}\n     * @private\n     */\n    this._ctor = null;\n}\n\nObject.defineProperties(Type.prototype, {\n\n    /**\n     * Message fields by id.\n     * @name Type#fieldsById\n     * @type {Object.<number,Field>}\n     * @readonly\n     */\n    fieldsById: {\n        get: function() {\n\n            /* istanbul ignore if */\n            if (this._fieldsById)\n                return this._fieldsById;\n\n            this._fieldsById = {};\n            for (var names = Object.keys(this.fields), i = 0; i < names.length; ++i) {\n                var field = this.fields[names[i]],\n                    id = field.id;\n\n                /* istanbul ignore if */\n                if (this._fieldsById[id])\n                    throw Error(\"duplicate id \" + id + \" in \" + this);\n\n                this._fieldsById[id] = field;\n            }\n            return this._fieldsById;\n        }\n    },\n\n    /**\n     * Fields of this message as an array for iteration.\n     * @name Type#fieldsArray\n     * @type {Field[]}\n     * @readonly\n     */\n    fieldsArray: {\n        get: function() {\n            return this._fieldsArray || (this._fieldsArray = util.toArray(this.fields));\n        }\n    },\n\n    /**\n     * Oneofs of this message as an array for iteration.\n     * @name Type#oneofsArray\n     * @type {OneOf[]}\n     * @readonly\n     */\n    oneofsArray: {\n        get: function() {\n            return this._oneofsArray || (this._oneofsArray = util.toArray(this.oneofs));\n        }\n    },\n\n    /**\n     * The registered constructor, if any registered, otherwise a generic constructor.\n     * Assigning a function replaces the internal constructor. If the function does not extend {@link Message} yet, its prototype will be setup accordingly and static methods will be populated. If it already extends {@link Message}, it will just replace the internal constructor.\n     * @name Type#ctor\n     * @type {Constructor<{}>}\n     */\n    ctor: {\n        get: function() {\n            return this._ctor || (this.ctor = Type.generateConstructor(this)());\n        },\n        set: function(ctor) {\n\n            // Ensure proper prototype\n            var prototype = ctor.prototype;\n            if (!(prototype instanceof Message)) {\n                (ctor.prototype = new Message()).constructor = ctor;\n                util.merge(ctor.prototype, prototype);\n            }\n\n            // Classes and messages reference their reflected type\n            ctor.$type = ctor.prototype.$type = this;\n\n            // Mix in static methods\n            util.merge(ctor, Message, true);\n\n            this._ctor = ctor;\n\n            // Messages have non-enumerable default values on their prototype\n            var i = 0;\n            for (; i < /* initializes */ this.fieldsArray.length; ++i)\n                this._fieldsArray[i].resolve(); // ensures a proper value\n\n            // Messages have non-enumerable getters and setters for each virtual oneof field\n            var ctorProperties = {};\n            for (i = 0; i < /* initializes */ this.oneofsArray.length; ++i)\n                ctorProperties[this._oneofsArray[i].resolve().name] = {\n                    get: util.oneOfGetter(this._oneofsArray[i].oneof),\n                    set: util.oneOfSetter(this._oneofsArray[i].oneof)\n                };\n            if (i)\n                Object.defineProperties(ctor.prototype, ctorProperties);\n        }\n    }\n});\n\n/**\n * Generates a constructor function for the specified type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nType.generateConstructor = function generateConstructor(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"p\"], mtype.name);\n    // explicitly initialize mutable object/array fields so that these aren't just inherited from the prototype\n    for (var i = 0, field; i < mtype.fieldsArray.length; ++i)\n        if ((field = mtype._fieldsArray[i]).map) gen\n            (\"this%s={}\", util.safeProp(field.name));\n        else if (field.repeated) gen\n            (\"this%s=[]\", util.safeProp(field.name));\n    return gen\n    (\"if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)\") // omit undefined or null\n        (\"this[ks[i]]=p[ks[i]]\");\n    /* eslint-enable no-unexpected-multiline */\n};\n\nfunction clearCache(type) {\n    type._fieldsById = type._fieldsArray = type._oneofsArray = null;\n    delete type.encode;\n    delete type.decode;\n    delete type.verify;\n    return type;\n}\n\n/**\n * Message type descriptor.\n * @interface IType\n * @extends INamespace\n * @property {Object.<string,IOneOf>} [oneofs] Oneof descriptors\n * @property {Object.<string,IField>} fields Field descriptors\n * @property {number[][]} [extensions] Extension ranges\n * @property {number[][]} [reserved] Reserved ranges\n * @property {boolean} [group=false] Whether a legacy group or not\n */\n\n/**\n * Creates a message type from a message type descriptor.\n * @param {string} name Message name\n * @param {IType} json Message type descriptor\n * @returns {Type} Created message type\n */\nType.fromJSON = function fromJSON(name, json) {\n    var type = new Type(name, json.options);\n    type.extensions = json.extensions;\n    type.reserved = json.reserved;\n    var names = Object.keys(json.fields),\n        i = 0;\n    for (; i < names.length; ++i)\n        type.add(\n            ( typeof json.fields[names[i]].keyType !== \"undefined\"\n            ? MapField.fromJSON\n            : Field.fromJSON )(names[i], json.fields[names[i]])\n        );\n    if (json.oneofs)\n        for (names = Object.keys(json.oneofs), i = 0; i < names.length; ++i)\n            type.add(OneOf.fromJSON(names[i], json.oneofs[names[i]]));\n    if (json.nested)\n        for (names = Object.keys(json.nested), i = 0; i < names.length; ++i) {\n            var nested = json.nested[names[i]];\n            type.add( // most to least likely\n                ( nested.id !== undefined\n                ? Field.fromJSON\n                : nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    if (json.extensions && json.extensions.length)\n        type.extensions = json.extensions;\n    if (json.reserved && json.reserved.length)\n        type.reserved = json.reserved;\n    if (json.group)\n        type.group = true;\n    if (json.comment)\n        type.comment = json.comment;\n    return type;\n};\n\n/**\n * Converts this message type to a message type descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IType} Message type descriptor\n */\nType.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\"    , inherited && inherited.options || undefined,\n        \"oneofs\"     , Namespace.arrayToJSON(this.oneofsArray, toJSONOptions),\n        \"fields\"     , Namespace.arrayToJSON(this.fieldsArray.filter(function(obj) { return !obj.declaringField; }), toJSONOptions) || {},\n        \"extensions\" , this.extensions && this.extensions.length ? this.extensions : undefined,\n        \"reserved\"   , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"group\"      , this.group || undefined,\n        \"nested\"     , inherited && inherited.nested || undefined,\n        \"comment\"    , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nType.prototype.resolveAll = function resolveAll() {\n    var fields = this.fieldsArray, i = 0;\n    while (i < fields.length)\n        fields[i++].resolve();\n    var oneofs = this.oneofsArray; i = 0;\n    while (i < oneofs.length)\n        oneofs[i++].resolve();\n    return Namespace.prototype.resolveAll.call(this);\n};\n\n/**\n * @override\n */\nType.prototype.get = function get(name) {\n    return this.fields[name]\n        || this.oneofs && this.oneofs[name]\n        || this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Adds a nested object to this type.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name or, if a field, when there is already a field with this id\n */\nType.prototype.add = function add(object) {\n\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Field && object.extend === undefined) {\n        // NOTE: Extension fields aren't actual fields on the declaring type, but nested objects.\n        // The root object takes care of adding distinct sister-fields to the respective extended\n        // type instead.\n\n        // avoids calling the getter if not absolutely necessary because it's called quite frequently\n        if (this._fieldsById ? /* istanbul ignore next */ this._fieldsById[object.id] : this.fieldsById[object.id])\n            throw Error(\"duplicate id \" + object.id + \" in \" + this);\n        if (this.isReservedId(object.id))\n            throw Error(\"id \" + object.id + \" is reserved in \" + this);\n        if (this.isReservedName(object.name))\n            throw Error(\"name '\" + object.name + \"' is reserved in \" + this);\n\n        if (object.parent)\n            object.parent.remove(object);\n        this.fields[object.name] = object;\n        object.message = this;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n        if (!this.oneofs)\n            this.oneofs = {};\n        this.oneofs[object.name] = object;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * Removes a nested object from this type.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this type\n */\nType.prototype.remove = function remove(object) {\n    if (object instanceof Field && object.extend === undefined) {\n        // See Type#add for the reason why extension fields are excluded here.\n\n        /* istanbul ignore if */\n        if (!this.fields || this.fields[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.fields[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n\n        /* istanbul ignore if */\n        if (!this.oneofs || this.oneofs[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.oneofs[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<{}>} Message instance\n */\nType.prototype.create = function create(properties) {\n    return new this.ctor(properties);\n};\n\n/**\n * Sets up {@link Type#encode|encode}, {@link Type#decode|decode} and {@link Type#verify|verify}.\n * @returns {Type} `this`\n */\nType.prototype.setup = function setup() {\n    // Sets up everything at once so that the prototype chain does not have to be re-evaluated\n    // multiple times (V8, soft-deopt prototype-check).\n\n    var fullName = this.fullName,\n        types    = [];\n    for (var i = 0; i < /* initializes */ this.fieldsArray.length; ++i)\n        types.push(this._fieldsArray[i].resolve().resolvedType);\n\n    // Replace setup methods with type-specific generated functions\n    this.encode = encoder(this)({\n        Writer : Writer,\n        types  : types,\n        util   : util\n    });\n    this.decode = decoder(this)({\n        Reader : Reader,\n        types  : types,\n        util   : util\n    });\n    this.verify = verifier(this)({\n        types : types,\n        util  : util\n    });\n    this.fromObject = converter.fromObject(this)({\n        types : types,\n        util  : util\n    });\n    this.toObject = converter.toObject(this)({\n        types : types,\n        util  : util\n    });\n\n    // Inject custom wrappers for common types\n    var wrapper = wrappers[fullName];\n    if (wrapper) {\n        var originalThis = Object.create(this);\n        // if (wrapper.fromObject) {\n            originalThis.fromObject = this.fromObject;\n            this.fromObject = wrapper.fromObject.bind(originalThis);\n        // }\n        // if (wrapper.toObject) {\n            originalThis.toObject = this.toObject;\n            this.toObject = wrapper.toObject.bind(originalThis);\n        // }\n    }\n\n    return this;\n};\n\n/**\n * Encodes a message of this type. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encode = function encode_setup(message, writer) {\n    return this.setup().encode(message, writer); // overrides this method\n};\n\n/**\n * Encodes a message of this type preceeded by its byte length as a varint. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.encode(message, writer && writer.len ? writer.fork() : writer).ldelim();\n};\n\n/**\n * Decodes a message of this type.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @param {number} [length] Length of the message, if known beforehand\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError<{}>} If required fields are missing\n */\nType.prototype.decode = function decode_setup(reader, length) {\n    return this.setup().decode(reader, length); // overrides this method\n};\n\n/**\n * Decodes a message of this type preceeded by its byte length as a varint.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError} If required fields are missing\n */\nType.prototype.decodeDelimited = function decodeDelimited(reader) {\n    if (!(reader instanceof Reader))\n        reader = Reader.create(reader);\n    return this.decode(reader, reader.uint32());\n};\n\n/**\n * Verifies that field values are valid and that required fields are present.\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {null|string} `null` if valid, otherwise the reason why it is not\n */\nType.prototype.verify = function verify_setup(message) {\n    return this.setup().verify(message); // overrides this method\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object to convert\n * @returns {Message<{}>} Message instance\n */\nType.prototype.fromObject = function fromObject(object) {\n    return this.setup().fromObject(object);\n};\n\n/**\n * Conversion options as used by {@link Type#toObject} and {@link Message.toObject}.\n * @interface IConversionOptions\n * @property {Function} [longs] Long conversion type.\n * Valid values are `String` and `Number` (the global types).\n * Defaults to copy the present value, which is a possibly unsafe number without and a {@link Long} with a long library.\n * @property {Function} [enums] Enum value conversion type.\n * Only valid value is `String` (the global type).\n * Defaults to copy the present value, which is the numeric id.\n * @property {Function} [bytes] Bytes value conversion type.\n * Valid values are `Array` and (a base64 encoded) `String` (the global types).\n * Defaults to copy the present value, which usually is a Buffer under node and an Uint8Array in the browser.\n * @property {boolean} [defaults=false] Also sets default values on the resulting object\n * @property {boolean} [arrays=false] Sets empty arrays for missing repeated fields even if `defaults=false`\n * @property {boolean} [objects=false] Sets empty objects for missing map fields even if `defaults=false`\n * @property {boolean} [oneofs=false] Includes virtual oneof properties set to the present field's name, if any\n * @property {boolean} [json=false] Performs additional JSON compatibility conversions, i.e. NaN and Infinity to strings\n */\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n */\nType.prototype.toObject = function toObject(message, options) {\n    return this.setup().toObject(message, options);\n};\n\n/**\n * Decorator function as returned by {@link Type.d} (TypeScript).\n * @typedef TypeDecorator\n * @type {function}\n * @param {Constructor<T>} target Target constructor\n * @returns {undefined}\n * @template T extends Message<T>\n */\n\n/**\n * Type decorator (TypeScript).\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {TypeDecorator<T>} Decorator function\n * @template T extends Message<T>\n */\nType.d = function decorateType(typeName) {\n    return function typeDecorator(target) {\n        util.decorateType(target, typeName);\n    };\n};\n", "\"use strict\";\n\n/**\n * Common type constants.\n * @namespace\n */\nvar types = exports;\n\nvar util = require(37);\n\nvar s = [\n    \"double\",   // 0\n    \"float\",    // 1\n    \"int32\",    // 2\n    \"uint32\",   // 3\n    \"sint32\",   // 4\n    \"fixed32\",  // 5\n    \"sfixed32\", // 6\n    \"int64\",    // 7\n    \"uint64\",   // 8\n    \"sint64\",   // 9\n    \"fixed64\",  // 10\n    \"sfixed64\", // 11\n    \"bool\",     // 12\n    \"string\",   // 13\n    \"bytes\"     // 14\n];\n\nfunction bake(values, offset) {\n    var i = 0, o = {};\n    offset |= 0;\n    while (i < values.length) o[s[i + offset]] = values[i++];\n    return o;\n}\n\n/**\n * Basic type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n * @property {number} bytes=2 Ldelim wire type\n */\ntypes.basic = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2,\n    /* bytes    */ 2\n]);\n\n/**\n * Basic type defaults.\n * @type {Object.<string,*>}\n * @const\n * @property {number} double=0 Double default\n * @property {number} float=0 Float default\n * @property {number} int32=0 Int32 default\n * @property {number} uint32=0 Uint32 default\n * @property {number} sint32=0 Sint32 default\n * @property {number} fixed32=0 Fixed32 default\n * @property {number} sfixed32=0 Sfixed32 default\n * @property {number} int64=0 Int64 default\n * @property {number} uint64=0 Uint64 default\n * @property {number} sint64=0 Sint32 default\n * @property {number} fixed64=0 Fixed64 default\n * @property {number} sfixed64=0 Sfixed64 default\n * @property {boolean} bool=false Bool default\n * @property {string} string=\"\" String default\n * @property {Array.<number>} bytes=Array(0) Bytes default\n * @property {null} message=null Message default\n */\ntypes.defaults = bake([\n    /* double   */ 0,\n    /* float    */ 0,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 0,\n    /* sfixed32 */ 0,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 0,\n    /* sfixed64 */ 0,\n    /* bool     */ false,\n    /* string   */ \"\",\n    /* bytes    */ util.emptyArray,\n    /* message  */ null\n]);\n\n/**\n * Basic long type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n */\ntypes.long = bake([\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1\n], 7);\n\n/**\n * Allowed types for map keys with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n */\ntypes.mapKey = bake([\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2\n], 2);\n\n/**\n * Allowed types for packed repeated fields with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n */\ntypes.packed = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0\n]);\n", "\"use strict\";\n\n/**\n * Various utility functions.\n * @namespace\n */\nvar util = module.exports = require(39);\n\nvar roots = require(30);\n\nvar Type, // cyclic\n    Enum;\n\nutil.codegen = require(3);\nutil.fetch   = require(5);\nutil.path    = require(8);\n\n/**\n * Node's fs module if available.\n * @type {Object.<string,*>}\n */\nutil.fs = util.inquire(\"fs\");\n\n/**\n * Converts an object's values to an array.\n * @param {Object.<string,*>} object Object to convert\n * @returns {Array.<*>} Converted array\n */\nutil.toArray = function toArray(object) {\n    if (object) {\n        var keys  = Object.keys(object),\n            array = new Array(keys.length),\n            index = 0;\n        while (index < keys.length)\n            array[index] = object[keys[index++]];\n        return array;\n    }\n    return [];\n};\n\n/**\n * Converts an array of keys immediately followed by their respective value to an object, omitting undefined values.\n * @param {Array.<*>} array Array to convert\n * @returns {Object.<string,*>} Converted object\n */\nutil.toObject = function toObject(array) {\n    var object = {},\n        index  = 0;\n    while (index < array.length) {\n        var key = array[index++],\n            val = array[index++];\n        if (val !== undefined)\n            object[key] = val;\n    }\n    return object;\n};\n\nvar safePropBackslashRe = /\\\\/g,\n    safePropQuoteRe     = /\"/g;\n\n/**\n * Tests whether the specified name is a reserved word in JS.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nutil.isReserved = function isReserved(name) {\n    return /^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(name);\n};\n\n/**\n * Returns a safe property accessor for the specified property name.\n * @param {string} prop Property name\n * @returns {string} Safe accessor\n */\nutil.safeProp = function safeProp(prop) {\n    if (!/^[$\\w_]+$/.test(prop) || util.isReserved(prop))\n        return \"[\\\"\" + prop.replace(safePropBackslashRe, \"\\\\\\\\\").replace(safePropQuoteRe, \"\\\\\\\"\") + \"\\\"]\";\n    return \".\" + prop;\n};\n\n/**\n * Converts the first character of a string to upper case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.ucFirst = function ucFirst(str) {\n    return str.charAt(0).toUpperCase() + str.substring(1);\n};\n\nvar camelCaseRe = /_([a-z])/g;\n\n/**\n * Converts a string to camel case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.camelCase = function camelCase(str) {\n    return str.substring(0, 1)\n         + str.substring(1)\n               .replace(camelCaseRe, function($0, $1) { return $1.toUpperCase(); });\n};\n\n/**\n * Compares reflected fields by id.\n * @param {Field} a First field\n * @param {Field} b Second field\n * @returns {number} Comparison value\n */\nutil.compareFieldsById = function compareFieldsById(a, b) {\n    return a.id - b.id;\n};\n\n/**\n * Decorator helper for types (TypeScript).\n * @param {Constructor<T>} ctor Constructor function\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {Type} Reflected type\n * @template T extends Message<T>\n * @property {Root} root Decorators root\n */\nutil.decorateType = function decorateType(ctor, typeName) {\n\n    /* istanbul ignore if */\n    if (ctor.$type) {\n        if (typeName && ctor.$type.name !== typeName) {\n            util.decorateRoot.remove(ctor.$type);\n            ctor.$type.name = typeName;\n            util.decorateRoot.add(ctor.$type);\n        }\n        return ctor.$type;\n    }\n\n    /* istanbul ignore next */\n    if (!Type)\n        Type = require(35);\n\n    var type = new Type(typeName || ctor.name);\n    util.decorateRoot.add(type);\n    type.ctor = ctor; // sets up .encode, .decode etc.\n    Object.defineProperty(ctor, \"$type\", { value: type, enumerable: false });\n    Object.defineProperty(ctor.prototype, \"$type\", { value: type, enumerable: false });\n    return type;\n};\n\nvar decorateEnumIndex = 0;\n\n/**\n * Decorator helper for enums (TypeScript).\n * @param {Object} object Enum object\n * @returns {Enum} Reflected enum\n */\nutil.decorateEnum = function decorateEnum(object) {\n\n    /* istanbul ignore if */\n    if (object.$type)\n        return object.$type;\n\n    /* istanbul ignore next */\n    if (!Enum)\n        Enum = require(15);\n\n    var enm = new Enum(\"Enum\" + decorateEnumIndex++, object);\n    util.decorateRoot.add(enm);\n    Object.defineProperty(object, \"$type\", { value: enm, enumerable: false });\n    return enm;\n};\n\n\n/**\n * Sets the value of a property by property path. If a value already exists, it is turned to an array\n * @param {Object.<string,*>} dst Destination object\n * @param {string} path dot '.' delimited path of the property to set\n * @param {Object} value the value to set\n * @returns {Object.<string,*>} Destination object\n */\nutil.setProperty = function setProperty(dst, path, value) {\n    function setProp(dst, path, value) {\n        var part = path.shift();\n        if (path.length > 0) {\n            dst[part] = setProp(dst[part] || {}, path, value);\n        } else {\n            var prevValue = dst[part];\n            if (prevValue)\n                value = [].concat(prevValue).concat(value);\n            dst[part] = value;\n        }\n        return dst;\n    }\n\n    if (typeof dst !== \"object\")\n        throw TypeError(\"dst must be an object\");\n    if (!path)\n        throw TypeError(\"path must be specified\");\n\n    path = path.split(\".\");\n    return setProp(dst, path, value);\n};\n\n/**\n * Decorator root (TypeScript).\n * @name util.decorateRoot\n * @type {Root}\n * @readonly\n */\nObject.defineProperty(util, \"decorateRoot\", {\n    get: function() {\n        return roots[\"decorated\"] || (roots[\"decorated\"] = new (require(29))());\n    }\n});\n", "\"use strict\";\nmodule.exports = LongBits;\n\nvar util = require(39);\n\n/**\n * Constructs new long bits.\n * @classdesc Helper class for working with the low and high bits of a 64 bit value.\n * @memberof util\n * @constructor\n * @param {number} lo Low 32 bits, unsigned\n * @param {number} hi High 32 bits, unsigned\n */\nfunction LongBits(lo, hi) {\n\n    // note that the casts below are theoretically unnecessary as of today, but older statically\n    // generated converter code might still call the ctor with signed 32bits. kept for compat.\n\n    /**\n     * Low bits.\n     * @type {number}\n     */\n    this.lo = lo >>> 0;\n\n    /**\n     * High bits.\n     * @type {number}\n     */\n    this.hi = hi >>> 0;\n}\n\n/**\n * Zero bits.\n * @memberof util.LongBits\n * @type {util.LongBits}\n */\nvar zero = LongBits.zero = new LongBits(0, 0);\n\nzero.toNumber = function() { return 0; };\nzero.zzEncode = zero.zzDecode = function() { return this; };\nzero.length = function() { return 1; };\n\n/**\n * Zero hash.\n * @memberof util.LongBits\n * @type {string}\n */\nvar zeroHash = LongBits.zeroHash = \"\\0\\0\\0\\0\\0\\0\\0\\0\";\n\n/**\n * Constructs new long bits from the specified number.\n * @param {number} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.fromNumber = function fromNumber(value) {\n    if (value === 0)\n        return zero;\n    var sign = value < 0;\n    if (sign)\n        value = -value;\n    var lo = value >>> 0,\n        hi = (value - lo) / 4294967296 >>> 0;\n    if (sign) {\n        hi = ~hi >>> 0;\n        lo = ~lo >>> 0;\n        if (++lo > 4294967295) {\n            lo = 0;\n            if (++hi > 4294967295)\n                hi = 0;\n        }\n    }\n    return new LongBits(lo, hi);\n};\n\n/**\n * Constructs new long bits from a number, long or string.\n * @param {Long|number|string} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.from = function from(value) {\n    if (typeof value === \"number\")\n        return LongBits.fromNumber(value);\n    if (util.isString(value)) {\n        /* istanbul ignore else */\n        if (util.Long)\n            value = util.Long.fromString(value);\n        else\n            return LongBits.fromNumber(parseInt(value, 10));\n    }\n    return value.low || value.high ? new LongBits(value.low >>> 0, value.high >>> 0) : zero;\n};\n\n/**\n * Converts this long bits to a possibly unsafe JavaScript number.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {number} Possibly unsafe number\n */\nLongBits.prototype.toNumber = function toNumber(unsigned) {\n    if (!unsigned && this.hi >>> 31) {\n        var lo = ~this.lo + 1 >>> 0,\n            hi = ~this.hi     >>> 0;\n        if (!lo)\n            hi = hi + 1 >>> 0;\n        return -(lo + hi * 4294967296);\n    }\n    return this.lo + this.hi * 4294967296;\n};\n\n/**\n * Converts this long bits to a long.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long} Long\n */\nLongBits.prototype.toLong = function toLong(unsigned) {\n    return util.Long\n        ? new util.Long(this.lo | 0, this.hi | 0, Boolean(unsigned))\n        /* istanbul ignore next */\n        : { low: this.lo | 0, high: this.hi | 0, unsigned: Boolean(unsigned) };\n};\n\nvar charCodeAt = String.prototype.charCodeAt;\n\n/**\n * Constructs new long bits from the specified 8 characters long hash.\n * @param {string} hash Hash\n * @returns {util.LongBits} Bits\n */\nLongBits.fromHash = function fromHash(hash) {\n    if (hash === zeroHash)\n        return zero;\n    return new LongBits(\n        ( charCodeAt.call(hash, 0)\n        | charCodeAt.call(hash, 1) << 8\n        | charCodeAt.call(hash, 2) << 16\n        | charCodeAt.call(hash, 3) << 24) >>> 0\n    ,\n        ( charCodeAt.call(hash, 4)\n        | charCodeAt.call(hash, 5) << 8\n        | charCodeAt.call(hash, 6) << 16\n        | charCodeAt.call(hash, 7) << 24) >>> 0\n    );\n};\n\n/**\n * Converts this long bits to a 8 characters long hash.\n * @returns {string} Hash\n */\nLongBits.prototype.toHash = function toHash() {\n    return String.fromCharCode(\n        this.lo        & 255,\n        this.lo >>> 8  & 255,\n        this.lo >>> 16 & 255,\n        this.lo >>> 24      ,\n        this.hi        & 255,\n        this.hi >>> 8  & 255,\n        this.hi >>> 16 & 255,\n        this.hi >>> 24\n    );\n};\n\n/**\n * Zig-zag encodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzEncode = function zzEncode() {\n    var mask =   this.hi >> 31;\n    this.hi  = ((this.hi << 1 | this.lo >>> 31) ^ mask) >>> 0;\n    this.lo  = ( this.lo << 1                   ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Zig-zag decodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzDecode = function zzDecode() {\n    var mask = -(this.lo & 1);\n    this.lo  = ((this.lo >>> 1 | this.hi << 31) ^ mask) >>> 0;\n    this.hi  = ( this.hi >>> 1                  ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Calculates the length of this longbits when encoded as a varint.\n * @returns {number} Length\n */\nLongBits.prototype.length = function length() {\n    var part0 =  this.lo,\n        part1 = (this.lo >>> 28 | this.hi << 4) >>> 0,\n        part2 =  this.hi >>> 24;\n    return part2 === 0\n         ? part1 === 0\n           ? part0 < 16384\n             ? part0 < 128 ? 1 : 2\n             : part0 < 2097152 ? 3 : 4\n           : part1 < 16384\n             ? part1 < 128 ? 5 : 6\n             : part1 < 2097152 ? 7 : 8\n         : part2 < 128 ? 9 : 10;\n};\n", "\"use strict\";\nvar util = exports;\n\n// used to return a Promise where callback is omitted\nutil.asPromise = require(1);\n\n// converts to / from base64 encoded strings\nutil.base64 = require(2);\n\n// base class of rpc.Service\nutil.EventEmitter = require(4);\n\n// float handling accross browsers\nutil.float = require(6);\n\n// requires modules optionally and hides the call from bundlers\nutil.inquire = require(7);\n\n// converts to / from utf8 encoded strings\nutil.utf8 = require(10);\n\n// provides a node-like buffer pool in the browser\nutil.pool = require(9);\n\n// utility to work with the low and high bits of a 64 bit value\nutil.LongBits = require(38);\n\n/**\n * Whether running within node or not.\n * @memberof util\n * @type {boolean}\n */\nutil.isNode = Boolean(typeof global !== \"undefined\"\n                   && global\n                   && global.process\n                   && global.process.versions\n                   && global.process.versions.node);\n\n/**\n * Global object reference.\n * @memberof util\n * @type {Object}\n */\nutil.global = util.isNode && global\n           || typeof window !== \"undefined\" && window\n           || typeof self   !== \"undefined\" && self\n           || this; // eslint-disable-line no-invalid-this\n\n/**\n * An immuable empty array.\n * @memberof util\n * @type {Array.<*>}\n * @const\n */\nutil.emptyArray = Object.freeze ? Object.freeze([]) : /* istanbul ignore next */ []; // used on prototypes\n\n/**\n * An immutable empty object.\n * @type {Object}\n * @const\n */\nutil.emptyObject = Object.freeze ? Object.freeze({}) : /* istanbul ignore next */ {}; // used on prototypes\n\n/**\n * Tests if the specified value is an integer.\n * @function\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is an integer\n */\nutil.isInteger = Number.isInteger || /* istanbul ignore next */ function isInteger(value) {\n    return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\n};\n\n/**\n * Tests if the specified value is a string.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a string\n */\nutil.isString = function isString(value) {\n    return typeof value === \"string\" || value instanceof String;\n};\n\n/**\n * Tests if the specified value is a non-null object.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a non-null object\n */\nutil.isObject = function isObject(value) {\n    return value && typeof value === \"object\";\n};\n\n/**\n * Checks if a property on a message is considered to be present.\n * This is an alias of {@link util.isSet}.\n * @function\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isset =\n\n/**\n * Checks if a property on a message is considered to be present.\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isSet = function isSet(obj, prop) {\n    var value = obj[prop];\n    if (value != null && obj.hasOwnProperty(prop)) // eslint-disable-line eqeqeq, no-prototype-builtins\n        return typeof value !== \"object\" || (Array.isArray(value) ? value.length : Object.keys(value).length) > 0;\n    return false;\n};\n\n/**\n * Any compatible Buffer instance.\n * This is a minimal stand-alone definition of a Buffer instance. The actual type is that exported by node's typings.\n * @interface Buffer\n * @extends Uint8Array\n */\n\n/**\n * Node's Buffer class if available.\n * @type {Constructor<Buffer>}\n */\nutil.Buffer = (function() {\n    try {\n        var Buffer = util.inquire(\"buffer\").Buffer;\n        // refuse to use non-node buffers if not explicitly assigned (perf reasons):\n        return Buffer.prototype.utf8Write ? Buffer : /* istanbul ignore next */ null;\n    } catch (e) {\n        /* istanbul ignore next */\n        return null;\n    }\n})();\n\n// Internal alias of or polyfull for Buffer.from.\nutil._Buffer_from = null;\n\n// Internal alias of or polyfill for Buffer.allocUnsafe.\nutil._Buffer_allocUnsafe = null;\n\n/**\n * Creates a new buffer of whatever type supported by the environment.\n * @param {number|number[]} [sizeOrArray=0] Buffer size or number array\n * @returns {Uint8Array|Buffer} Buffer\n */\nutil.newBuffer = function newBuffer(sizeOrArray) {\n    /* istanbul ignore next */\n    return typeof sizeOrArray === \"number\"\n        ? util.Buffer\n            ? util._Buffer_allocUnsafe(sizeOrArray)\n            : new util.Array(sizeOrArray)\n        : util.Buffer\n            ? util._Buffer_from(sizeOrArray)\n            : typeof Uint8Array === \"undefined\"\n                ? sizeOrArray\n                : new Uint8Array(sizeOrArray);\n};\n\n/**\n * Array implementation used in the browser. `Uint8Array` if supported, otherwise `Array`.\n * @type {Constructor<Uint8Array>}\n */\nutil.Array = typeof Uint8Array !== \"undefined\" ? Uint8Array /* istanbul ignore next */ : Array;\n\n/**\n * Any compatible Long instance.\n * This is a minimal stand-alone definition of a Long instance. The actual type is that exported by long.js.\n * @interface Long\n * @property {number} low Low bits\n * @property {number} high High bits\n * @property {boolean} unsigned Whether unsigned or not\n */\n\n/**\n * Long.js's Long class if available.\n * @type {Constructor<Long>}\n */\nutil.Long = /* istanbul ignore next */ util.global.dcodeIO && /* istanbul ignore next */ util.global.dcodeIO.Long\n         || /* istanbul ignore next */ util.global.Long\n         || util.inquire(\"long\");\n\n/**\n * Regular expression used to verify 2 bit (`bool`) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key2Re = /^true|false|0|1$/;\n\n/**\n * Regular expression used to verify 32 bit (`int32` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key32Re = /^-?(?:0|[1-9][0-9]*)$/;\n\n/**\n * Regular expression used to verify 64 bit (`int64` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key64Re = /^(?:[\\\\x00-\\\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;\n\n/**\n * Converts a number or long to an 8 characters long hash string.\n * @param {Long|number} value Value to convert\n * @returns {string} Hash\n */\nutil.longToHash = function longToHash(value) {\n    return value\n        ? util.LongBits.from(value).toHash()\n        : util.LongBits.zeroHash;\n};\n\n/**\n * Converts an 8 characters long hash string to a long or number.\n * @param {string} hash Hash\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long|number} Original value\n */\nutil.longFromHash = function longFromHash(hash, unsigned) {\n    var bits = util.LongBits.fromHash(hash);\n    if (util.Long)\n        return util.Long.fromBits(bits.lo, bits.hi, unsigned);\n    return bits.toNumber(Boolean(unsigned));\n};\n\n/**\n * Merges the properties of the source object into the destination object.\n * @memberof util\n * @param {Object.<string,*>} dst Destination object\n * @param {Object.<string,*>} src Source object\n * @param {boolean} [ifNotSet=false] Merges only if the key is not already set\n * @returns {Object.<string,*>} Destination object\n */\nfunction merge(dst, src, ifNotSet) { // used by converters\n    for (var keys = Object.keys(src), i = 0; i < keys.length; ++i)\n        if (dst[keys[i]] === undefined || !ifNotSet)\n            dst[keys[i]] = src[keys[i]];\n    return dst;\n}\n\nutil.merge = merge;\n\n/**\n * Converts the first character of a string to lower case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.lcFirst = function lcFirst(str) {\n    return str.charAt(0).toLowerCase() + str.substring(1);\n};\n\n/**\n * Creates a custom error constructor.\n * @memberof util\n * @param {string} name Error name\n * @returns {Constructor<Error>} Custom error constructor\n */\nfunction newError(name) {\n\n    function CustomError(message, properties) {\n\n        if (!(this instanceof CustomError))\n            return new CustomError(message, properties);\n\n        // Error.call(this, message);\n        // ^ just returns a new error instance because the ctor can be called as a function\n\n        Object.defineProperty(this, \"message\", { get: function() { return message; } });\n\n        /* istanbul ignore next */\n        if (Error.captureStackTrace) // node\n            Error.captureStackTrace(this, CustomError);\n        else\n            Object.defineProperty(this, \"stack\", { value: new Error().stack || \"\" });\n\n        if (properties)\n            merge(this, properties);\n    }\n\n    (CustomError.prototype = Object.create(Error.prototype)).constructor = CustomError;\n\n    Object.defineProperty(CustomError.prototype, \"name\", { get: function() { return name; } });\n\n    CustomError.prototype.toString = function toString() {\n        return this.name + \": \" + this.message;\n    };\n\n    return CustomError;\n}\n\nutil.newError = newError;\n\n/**\n * Constructs a new protocol error.\n * @classdesc Error subclass indicating a protocol specifc error.\n * @memberof util\n * @extends Error\n * @template T extends Message<T>\n * @constructor\n * @param {string} message Error message\n * @param {Object.<string,*>} [properties] Additional properties\n * @example\n * try {\n *     MyMessage.decode(someBuffer); // throws if required fields are missing\n * } catch (e) {\n *     if (e instanceof ProtocolError && e.instance)\n *         console.log(\"decoded so far: \" + JSON.stringify(e.instance));\n * }\n */\nutil.ProtocolError = newError(\"ProtocolError\");\n\n/**\n * So far decoded message instance.\n * @name util.ProtocolError#instance\n * @type {Message<T>}\n */\n\n/**\n * A OneOf getter as returned by {@link util.oneOfGetter}.\n * @typedef OneOfGetter\n * @type {function}\n * @returns {string|undefined} Set field name, if any\n */\n\n/**\n * Builds a getter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfGetter} Unbound getter\n */\nutil.oneOfGetter = function getOneOf(fieldNames) {\n    var fieldMap = {};\n    for (var i = 0; i < fieldNames.length; ++i)\n        fieldMap[fieldNames[i]] = 1;\n\n    /**\n     * @returns {string|undefined} Set field name, if any\n     * @this Object\n     * @ignore\n     */\n    return function() { // eslint-disable-line consistent-return\n        for (var keys = Object.keys(this), i = keys.length - 1; i > -1; --i)\n            if (fieldMap[keys[i]] === 1 && this[keys[i]] !== undefined && this[keys[i]] !== null)\n                return keys[i];\n    };\n};\n\n/**\n * A OneOf setter as returned by {@link util.oneOfSetter}.\n * @typedef OneOfSetter\n * @type {function}\n * @param {string|undefined} value Field name\n * @returns {undefined}\n */\n\n/**\n * Builds a setter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfSetter} Unbound setter\n */\nutil.oneOfSetter = function setOneOf(fieldNames) {\n\n    /**\n     * @param {string} name Field name\n     * @returns {undefined}\n     * @this Object\n     * @ignore\n     */\n    return function(name) {\n        for (var i = 0; i < fieldNames.length; ++i)\n            if (fieldNames[i] !== name)\n                delete this[fieldNames[i]];\n    };\n};\n\n/**\n * Default conversion options used for {@link Message#toJSON} implementations.\n *\n * These options are close to proto3's JSON mapping with the exception that internal types like Any are handled just like messages. More precisely:\n *\n * - Longs become strings\n * - Enums become string keys\n * - Bytes become base64 encoded strings\n * - (Sub-)Messages become plain objects\n * - Maps become plain objects with all string keys\n * - Repeated fields become arrays\n * - NaN and Infinity for float and double fields become strings\n *\n * @type {IConversionOptions}\n * @see https://developers.google.com/protocol-buffers/docs/proto3?hl=en#json\n */\nutil.toJSONOptions = {\n    longs: String,\n    enums: String,\n    bytes: String,\n    json: true\n};\n\n// Sets up buffer utility according to the environment (called in index-minimal)\nutil._configure = function() {\n    var Buffer = util.Buffer;\n    /* istanbul ignore if */\n    if (!Buffer) {\n        util._Buffer_from = util._Buffer_allocUnsafe = null;\n        return;\n    }\n    // because node 4.x buffers are incompatible & immutable\n    // see: https://github.com/dcodeIO/protobuf.js/pull/665\n    util._Buffer_from = Buffer.from !== Uint8Array.from && Buffer.from ||\n        /* istanbul ignore next */\n        function Buffer_from(value, encoding) {\n            return new Buffer(value, encoding);\n        };\n    util._Buffer_allocUnsafe = Buffer.allocUnsafe ||\n        /* istanbul ignore next */\n        function Buffer_allocUnsafe(size) {\n            return new Buffer(size);\n        };\n};\n", "\"use strict\";\nmodule.exports = verifier;\n\nvar Enum      = require(15),\n    util      = require(37);\n\nfunction invalid(field, expected) {\n    return field.name + \": \" + expected + (field.repeated && expected !== \"array\" ? \"[]\" : field.map && expected !== \"object\" ? \"{k:\"+field.keyType+\"}\" : \"\") + \" expected\";\n}\n\n/**\n * Generates a partial value verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyValue(gen, field, fieldIndex, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(%s){\", ref)\n                (\"default:\")\n                    (\"return%j\", invalid(field, \"enum value\"));\n            for (var keys = Object.keys(field.resolvedType.values), j = 0; j < keys.length; ++j) gen\n                (\"case %i:\", field.resolvedType.values[keys[j]]);\n            gen\n                    (\"break\")\n            (\"}\");\n        } else {\n            gen\n            (\"{\")\n                (\"var e=types[%i].verify(%s);\", fieldIndex, ref)\n                (\"if(e)\")\n                    (\"return%j+e\", field.name + \".\")\n            (\"}\");\n        }\n    } else {\n        switch (field.type) {\n            case \"int32\":\n            case \"uint32\":\n            case \"sint32\":\n            case \"fixed32\":\n            case \"sfixed32\": gen\n                (\"if(!util.isInteger(%s))\", ref)\n                    (\"return%j\", invalid(field, \"integer\"));\n                break;\n            case \"int64\":\n            case \"uint64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))\", ref, ref, ref, ref)\n                    (\"return%j\", invalid(field, \"integer|Long\"));\n                break;\n            case \"float\":\n            case \"double\": gen\n                (\"if(typeof %s!==\\\"number\\\")\", ref)\n                    (\"return%j\", invalid(field, \"number\"));\n                break;\n            case \"bool\": gen\n                (\"if(typeof %s!==\\\"boolean\\\")\", ref)\n                    (\"return%j\", invalid(field, \"boolean\"));\n                break;\n            case \"string\": gen\n                (\"if(!util.isString(%s))\", ref)\n                    (\"return%j\", invalid(field, \"string\"));\n                break;\n            case \"bytes\": gen\n                (\"if(!(%s&&typeof %s.length===\\\"number\\\"||util.isString(%s)))\", ref, ref, ref)\n                    (\"return%j\", invalid(field, \"buffer\"));\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a partial key verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyKey(gen, field, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    switch (field.keyType) {\n        case \"int32\":\n        case \"uint32\":\n        case \"sint32\":\n        case \"fixed32\":\n        case \"sfixed32\": gen\n            (\"if(!util.key32Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"integer key\"));\n            break;\n        case \"int64\":\n        case \"uint64\":\n        case \"sint64\":\n        case \"fixed64\":\n        case \"sfixed64\": gen\n            (\"if(!util.key64Re.test(%s))\", ref) // see comment above: x is ok, d is not\n                (\"return%j\", invalid(field, \"integer|Long key\"));\n            break;\n        case \"bool\": gen\n            (\"if(!util.key2Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"boolean key\"));\n            break;\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a verifier specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction verifier(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n\n    var gen = util.codegen([\"m\"], mtype.name + \"$verify\")\n    (\"if(typeof m!==\\\"object\\\"||m===null)\")\n        (\"return%j\", \"object expected\");\n    var oneofs = mtype.oneofsArray,\n        seenFirstField = {};\n    if (oneofs.length) gen\n    (\"var p={}\");\n\n    for (var i = 0; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            ref   = \"m\" + util.safeProp(field.name);\n\n        if (field.optional) gen\n        (\"if(%s!=null&&m.hasOwnProperty(%j)){\", ref, field.name); // !== undefined && !== null\n\n        // map fields\n        if (field.map) { gen\n            (\"if(!util.isObject(%s))\", ref)\n                (\"return%j\", invalid(field, \"object\"))\n            (\"var k=Object.keys(%s)\", ref)\n            (\"for(var i=0;i<k.length;++i){\");\n                genVerifyKey(gen, field, \"k[i]\");\n                genVerifyValue(gen, field, i, ref + \"[k[i]]\")\n            (\"}\");\n\n        // repeated fields\n        } else if (field.repeated) { gen\n            (\"if(!Array.isArray(%s))\", ref)\n                (\"return%j\", invalid(field, \"array\"))\n            (\"for(var i=0;i<%s.length;++i){\", ref);\n                genVerifyValue(gen, field, i, ref + \"[i]\")\n            (\"}\");\n\n        // required or present fields\n        } else {\n            if (field.partOf) {\n                var oneofProp = util.safeProp(field.partOf.name);\n                if (seenFirstField[field.partOf.name] === 1) gen\n            (\"if(p%s===1)\", oneofProp)\n                (\"return%j\", field.partOf.name + \": multiple values\");\n                seenFirstField[field.partOf.name] = 1;\n                gen\n            (\"p%s=1\", oneofProp);\n            }\n            genVerifyValue(gen, field, i, ref);\n        }\n        if (field.optional) gen\n        (\"}\");\n    }\n    return gen\n    (\"return null\");\n    /* eslint-enable no-unexpected-multiline */\n}", "\"use strict\";\n\n/**\n * Wrappers for common types.\n * @type {Object.<string,IWrapper>}\n * @const\n */\nvar wrappers = exports;\n\nvar Message = require(21);\n\n/**\n * From object converter part of an {@link IWrapper}.\n * @typedef WrapperFromObjectConverter\n * @type {function}\n * @param {Object.<string,*>} object Plain object\n * @returns {Message<{}>} Message instance\n * @this Type\n */\n\n/**\n * To object converter part of an {@link IWrapper}.\n * @typedef WrapperToObjectConverter\n * @type {function}\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @this Type\n */\n\n/**\n * Common type wrapper part of {@link wrappers}.\n * @interface IWrapper\n * @property {WrapperFromObjectConverter} [fromObject] From object converter\n * @property {WrapperToObjectConverter} [toObject] To object converter\n */\n\n// Custom wrapper for Any\nwrappers[\".google.protobuf.Any\"] = {\n\n    fromObject: function(object) {\n\n        // unwrap value type if mapped\n        if (object && object[\"@type\"]) {\n             // Only use fully qualified type name after the last '/'\n            var name = object[\"@type\"].substring(object[\"@type\"].lastIndexOf(\"/\") + 1);\n            var type = this.lookup(name);\n            /* istanbul ignore else */\n            if (type) {\n                // type_url does not accept leading \".\"\n                var type_url = object[\"@type\"].charAt(0) === \".\" ?\n                    object[\"@type\"].substr(1) : object[\"@type\"];\n                // type_url prefix is optional, but path seperator is required\n                if (type_url.indexOf(\"/\") === -1) {\n                    type_url = \"/\" + type_url;\n                }\n                return this.create({\n                    type_url: type_url,\n                    value: type.encode(type.fromObject(object)).finish()\n                });\n            }\n        }\n\n        return this.fromObject(object);\n    },\n\n    toObject: function(message, options) {\n\n        // Default prefix\n        var googleApi = \"type.googleapis.com/\";\n        var prefix = \"\";\n        var name = \"\";\n\n        // decode value if requested and unmapped\n        if (options && options.json && message.type_url && message.value) {\n            // Only use fully qualified type name after the last '/'\n            name = message.type_url.substring(message.type_url.lastIndexOf(\"/\") + 1);\n            // Separate the prefix used\n            prefix = message.type_url.substring(0, message.type_url.lastIndexOf(\"/\") + 1);\n            var type = this.lookup(name);\n            /* istanbul ignore else */\n            if (type)\n                message = type.decode(message.value);\n        }\n\n        // wrap value if unmapped\n        if (!(message instanceof this.ctor) && message instanceof Message) {\n            var object = message.$type.toObject(message, options);\n            var messageName = message.$type.fullName[0] === \".\" ?\n                message.$type.fullName.substr(1) : message.$type.fullName;\n            // Default to type.googleapis.com prefix if no prefix is used\n            if (prefix === \"\") {\n                prefix = googleApi;\n            }\n            name = prefix + messageName;\n            object[\"@type\"] = name;\n            return object;\n        }\n\n        return this.toObject(message, options);\n    }\n};\n", "\"use strict\";\nmodule.exports = Writer;\n\nvar util      = require(39);\n\nvar BufferWriter; // cyclic\n\nvar LongBits  = util.LongBits,\n    base64    = util.base64,\n    utf8      = util.utf8;\n\n/**\n * Constructs a new writer operation instance.\n * @classdesc Scheduled writer operation.\n * @constructor\n * @param {function(*, Uint8Array, number)} fn Function to call\n * @param {number} len Value byte length\n * @param {*} val Value to write\n * @ignore\n */\nfunction Op(fn, len, val) {\n\n    /**\n     * Function to call.\n     * @type {function(Uint8Array, number, *)}\n     */\n    this.fn = fn;\n\n    /**\n     * Value byte length.\n     * @type {number}\n     */\n    this.len = len;\n\n    /**\n     * Next operation.\n     * @type {Writer.Op|undefined}\n     */\n    this.next = undefined;\n\n    /**\n     * Value to write.\n     * @type {*}\n     */\n    this.val = val; // type varies\n}\n\n/* istanbul ignore next */\nfunction noop() {} // eslint-disable-line no-empty-function\n\n/**\n * Constructs a new writer state instance.\n * @classdesc Copied writer state.\n * @memberof Writer\n * @constructor\n * @param {Writer} writer Writer to copy state from\n * @ignore\n */\nfunction State(writer) {\n\n    /**\n     * Current head.\n     * @type {Writer.Op}\n     */\n    this.head = writer.head;\n\n    /**\n     * Current tail.\n     * @type {Writer.Op}\n     */\n    this.tail = writer.tail;\n\n    /**\n     * Current buffer length.\n     * @type {number}\n     */\n    this.len = writer.len;\n\n    /**\n     * Next state.\n     * @type {State|null}\n     */\n    this.next = writer.states;\n}\n\n/**\n * Constructs a new writer instance.\n * @classdesc Wire format writer using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n */\nfunction Writer() {\n\n    /**\n     * Current length.\n     * @type {number}\n     */\n    this.len = 0;\n\n    /**\n     * Operations head.\n     * @type {Object}\n     */\n    this.head = new Op(noop, 0, 0);\n\n    /**\n     * Operations tail\n     * @type {Object}\n     */\n    this.tail = this.head;\n\n    /**\n     * Linked forked states.\n     * @type {Object|null}\n     */\n    this.states = null;\n\n    // When a value is written, the writer calculates its byte length and puts it into a linked\n    // list of operations to perform when finish() is called. This both allows us to allocate\n    // buffers of the exact required size and reduces the amount of work we have to do compared\n    // to first calculating over objects and then encoding over objects. In our case, the encoding\n    // part is just a linked list walk calling operations with already prepared values.\n}\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup() {\n            return (Writer.create = function create_buffer() {\n                return new BufferWriter();\n            })();\n        }\n        /* istanbul ignore next */\n        : function create_array() {\n            return new Writer();\n        };\n};\n\n/**\n * Creates a new writer.\n * @function\n * @returns {BufferWriter|Writer} A {@link BufferWriter} when Buffers are supported, otherwise a {@link Writer}\n */\nWriter.create = create();\n\n/**\n * Allocates a buffer of the specified size.\n * @param {number} size Buffer size\n * @returns {Uint8Array} Buffer\n */\nWriter.alloc = function alloc(size) {\n    return new util.Array(size);\n};\n\n// Use Uint8Array buffer pool in the browser, just like node does with buffers\n/* istanbul ignore else */\nif (util.Array !== Array)\n    Writer.alloc = util.pool(Writer.alloc, util.Array.prototype.subarray);\n\n/**\n * Pushes a new operation to the queue.\n * @param {function(Uint8Array, number, *)} fn Function to call\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @returns {Writer} `this`\n * @private\n */\nWriter.prototype._push = function push(fn, len, val) {\n    this.tail = this.tail.next = new Op(fn, len, val);\n    this.len += len;\n    return this;\n};\n\nfunction writeByte(val, buf, pos) {\n    buf[pos] = val & 255;\n}\n\nfunction writeVarint32(val, buf, pos) {\n    while (val > 127) {\n        buf[pos++] = val & 127 | 128;\n        val >>>= 7;\n    }\n    buf[pos] = val;\n}\n\n/**\n * Constructs a new varint writer operation instance.\n * @classdesc Scheduled varint writer operation.\n * @extends Op\n * @constructor\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @ignore\n */\nfunction VarintOp(len, val) {\n    this.len = len;\n    this.next = undefined;\n    this.val = val;\n}\n\nVarintOp.prototype = Object.create(Op.prototype);\nVarintOp.prototype.fn = writeVarint32;\n\n/**\n * Writes an unsigned 32 bit value as a varint.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.uint32 = function write_uint32(value) {\n    // here, the call to this.push has been inlined and a varint specific Op subclass is used.\n    // uint32 is by far the most frequently used operation and benefits significantly from this.\n    this.len += (this.tail = this.tail.next = new VarintOp(\n        (value = value >>> 0)\n                < 128       ? 1\n        : value < 16384     ? 2\n        : value < 2097152   ? 3\n        : value < 268435456 ? 4\n        :                     5,\n    value)).len;\n    return this;\n};\n\n/**\n * Writes a signed 32 bit value as a varint.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.int32 = function write_int32(value) {\n    return value < 0\n        ? this._push(writeVarint64, 10, LongBits.fromNumber(value)) // 10 bytes per spec\n        : this.uint32(value);\n};\n\n/**\n * Writes a 32 bit value as a varint, zig-zag encoded.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sint32 = function write_sint32(value) {\n    return this.uint32((value << 1 ^ value >> 31) >>> 0);\n};\n\nfunction writeVarint64(val, buf, pos) {\n    while (val.hi) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = (val.lo >>> 7 | val.hi << 25) >>> 0;\n        val.hi >>>= 7;\n    }\n    while (val.lo > 127) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = val.lo >>> 7;\n    }\n    buf[pos++] = val.lo;\n}\n\n/**\n * Writes an unsigned 64 bit value as a varint.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.uint64 = function write_uint64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a signed 64 bit value as a varint.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.int64 = Writer.prototype.uint64;\n\n/**\n * Writes a signed 64 bit value as a varint, zig-zag encoded.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sint64 = function write_sint64(value) {\n    var bits = LongBits.from(value).zzEncode();\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a boolish value as a varint.\n * @param {boolean} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bool = function write_bool(value) {\n    return this._push(writeByte, 1, value ? 1 : 0);\n};\n\nfunction writeFixed32(val, buf, pos) {\n    buf[pos    ] =  val         & 255;\n    buf[pos + 1] =  val >>> 8   & 255;\n    buf[pos + 2] =  val >>> 16  & 255;\n    buf[pos + 3] =  val >>> 24;\n}\n\n/**\n * Writes an unsigned 32 bit value as fixed 32 bits.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.fixed32 = function write_fixed32(value) {\n    return this._push(writeFixed32, 4, value >>> 0);\n};\n\n/**\n * Writes a signed 32 bit value as fixed 32 bits.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sfixed32 = Writer.prototype.fixed32;\n\n/**\n * Writes an unsigned 64 bit value as fixed 64 bits.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.fixed64 = function write_fixed64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeFixed32, 4, bits.lo)._push(writeFixed32, 4, bits.hi);\n};\n\n/**\n * Writes a signed 64 bit value as fixed 64 bits.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sfixed64 = Writer.prototype.fixed64;\n\n/**\n * Writes a float (32 bit).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.float = function write_float(value) {\n    return this._push(util.float.writeFloatLE, 4, value);\n};\n\n/**\n * Writes a double (64 bit float).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.double = function write_double(value) {\n    return this._push(util.float.writeDoubleLE, 8, value);\n};\n\nvar writeBytes = util.Array.prototype.set\n    ? function writeBytes_set(val, buf, pos) {\n        buf.set(val, pos); // also works for plain array values\n    }\n    /* istanbul ignore next */\n    : function writeBytes_for(val, buf, pos) {\n        for (var i = 0; i < val.length; ++i)\n            buf[pos + i] = val[i];\n    };\n\n/**\n * Writes a sequence of bytes.\n * @param {Uint8Array|string} value Buffer or base64 encoded string to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bytes = function write_bytes(value) {\n    var len = value.length >>> 0;\n    if (!len)\n        return this._push(writeByte, 1, 0);\n    if (util.isString(value)) {\n        var buf = Writer.alloc(len = base64.length(value));\n        base64.decode(value, buf, 0);\n        value = buf;\n    }\n    return this.uint32(len)._push(writeBytes, len, value);\n};\n\n/**\n * Writes a string.\n * @param {string} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.string = function write_string(value) {\n    var len = utf8.length(value);\n    return len\n        ? this.uint32(len)._push(utf8.write, len, value)\n        : this._push(writeByte, 1, 0);\n};\n\n/**\n * Forks this writer's state by pushing it to a stack.\n * Calling {@link Writer#reset|reset} or {@link Writer#ldelim|ldelim} resets the writer to the previous state.\n * @returns {Writer} `this`\n */\nWriter.prototype.fork = function fork() {\n    this.states = new State(this);\n    this.head = this.tail = new Op(noop, 0, 0);\n    this.len = 0;\n    return this;\n};\n\n/**\n * Resets this instance to the last state.\n * @returns {Writer} `this`\n */\nWriter.prototype.reset = function reset() {\n    if (this.states) {\n        this.head   = this.states.head;\n        this.tail   = this.states.tail;\n        this.len    = this.states.len;\n        this.states = this.states.next;\n    } else {\n        this.head = this.tail = new Op(noop, 0, 0);\n        this.len  = 0;\n    }\n    return this;\n};\n\n/**\n * Resets to the last state and appends the fork state's current write length as a varint followed by its operations.\n * @returns {Writer} `this`\n */\nWriter.prototype.ldelim = function ldelim() {\n    var head = this.head,\n        tail = this.tail,\n        len  = this.len;\n    this.reset().uint32(len);\n    if (len) {\n        this.tail.next = head.next; // skip noop\n        this.tail = tail;\n        this.len += len;\n    }\n    return this;\n};\n\n/**\n * Finishes the write operation.\n * @returns {Uint8Array} Finished buffer\n */\nWriter.prototype.finish = function finish() {\n    var head = this.head.next, // skip noop\n        buf  = this.constructor.alloc(this.len),\n        pos  = 0;\n    while (head) {\n        head.fn(head.val, buf, pos);\n        pos += head.len;\n        head = head.next;\n    }\n    // this.head = this.tail = null;\n    return buf;\n};\n\nWriter._configure = function(BufferWriter_) {\n    BufferWriter = BufferWriter_;\n    Writer.create = create();\n    BufferWriter._configure();\n};\n", "\"use strict\";\nmodule.exports = <PERSON><PERSON>erWriter;\n\n// extends Writer\nvar Writer = require(42);\n(BufferWriter.prototype = Object.create(Writer.prototype)).constructor = BufferWriter;\n\nvar util = require(39);\n\n/**\n * Constructs a new buffer writer instance.\n * @classdesc Wire format writer using node buffers.\n * @extends Writer\n * @constructor\n */\nfunction BufferWriter() {\n    Writer.call(this);\n}\n\nBufferWriter._configure = function () {\n    /**\n     * Allocates a buffer of the specified size.\n     * @function\n     * @param {number} size Buffer size\n     * @returns {Buffer} Buffer\n     */\n    BufferWriter.alloc = util._Buffer_allocUnsafe;\n\n    BufferWriter.writeBytesBuffer = util.Buffer && util.Buffer.prototype instanceof Uint8Array && util.Buffer.prototype.set.name === \"set\"\n        ? function writeBytesBuffer_set(val, buf, pos) {\n          buf.set(val, pos); // faster than copy (requires node >= 4 where Buffers extend Uint8Array and set is properly inherited)\n          // also works for plain array values\n        }\n        /* istanbul ignore next */\n        : function writeBytesBuffer_copy(val, buf, pos) {\n          if (val.copy) // Buffer values\n            val.copy(buf, pos, 0, val.length);\n          else for (var i = 0; i < val.length;) // plain array values\n            buf[pos++] = val[i++];\n        };\n};\n\n\n/**\n * @override\n */\nBufferWriter.prototype.bytes = function write_bytes_buffer(value) {\n    if (util.isString(value))\n        value = util._Buffer_from(value, \"base64\");\n    var len = value.length >>> 0;\n    this.uint32(len);\n    if (len)\n        this._push(BufferWriter.writeBytesBuffer, len, value);\n    return this;\n};\n\nfunction writeStringBuffer(val, buf, pos) {\n    if (val.length < 40) // plain js is faster for short strings (probably due to redundant assertions)\n        util.utf8.write(val, buf, pos);\n    else if (buf.utf8Write)\n        buf.utf8Write(val, pos);\n    else\n        buf.write(val, pos);\n}\n\n/**\n * @override\n */\nBufferWriter.prototype.string = function write_string_buffer(value) {\n    var len = util.Buffer.byteLength(value);\n    this.uint32(len);\n    if (len)\n        this._push(writeStringBuffer, len, value);\n    return this;\n};\n\n\n/**\n * Finishes the write operation.\n * @name BufferWriter#finish\n * @function\n * @returns {Buffer} Finished buffer\n */\n\nBufferWriter._configure();\n"], "sourceRoot": "."}