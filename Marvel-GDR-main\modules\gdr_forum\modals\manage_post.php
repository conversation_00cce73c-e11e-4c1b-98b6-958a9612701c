<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

include_once __DIR__.'/../../../core.php';

use Modules\Bacheche\Post;

// Recupera il post da modificare
$post = Post::find(get('id_post'));

if(!$post) {
    echo '<div class="alert alert-danger">'.tr('Post non trovato').'</div>';
    return;
}

// Verifica i permessi
if($user->gruppo != 'Amministratori' && $post->id_utente != $user->id) {
    echo '<div class="alert alert-warning">'.tr('Non hai i permessi per modificare questo post').'</div>';
    return;
}

// CSS personalizzato per il modal di modifica post
echo '
<style>
.modal-post-edit {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 20px;
}

.edit-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.edit-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 15px;
    border: 3px solid #e9ecef;
    object-fit: cover;
}

.edit-info h4 {
    margin: 0;
    color: #343a40;
    font-weight: 600;
}

.edit-info p {
    margin: 5px 0 0 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.form-section {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.section-title {
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e9ecef;
}

.btn-modal {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-modal:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.character-selector {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}
</style>';

// Determina l'avatar del post
$post_avatar = '';
if($post->personaggio) {
    $post_avatar = ($post->nome == $post->personaggio->alias ? $post->personaggio->immagine_chat_alias : $post->personaggio->immagine_chat);
}
if(empty($post_avatar)) {
    $post_avatar = $rootdir.'/assets/dist/img/user.png';
}

echo '
<div class="modal-post-edit">
    <div class="edit-header">
        <img src="'.$post_avatar.'" class="edit-avatar" alt="'.$post->nome.'">
        <div class="edit-info">
            <h4>'.tr('Modifica Post').'</h4>
            <p><i class="fa fa-user"></i> '.$post->nome.' • <i class="fa fa-clock"></i> '.date('d/m/Y H:i', strtotime($post->created_at)).'</p>
        </div>
    </div>
    
    <form action="" method="post" id="edit_message-form" enctype="multipart/form-data">
        <input type="hidden" name="backto" value="record-edit">
        <input type="hidden" name="op" value="update_risposta">
        <input type="hidden" name="id_utente" value="'.$user->id.'">
        <input type="hidden" name="id_post" value="'.$post->id.'">

        <div class="form-section">
            <h5 class="section-title">
                <i class="fa fa-user-edit"></i> '.tr('Informazioni Autore').'
            </h5>
            
            <div class="row">';

// Selezione personaggio per amministratori
if( $user->gruppo=='Amministratori' ){
    echo '
                <div class="col-md-6">
                    <div class="character-selector">
                        {["type":"select", "label":"'.tr('Personaggio').'", "name":"idpersonaggio_edit", "ajax-source":"lista_png", "select-options": '.json_encode(['idpersonaggio' => $user->idpersonaggio, "gruppo" => $user->gruppo]).', "value":"'.$post->idpersonaggio.'" ]}
                    </div>
                </div>';
}else{
    echo '
                <input type="hidden" name="idpersonaggio_edit" value="'.$post->idpersonaggio.'">';
}

echo '
                <div class="col-md-6">
                    <div class="character-selector">
                        {["type":"select", "label":"'.tr('Nome da visualizzare').'", "name":"nome_edit", "value":"'.$post->nome.'", "ajax-source":"nome_chat", "select-options":'.json_encode(['idpersonaggio' => $post->idpersonaggio]).' ]}
                    </div>
                </div>
            </div>
        </div>

        <div class="form-section">
            <h5 class="section-title">
                <i class="fa fa-edit"></i> '.tr('Contenuto del Post').'
            </h5>
            
            <div class="row">
                <div class="col-md-12">
                    '.input([
                        'type' => 'ckeditor',
                        'label' => tr('Messaggio'),
                        'name' => 'messaggio_edit',
                        'required' => 1,
                        'value' => $post->messaggio,
                        'extra' => 'style="min-height:200px;"',
                        'help' => tr('Modifica il contenuto del tuo post')
                    ]).'
                </div>
            </div>
        </div>
    </form>
    
    <div class="text-right">
        <button type="button" class="btn btn-secondary btn-modal" data-dismiss="modal">
            <i class="fa fa-times"></i> '.tr('Annulla').'
        </button>
        <button type="button" class="btn btn-primary btn-modal" onclick="modificaPost();">
            <i class="fa fa-save"></i> '.tr('Salva Modifiche').'
        </button>
    </div>
</div>';

// JavaScript per la gestione del form
echo '
<script>
$(document).ready(function(){
    // Inizializza i componenti del form
    init();
    
    // Animazione di entrata
    $(".modal-post-edit").css("opacity", "0").animate({opacity: 1}, 300);
});

/**
 * Gestisce il cambio di personaggio per gli amministratori
 */
$("#idpersonaggio_edit").on("change", function(){
    var selectedPersonaggio = $(this).val();
    
    // Aggiorna le opzioni per la selezione del nome
    updateSelectOption("idpersonaggio", selectedPersonaggio);
    session_set("superselect,idpersonaggio", selectedPersonaggio, 0);
    
    // Reset della selezione nome
    $("#nome_edit").selectReset();
    
    // Feedback visivo
    $(".character-selector").addClass("border-primary").delay(1000).queue(function(){
        $(this).removeClass("border-primary").dequeue();
    });
});

/**
 * Salva le modifiche al post
 */
function modificaPost() {
    // Validazione del form
    if(!$("#messaggio_edit").val().trim()) {
        swal("'.tr('Attenzione').'", "'.tr('Il messaggio non può essere vuoto').'", "warning");
        return;
    }
    
    if(!$("#nome_edit").val()) {
        swal("'.tr('Attenzione').'", "'.tr('Seleziona un nome da visualizzare').'", "warning");
        return;
    }
    
    // Mostra indicatore di caricamento
    var submitBtn = $(".btn-primary.btn-modal");
    var originalText = submitBtn.html();
    submitBtn.html(\'<i class="fa fa-spinner fa-spin"></i> '.tr('Salvando...').'\').prop("disabled", true);
    
    // Invia il form
    salvaForm("#edit_message-form", {
        id_module: "'.$id_module.'",
    }).then(function(response) {
        // Chiude il modal
        $(".modal").modal("hide");
        
        // Ricarica i post della bacheca
        $("#post").load(globals.rootdir + "/ajax_complete.php?module=Bacheche&op=get_post&id_module='.$id_module.'&id_bacheca='.$post->id_forum.'");
        
        // Mostra messaggio di successo
        swal("'.tr('Successo').'", response.message || "'.tr('Post modificato con successo!').'", "success");
        
    }).catch(function(error) {
        // Ripristina il pulsante in caso di errore
        submitBtn.html(originalText).prop("disabled", false);
        
        swal("'.tr('Errore').'", error.message || "'.tr('Errore durante la modifica').'", "error");
    });
}

/**
 * Gestisce l\'invio del form con Enter (solo se non si sta scrivendo nel CKEditor)
 */
$(document).on("keydown", function(e) {
    if(e.ctrlKey && e.which === 13) { // Ctrl+Enter
        modificaPost();
    }
});

/**
 * Auto-save del contenuto (opzionale)
 */
var autoSaveTimer;
$("#messaggio_edit").on("input", function() {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = setTimeout(function() {
        // Salva in localStorage come backup
        localStorage.setItem("post_edit_backup_'.$post->id.'", $("#messaggio_edit").val());
    }, 2000);
});

// Ripristina il backup se disponibile
$(document).ready(function() {
    var backup = localStorage.getItem("post_edit_backup_'.$post->id.'");
    if(backup && backup !== $("#messaggio_edit").val()) {
        swal({
            title: "'.tr('Ripristino Automatico').'",
            text: "'.tr('È stato trovato un backup non salvato. Vuoi ripristinarlo?').'",
            type: "question",
            showCancelButton: true,
            confirmButtonText: "'.tr('Ripristina').'",
            cancelButtonText: "'.tr('Ignora').'"
        }).then(function() {
            if(typeof CKEDITOR !== "undefined" && CKEDITOR.instances.messaggio_edit) {
                CKEDITOR.instances.messaggio_edit.setData(backup);
            } else {
                $("#messaggio_edit").val(backup);
            }
        });
    }
});

</script>';
