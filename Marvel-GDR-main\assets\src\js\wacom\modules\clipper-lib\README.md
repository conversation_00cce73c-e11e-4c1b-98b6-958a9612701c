# ClipperLib

> forked from [Javascript Clipper](http://sourceforge.net/projects/jsclipper/)

## Description

The Javascript Clipper library performs clipping and offsetting for both lines and polygons. All four boolean clipping operations are supported - intersection, union, difference and exclusive-or. Polygons can be of any shape including self-intersecting polygons.

Javascript Clipper is a port of <PERSON>'s Clipper library: <https://sourceforge.net/projects/polyclipping/>

LIVE DEMO: <http://jsclipper.sourceforge.net/*******/main_demo.html>

Information and examples:
<http://jsclipper.sourceforge.net/*******/>

Donate Javascript Clipper Project: <https://sourceforge.net/p/jsclipper/wiki/Donations/>

Use cases:
* Over 1500 schools in the UK uses Javascript Clipper in Digimap for Schools service. Digimap for Schools is an online mapping service for use by teachers and pupils. Read more:
<https://mobilegeo.wordpress.com/> and
<http://digimapforschools.edina.ac.uk/cosmo-free/osmapper>

[Javascript Clipper Web Site](https://sourceforge.net/p/jsclipper/wiki/)

## Features

- Line and polygon clipping - intersection, union, difference & xor
- Line and polygon offsetting with 3 types of joining - miter, square and round
- Polygons can be of any shape, including self-intersecting polygons
- Minkowski Addition and Minkowski Difference functions included
- The library is written in Javascript
- Comprehensive documentation
- Demos use inline SVG and Canvas libraries
- The library is significantly faster than commercial alternatives
- Uses Tom Wu's fast big integer library
- UMD support

## Categories

Algorithms, Graphics

## Links

- [Documentation](./Documentation.md)
- [ChangeLog](./ChangeLog.txt)

## License

[Boost Software License (BSL1.0)](http://www.boost.org/LICENSE_1_0.txt)
