<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

include_once __DIR__.'/../../../core.php';

use Carbon\Carbon;
use Models\Module;
use Models\User;

$skin = Modules\Skins\Skin::find(setting('Skin'));

$paths = App::getPaths();

foreach (App::getAssets()['css'] as $style) {
    echo '
        <link rel="stylesheet" type="text/css" media="all" href="'.$style.'"/>';
}

// Print CSS
foreach (App::getAssets()['print'] as $style) {
    echo '
        <link rel="stylesheet" type="text/css" media="print" href="'.$style.'"/>';
}

echo '
<link rel="stylesheet" type="text/css" href="'.$baseurl.'/assets/dist/css/app.min.css" />
<link href="'.$paths['img'].'/favicon.png" rel="icon" type="image/x-icon" />
<link rel="stylesheet" type="text/css" media="all" href="'.$paths['css'].'/../img/skins/custom.css" />
<script src="'.$baseurl.'/assets/dist/js/app.min.js"></script>';

if (file_exists(base_dir().'/manifest.json')) {
    echo '
        <link rel="manifest" href="'.base_path().'/manifest.json?r='.random_int(0, mt_getrandmax()).'">';
}

echo '
<div class="content-wrapper" style="overflow:hidden;">
    <form id="chat-form" action="" method="post">
        <input type="hidden" name="backto" value="record-edit">
        <input type="hidden" name="op" value="add_risposta">
        <input type="hidden" name="id_module" value="'.Module::where('name', 'Chat')->first()->id.'">
        <input type="hidden" name="idutente" value="'.$user->id.'">

        <div class="row">
            <div class="col-md-12" id="area_chat" style="margin: 3%;border: 2px solid black;height:70vh;overflow: auto;padding: 0px !important;">
                <div id="frame-chat" style="height: 100%;">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12" style="margin-left: 3%;margin-right: 3%;">
                {[ "type": "textarea", "label": "", "name":"'.tr('Nuova risposta').'", "name": "contenuto", "required": 1, "value": "", "rows": "3", "extra": "style=\"width: 100%;\" ", "placeholder":"'.tr('Inserisci qui la tua risposta').'" ]}
            </div>
        </div>
        <div class="row">
            <div class="col-md-12" style="margin-left: 3%;margin-right: 3%;">
                <a type="button" class="btn btn-primary btn-block btn-lg" onclick="invia_chat();" ><i class="fa fa-send"></i> '.tr('Invia').'</a>
            </div>
        </div>
    </form>
</div>';

echo '
<script>
    $(document).ready(function(){
        $("#contenuto").focus();
        caricaChat($("#frame-chat"));

        setInterval(function(){
            caricaChat($("#frame-chat"));
        },3000);
    });

    function caricaChat(container){
        return $.get("'.base_path().'/modules/gdr_chat/ajax/complete.php?op=carica_chat_off", function(result) {
            var data = JSON.parse(result);
            container.html(data.html);
        });
    }

    function invia_chat(){
        if( $("#contenuto").val() ){
            swal({
                title: "'.tr('Invio messaggio').'",
                text: "'.tr('Confermi di voler inviare il messaggio?').'",
                type: "warning",
                showCancelButton: true,
                confirmButtonText: "'.tr('Invia').'",
            }).then(function (result) {
                $.ajax({
                    url: "'.base_path().'/actions.php",
                    cache: false,
                    type: "POST",
                    data: $("#chat-form").serialize(),
                    success: function(result) {
                        setTimeout(function(){
                            location.reload();
                        },500);
                    }
                });
            });
        }
    }
</script>';

//carico la skin preimpostata
if (isset($user)) {
    if ($user->getThemeAttribute()) {
        $theme = $user->getThemeAttribute();
    }

    //Inlcusione CSS skin
    try {
        $skin = Modules\Skins\Skin::find(setting('Skin'));
        echo '
        <style>'.html_entity_decode($skin->css).'</style>';
    }catch(Exception $e) {
        flash()->error(tr('La skin _NOME_ risulta avere qualche problema di codice e non può essere utilizzata! Per assistenza contatta pure la gestione.',[
            '_NOME_' => $skin->nome,
        ]));

        $setting = $dbo->table('zz_settings')
            ->where('nome','Skin')
            ->first();

        $skin = Modules\Skins\Skin::find($setting->valore);

        echo '
        <style>'.html_entity_decode($skin->css).'</style>';

        $impostazione_utente = Impostazioni::where('id_user', $user->id)->where('id_setting', $setting->id)->first();
        $impostazione_utente->value = $setting->valore;
        $impostazione_utente->save();
    }

}