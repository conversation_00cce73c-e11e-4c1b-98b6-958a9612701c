/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

.main-header {
    background-color: transparent;
}

.dataTable td{
    vertical-align: middle;
}

.content:has(.dataTables_wrapper) {
    min-height: 93%;
}

.div-button {
    padding-top:2.5vh;
}

.mappa_gdr {
    width: 100%;
    height: 830px;
} 

.contenuto_scheda .col-md-6{
    max-width: 49%;
}

@keyframes marquee {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

.marvel-text {
    overflow: hidden;
    white-space: nowrap;
    animation: marquee 10s linear infinite;
}

.marvel-text p {
    display: inline-block;
    padding-left: 100%;
    color: #ed1d24;
    font-size: 2rem;
    font-weight: bold;
}

.row {
    position: relative;
    z-index: 2;
}

.sfondo_riga {
    background-size: 310%;
    background-position: initial;
    display: inline-block !important;
    border: 5px solid rgba(255, 0, 0, 0.5);
    color: white;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.direct-chat {
    background-color: transparent;
}

#data-ora > h1{
    color: white;
}

.direct-chat-name, .direct-chat-timestamp{
    color: white !important;
}


.content-wrapper {
    background-size: 100%;
    background-size: contain;
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    height: 100%;
    overflow: auto;
}

.immagine_scheda {
    width: 100%;
    display: block;
    transition: opacity 0.5s ease;
}

.primary-image {
    display: block;
    transition: opacity 0.5s ease;
}

.hover-image {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.image-container:hover .hover-image {
    opacity: 1;
}

.image-container:hover .primary-image {
    opacity: 0;
}

.text-stats, .text-stats ~ span {
    color: black;
    font-size: 20px;
    font-weight: bold;
}

.text-stats ~ span {
    font-size: 13px;
}

.quadrato {
    text-align: center;
    width: 70px;
    height: 70px;
    background-color: white;
    border-radius: 20px;
    display: inline-block;
}

.quadrato-smussato {
    width: 90px;
    height: 70px;
    background-color: white;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 0;
    display: inline-block;
    text-align: center;
}

/* Imposta l\'altezza massima della tabella */
.table-container {
    max-height: 600px;
    overflow-y: auto;
}

/* Imposta l\'intestazione della tabella come posizione fissa */
.table-container thead {
    position: sticky;
    top: 0;
    z-index: 10;
    border: 0px solid transparent;
    background-color: #f5f5f5; 
}

/* Imposta l\'ultima riga della tabella come posizione fissa */
.table-container tfoot {
    position: sticky;
    bottom: 0;
    z-index: 10;
    background-color: #f5f5f5; 
}

.lista-dettagli {
    background-color: white;
    color: black;
    width: 100%;
    padding: 1px;
    clip-path: polygon(10px 0, 100% 0, calc(100% - 10px) 100%, 0% 100%);
    cursor: pointer;
}

.lista-dettagli-bordo {
    background-color: red;
    padding: 3px;
    clip-path: polygon(10px 0, 100% 0, calc(100% - 10px) 100%, 0% 100%);
}

.background {
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 20px;
    width:99%;
    margin-left:0.5%;
    margin-top: 3px;
    box-sizing: border-box;
}

.img-left {
    display: flex;
    align-items: flex-start;
}

.img-left img {
    margin-right: 10px;
}

#frame-chat {
    height: 73vh;
    overflow: auto;
    border:1px solid black;
    background-color: black;
    opacity:0.5;
    color: white;
    padding:1%;
}

#frame-chat > table {
    font-family: "Comic Sans MS", "Comic Sans";
    font-size: 12px !important;
}

.img-chat {
    border: 3px solid gray;
    width: 70px;
    height: 70px;
}

.table-condensed td {
    border-top: none;
}

.control-sidebar-open {
    width: 300px !important;
}

.bg-amazing {
    background-color: #bdb040 !important;
}

.dice {
    width: 25px; 
    height: 25px; 
    background-image: url("chat/dado.png");
    background-position: center;
    background-size: cover;
    color:black;
    display: inline-block;
    font-size: 20px !important;
}

.dice-m {
    width: 25px; 
    height: 25px; 
    background-image: url("chat/dado_m.png");
    background-position: center;
    background-size: cover;
    color:white;
    display: inline-block;
    font-size: 20px !important;
} 

.dice-m-critico {
    width: 25px; 
    height: 25px; 
    background-image: url("chat/dice.png");
    background-position: center;
    background-size: cover;
    color:white;
    display: inline-block;
    animation: vibrate 2s linear infinite;
    font-size: 20px !important;
} 

/* Definizione dell'animazione di vibrazione */
@keyframes vibrate {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

table tr[data-row-type="even"] {
    background-color: #f2f2f2;
}

table tr[data-row-type="odd"] {
    background-color: white;
}

.modal-presenti{
    background-size: 100%;
    background-size: cover;
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    color: white;
    font-size: 15px;
    font-family: "Comic Sans MS", "Comic Sans";
    border: 3px solid gray;
}

.lista_presenti {
    width: 100%;
    height: 80vh;  
}

.losta_presenti .table-container {
    max-height: 75vh; 
}

.gdr-header {
    color: white;
    font-weight: bold;
    cursor: pointer;
}

.bounce {
    display: inline-block;
    position: relative;
    -moz-animation: bounce 0.5s infinite linear;
    -o-animation: bounce 0.5s infinite linear;
    -webkit-animation: bounce 0.5s infinite linear;
    animation: bounce 0.5s infinite linear;
}

@-webkit-keyframes bounce {
    0% { top: 0; }
    50% { top: -0.2em; }
    70% { top: -0.3em; }
    100% { top: 0; }
}
@-moz-keyframes bounce {
    0% { top: 0; }
    50% { top: -0.2em; }
    70% { top: -0.3em; }
    100% { top: 0; }
}
@-o-keyframes bounce {
    0% { top: 0; }
    50% { top: -0.2em; }
    70% { top: -0.3em; }
    100% { top: 0; }
}
@-ms-keyframes bounce {
    0% { top: 0; }
    50% { top: -0.2em; }
    70% { top: -0.3em; }
    100% { top: 0; }
}
@keyframes bounce {
    0% { top: 0; }
    50% { top: -0.2em; }
    70% { top: -0.3em; }
    100% { top: 0; }
}

.smartphone {
    height: 90vh; /* Altezza dello smartphone */
    width: 55vh;
    border: 16px solid #333; /* Bordo dello smartphone */
    border-radius: 36px; /* Angoli arrotondati */
    background-image: url("xmen/body-bg.jpg");
    background-size: cover;
    background-position: left;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5); /* Ombra per dare profondità */
    margin: 20px auto; /* Centra il div nella pagina */
}

.smartphone .header {
    background-color: #25D366; /* Verde WhatsApp */
    color: white; /* Testo bianco */
    padding: 10px;
    text-align: center; /* Centra il testo */
}

.screen {
    padding: 20px;
    height: calc(100% - 60px); /* Altezza della schermata */
    overflow: auto; /* Permette lo scorrimento se il contenuto è troppo lungo */
    color: white;
    background-color: rgba(0, 0, 0, 0.3);
}

.navigation {
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 60px; /* Altezza della barra di navigazione */
    background-color: #ccc; /* Colore di sfondo della barra di navigazione */
    border-bottom-left-radius: 36px; /* Angoli arrotondati */
    border-bottom-right-radius: 36px; /* Angoli arrotondati */
}

.button {
    flex: 1;
    text-align: center;
    padding: 10px;
    cursor: pointer;
    font-weight: bold;
    color: #333;
}

.messages {
    flex: 1; /* Permette alla sezione dei messaggi di espandersi */
    padding: 10px;
    overflow-y: auto; /* Abilita lo scorrimento verticale */
    height: calc(100% - 120px); /* Altezza dinamica per i messaggi */
}

.message {
    margin: 5px 0; /* Margine tra i messaggi */
    padding: 10px;
    border-radius: 10px; /* Angoli arrotondati per i messaggi */
    max-width: 70%; /* Larghezza massima dei messaggi */
    clear: both; /* Assicura che i messaggi non si sovrappongano */
}

.received {
    background-color: #e5e5ea; /* Colore di sfondo per i messaggi ricevuti */
    color: black; /* Testo bianco per i messaggi inviati */
}

.sent {
    background-color: #25D366; /* Colore di sfondo per i messaggi inviati */
    color: white; /* Testo bianco per i messaggi inviati */
    float: right; /* Allinea a destra */
}

.table-messaggi td{
    color: white;
    border: 0;
}

.table-messaggi tr{
    border: 2px solid rgba(128, 128, 128, 0.5); /* Grigio con 50% di trasparenza */
    background-color: rgba(0, 0, 0, 0.7);
}

.app {
    width: 25%;
    height: 25%;
    cursor: pointer;
    display: inline-block;
}

.messaggistica {
    background-image: url("messaggi/messaggio.png");
    background-size: 90%;
    background-position: center;
    background-repeat: no-repeat;
}

.settings {
    background-image: url("messaggi/settings.png");
    background-size: 90%;
    background-position: center;
    background-repeat: no-repeat;
}

.rubrica {
    background-image: url("messaggi/rubrica.png");
    background-size: 90%;
    background-position: center;
    background-repeat: no-repeat;
}

.daily_buggle{
    background-image: url("messaggi/daily_bugle.jpg");
    background-size: 90%;
    background-position: center;
    background-repeat: no-repeat;
}

.buletin{
    background-image: url("messaggi/buletin.jpg");
    background-size: 90%;
    background-position: center;
    background-repeat: no-repeat;
}

.forum {
    background-color: white;
    padding-top: 1%;
}

.navbar {
    background-color: #343a40;
    border-bottom: 1px solid #343a40;
}

.navbar-light .navbar-nav .nav-link {
    color: white;
}

.login-page {
    background-image: url("https://img.freepik.com/free-vector/comic-style-wallpaper_79603-1248.jpg");
    background-size: 100%;
    background-position: center;
    background-repeat: no-repeat;
}

.direct-chat-name {
    color: black;
}

.users-list > li img  {
    width: 40% !important;
    aspect-ratio: 1 / 1; 
    object-fit: cover;
}

.contacts-list-img, .user-panel img  {
    aspect-ratio: 1 / 1; 
    object-fit: cover;
}

.img-list  {
    width: 80% !important;
    aspect-ratio: 1 / 1; 
    object-fit: cover;
}

.card-solid {
    height: 85%;
    overflow: auto;
}

.direct-chat-contacts {
    background-color: transparent;
    height: 73vh;
}

.direct-chat-messages {
    height: 100%;
}

.animation__shake {
    width: 20%;
}

.card-center-large{
    margin: 1% auto 1%;
}

.brand-link{
    font-size: 1.8rem;
    color: #fff;
    text-shadow: 
        2px 2px 4px #912020,
        0 0 10px rgba(255, 0, 0, 0.4);
}

div.dataTables_wrapper {
    background-color: white;
}

#fields, #calendar {
    background-color: white;
}

blockquote {
    background-color: gray !important;
}

.fc-license-message {
    display:none;
}

.index-footer {
    position: absolute; /* Posizionamento assoluto per sovrapporre il div */
    top: 0%;
    right: -18%;
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.7);
    padding:5px;
}

@media only screen and (max-width: 480px) {
    .animation__shake {
        width: 50%;
    }
}

.blog {
    background-image: url(messaggi/blog_icona.png);
    background-size: 90%;
    background-position: center;
    background-repeat: no-repeat;
}
