<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

use Models\Plugin;

include_once __DIR__.'/core.php';

echo '
<style>
.table-condensed td {
    border-top: none;
}
</style>';

$read_only = $structure->permission == 'r';

if (!empty($id_record) && !empty($id_module)) {
    redirect(base_path().'/editor.php?id_module='.$id_module.'&id_record='.$id_record);
} elseif (empty($id_module)) {
    redirect(base_path().'/index.php');
}

include_once App::filepath('include|custom|', 'top.php');

// Inclusione gli elementi fondamentali
include_once base_dir().'/actions.php';

// Widget in alto
echo '{( "name": "widgets", "id_module": "'.$id_module.'", "position": "top", "place": "controller" )}';

$segmenti = $dbo->FetchArray('SELECT `id` FROM `zz_segments` WHERE `id_module` = '.prepare($id_module));
if ($segmenti) {
    $segmenti = Modules::getSegments($id_module);
    if (empty($segmenti)) {
        echo '
<div class="alert alert-warning">
	<i class="fa fa-warning-circle"></i> '.tr('Questo gruppo di utenti non ha i permessi per visualizzare nessun segmento di questo modulo').'.
</div>';
    }
}

// Lettura eventuali plugins modulo da inserire come tab
echo '
<section class="content-header">
	<div class="container-fluid">
		<div class="row mb-2">
			<div class="col-sm-6">
				<h1>
					<i class="'.$structure['icon'].'"></i> '.$structure->getTranslation('title');

// Pulsante "Aggiungi" solo se il modulo è di tipo "table" e se esiste il template per la popup
if ($structure->hasAddFile() && $structure->permission == 'rw') {
    echo '
						<button type="button" class="btn btn-primary" data-widget="modal" data-title="'.tr('Aggiungi').'..." data-href="add.php?id_module='.$id_module.'&id_plugin='.$id_plugin.'"><i class="fa fa-plus"></i></button>';
}

echo '
				</h1>
			</div>
		</div>
	</div>
</section>

<div class="tab-content">
	<div id="tab_0" class="tab-pane active">';

include base_dir().'/include/manager.php';

echo '
	</div>';

// Plugins
$plugins = Plugin::where('idmodule_to', $id_module)->where('position', 'tab_main')->where('enabled', 1)->get();

$module_record = $record;
foreach ($plugins as $plugin) {
    $record = $module_record;

    echo '
	<div id="tab_'.$plugin->id.'" class="tab-pane">';

    $id_plugin = $plugin->id;

    include base_dir().'/include/manager.php';

    echo '
	</div>';
}

$record = $module_record;

redirectOperation($id_module, !empty($id_parent) ? $id_parent : $id_record);

echo '
</div>';

// Widget in basso
echo '{( "name": "widgets", "id_module": "'.$id_module.'", "position": "right", "place": "controller" )}';

include_once App::filepath('include|custom|', 'bottom.php');

echo '
<script>
    $(document).ready(function(){
        setTimeout(function(){
            $(".unblockable").each(function(){
                $(this).attr("readonly", false);            
            })
        },300);
    });';

// Se l'utente ha i permessi in sola lettura per il modulo, converto tutti i campi di testo in span
if ($read_only || !empty($block_edit)) {
    $not = $read_only ? '' : '.not(".unblockable")';

    echo '
			$(document).ready(function(){
				$("input, textarea, select", "section.content")'.$not.'.attr("readonly", "true");
                $("select, input[type=checkbox]", "section.content")'.$not.'.prop("disabled", true);
                $(".checkbox-buttons badge", "section.content")'.$not.'.addClass("disabled");
                ';

    // Nascondo il plugin Note interne ai clienti
    if ($user->gruppo == 'Clienti') {
        echo '
                $("#link-tab_note").hide();';
    }

    if ($read_only) {
        echo '
				$("a.btn, button, input[type=button], input[type=submit]", "section.content").not(".unblockable").hide();
                $("a.btn-info, button.btn-info, input[type=button].btn-info, #back", "section.content").show();';
    }

    echo '
			});';
}

echo '
</script>';