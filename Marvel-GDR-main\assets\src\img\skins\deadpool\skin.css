.content-wrapper {
    background: url("body-bg.jpg") no-repeat;
    background-size: cover;
}
.sidebar {
    background-color: #520606;
}
.modal-presenti{
    background: #1c1c1c;
    border: 1px solid silver;
}
.modal-presenti .close{
    color: white;
    font-size: 25px;
    font-weight: 100 ;

}
.modal-presenti .modal-header{
    background: #5e0000;

}
.modal-title{
    color: #fff; /* Colore del testo per contrasto */
    text-shadow:
            2px 2px 4px rgba(0, 0, 0, 0.7), /* Ombra scura di base */
            0 0 10px rgba(255, 0, 0, 0.4); /* Luce rossa diffusa intorno al testo */
}
#frame-chat{
    opacity: 0.9;
}
.img-chat {
border: 2px solid #a60b0d ;
}
#frame-chat .azione-chat, #frame-chat .esito-chat {
    border: 2px solid #333; /* <PERSON>rdo grigio scuro per un contrasto meno intenso */
    box-shadow:
            inset 0 0 8px rgba(0, 0, 0, 0.4), /* Ombra diffusa interna più morbida */
            inset 0 0 0 3px rgba(0, 0, 0, 0.2), /* Bordo interno leggero */
            inset -3px -3px 0 rgba(0, 0, 0, 0.2), /* Ombra leggermente sfalsata */
            3px 3px 0 rgba(0, 0, 0, 0.3); /* Ombra esterna più leggera */
    padding: 15px;
    letter-spacing: 1px;
    color: #f5f5f5; /* Testo chiaro per un buon contrasto */
    font-size: 14px;
font-weight: 200;
}

/* Aggiungi uno stile per il testo */
#frame-chat .azione-chat p, #frame-chat .esito-chat p {
    line-height: 1.5; /* Spaziatura tra le righe per migliorare la leggibilità */
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6); /* Leggero contorno per far risaltare il testo */
}

#frame-chat .azione-chat {
    background: linear-gradient(135deg, #5d0405, #5e0000); /* Sfumatura di rosso */
}

#frame-chat .esito-chat {
    background: linear-gradient(135deg, #545d04, #5e5d00); /* Sfumatura di rosso */
}

#mappa{
    border: 2px solid #a60b0d ;
    box-shadow:
            inset 0 0 8px rgba(0, 0, 0, 0.4), /* Ombra diffusa interna più morbida */
            inset 0 0 0 3px rgba(0, 0, 0, 0.2), /* Bordo interno leggero */
            inset -3px -3px 0 rgba(0, 0, 0, 0.2), /* Ombra leggermente sfalsata */
            5px 5px 0 rgba(0, 0, 0, 0.7); /* Ombra esterna più leggera */

}

/* Stili per la scrollbar */
::-webkit-scrollbar {
    width: 12px; /* Larghezza della scrollbar */
    background-color: #000; /* Colore del tracciato */
}

/* Stili per la maniglia di scorrimento */
::-webkit-scrollbar-thumb {
    background-color:rgb(166 11 13 / 50%); /* Colore della maniglia (rosso Deadpool) */

    border: 1px solid #000; /* Bordo bianco attorno alla maniglia */
}

/* Stili per la maniglia durante l'hover */
::-webkit-scrollbar-thumb:hover {
    background-color: #8c0b0b; /* Colore leggermente più chiaro in hover */
}