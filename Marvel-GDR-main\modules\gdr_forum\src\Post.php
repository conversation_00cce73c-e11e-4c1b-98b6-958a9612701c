<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

namespace Modules\Bacheche;

use Common\SimpleModelTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Traits\RecordTrait;
use Modules\Schede\Scheda;
use Models\User;

class Post extends Model
{
    use SimpleModelTrait;
    use RecordTrait;
    use SoftDeletes;

    protected $table = 'gdr_forum_post';
    protected $primaryKey = 'id';
    protected $module = 'Bacheche';

    protected $guarded = [];

    public static function build($id_utente, $idpersonaggio, $id_forum, $nome)
    {
        $model = new static();

        $model->id_utente = $id_utente;
        $model->idpersonaggio = $idpersonaggio;
        $model->id_forum = $id_forum;
        $model->nome = $nome;
        $model->save();

        return $model;
    }

    public function save(array $options = [])
    {
        return parent::save($options);
    }

    // Attributi Eloquent
    public function getModuleAttribute()
    {
        return 'Bacheche';
    }

    public function personaggio()
    {
        return $this->belongsTo(Scheda::class, 'idpersonaggio');
    }

    /**
     * Ottiene il numero di "Mi piace" per questo post
     * @return int
     */
    public function getLikesCount()
    {
        return database()->fetchOne("
            SELECT COUNT(*) as total
            FROM gdr_forum_likes
            WHERE id_post = " . prepare($this->id)
        )['total'] ?? 0;
    }

    /**
     * Verifica se un utente ha messo "Mi piace" a questo post
     * @param int $user_id ID dell'utente
     * @return bool
     */
    public function isLikedByUser($user_id)
    {
        $result = database()->fetchOne("
            SELECT id FROM gdr_forum_likes
            WHERE id_post = " . prepare($this->id) . "
            AND id_utente = " . prepare($user_id)
        );

        return !empty($result);
    }

    /**
     * Aggiunge o rimuove il "Mi piace" di un utente per questo post
     * @param int $user_id ID dell'utente
     * @return array Risultato dell'operazione con stato e conteggio aggiornato
     */
    public function toggleLike($user_id)
    {
        $is_liked = $this->isLikedByUser($user_id);

        if ($is_liked) {
            // Rimuovi il "mi piace"
            database()->query("
                DELETE FROM gdr_forum_likes
                WHERE id_post = " . prepare($this->id) . "
                AND id_utente = " . prepare($user_id)
            );
            $user_liked = false;
        } else {
            // Aggiungi il "mi piace"
            database()->query("
                INSERT INTO gdr_forum_likes (id_post, id_utente, created_at)
                VALUES (" . prepare($this->id) . ", " . prepare($user_id) . ", NOW())
            ");
            $user_liked = true;
        }

        return [
            'user_liked' => $user_liked,
            'likes_count' => $this->getLikesCount()
        ];
    }

    /**
     * Ottiene la lista degli utenti che hanno messo "Mi piace" a questo post
     * @param int $limit Numero massimo di utenti da restituire
     * @return array Lista degli utenti
     */
    public function getLikedUsers($limit = 10)
    {
        return database()->fetchArray("
            SELECT u.username, p.nome, p.cognome, l.created_at
            FROM gdr_forum_likes l
            JOIN zz_users u ON l.id_utente = u.id
            LEFT JOIN gdr_schede p ON u.idpersonaggio = p.id
            WHERE l.id_post = " . prepare($this->id) . "
            ORDER BY l.created_at DESC
            LIMIT " . (int)$limit
        );
    }

    public function utente()
    {
        return $this->belongsTo(User::class, 'id_utente');
    }
}
