<?php

declare(strict_types=1);

/*
 * This file is a part of dflydev/dot-access-data.
 *
 * (c) Dragonfly Development Inc.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Dflydev\DotAccessData\Exception;

/**
 * Thrown when trying to access an invalid path in the data array
 */
class InvalidPathException extends DataException
{
}
