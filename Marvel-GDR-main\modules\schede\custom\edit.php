<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

include_once __DIR__.'/../../core.php';
use Models\Module;
use Models\User;

if( $user->username!='Superuser' ){
    include_once __DIR__.'/../edit.php';
}else{

    //Includo il css
    echo '
    <link rel="stylesheet" type="text/css" media="all" href="'.$structure->fileurl('custom/style.css').'"/>';

    echo '
    <style>
        .squadrato-3d {
            font-family: \'SquareDeal\', sans-serif;
            font-size: 30px;
            font-weight: bold;
            padding-rigth: 2px;
            text-shadow: 4px 4px 0 #000, 8px 8px 0 rgba(0,0,0,0.3);
            display: inline-block;
        }
    </style>';

    $blocca_statistiche = true;
    $blocca_modifica = ($user->gruppo=='Amministratori' || $user->idpersonaggio==$id_record ? 0 : 1);

    $descrizione = ($scheda->descrizione ? json_encode(htmlentities($scheda->descrizione)) : null);
    $background = ($scheda->background ? json_encode(htmlentities($scheda->background)) : null);
    $background_privato = ($scheda->background_privato ? json_encode(htmlentities($scheda->background_privato)) : null);
    $max_value = $scheda->rank + 3;

    ?>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-1"></div>
            <div class="col-md-4 text-center">
                <div class="character-image-wrapper text-center">
                    <img class="character-image main" src="<?php echo $scheda->image;?>"/>
                    <img  class="character-image hover" src="<?php echo ($scheda->imagealias ? $scheda->imagealias : $scheda->image);?>"/>
                </div>
                <div class="custom-audio-player" id="audioWrapper">
                    <button class="audio-btn" id="playPauseBtn" title="Play/Pause">
                        <i class="bi bi-play-fill"></i>
                    </button>
                    <div class="audio-middle">
                        <div class="audio-track">
                            <div id="progressBar"></div>
                        </div>
                        <div class="time-volume">
                            <div class="time-display" id="timeDisplay">
                                0:00 / 0:00
                            </div>
                            <div class="volume-control">
                                <i class="bi bi-volume-up-fill"></i>
                                <input id="volumeSlider" max="1" min="0" step="0.01" type="range" value="1"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="icone">
                <?php
                    $utente_scheda = User::where('idpersonaggio',$scheda->id)->first();
                    if( $utente_scheda->gruppo=='Amministratori' ){
                        echo '
                            <img src="https://thanosvibs.money/static/assets/items/artifact_doctordoom.png" title="Gestore"/>';
                    }elseif( $utente_scheda->gruppo=='Master' ){
                        echo '
                            <img src="https://yoolk.ninja/wp-content/uploads/2022/04/Marvel-Uatu-hte-Watcher-1024x819.png" title="Staff"/>';
                    }

                    foreach($gruppi as $gruppo){
                        if( $gruppo->icona && $gruppo->visible ){
                        echo '
                            <img src="'.$gruppo->icona.'" title="'.$gruppo->nome.($gruppo->ruolo ? ' - '.$gruppo->ruolo : '').'" style="max-width: 35px;max-height: 35px;"/>';
                        }
                    }
                    ?>
                </div>
            <?php
                if( !empty($scheda->musica) ){
                    echo '
                    <audio id="audioScheda" preload="auto">
                        <source src="'.$scheda->musica.'"/>
                    </audio>';
                }
            ?>
            </div>

            <div class="col-md-5">
                <h1 class="character-name text-center">
                    <?php echo ($scheda->is_approvato ? ' <i class="fa fa-check-circle text-success" title="'.tr('Approvato dallo staff').'"></i> ' : '').$scheda->nome.' '.$scheda->cognome;?>
                    <span class="character-alias mb-3"><?php echo $scheda->alias;?></span>
                </h1>

                <!-- Attributi in stile scheda gioco di ruolo -->
                <div class="attributes-container">
                    <div class="diamond-stats-container d-flex justify-content-center">
                        <!-- Health Diamond -->
                        <div class="diamond-stat">
                            <div class="diamond-shape">
                                <div class="diamond-content">
                                    <div class="stat-name"><?php echo tr('Rank');?></div>
                                    <div class="stat-value"><?php echo $scheda->rank;?></div>
                                </div>
                            </div>
                        </div>
                        <!-- Karma Diamond -->
            <div class="diamond-stat">
                <div class="diamond-shape">
                    <div class="diamond-content">
                        <div class="stat-name"><?php echo tr('Livello');?></div>
                        <div class="stat-value"><?php echo $scheda->livello;?></div>
                    </div>
                </div>
            </div>
            <!-- Focus Diamond -->
            <div class="diamond-stat">
                <div class="diamond-shape">
                <div class="diamond-content">
                <div class="stat-name"><?php echo tr('EXP');?></div>
                <div class="stat-value"><?php echo Translator::numberToLocale($scheda->punti_esperienza,2);?></div>
                </div>
                </div>
            </div>
            <!-- Health Diamond -->
            <div class="diamond-stat">
                <div class="diamond-shape">
                    <div class="diamond-content">
                        <div class="stat-name"><?php echo tr('Salute');?></div>
                        <div class="stat-value"><?php echo $scheda->salute_attuale.'/'.$scheda->salute;?></div>
                        <div class="stat-max"><?php echo tr('Riduzione');?>
                            <br/>
                            <?php echo ($riduzione_salute ? -$riduzione_salute : '---');?>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Karma Diamond -->
            <div class="diamond-stat">
                <div class="diamond-shape">
                    <div class="diamond-content">
                        <div class="stat-name"><?php echo tr('Focus');?></div>
                        <div class="stat-value"><?php echo $scheda->focus_attuale.'/'.$scheda->focus;?></div>
                        <div class="stat-max"><?php echo tr('Riduzione');?>
                            <br/>
                            <?php echo ($riduzione_focus ? -$riduzione_focus : '---');?>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Focus Diamond -->
            <div class="diamond-stat">
                <div class="diamond-shape">
                    <div class="diamond-content">
                        <div class="stat-name"><?php echo tr('Karma');?></div>
                        <div class="stat-value"><?php echo $scheda->karma;?></div>
                    </div>
                </div>
            </div>
            </div>
            </div>
            <form action="" method="post" id="stats-form">
                <input type="hidden" name="backto" value="record-edit">
                <input type="hidden" name="op" value="update_caratteristiche">

                <div class="ability-section mt-4">
                    <div class="ability-header d-flex justify-content-between align-items-center">
                    <h3 class="ability-title"><?php echo tr('Caratteristiche');?></h3>
                    </div>
                    <div class="ability-table">
                        <?php
                        if( ($scheda->punti_abilità_totali - $scheda->punti_abilità_usati)>0 && ($user->gruppo=='Amministratori' || $user->idpersonaggio==$id_record) ){
                            $blocca_statistiche = 0;

                            echo '
                            <div class="alert alert-info">
                                '.tr('Punti disponibili _DISPONIBILI_ / _TOTALI_. I punti massimi di ogni statistica non devono superare 3 + Rank.',[
                                    '_TOTALI_' => $scheda->punti_abilità_totali,
                                    '_DISPONIBILI_' => '<span id="punti_abilità_disponibili">'.($scheda->punti_abilità_totali - $scheda->punti_abilità_usati).'</span>',
                                ]).'
                                <button class="btn btn-primary btn-block" type="submit"><i class="fa fa-save"></i> '.tr('Salva').'</button>
                            </div>';
                        }elseif($user->gruppo=='Amministratori'){
                            $blocca_statistiche = 0;

                            echo '
                            <div class="alert alert-info">
                                <button class="btn btn-primary btn-block" type="submit"><i class="fa fa-save"></i> '.tr('Salva').'</button>
                            </div>';
                        }
                        ?>
                        <div class="ability-table-header">
                            <div class="ability-col"></div>
                            <div class="ability-col"><?php echo tr('Statistica');?></div>
                            <div class="ability-col"><?php echo tr('Difesa passiva');?></div>
                            <div class="ability-col"><?php echo tr('Danni');?></div>
                        </div>
                        <!-- Melee -->
                        <div class="ability-row">
                            <div class="ability-col ability-name">
                            <span class="ability-firstletter">M</span>ischia</div>
                            <div class="ability-col ability-score">
                                {["type":"number", "label":"", "name":"car_0", "value":"$car_0$", "readonly":<?php echo $blocca_statistiche;?>, "class":"text-center text-stats", "decimals":"0", "class":"<?php echo ($blocca_statistiche ? 'ability-score-input' : 'text-center');?>"]}
                            </div>
                            <div class="ability-col ability-defense"><?php echo ($scheda->car_0+10+$bonus['difesa_forza']);?></div>
                            <div class="ability-col ability-attack">
                                <img src="https://excelsiorgdr.altervista.org/assets/dist/img/skins/chat/dice.png"> X <?php echo $scheda->rank.' + '.$danno_forza;?>
                            </div>
                        </div>
                        <!-- Agility -->
                        <div class="ability-row">
                            <div class="ability-col ability-name">
                            <span class="ability-firstletter">A</span>gilità</div>
                            <div class="ability-col ability-score">
                                {["type":"number", "label":"", "name":"car_1", "value":"$car_1$", "readonly":<?php echo $blocca_statistiche;?>, "class":"text-center text-stats", "decimals":"0", "class":"<?php echo ($blocca_statistiche ? 'ability-score-input' : 'text-center');?>"]}
                            </div>
                            <div class="ability-col ability-defense"><?php echo ($scheda->car_1+10+$bonus['difesa_destrezza']);?></div>
                            <div class="ability-col ability-attack">
                                <img src="https://excelsiorgdr.altervista.org/assets/dist/img/skins/chat/dice.png"> X <?php echo $scheda->rank.' + '.$danno_destrezza;?>
                            </div>
                        </div>
                        <!-- Resilience -->
                        <div class="ability-row">
                            <div class="ability-col ability-name">
                            <span class="ability-firstletter">R</span>esilienza</div>
                            <div class="ability-col ability-score">
                                {["type":"number", "label":"", "name":"car_2", "value":"$car_2$", "readonly":<?php echo $blocca_statistiche;?>, "class":"text-center text-stats", "decimals":"0", "class":"<?php echo ($blocca_statistiche ? 'ability-score-input' : 'text-center');?>"]}
                            </div>
                            <div class="ability-col ability-defense"><?php echo ($scheda->car_2+10+$bonus['difesa_costituzione']);?></div>
                            <div class="ability-col ability-attack"></div>
                        </div>
                        <!-- Vigilance -->
                        <div class="ability-row">
                            <div class="ability-col ability-name">
                            <span class="ability-firstletter">V</span>igilanza</div>
                        <div class="ability-col ability-score">
                                {["type":"number", "label":"", "name":"car_3", "value":"$car_3$", "readonly":<?php echo $blocca_statistiche;?>, "class":"text-center text-stats", "decimals":"0", "class":"<?php echo ($blocca_statistiche ? 'ability-score-input' : 'text-center');?>"]}
                            </div>
                            <div class="ability-col ability-defense"><?php echo ($scheda->car_3+10+$bonus['difesa_percezione']);?></div>
                            <div class="ability-col ability-attack"></div>
                        </div>
                        <!-- Ego -->
                        <div class="ability-row">
                            <div class="ability-col ability-name">
                            <span class="ability-firstletter">E</span>go</div>
                            <div class="ability-col ability-score">
                                {["type":"number", "label":"", "name":"car_4", "value":"$car_4$", "readonly":<?php echo $blocca_statistiche;?>, "class":"text-center text-stats", "decimals":"0", "class":"<?php echo ($blocca_statistiche ? 'ability-score-input' : 'text-center');?>"]}
                            </div>
                            <div class="ability-col ability-defense"><?php echo ($scheda->car_4+10+$bonus['difesa_volonta']);?></div>
                            <div class="ability-col ability-attack">
                                <img src="https://excelsiorgdr.altervista.org/assets/dist/img/skins/chat/dice.png"> X <?php echo $scheda->rank.' + '.$danno_volonta;?>
                            </div>
                        </div>
                        <!-- Logic -->
                        <div class="ability-row">
                            <div class="ability-col ability-name">
                            <span class="ability-firstletter">L</span>ogica</div>
                            <div class="ability-col ability-score">
                                {["type":"number", "label":"", "name":"car_5", "value":"$car_5$", "readonly":<?php echo $blocca_statistiche;?>, "class":"text-center text-stats", "decimals":"0", "class":"<?php echo ($blocca_statistiche ? 'ability-score-input' : 'text-center');?>"]}
                            </div>
                            <div class="ability-col ability-defense"><?php echo ($scheda->car_5+10+$bonus['difesa_intelligenza']);?></div>
                            <div class="ability-col ability-attack">
                                <img src="https://excelsiorgdr.altervista.org/assets/dist/img/skins/chat/dice.png"> X <?php echo $scheda->rank.' + '.$danno_intelligenza;?>
                            </div>
                        </div>
                    </div>
                    <div class="ability-header d-flex justify-content-between align-items-center">
                        <h3 class="ability-title"><?php echo tr('Velocità');?></h3>
                    </div>
                    <div class="ability-table">
                        <div class="ability-row">
                            <div class="ability-col ability-name"><?php echo tr('Corsa');?></div>
                            <div class="ability-col ability-score"><?php echo $movimento['corsa'];?></div>
                        </div>
                        <div class="ability-row">
                            <div class="ability-col ability-name"><?php echo tr('Arrampicata/Salto');?></div>
                            <div class="ability-col ability-score"><?php echo $movimento['salto'];?></div>
                        </div>
                        <div class="ability-row">
                            <div class="ability-col ability-name"><?php echo tr('Nuoto');?></div>
                            <div class="ability-col ability-score"><?php echo $movimento['nuoto'];?></div>
                        </div>
                    <?php
                        //Movimenti speciali
                        if( !empty($movimento['volo']) ){
                            echo '
                            <div class="ability-row">
                                <div class="ability-col ability-name">'.tr('Volo').'</div>
                                <div class="ability-col ability-score">'.$movimento['volo'].'</div>
                            </div>';
                        }

                        if( !empty($movimento['oscillazione']) ){
                            echo '
                            <div class="ability-row">
                                <div class="ability-col ability-name">'.tr('Oscillazione').'</div>
                                <div class="ability-col ability-score">'.$movimento['oscillazione'].'</div>
                            </div>';
                        }

                        if( !empty($movimento['grinding']) ){
                            echo '
                            <div class="ability-row">
                                <div class="ability-col ability-name">'.tr('Grinding').'</div>
                                <div class="ability-col ability-score">'.$movimento['grinding'].'</div>
                            </div>';
                        }
                    ?>
                    </div>
                </div>
            </form>
            <div class="bottoni">
                <a class="btn btn-primary btn-lg unblockable" data-title="<?php echo tr('Statistiche personaggio');?>" data-widget="modal" data-href="<?php echo $rootdir;?>/modules/schede/plugins/statistiche.php?id_record=<?php echo $id_record;?>">
                    <i class="fa fa-line-chart"></i>&ensp;<?php echo tr('Statistiche');?>
                </a>

                <a class="btn btn-primary btn-lg unblockable" data-title="<?php echo tr('Visualizza collezioni');?>" data-widget="modal" data-href="<?php echo $rootdir;?>/modules/gdr_collezionabili/modals/anteprima_raccolta.php?idpersonaggio=<?php echo $id_record;?>">
                    <i class="fa fa-list"></i>&ensp;<?php echo tr('Collezioni');?>
                </a>

                <a class="btn btn-primary btn-lg unblockable bound clickable" onclick="launch_modal('<?php echo tr('Messaggio privato OFF')?>', '<?php echo base_path();?>/modules/gdr_messaggi/add_messaggio.php?id_module=<?php echo Module::where('name','Messaggi')->first()->id;?>&iddestinatario=<?php echo $id_record;?>');" title="<?php echo tr('Invia messaggio privato OFF');?>">
                    <i class="fa fa-send"></i>&ensp;<?php echo tr('Invia messaggio privato OFF');?>
                </a>
            </div>
            </div>

        </div>
    </div>

    <script>
        <?php
            if( !empty($scheda->musica) ){
            ?>
                const audio = document.getElementById('audioScheda');
                const playBtn = document.getElementById('playPauseBtn');
                const progress = document.getElementById('progressBar');
                const volumeSlider = document.getElementById('volumeSlider');
                const timeDisplay = document.getElementById('timeDisplay');
                const themeSelector = document.getElementById('themeSelector');

                function formatTime(seconds) {
                const min = Math.floor(seconds / 60);
                const sec = Math.floor(seconds % 60).toString().padStart(2, '0');
                return `${min}:${sec}`;
                }
                
                playBtn.addEventListener('click', () => {
                if (audio.paused) {
                    audio.play();
                    playBtn.innerHTML = '<i class="bi bi-pause-fill"></i>';
                } else {
                    audio.pause();
                    playBtn.innerHTML = '<i class="bi bi-play-fill"></i>';
                }
                });
                
                audio.addEventListener('timeupdate', () => {
                const percentage = (audio.currentTime / audio.duration) * 100;
                progress.style.width = percentage + '%';
                timeDisplay.textContent = `${formatTime(audio.currentTime)} / ${formatTime(audio.duration)}`;
                });
                
                audio.addEventListener('ended', () => {
                playBtn.innerHTML = '<i class="bi bi-play-fill"></i>';
                });
                
                volumeSlider.addEventListener('input', () => {
                audio.volume = volumeSlider.value;
                });

                /*
                const themeSelector = document.getElementById('themeSelector');

                themeSelector.addEventListener('change', function () {
                    const newTheme = this.value;
                    document.body.className = newTheme;
                });
                */
                <?php
            }
        ?>

        $("#car_0, #car_1, #car_2, #car_3, #car_4, #car_5").on("keyup",function(){
            var car_0 = parseFloat($("#car_0").val().toEnglish());
            var car_1 = parseFloat($("#car_1").val().toEnglish());
            var car_2 = parseFloat($("#car_2").val().toEnglish());
            var car_3 = parseFloat($("#car_3").val().toEnglish());
            var car_4 = parseFloat($("#car_4").val().toEnglish());
            var car_5 = parseFloat($("#car_5").val().toEnglish());

            if( car_0 > parseFloat("<?php echo $max_value;?>") ){
                car_0 = <?php echo $max_value;?>;
                $("#car_0").val(<?php echo $max_value;?>);
            }
            if( car_1 > parseFloat("<?php echo $max_value;?>") ){
                car_1 = <?php echo $max_value;?>;
                $("#car_1").val(<?php echo $max_value;?>);
            }
            if( car_2 > parseFloat("<?php echo $max_value;?>") ){
                car_2 = <?php echo $max_value;?>;
                $("#car_2").val(<?php echo $max_value;?>);
            }
            if( car_3 > parseFloat("<?php echo $max_value;?>") ){
                car_3 = <?php echo $max_value;?>;
                $("#car_3").val(<?php echo $max_value;?>);
            }
            if( car_4 > parseFloat("<?php echo $max_value;?>") ){
                car_4 = <?php echo $max_value;?>;
                $("#car_4").val(<?php echo $max_value;?>);
            }
            if( car_5 > parseFloat("<?php echo $max_value;?>") ){
                car_5 = <?php echo $max_value;?>;
                $("#car_5").val(<?php echo $max_value;?>);
            }

            var totale = car_0 + car_1 + car_2 + car_3 + car_4 + car_5;
            totale = totale.toFixed(0);

            if( totale > <?php echo $scheda->punti_abilità_totali;?> ){
                totale = totale - parseFloat($(this).val().toEnglish());

                $(this).val(0).trigger("change");
                $("#punti_abilità_disponibili").html( (<?php echo $scheda->punti_abilità_totali;?> - totale) ).trigger("change");

                swal("<?php echo tr('Attenzione!!');?>", "<?php echo tr('Il totale delle statistiche supera il totale consentito per il tuo Rank!');?>.", "error");
            }else{
                $("#punti_abilità_disponibili").html((<?php echo $scheda->punti_abilità_totali;?> - totale)).trigger("change");
            }
        });

  </script>

<?php
}


