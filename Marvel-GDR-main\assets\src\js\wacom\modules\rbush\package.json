{"name": "rbush", "version": "3.0.1", "description": "High-performance 2D spatial index for rectangles (based on R*-tree with bulk loading and bulk insertion algorithms)", "homepage": "https://github.com/mourner/rbush", "repository": {"type": "git", "url": "git://github.com/mourner/rbush.git"}, "keywords": ["spatial", "tree", "search", "rectangle", "index", "math"], "author": "<PERSON>", "license": "MIT", "main": "rbush.js", "module": "index.js", "browser": "rbush.min.js", "jsdelivr": "rbush.min.js", "unpkg": "rbush.min.js", "devDependencies": {"benchmark": "^2.1.4", "c8": "^5.0.1", "eslint": "^6.1.0", "eslint-config-mourner": "^3.0.0", "esm": "^3.2.25", "rollup": "^1.17.0", "rollup-plugin-buble": "^0.19.8", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "tape": "^4.11.0"}, "scripts": {"pretest": "eslint index.js test/test.js bench/*.js", "test": "tape -r esm test/test.js", "perf": "node -r esm ./bench/perf.js", "cov": "c8 npm run test", "build": "rollup -c", "prepublishOnly": "npm run build"}, "files": ["index.js", "rbush.js", "rbush.min.js"], "eslintConfig": {"extends": "mourner"}, "dependencies": {"quickselect": "^2.0.0"}}