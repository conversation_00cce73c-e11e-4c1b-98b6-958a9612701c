/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

.skin-default .wrapper,
.skin-default .main-sidebar,
.skin-default .left-side {
    background: #333;
}

.skin-default.login-page .wrapper {
    background: #d2d6de;
    display: block;
}

.skin-default .content-wrapper,
.skin-default .content {
    background: #f6f6f6;
}

.skin-default #supersearch {
    color: #eee;
}

.skin-default #supersearch:focus {
    color: #222;
}

.skin-default .inner {
    color: #eee;
}

.skin-default .nav-tabs-custom .nav-tabs li a {
    color: #3c8dbc;
}

.skin-default .nav-tabs-custom .nav-tabs li.active a {
    color: inherit;
}

.skin-default .nav-tabs-custom .nav-tabs.pull-right li a.back-btn {
    color: #3C8DBC;
}

.skin-default .nav-tabs-custom .nav-tabs.pull-right li a.back-btn:hover {
    color: #72AFD2;
}

.skin-default .main-header .navbar > span {
    color: #eee;
    background: #222;
    border: none;
}

.skin-default .main-header .navbar .nav > li > a
.skin-default .main-header .navbar .nav > li > a:hover,
.skin-default .main-header .navbar .nav > li > a:active,
.skin-default .main-header .navbar .nav > li > a:focus,
.skin-default .main-header .navbar .nav .open > a,
.skin-default .main-header .navbar .nav .open > a:hover,
.skin-default .main-header .navbar .nav .open > a:focus,
.skin-default .main-header .navbar .nav .active a,
.skin-default .main-header .navbar .nav .actual a,
.skin-default .main-header .navbar .nav .menu-open a {
    background: rgba(0, 0, 0, 0.2);
    color: #f6f6f6;
}

.skin-default .main-header .navbar .sidebar-toggle,
.skin-default .main-header .navbar .sidebar-toggle .icon-bar {
    color: #f6f6f6;
}

.skin-default .main-header .navbar .sidebar-toggle:hover {
    color: #f6f6f6;
    background: rgba(0, 0, 0, 0.2);
}

.skin-default .main-header .navbar .sidebar-toggle {
    color: #f6f6f6;
}

.skin-default .main-header .navbar .sidebar-toggle:hover {
    background: #222;
}

@media (max-width: 767px) {
    .skin-default .main-header .navbar .dropdown-menu li.divider {
        background: rgba(255, 255, 255, 0.1);
    }

    .skin-default .main-header .navbar .dropdown-menu li a {
        color: #f6f6f6;
    }

    .skin-default .main-header .navbar .dropdown-menu li a:hover {
        background: #222;
    }
}

.skin-default .main-header .navbar .sidebar-toggle .icon-bar {
    background: #f6f6f6;
}

.skin-default .main-header .logo {
    background: #222;
    color: #f6f6f6;
    border-bottom: 0 solid transparent;
}

.skin-default .main-header .logo:hover {
    background: #222;
}

.skin-default .main-header li.user-header {
    background: #222;
}

.skin-default .main-header a {
    text-decoration: none;
}

.skin-default .content-header {
    background: transparent;
}

.skin-default .user-panel .info,
.skin-default .user-panel .info a {
    color: #f6f6f6;
}

.skin-default .sidebar-menu li.header {
    color: #4b646f;
    background: #222;
}

.skin-default .sidebar-menu li a {
    border-left: 3px solid transparent;
}

.skin-default .sidebar-menu li:hover a,
.skin-default .sidebar-menu li.active a,
.skin-default .sidebar-menu li.actual a,
.skin-default .sidebar-menu li.menu-open a {
    background: #222;
    border-left-color: #222;
}

.skin-default .sidebar-menu li:hover > a,
.skin-default .sidebar-menu li.actual > a {
    color: #f6f6f6;
}

.skin-default .sidebar-menu li .treeview-menu {
    margin: 0 1px;
    background: #222;
}

.skin-default .sidebar a {
    color: #f6f6f6;
}

.skin-default .sidebar a:hover {
    text-decoration: none;
}

.skin-default .treeview-menu li a {
    color: #f6f6f6;
}

.skin-default .sidebar-form {
    border-color: #222;
}

.skin-default .sidebar-form input[type="text"],
.skin-default .sidebar-form .btn {
    box-shadow: none;
    background: #222;
    border-color: transparent;
}

.skin-default .sidebar-form input[type="text"] {
    color: #666;
}

.skin-default .sidebar-form input[type="text"]:focus,
.skin-default .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
    background: #f6f6f6;
    color: #666;
}

.skin-default .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
    border-left-color: #f6f6f6;
}

.skin-default .sidebar-form .btn {
    color: #999;
}

.skin-default .main-sidebar li,
.skin-default .main-sidebar li a {
    color: #ccc;
}

.skin-default .panel-primary .panel-heading {
    border-bottom: 2px solid #57a;
}

.skin-default .nav-button {
    background-color: #333;
}
