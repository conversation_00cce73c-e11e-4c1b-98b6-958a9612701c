/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

/*!
 * DataTables + Font Awesome integration
 * License: MIT - http://datatables.net/license
 */


/*
 * Sort styling
 */

table.dataTable thead th {
    position: relative;
    background-image: none !important;
    /* Remove the DataTables bootstrap integration styling */
}

table.dataTable thead th.sorting:after,
table.dataTable thead th.sorting_asc:after,
table.dataTable thead th.sorting_desc:after {
    position: absolute;
    top: 12px;
    right: 8px;
    display: block;
    font-family: FontAwesome;
}

table.dataTable thead th.sorting:after {
    content: "\f0dc";
    color: #ddd;
    font-size: 0.8em;
    padding-top: 0.12em;
}

table.dataTable thead th.sorting_asc:after {
    content: "\f0de";
}

table.dataTable thead th.sorting_desc:after {
    content: "\f0dd";
}

div.dataTables_scrollBody table.dataTable thead th.sorting:after,
div.dataTables_scrollBody table.dataTable thead th.sorting_asc:after,
div.dataTables_scrollBody table.dataTable thead th.sorting_desc:after {
    content: "";
}


/* In Bootstrap and Foundation the padding top is a little different from the DataTables stylesheet */

table.table thead th.sorting:after,
table.table thead th.sorting_asc:after,
table.table thead th.sorting_desc:after {
    top: 8px;
}


/*
 * DataTables style pagination controls
 */

div.dataTables_paginate a.paginate_button.first,
div.dataTables_paginate a.paginate_button.previous {
    position: relative;
    padding-left: 24px;
}

div.dataTables_paginate a.paginate_button.next,
div.dataTables_paginate a.paginate_button.last {
    position: relative;
    padding-right: 24px;
}

div.dataTables_paginate a.first:before,
div.dataTables_paginate a.previous:before {
    position: absolute;
    top: 8px;
    left: 10px;
    display: block;
    font-family: FontAwesome;
}

div.dataTables_paginate a.next:after,
div.dataTables_paginate a.last:after {
    position: absolute;
    top: 8px;
    right: 10px;
    display: block;
    font-family: FontAwesome;
}

div.dataTables_paginate a.first:before {
    content: "\f100";
}

div.dataTables_paginate a.previous:before {
    content: "\f104";
}

div.dataTables_paginate a.next:after {
    content: "\f105";
}

div.dataTables_paginate a.last:after {
    content: "\f101";
}


/*
 * Bootstrap and foundation style pagination controls
 */

div.dataTables_paginate li.first > a,
div.dataTables_paginate li.previous > a {
    position: relative;
    padding-left: 24px;
}

div.dataTables_paginate li.next > a,
div.dataTables_paginate li.last > a {
    position: relative;
    padding-right: 24px;
}

div.dataTables_paginate li.first a:before,
div.dataTables_paginate li.previous a:before {
    position: absolute;
    top: 6px;
    left: 10px;
    display: block;
    font-family: FontAwesome;
}

div.dataTables_paginate li.next a:after,
div.dataTables_paginate li.last a:after {
    position: absolute;
    top: 6px;
    right: 10px;
    display: block;
    font-family: FontAwesome;
}

div.dataTables_paginate li.first a:before {
    content: "\f100";
}

div.dataTables_paginate li.previous a:before {
    content: "\f104";
}

div.dataTables_paginate li.next a:after {
    content: "\f105";
}

div.dataTables_paginate li.last a:after {
    content: "\f101";
}


/* In Foundation we don't want the padding like in bootstrap */

div.columns div.dataTables_paginate li.first a:before,
div.columns div.dataTables_paginate li.previous a:before,
div.columns div.dataTables_paginate li.next a:after,
div.columns div.dataTables_paginate li.last a:after {
    top: 0;
}


/* Fix for Scroller plugin */

div.DTS {
    display: block !important;
}

div.DTS tbody td {
    white-space: normal;
}

div.DTS div.DTS_Loading {
    z-index: 1;
}

div.DTS div.dataTables_scrollBody {
    background: none;
}

div.DTS div.dataTables_scrollBody table {
    z-index: 2;
}

div.DTS div.dataTables_paginate,
div.DTS div.dataTables_length {
    display: none;
}


/* Custom */

div.dataTables_wrapper {
    min-height: 150px;
}

.dataTables_filter input {
    display: inline-block;
    border-radius: 0px !important;
    box-shadow: none;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857;
    vertical-align: middle;
    background-color: #FFF;
    background-image: none;
    border: 1px solid #CCC;
    transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
}

.dataTables_info .select-info {
    display: none;
}

.dataTables_processing
{
    z-index: 1050
}
