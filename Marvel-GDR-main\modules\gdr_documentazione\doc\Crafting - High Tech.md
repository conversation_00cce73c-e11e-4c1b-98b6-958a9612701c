# Crafting - High Tech

<p>Gli <strong>High Tech</strong> sono personaggi il cui potere dipende <strong>totalmente dalla tecnologia</strong>: tute, armature, protesi, strumenti avanzati, IA, moduli energetici, ecc. I poteri <strong>scelti </strong>da questi personaggi sono <strong>vincolati alla tecnologia che utilizzano</strong>.<br />
<PERSON><PERSON><PERSON>, <strong>non possono essere dati per scontati</strong>: ogni nuovo potere o aggiornamento dovrà essere <strong>giocato</strong> e <strong>giustificato in game</strong> tramite una serie di giocate di crafting.</p>

<blockquote>
<p> Prima di procedere, è necessario inviare una <strong>breve descrizione alla Gestione</strong>, con:</p>

<ul>
	<li>
	<p>Funzione narrativa o tecnica</p>
	</li>
	<li>
	<p>Potere/i associato/i </p>
	</li>
</ul>
</blockquote>

<p>Si ricorda che i blocchi di potere sono previsti <strong>ai livelli pari</strong> (2, 4, 6...).</p>

<p>Per coerenza narrativa e maggiore coinvolgimento, si consiglia di <strong>dilazionare le giocate di crafting</strong> (progettazione, costruzione, test) <strong>nelle settimane che precedono o seguono il level up</strong>, <strong>collocando idealmente il test o il primo utilizzo del prototipo/prova sul campo proprio in concomitanza</strong> con l’ottenimento effettivo del nuovo potere.<br />
Questo permette di <strong>giustificare narrativamente l'acquisizione</strong> e di rendere il progresso del personaggio più organico e credibile.</p>

<blockquote>
<p>Esempio:<br />
Il personaggio inizia la progettazione a livello 1, costruisce un prototipo in una giocata successiva, e al raggiungimento del livello 2 effettua un test sul campo (che coincide con lo sblocco del potere).</p>
</blockquote>

### Step suggeriti:

<ol>
	<li>
	<p><strong>Progettazione</strong> <em>(livello precedente al power-up)</em><br />
	Ricerca teorica, studi su materiali, elaborazione tecnica, schizzi progettuali.</p>

	<blockquote>
	<p>Esempio: il personaggio lavora a una nuova interfaccia o studia un tipo di lega.</p>
	</blockquote>
	</li>
	<li>
	<p><strong>Assemblaggio o prototipo iniziale oppure Aggiornamento</strong><br />
	Costruzione vera e propria o primi test di laboratorio. Il dispositivo può avere problemi, malfunzionamenti o limiti. Il dispositivo potrebbe già essere in uso del personaggio, che sta cercando invece di aggiornarlo</p>

	<blockquote>
	<p>Questo step è ottimo da giocare <strong>appena prima del level-up</strong>, per introdurre il potere in arrivo.</p>
	</blockquote>
	</li>
	<li>
	<p><strong>Test sul campo</strong> <em>(coincidente con il level-up)</em><br />
	Il nuovo potere viene sperimentato per la prima volta: in simulazione, in una breve missione o in una situazione di emergenza.</p>

	<blockquote>
	<p>Questa è la giocata che <strong>giustifica lo sblocco effettivo del potere</strong>.</p>
	</blockquote>
	</li>
</ol>

<p> </p>