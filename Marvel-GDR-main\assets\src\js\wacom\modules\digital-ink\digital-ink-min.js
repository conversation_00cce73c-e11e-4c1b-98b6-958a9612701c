!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("js-ext"),require("js-md5"),require("long"),require("gl-matrix"),require("poly2tri"),require("clipper-lib"),require("rbush"),require("jszip"),require("protobufjs")):"function"==typeof define&&define.amd?define(["exports","js-ext","js-md5","long","gl-matrix","poly2tri","clipper-lib","rbush","jszip","protobufjs"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).DigitalInk={},e.jsExt,e.md5,e.Long,e.glMatrix,e.poly2tri,e.<PERSON><PERSON><PERSON>,e<PERSON>,e.J<PERSON><PERSON>,e.protobuf)}(this,(function(e,t,n,r,i,o,a,s,u,c){"use strict";void 0===a&&console.warn("digital-ink dependency clipper-lib not found, expected in global scope"),void 0===o&&console.warn("digital-ink dependency poly2tri not found, expected in global scope"),void 0===n&&console.warn("digital-ink dependency js-md5 not found, expected in global scope"),void 0===u&&console.warn("digital-ink dependency jszip not found, expected in global scope"),void 0===c&&console.warn("digital-ink dependency protobufjs not found, expected in global scope"),void 0===t&&console.warn("digital-ink dependency js-ext not found, expected in global scope"),void 0===i&&console.warn("digital-ink dependency gl-matrix not found, expected in global scope"),void 0===s&&console.warn("digital-ink dependency rbush not found, expected in global scope"),void 0===r&&console.warn("digital-ink dependency long not found, expected in global scope");const l="undefined"==typeof document?void 0:document.currentScript.src;function h(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function f(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var d=f(n),p=h(r),y=f(o),v=f(a),m=h(s),g=f(u),b=f(c),E="1.5.0";function P(e){return P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},P(e)}function k(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function w(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function S(e,t,n){return t&&w(e.prototype,t),n&&w(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var I=function(){function e(){k(this,e)}return S(e,[{key:"getInkBuilder",value:function(e){throw new Error("InkController.getInkBuilder(pointerID) is abstract and should be implemented")}},{key:"registerInputProvider",value:function(e,t){throw new Error("InkController.registerInputProvider(pointerID, isPrimary) is abstract and should be implemented")}},{key:"reset",value:function(e){throw new Error("InkController.reset(sensorPoint) is abstract and should be implemented")}},{key:"begin",value:function(e){throw new Error("InkController.begin(sensorPoint) is abstract and should be implemented")}},{key:"move",value:function(e,t){throw new Error("InkController.move(sensorPoint, [prediction]) is abstract and should be implemented")}},{key:"end",value:function(e){throw new Error("InkController.end(sensorPoint) is abstract and should be implemented")}},{key:"abort",value:function(e){throw new Error("InkController.abort(pointerID) is abstract and should be implemented")}},{key:"resize",value:function(e){throw new Error("InkController.resize() is abstract and should be implemented")}}]),e}();function x(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function R(e,t){return R=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},R(e,t)}function T(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&R(e,t)}function A(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function O(e,t){if(t&&("object"===P(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return A(e)}function C(e){return C=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},C(e)}function D(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function M(e,t,n){return M=D()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var i=new(Function.bind.apply(e,r));return n&&R(i,n.prototype),i},M.apply(null,arguments)}function N(e){var t="function"==typeof Map?new Map:void 0;return N=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return M(e,arguments,C(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),R(r,e)},N(e)}function L(e,t,n){if(!t.has(e))throw new TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function B(e,t){return function(e,t){return t.get?t.get.call(e):t.value}(e,L(e,t,"get"))}function _(e,t,n){return function(e,t,n){if(t.set)t.set.call(e,n);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=n}}(e,L(e,t,"set"),n),n}var F=d?d.default||globalThis.md5:{},U={mask:"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx",generate:function(){var e=Date.now();return this.mask.replace(/[xy]/g,(function(t){var n=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"==t?n:3&n|8).toString(16)}))},validate:function(e){return"string"==typeof e&&!!e.match(/^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$/)},format:function(e){var t=[],n=0;return this.mask.split("-").forEach((function(r){t.push(e.substring(n,n+r.length)),n+=r.length})),t.join("-")},fromString:function(e){return this.fromBytes(new Uint8Array(F.arrayBuffer(e)))},toBytes:function(e){var t=[];return e.split("-").map((function(e,n){(n<3?e.match(/.{1,2}/g).reverse():e.match(/.{1,2}/g)).map((function(e){return t.push(parseInt(e,16))}))})),new Uint8Array(t)},fromBytes:function(e){var t=Array.from(e).map((function(e){return e.toString(16)})).map((function(e){return(1==e.length?"0":"")+e}));return t.slice(0,4).reverse().join("")+"-"+t.slice(4,6).reverse().join("")+"-"+t.slice(6,8).reverse().join("")+"-"+t.slice(8,10).join("")+"-"+t.slice(10).join("")},toUint32Array:function(e){var t=new Uint32Array(4),n=this.toBytes(e);return t[0]=new Uint32Array(n.slice(0,4).buffer)[0],t[1]=new Uint32Array(n.slice(4,8).buffer)[0],t[2]=new Uint32Array(n.slice(8,12).buffer)[0],t[3]=new Uint32Array(n.slice(12).buffer)[0],t},fromUint32Array:function(e){return this.fromBytes(new Uint8Array(e.buffer))},toUint64Array:function(e){var t=new BigUint64Array(2),n=this.toBytes(e);return t[0]=new BigUint64Array(n.slice(0,8).buffer)[0],t[1]=new BigUint64Array(n.slice(8).buffer)[0],t},fromUint64Array:function(e){return this.fromBytes(new Uint8Array(e.buffer))},toLongArray:function(e){var t=new Array(2),n=this.toBytes(e);return t[0]=p.default.fromBytes(n.slice(0,8)),t[1]=p.default.fromBytes(n.slice(8)),t},fromLongArray:function(e){var t=e[0].toBytes().concat(e[1].toBytes());return this.fromBytes(t)}};function j(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return G(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return G(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function G(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Y(e,t,n){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}var X=new WeakMap,z=function(){function e(t){var n,r=this;if(k(this,e),Y(this,X,{writable:!0,value:void 0}),_(this,X,t),"function"==typeof this.getMD5Message)n=e.Algorithm.MD5;else if("function"==typeof this.buildURI)n=e.Algorithm.URI;else if(n=e.Algorithm.GUID,t&&!U.validate(t))throw new Error("Identifiable ".concat(t," is not a well formed UUID"));Object.defineProperty(this,"algorithm",{value:n}),Object.defineProperty(this,"id",{get:function(){return B(r,X)||_(r,X,r.resolveID()),B(r,X)},set:function(e){if(B(r,X))throw new Error("id is immutable");_(r,X,e)},enumerable:!0})}return S(e,[{key:"invalidateID",value:function(){if(this.algorithm!=e.Algorithm.MD5)throw new Error("Invalidate id is not applicable for ".concat(this.algorithm," algorithm"));_(this,X,void 0)}},{key:"resolveID",value:function(){if(this.algorithm==e.Algorithm.MD5){var t,n="",r=j(this.getMD5Message());try{for(r.s();!(t=r.n()).done;){var i=t.value;if(Array.isArray(i)){var o,a=j(i);try{for(a.s();!(o=a.n()).done;){n+=o.value,n+="\n"}}catch(e){a.e(e)}finally{a.f()}}else n+=i;n+="\n"}}catch(e){r.e(e)}finally{r.f()}if(!n)throw new Error("Empty MD5 message container found");return F(n)}return this.algorithm==e.Algorithm.URI?this.buildURI():U.generate()}}],[{key:"SEPARATOR",get:function(){return"\n"}},{key:"buildMD5Tokens",value:function(e){var t=[];return Object.keys(e).sort().forEach((function(n){return t.push(n,e[n])})),t}}]),e}();function V(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}Object.defineEnum(z,"Algorithm",["GUID","MD5","URI"]);var H=function(e){T(n,e);var t=V(n);function n(e){var r,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return k(this,n),(r=t.call(this)).type=e,r.props=Object.assign({},i),r}return S(n,[{key:"getMD5Message",value:function(){if(!Object.isFrozen(this))throw new Error("ID generation failed. InkInputProvider do not belongs to any SensorChannelsContext yet");return["InkInputProvider",this.type.name,z.buildMD5Tokens(this.props)]}}]),n}(z);Object.defineEnum(H,"Type",["PEN","TOUCH","MOUSE","CONTROLLER"]);var W=function(){function e(){k(this,e),this.phase=e.Phase.END}return S(e,[{key:"add",value:function(e,t,n){if(!this.move(e))throw new Error("Cannot move from phase ".concat(this.phase.name," to phase ").concat(e.name));return this.debug&&(console.log("-------------------------------------"),console.log(this.constructor.name,e.name)),this.addImpl(t,n)}},{key:"addImpl",value:function(e,t){throw new Error("Abstract method addImpl of DataProcessor should be implemented")}},{key:"move",value:function(t){return(this.phase!=e.Phase.END||t==e.Phase.BEGIN)&&((this.phase!=e.Phase.BEGIN||t==e.Phase.UPDATE||t==e.Phase.END)&&((this.phase!=e.Phase.UPDATE||t==e.Phase.UPDATE||t==e.Phase.END)&&(t==e.Phase.BEGIN&&this.reset(),this.phase=t,!0)))}},{key:"reset",value:function(){this.phase=e.Phase.END}}]),e}();Object.defineEnum(W,"Phase",["BEGIN","UPDATE","END"]);var Z=function(){function e(t,n,r,o){var a=this;if(k(this,e),isNaN(t))throw new Error("Invalid x found: ".concat(t));if(isNaN(n))throw new Error("Invalid y found: ".concat(n));var s=[t,n];isFinite(r)&&(s.push(r),isFinite(o)&&s.push(o)),this.value=s.toFloat32Array(),Object.defineProperty(this,"x",{get:function(){return a.value[0]},set:function(e){a.value[0]=e},enumerable:!0}),Object.defineProperty(this,"y",{get:function(){return a.value[1]},set:function(e){a.value[1]=e},enumerable:!0}),2==s.length?this.vec=i.vec2:3==s.length?(this.vec=i.vec3,Object.defineProperty(this,"z",{get:function(){return a.value[2]},set:function(e){a.value[2]=e},enumerable:!0})):(this.vec=i.vec4,Object.defineProperty(this,"w",{get:function(){return a.value[3]},set:function(e){a.value[3]=e},enumerable:!0}))}return S(e,[{key:"add",value:function(t){t instanceof e||(t=e.fromPoint(t));var n=this.vec.create();return this.vec.add(n,this.value,t.value),e.fromPoint(n)}},{key:"addSelf",value:function(t){return t instanceof e||(t=e.fromPoint(t)),this.vec.add(this.value,this.value,t.value),this}},{key:"subtract",value:function(t){t instanceof e||(t=e.fromPoint(t));var n=this.vec.create();return this.vec.subtract(n,this.value,t.value),e.fromPoint(n)}},{key:"subtractSelf",value:function(t){return t instanceof e||(t=e.fromPoint(t)),this.vec.subtract(this.value,this.value,t.value),this}},{key:"multiply",value:function(t){t instanceof e||(t=e.fromPoint(t));var n=this.vec.create();return this.vec.multiply(n,this.value,t.value),e.fromPoint(n)}},{key:"multiplySelf",value:function(t){return t instanceof e||(t=e.fromPoint(t)),this.vec.multiply(this.value,this.value,t.value),this}},{key:"divide",value:function(t){t instanceof e||(t=e.fromPoint(t));var n=this.vec.create();return this.vec.divide(n,this.value,t.value),e.fromPoint(n)}},{key:"divideSelf",value:function(t){return t instanceof e||(t=e.fromPoint(t)),this.vec.divide(this.value,this.value,t.value),this}},{key:"scale",value:function(t){var n=this.vec.create();return this.vec.scale(n,this.value,t),e.fromPoint(n)}},{key:"scaleSelf",value:function(e){return this.vec.scale(this.value,this.value,e),this}},{key:"abs",value:function(){return new e(Math.abs(this.x),Math.abs(this.y),isFinite(this.z)?Math.abs(this.z):void 0,isFinite(this.w)?Math.abs(this.w):void 0)}},{key:"absSelf",value:function(){return this.x=Math.abs(this.x),this.y=Math.abs(this.y),isFinite(this.z)&&(this.z=Math.abs(this.z)),isFinite(this.w)&&(this.w=Math.abs(this.w)),this}},{key:"transform",value:function(t){if(!t)return this;var n=this.vec.create();return this.vec.transformMat4(n,this.value,t.toFloat32Array()),e.fromPoint(n)}},{key:"transformSelf",value:function(e){return this.vec.transformMat4(this.value,this.value,e.toFloat32Array()),this}},{key:"toFloat32Array",value:function(){return this.value}},{key:"toJSON",value:function(){var e={x:this.x,y:this.y};return isFinite(this.z)&&(e.z=this.z,isFinite(this.w)&&(e.w=this.w)),e}},{key:"toString",value:function(){return"point(".concat(this.value.join(", "),")")}},{key:"clone",value:function(){return e.fromPoint(this)}}],[{key:"fromPoint",value:function(t){return Array.isArray(t)||ArrayBuffer.isTypedArray(t)?new e(t[0],t[1],t[2],t[3]):new e(t.x,t.y,t.z,t.w)}}]),e}();function q(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return K(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return K(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function K(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var J=function(){function e(t,n,r,i){k(this,e);var o=t,a=n,s=t+r,u=n+i;Object.defineProperties(this,{left:{value:o,enumerable:!0},x:{value:t,enumerable:!0},bottom:{value:a,enumerable:!0},y:{value:n,enumerable:!0},right:{value:s,enumerable:!0},top:{value:u,enumerable:!0},width:{value:r,enumerable:!0},height:{value:i,enumerable:!0}})}return S(e,[{key:"union",value:function(t){if(t&&!(t instanceof e))throw new TypeError("rect must be instance of RectGL");return t?e.ofEdges(Math.min(this.left,t.left),Math.min(this.bottom,t.bottom),Math.max(this.right,t.right),Math.max(this.top,t.top)):this}},{key:"intersect",value:function(t){if(t&&!(t instanceof e))throw new TypeError("rect must be instance of RectGL");if(!t)return null;var n=e.ofEdges(Math.max(this.left,t.left),Math.max(this.bottom,t.bottom),Math.min(this.right,t.right),Math.min(this.top,t.top));return n.width>0&&n.height>0?n:null}},{key:"ceil",value:function(){return e.ofEdges(Math.floor(this.left),Math.floor(this.bottom),Math.ceil(this.right),Math.ceil(this.top))}},{key:"floor",value:function(){return e.ofEdges(Math.ceil(this.left),Math.ceil(this.bottom),Math.floor(this.right),Math.floor(this.top))}},{key:"transform",value:function(t){if(!t)return this;var n=Z.fromPoint({x:this.left,y:this.bottom}).transform(t),r=Z.fromPoint({x:this.right,y:this.bottom}).transform(t),i=Z.fromPoint({x:this.left,y:this.top}).transform(t),o=Z.fromPoint({x:this.right,y:this.top}).transform(t),a=Math.min(i.x,o.x,n.x,r.x),s=Math.min(i.y,o.y,n.y,r.y),u=Math.max(i.x,o.x,n.x,r.x),c=Math.max(i.y,o.y,n.y,r.y);return e.ofEdges(a,s,u,c)}},{key:"toQuad",value:function(e){var t;if(e){var n=Z.fromPoint({x:this.left,y:this.bottom}).transform(e),r=Z.fromPoint({x:this.right,y:this.bottom}).transform(e),o=Z.fromPoint({x:this.left,y:this.top}).transform(e),a=Z.fromPoint({x:this.right,y:this.top}).transform(e);t=i.quat2.fromValues(n.x,n.y,r.x,r.y,o.x,o.y,a.x,a.y)}else t=i.quat2.fromValues(this.left,this.bottom,this.right,this.bottom,this.left,this.top,this.right,this.top);return t}},{key:"toString",value:function(){return"gl-rect(".concat(this.x,", ").concat(this.y,", ").concat(this.width,", ").concat(this.height,")")}}],[{key:"ofEdges",value:function(t,n,r,i){return new e(t,n,r-t,i-n)}},{key:"calculateBrushGLSegmentBounds",value:function(t){var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2?arguments[2]:void 0,o=.5*t.size,a=Math.abs(r*o);if(i){var s=new Z(t.x,t.y,t.x,t.z);n=s.transform(i)}else n=t;var u,c=n.x,l=n.y,h=t.scaleX*o,f=t.scaleY*o,d=t.offsetX,p=-t.offsetY,y=Math.cos(t.rotation),v=Math.sin(t.rotation),m=Number.MAX_SAFE_INTEGER,g=Number.MIN_SAFE_INTEGER,b=Number.MAX_SAFE_INTEGER,E=Number.MIN_SAFE_INTEGER,P=q(e.SQURE);try{for(P.s();!(u=P.n()).done;){var k=u.value,w=k.x*h+d,S=k.y*f+p,I=y*w+v*S+c,x=-v*w+y*S+l,R=I-a;m=Math.min(m,R),g=Math.max(g,R),R=I+a,m=Math.min(m,R),g=Math.max(g,R);var T=x-a;b=Math.min(b,T),E=Math.max(E,T),T=x+a,b=Math.min(b,T),E=Math.max(E,T)}}catch(e){P.e(e)}finally{P.f()}return e.ofEdges(m,b,g,E)}}]),e}();Object.defineProperty(J,"SQURE",{value:Object.freeze([Object.freeze({x:-1,y:-1}),Object.freeze({x:1,y:-1}),Object.freeze({x:-1,y:1}),Object.freeze({x:1,y:1})]),enumerable:!0});var $=function(){function e(t,n,r,i){k(this,e);var o,a,s,u=t,c=n,l=t+r,h=n+i;Object.defineProperties(this,{left:{value:u,enumerable:!0},top:{value:c,enumerable:!0},right:{value:l,enumerable:!0},bottom:{value:h,enumerable:!0},x:{value:t,enumerable:!0},y:{value:n,enumerable:!0},width:{value:r,enumerable:!0},height:{value:i,enumerable:!0},size:{get:function(){return o||(o={width:r,height:i}),o},enumerable:!0},area:{get:function(){return a||(a=r*i),a},enumerable:!0},center:{get:function(){return s||(s={x:(u+l)/2,y:(c+h)/2}),s},enumerable:!0}})}return S(e,[{key:"union",value:function(t){return t?e.ofEdges(Math.min(this.left,t.left),Math.min(this.top,t.top),Math.max(this.right,t.right),Math.max(this.bottom,t.bottom)):this}},{key:"intersect",value:function(t){if(!t)return null;var n=e.ofEdges(Math.max(this.left,t.left),Math.max(this.top,t.top),Math.min(this.right,t.right),Math.min(this.bottom,t.bottom));return n.width>0&&n.height>0?n:null}},{key:"intersects",value:function(e){return this.left<=e.right&&this.right>=e.left&&this.top<=e.bottom&&this.bottom>=e.top}},{key:"ceil",value:function(t){var n=Math.floor(this.left),r=Math.floor(this.top),i=Math.ceil(this.right),o=Math.ceil(this.bottom);if(t){var a=i-n,s=o-r;i=n+(a+=a%2),o=r+(s+=s%2)}return e.ofEdges(n,r,i,o)}},{key:"floor",value:function(t){var n=Math.ceil(this.left),r=Math.ceil(this.top),i=Math.floor(this.right),o=Math.floor(this.bottom);if(t){var a=i-n,s=o-r;i=n+(a-=a%2),o=r+(s-=s%2)}return e.ofEdges(n,r,i,o)}},{key:"contains",value:function(e){return this.left<=e.x&&this.right>=e.x&&this.top<=e.y&&this.bottom>=e.y}},{key:"includes",value:function(e){return this.left<=e.left&&this.right>=e.right&&this.top<=e.top&&this.bottom>=e.bottom}},{key:"transform",value:function(t){if(!t)return this;var n=Z.fromPoint({x:this.left,y:this.top}).transform(t),r=Z.fromPoint({x:this.right,y:this.top}).transform(t),i=Z.fromPoint({x:this.left,y:this.bottom}).transform(t),o=Z.fromPoint({x:this.right,y:this.bottom}).transform(t),a=Math.min(n.x,r.x,i.x,o.x),s=Math.min(n.y,r.y,i.y,o.y),u=Math.max(n.x,r.x,i.x,o.x),c=Math.max(n.y,r.y,i.y,o.y);return e.ofEdges(a,s,u,c)}},{key:"toPath",value:function(e){throw new Error("Rect.toPath is deprecated. Path.fromRect(rect, [pointProps]) instead")}},{key:"toGLRect",value:function(){return new J(this.x,this.y,this.width,this.height)}},{key:"toString",value:function(){return"rect(".concat(this.x,", ").concat(this.y,", ").concat(this.width,", ").concat(this.height,")")}},{key:"toJSON",value:function(){return{x:this.left,y:this.top,width:this.width,height:this.height}}}],[{key:"fromGLRect",value:function(t){if(!t)return null;if(!(t instanceof J))throw new TypeError("rect must be instance of RectGL");return new e(t.left,t.bottom,t.width,t.height)}},{key:"isRect",value:function(e){return e&&isFinite(e.left)&&isFinite(e.top)&&isFinite(e.width)&&isFinite(e.height)}},{key:"fromString",value:function(t){return t=t.substring(t.indexOf("(")+1,t.indexOf(")")).split(/,\s*/g),new e(parseFloat(t[0]),parseFloat(t[1]),parseFloat(t[2]),parseFloat(t[3]))}},{key:"fromRect",value:function(t){return"string"==typeof t?e.fromString(t):new e(t.x,t.y,t.width,t.height)}},{key:"ofPolygon",value:function(t){if(t.shape&&(t=t.shape),0==t.length)return null;for(var n=Number.MAX_SAFE_INTEGER,r=Number.MAX_SAFE_INTEGER,i=Number.MIN_SAFE_INTEGER,o=Number.MIN_SAFE_INTEGER,a=0;a<t.length;a++){var s=t.getPointX(a),u=t.getPointY(a);n=Math.min(n,s),r=Math.min(r,u),i=Math.max(i,s),o=Math.max(o,u)}return e.ofEdges(n,r,i,o)}},{key:"ofSpline",value:function(t){for(var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=0;i<t.length;i++)n=J.calculateBrushGLSegmentBounds(t.getPointRef(i),r).union(n);return e.fromGLRect(n)}},{key:"ofEdges",value:function(t,n,r,i){var o=Math.min(t,r),a=Math.min(n,i);return new e(o,a,Math.max(t,r)-o,Math.max(n,i)-a)}},{key:"union",value:function(e,t){return e?t?e.union(t):e:t}},{key:"intersect",value:function(e,t){return e&&t?e.intersect(t):null}}]),e}(),Q={m11:0,m12:1,m13:2,m14:3,m21:4,m22:5,m23:6,m24:7,m31:8,m32:9,m33:10,m34:11,m41:12,m42:13,m43:14,m44:15},ee=Q.m11,te=Q.m12,ne=Q.m21,re=Q.m22,ie=Q.m41,oe=Q.m42,ae=function(){function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i.mat4.create(),r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.MultiplicationType.PRE;k(this,e),Object.defineProperty(this,"value",{value:n,enumerable:!0}),Object.defineProperty(this,"multiplicationType",{value:r,enumerable:!0});var o=function(e,t){var n=Q[e];this.value[n]=t};Object.defineProperty(this,"a",{get:function(){return t.value[ee]},set:o.bind(this,"m11"),enumerable:!0}),Object.defineProperty(this,"b",{get:function(){return t.value[te]},set:o.bind(this,"m12"),enumerable:!0}),Object.defineProperty(this,"c",{get:function(){return t.value[ne]},set:o.bind(this,"m21"),enumerable:!0}),Object.defineProperty(this,"d",{get:function(){return t.value[re]},set:o.bind(this,"m22"),enumerable:!0}),Object.defineProperty(this,"e",{get:function(){return t.value[ie]},set:o.bind(this,"m41"),enumerable:!0}),Object.defineProperty(this,"f",{get:function(){return t.value[oe]},set:o.bind(this,"m42"),enumerable:!0}),Object.defineProperty(this,"tx",{get:function(){return t.value[ie]},set:o.bind(this,"m41"),enumerable:!0}),Object.defineProperty(this,"ty",{get:function(){return t.value[oe]},set:o.bind(this,"m42"),enumerable:!0}),Object.defineProperty(this,"m11",{get:function(){return t.value[0]},set:o.bind(this,"m11"),enumerable:!0}),Object.defineProperty(this,"m12",{get:function(){return t.value[1]},set:o.bind(this,"m12"),enumerable:!0}),Object.defineProperty(this,"m13",{get:function(){return t.value[2]},set:o.bind(this,"m13"),enumerable:!0}),Object.defineProperty(this,"m14",{get:function(){return t.value[3]},set:o.bind(this,"m14"),enumerable:!0}),Object.defineProperty(this,"m21",{get:function(){return t.value[4]},set:o.bind(this,"m21"),enumerable:!0}),Object.defineProperty(this,"m22",{get:function(){return t.value[5]},set:o.bind(this,"m22"),enumerable:!0}),Object.defineProperty(this,"m23",{get:function(){return t.value[6]},set:o.bind(this,"m23"),enumerable:!0}),Object.defineProperty(this,"m24",{get:function(){return t.value[7]},set:o.bind(this,"m24"),enumerable:!0}),Object.defineProperty(this,"m31",{get:function(){return t.value[8]},set:o.bind(this,"m31"),enumerable:!0}),Object.defineProperty(this,"m32",{get:function(){return t.value[9]},set:o.bind(this,"m32"),enumerable:!0}),Object.defineProperty(this,"m33",{get:function(){return t.value[10]},set:o.bind(this,"m33"),enumerable:!0}),Object.defineProperty(this,"m34",{get:function(){return t.value[11]},set:o.bind(this,"m34"),enumerable:!0}),Object.defineProperty(this,"m41",{get:function(){return t.value[12]},set:o.bind(this,"m41"),enumerable:!0}),Object.defineProperty(this,"m42",{get:function(){return t.value[13]},set:o.bind(this,"m42"),enumerable:!0}),Object.defineProperty(this,"m43",{get:function(){return t.value[14]},set:o.bind(this,"m43"),enumerable:!0}),Object.defineProperty(this,"m44",{get:function(){return t.value[15]},set:o.bind(this,"m44"),enumerable:!0}),Object.defineProperty(this,"isIdentity",{get:function(){return 1==t.a&&0==t.b&&0==t.c&&1==t.d&&0==t.tx&&0==t.ty},enumerable:!0}),Object.defineProperty(this,"is2D",{get:function(){return!(0!=t.m31||0!=t.m32||0!=t.m13||0!=t.m23||1!=t.m33||0!=t.m43||0!=t.m14||0!=t.m24||0!=t.m34||1!=t.m44)},enumerable:!0}),Object.defineProperty(this,"translateX",{get:function(){return t.tx}}),Object.defineProperty(this,"translateY",{get:function(){return t.ty}}),Object.defineProperty(this,"skewX",{get:function(){return Math.tan(t.c)}}),Object.defineProperty(this,"skewY",{get:function(){return Math.tan(t.b)}}),Object.defineProperty(this,"scaleX",{get:function(){return Math.sqrt(t.a*t.a+t.c*t.c)}}),Object.defineProperty(this,"scaleY",{get:function(){return Math.sqrt(t.d*t.d+t.b*t.b)}}),Object.defineProperty(this,"rotation",{get:function(){return Math.atan2(t.b,t.a)}})}return S(e,[{key:"clone",value:function(){return new e(this.value.clone(),this.multiplicationType)}},{key:"translate",value:function(t){return this.multiply(e.fromTranslate(t))}},{key:"translateSelf",value:function(t){this.multiplySelf(e.fromTranslate(t))}},{key:"rotate",value:function(t,n){return this.multiply(e.fromRotate(t,n))}},{key:"rotateSelf",value:function(t,n){this.multiplySelf(e.fromRotate(t,n))}},{key:"scale",value:function(t,n){return this.multiply(e.fromScale(t,n))}},{key:"scaleSelf",value:function(t,n){this.multiplySelf(e.fromScale(t,n))}},{key:"multiply",value:function(t){return this.multiplicationType==e.MultiplicationType.PRE?this.preMultiply(t):this.postMultiply(t)}},{key:"preMultiply",value:function(t){var n=i.mat4.create();return i.mat4.multiply(n,t.toFloat32Array(),this.value),new e(n,this.multiplicationType)}},{key:"postMultiply",value:function(t){var n=i.mat4.create();return i.mat4.multiply(n,this.value,t.toFloat32Array()),new e(n,this.multiplicationType)}},{key:"multiplySelf",value:function(t){this.multiplicationType==e.MultiplicationType.PRE?this.preMultiplySelf(t):this.postMultiplySelf(t)}},{key:"preMultiplySelf",value:function(e){i.mat4.multiply(this.value,e.toFloat32Array(),this.value)}},{key:"postMultiplySelf",value:function(e){i.mat4.multiply(this.value,this.value,e.toFloat32Array())}},{key:"invert",value:function(){var t=i.mat4.create();return i.mat4.invert(t,this.value),new e(t,this.multiplicationType)}},{key:"invertSelf",value:function(){i.mat4.invert(this.value,this.value)}},{key:"decompose",value:function(){return{translate:{x:this.tx,y:this.ty},rotate:{angle:Math.atan2(this.b,this.a)},skew:{angleX:Math.tan(this.c),angleY:Math.tan(this.b)},scale:{x:Math.sqrt(this.a*this.a+this.c*this.c),y:Math.sqrt(this.d*this.d+this.b*this.b)},matrix:this.toJSON()}}},{key:"transformPoint",value:function(e){return Z.fromPoint(e).transform(this)}},{key:"toFloat32Array",value:function(){return this.value}},{key:"toJSON",value:function(){return{a:this.a,b:this.b,c:this.c,d:this.d,tx:this.tx,ty:this.ty}}},{key:"toString",value:function(e){if(e){var t=function(e){return((e<0?"":" ")+e.toPrecision(6)).substring(0,8)};return" Matrix 4x4\n"+"-".repeat(39)+"\n".concat(t(this.m11),", ").concat(t(this.m21),", ").concat(t(this.m31),", ").concat(t(this.m41))+"\n".concat(t(this.m12),", ").concat(t(this.m22),", ").concat(t(this.m32),", ").concat(t(this.m42))+"\n".concat(t(this.m13),", ").concat(t(this.m23),", ").concat(t(this.m33),", ").concat(t(this.m43))+"\n".concat(t(this.m14),", ").concat(t(this.m24),", ").concat(t(this.m34),", ").concat(t(this.m44))}return this.is2D?"matrix(".concat(this.a,", ").concat(this.b,", ").concat(this.c,", ").concat(this.d,", ").concat(this.tx,", ").concat(this.ty,")"):"matrix3d(".concat(this.m11,", ").concat(this.m12,", ").concat(this.m13,", ").concat(this.m14,", ").concat(this.m21,", ").concat(this.m22,", ").concat(this.m23,", ").concat(this.m24,", ").concat(this.m31,", ").concat(this.m32,", ").concat(this.m33,", ").concat(this.m34,", ").concat(this.m41,", ").concat(this.m42,", ").concat(this.m43,", ").concat(this.m44,")")}}],[{key:"fromString",value:function(t,n){var r=i.mat4.create();if("none"!=t){var o=t.substring(0,t.indexOf("("));t=t.substring(t.indexOf("(")+1,t.indexOf(")")).split(/,\s*/g),"matrix3d"==o?(r[0]=parseFloat(t[0]),r[1]=parseFloat(t[1]),r[2]=parseFloat(t[2]),r[3]=parseFloat(t[3]),r[4]=parseFloat(t[4]),r[5]=parseFloat(t[5]),r[6]=parseFloat(t[6]),r[7]=parseFloat(t[7]),r[8]=parseFloat(t[8]),r[9]=parseFloat(t[9]),r[10]=parseFloat(t[10]),r[11]=parseFloat(t[11]),r[12]=parseFloat(t[12]),r[13]=parseFloat(t[13]),r[14]=parseFloat(t[14]),r[15]=parseFloat(t[15])):(r[ee]=parseFloat(t[0]),r[te]=parseFloat(t[1]),r[ne]=parseFloat(t[2]),r[re]=parseFloat(t[3]),r[ie]=parseFloat(t[4]),r[oe]=parseFloat(t[5]))}return new e(r,n)}},{key:"fromMatrix",value:function(t,n){if(!t)throw new Error("data not found, Matrix instance creation failed");if("function"==typeof t)throw new Error("data type function is not allowed");if(t instanceof e)return t;if(Array.isArray(t)&&(t=new Float32Array(t)),t instanceof Float32Array)return new e(t,n);if("string"==typeof t)return e.fromString(t,n);var r=i.mat4.create(),o=Object.assign({},t);return isFinite(t.a)&&(o.m11=t.a),isFinite(t.b)&&(o.m12=t.b),isFinite(t.c)&&(o.m21=t.c),isFinite(t.d)&&(o.m22=t.d),isFinite(t.tx)?o.m41=t.tx:isFinite(t.e)?o.m41=t.e:isFinite(t.dx)&&(o.m41=t.dx),isFinite(t.ty)?o.m42=t.ty:isFinite(t.f)?o.m42=t.f:isFinite(t.dy)&&(o.m42=t.dy),isFinite(o.m11)&&(r[0]=o.m11),isFinite(o.m12)&&(r[1]=o.m12),isFinite(o.m13)&&(r[2]=o.m13),isFinite(o.m14)&&(r[3]=o.m14),isFinite(o.m21)&&(r[4]=o.m21),isFinite(o.m22)&&(r[5]=o.m22),isFinite(o.m23)&&(r[6]=o.m23),isFinite(o.m24)&&(r[7]=o.m24),isFinite(o.m31)&&(r[8]=o.m31),isFinite(o.m32)&&(r[9]=o.m32),isFinite(o.m33)&&(r[10]=o.m33),isFinite(o.m34)&&(r[11]=o.m34),isFinite(o.m41)&&(r[12]=o.m41),isFinite(o.m42)&&(r[13]=o.m42),isFinite(o.m43)&&(r[14]=o.m43),isFinite(o.m44)&&(r[15]=o.m44),new e(r,n||t.multiplicationType)}},{key:"fromTranslate",value:function(t){var n=isFinite(t)?{tx:t,ty:t}:{tx:t.x,ty:t.y};return e.fromMatrix(n)}},{key:"fromRotate",value:function(t,n){var r=Math.sin(t),i=Math.cos(t),o={a:i,b:r,c:-r,d:i};return n&&(o.tx=n.x-n.x*i+n.y*r,o.ty=n.y-n.x*r-n.y*i),e.fromMatrix(o)}},{key:"fromScale",value:function(t,n){isFinite(t)&&(t={x:t,y:t});var r={a:t.x,d:t.y};return n&&(r.tx=n.x-n.x*t.x,r.ty=n.y-n.y*t.y),e.fromMatrix(r)}},{key:"fromPoints",value:function(t,n){if(!Array.isArray(t)||!Array.isArray(n))throw new Error("Expected input type Array requirement not satisfied");if(3!=t.length||3!=n.length)throw new Error("Expected input size 3 requirement not satisfied");var r=e.fromMatrix({m11:t[0].x,m21:t[1].x,m31:t[2].x,m12:t[0].y,m22:t[1].y,m32:t[2].y,m13:1,m23:1,m33:1}),i=e.fromMatrix({m11:n[0].x,m21:n[1].x,m31:n[2].x,m12:n[0].y,m22:n[1].y,m32:n[2].y,m13:1,m23:1,m33:1}),o=r.invert().preMultiply(i);return e.fromMatrix({a:o.m11,b:o.m12,c:o.m21,d:o.m22,tx:o.m31,ty:o.m32})}},{key:"multiply",value:function(t,n){var r=i.mat4.create();return i.mat4.multiply(r,t.value,n.value),new e(r)}}]),e}();function se(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function ue(e){return function(e){if(Array.isArray(e))return se(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return se(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?se(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}ae.MultiplicationType=Object.freeze({PRE:"PRE",POST:"POST"});var ce={longToByteArray:function(e){for(var t=[0,0,0,0,0,0,0,0],n=0;n<t.length;n++){var r=255&e;t[n]=r,e=(e-r)/256}return t},byteArrayToLong:function(e){for(var t=0,n=e.length-1;n>=0;n--)t=256*t+e[n];return t},crc32:function(){for(var e=new Uint32Array(256),t=256;t--;){for(var n=t,r=8;r--;)n=1&n?3988292384^n>>>1:n>>>1;e[t]=n}return function(t){for(var n=-1,r=0,i=t.length;r<i;r++)n=n>>>8^e[255&n^t[r]];return(-1^n)>>>0}}(),encodeBitMask:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];if(0==e.length)return 0;for(var t="",n=Math.max.apply(Math,ue(e)),r=1;r<=n;r++)t+=e.includes(r)?"1":"0";return parseInt(t.split("").reverse().join(""),2)},decodeBitMask:function(e){for(var t=[],n=e.toString(2).split("").reverse(),r=0;r<n.length;r++)1==n[r]&&t.push(r+1);return t},mapTo:function(e,t,n){return n.min+(ce.clamp(e,t)-t.min)/(t.max-t.min)*(n.max-n.min)},clamp:function(e,t){return Math.min(Math.max(e,t.min),t.max)},debounce:function(e,t){var n=null;return function(){var r=this,i=arguments;clearTimeout(n),n=setTimeout((function(){e.apply(r,i)}),t)}},comparator:function(){var e=Array.prototype.slice.call(arguments),t=function(e,t,n){var r="asc"===n?1:-1;return e>t?1*r:e<t?-1*r:0},n=function(e,t,n){return t.replace("[",".").replace("]","").split(".").forEach((function(t){return e=e[t]})),n?e.toLowerCase():e};return function(r,i){return e.map((function(e){return t(n(r,e.sortBy,e.ignoreCase),n(i,e.sortBy,e.ignoreCase),e.sortOrder)})).reduceRight((function(e,t){return t||e}))}},isValidURL:function(e){if("string"!=typeof e)return!1;try{return new URL(e),!0}catch(e){return!1}},getPropName:function(e,t){var n=e.split("_"),r=n.first.toLowerCase();t&&(r=r.substring(0,1).toUpperCase()+r.substring(1));for(var i=1;i<n.length;i++)r+=n[i].substring(0,1),r+=n[i].substring(1).toLowerCase();return r},getEnumValueName:function(e){for(var t="",n=0;n<e.length;n++)n>0&&e[n]!=e[n].toLowerCase()&&(t+="_"),t+=e[n];return t.toUpperCase()}},le=function(){function e(t){var n=this;k(this,e),this.header=[],this.table=[],t.forEach((function(e){"string"==typeof e&&(e={name:e,title:e}),e.size=e.title.length,n.header.push(e)})),Object.defineProperty(this,"length",{get:function(){return n.table.length},enumerable:!0})}return S(e,[{key:"insert",value:function(e){this.table.push(e),this.header.forEach((function(t){var n=e[t.name];null!=n&&(t.size=Math.max(t.size,n.toString().length))}))}},{key:"build",value:function(){var e=this,t=[],n=this.header.length+1,r=[];return this.header.forEach((function(t){n+=t.size+2;var i=t.size-t.title.length,o=Math.floor(i/2),a=Math.ceil(i/2);r.push(e.getRepetedValue(o)+t.title+e.getRepetedValue(a))})),this.table.forEach((function(n){var r=[];e.header.forEach((function(t){var i=null==n[t.name]?"":n[t.name],o=t.size-i.toString().length;r.push(i+e.getRepetedValue(o))})),t.push(r)})),{delimiterLength:n,headerRow:r,content:t}}},{key:"getFormattedRow",value:function(e){var t="| ";return e.forEach((function(e){t+=e,t+=" | "})),t.trim()}},{key:"getRepetedValue",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ";return new Array(e+1).join(t)}},{key:"clear",value:function(){this.table.clear()}},{key:"toString",value:function(){var e=this,t="",n=this.build(),r=n.delimiterLength,i=n.headerRow,o=n.content;return t+=this.getRepetedValue(r,"="),t+="\n",t+=this.getFormattedRow(i),t+="\n",t+=this.getRepetedValue(r,"="),t+="\n",o.forEach((function(n){t+=e.getFormattedRow(n),t+="\n"})),t}}]),e}();function he(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}if("object"===("undefined"==typeof window?"undefined":P(window))&&"undefined"==typeof TouchEvent){var fe=function(e){T(n,e);var t=he(n);function n(){return k(this,n),t.apply(this,arguments)}return S(n)}(N(Event));window.TouchEvent=fe}var de={pointers:{mouse:["down","move","up"],touch:["start","move","end"]},inactive:[],open:function(e){if(!(e instanceof I))throw new Error("Argument doesn't implement InkController interface");this.inkController=e,this.canvas=e.canvas.surface,this.mounted||(this.mounted=!0,this.begin=this.begin.bind(this),this.move=this.move.bind(this),this.end=this.end.bind(this),this.abort=this.abort.bind(this)),addEventListener("touchcancel",this.abort,{passive:!0}),this.attachResize(),this.start()},reset:function(e){for(var t in this.pointers){var n=t==de.PointerType.TOUCH?{passive:!0}:void 0,r=t+this.pointers[t][0];this.canvas.removeEventListener(r,this.begin),e.canvas.surface.addEventListener(r,this.begin,n)}this.dettachResize(),this.inkController=e,this.canvas=e.canvas.surface,this.attachResize()},close:function(){this.stop(),this.dettachResize(),removeEventListener("touchcancel",this.abort),delete this.canvas,delete this.inkController},start:function(e){var t=e?x({},e,this.pointers[e]):this.pointers;for(e in e&&this.inactive.remove(e),this.inactive.forEach((function(e){return delete t[e]})),t)for(var n=e==de.PointerType.TOUCH?{passive:!0}:void 0,r=0;r<t[e].length;r++){var i=e+t[e][r];0==r?this.canvas.addEventListener(i,this.begin,n):addEventListener(i,2==r?this.end:this.move,n)}},stop:function(e){var t=e?x({},e,this.pointers[e]):this.pointers;for(e in e&&!this.inactive.includes(e)&&this.inactive.push(e),this.inactive.forEach((function(e){return delete t[e]})),e&&e!=de.PointerType.MOUSE||clearTimeout(this.timeoutID),t)for(var n=0;n<t[e].length;n++){var r=e+t[e][n];0==n?this.canvas.removeEventListener(r,this.begin):removeEventListener(r,2==n?this.end:this.move)}},isInputAllowed:function(e){var t=this.provider;if(e.type.endsWith("down")||e.type.endsWith("start")){if(t||(t=this.getInputProvider(e)),this.inactive.includes(t))return!1;if(t==de.PointerType.MOUSE&&0!=e.button)return!1;for(var n in this.suppressKeys)if(e[n])return!1}return t==this.getInputProvider(e)},isInputExpected:function(e){var t=!1,n=e.changedTouches?Array.from(e.changedTouches).map((function(e){return e.identifier})):[],r=this.inkController.getInkBuilder(n);return r&&(t=e.type.endsWith("down")||e.type.endsWith("start")?!r.phase:r.phase&&r.phase!=W.Phase.END),t},getSensorPoint:function(e){var t=void 0,n=e.changedTouches?Array.from(e.changedTouches).map((function(e){return e.identifier})):[],r=this.inkController.getInkBuilder(n);return r&&(t=this.createSensorPoint(e,r.pointerID)),t},createSensorPoint:function(e,t){var n=this.getOffset(e),r={x:n.x,y:n.y,z:void 0,timestamp:Math.floor(e.timeStamp),pressure:void 0,radiusX:void 0,radiusY:void 0,rotation:void 0},i={id:void 0,type:void 0,button:void 0,buttons:void 0};if(Object.defineProperty(r,"phase",{value:W.Phase[e.type.replace(/pointer|mouse|touch/g,"").replace(/down|start/,"BEGIN").replace(/move/,"UPDATE").replace(/up|end/,"END")],enumerable:!0}),Object.defineProperty(r,"pointer",{value:i,enumerable:!0}),Object.defineProperty(r.pointer,"provider",{get:function(){return H.Type[this.type.toUpperCase()]},enumerable:!0}),e instanceof MouseEvent)i.type=de.PointerType.MOUSE,i.button=e.button,i.buttons=e.buttons;else{if(!(e instanceof TouchEvent))throw new Error("Unexpected event detected: ".concat(e.constructor.name,". Expected event should be instance of MouseEvent or TouchEvent."));if(isNaN(t))throw new Error("pointerID is required for touch event");if(!(e=Array.from(e.changedTouches).filter((function(e){return e.identifier==t})).first))return null;isNaN(r.x)&&(r.x=e.offsetX||e.clientX),isNaN(r.y)&&(r.y=e.offsetY||e.clientY),i.id=e.identifier,i.type=de.PointerType.TOUCH,r.pressure=e.force,r.radiusX=e.radiusX,r.radiusY=e.radiusY,r.rotation=Math.toRadians(e.rotationAngle)}return r},getOffset:function(e){if(e.changedTouches){var t=Array.from(e.changedTouches).map((function(e){return e.identifier})),n=this.inkController.getInkBuilder(t);e=n.pointerID!=e.changedTouches.item(0).identifier?Array.from(e.changedTouches).filter((function(e){return e.identifier==n.pointerID})).first:e.changedTouches.item(0)}var r=this.canvas.offsetParent;if(!r)return{x:0,y:0};"flex"==r.getStyle("display")&&(r=this.canvas);var i=this.inkController.transform,o=this.canvas.getStyle("transform");"none"==o?o=r.getStyle("transform"):r=this.canvas,o="none"==o?null:ae.fromMatrix(o);var a=Z.fromPoint({x:e.clientX,y:e.clientY}),s=r.getBoundingClientRect();if(o){var u=o.invert();a=a.transform(u),s=$.fromRect(s).transform(u)}var c={x:a.x-s.x,y:a.y-s.y};return i&&(c=Z.fromPoint(c).transform(i.invert())),c},getInputProvider:function(e){return e.type.replace(/down|start|move|up|end/g,"")},begin:function(e){if(this.logSampleInput(e),!(e instanceof MouseEvent&&0!=e.button)&&this.isInputAllowed(e)){if(e instanceof TouchEvent){var t=Array.from(e.changedTouches).map((function(e){return e.identifier}));this.inkController.registerInputProvider(t)}if(this.isInputExpected(e)){this.provider||(this.provider=this.getInputProvider(e));var n=this.getSensorPoint(e);n&&this.inkController.begin(n,e)}}},move:function(e){if(this.isInputAllowed(e)&&this.isInputExpected(e)){this.logSampleInput(e);var t=this.getSensorPoint(e);t&&this.inkController.move(t,void 0,e)}},end:function(e){var t=this;if(this.isInputAllowed(e)&&this.isInputExpected(e)){this.logSampleInput(e);var n=this.getSensorPoint(e);n&&(this.inkController.end(n,e),e instanceof MouseEvent||this.inactive.includes(de.PointerType.MOUSE)||(this.stop(de.PointerType.MOUSE),this.timeoutID=setTimeout((function(){return t.start(de.PointerType.MOUSE)}),500))),e.touches&&0!=e.touches.length||delete this.provider}},abort:function(e){if(delete this.provider,this.inkController.abort)if(e instanceof TouchEvent){var t=Array.from(e.changedTouches).map((function(e){return e.identifier}));this.inkController.abort(t)}else this.inkController.abort()},logSampleInput:function(e){if(this.debug){var t=e.type.endsWith("move"),n="mouseup"==e.type||"touchend"==e.type;t&&!this.debug.move||n&&!this.debug.end||(n&&this.table.length>0&&(console.log(this.table.toString()),this.table.clear()),this.table||(this.table=new le(["type","id","pointerType","x","y","pressure","radius","rotation","timestamp"])),this.table.insert({type:e.type,id:e.changedTouches?e.changedTouches[0].identifier:void 0,pointerType:this.getInputProvider(e),x:e.changedTouches?e.changedTouches[0].clientX.toFixed(2):e.clientX,y:e.changedTouches?e.changedTouches[0].clientY.toFixed(2):e.clientY,pressure:e.changedTouches?e.changedTouches[0].force.toFixed(2):void 0,radius:e.changedTouches?e.changedTouches[0].radiusX.toFixed(2)+" / "+e.changedTouches[0].radiusY.toFixed(2):void 0,rotation:e.changedTouches?e.changedTouches[0].rotationAngle:e.twist,timestamp:e.timeStamp.toFixed(8)}),t||(console.log(this.table.toString()),this.table.clear()))}},PointerType:{MOUSE:"mouse",TOUCH:"touch"}},pe={inactive:[],suppressKeys:{ctrlKey:!0,altKey:!0,shiftKey:!0,metaKey:!0},open:function(e){if(!(e instanceof I))throw new Error("Argument doesn't implement InkController interface");this.inkController=e,this.canvas=e.canvas.surface,this.mounted||(this.mounted=!0,this.begin=this.begin.bind(this),this.move=this.move.bind(this),this.end=this.end.bind(this),this.abort=this.abort.bind(this)),addEventListener("pointercancel",this.abort),this.attachResize(),this.start()},reset:function(e){this.canvas.removeEventListener("pointerdown",this.begin),e.canvas.surface.addEventListener("pointerdown",this.begin),this.dettachResize(),this.inkController=e,this.canvas=e.canvas.surface,this.attachResize()},close:function(){this.stop(),this.dettachResize(),removeEventListener("pointercancel",this.abort),delete this.canvas,delete this.inkController},start:function(e){e?this.inactive.remove(e):(this.canvas.addEventListener("pointerdown",this.begin),addEventListener("pointermove",this.move),addEventListener("pointerup",this.end))},stop:function(e){e?this.inactive.includes(e)||this.inactive.push(e):(this.canvas.removeEventListener("pointerdown",this.begin),removeEventListener("pointermove",this.move),removeEventListener("pointerup",this.end))},attachResize:function(){var e=this;if(this.inkController.resize!=I.prototype.resize){var t,n=pe.ResizeReason.WINDOW,r=devicePixelRatio,i=screen.width;matchMedia("screen and (orientation: portrait)").addListener((function(e){n=pe.ResizeReason.ORIENTATION,i=screen.width})),function e(){t&&(t.removeListener(e),n=r<devicePixelRatio?pe.ResizeReason.ZOOM_IN:pe.ResizeReason.ZOOM_OUT,r=devicePixelRatio),(t=matchMedia("(resolution: ".concat(devicePixelRatio,"dppx)"))).addListener(e)}(),this.resize=ce.debounce((function(){n==pe.ResizeReason.WINDOW&&i!=screen.width&&(n=i<screen.width?pe.ResizeReason.SCREEN_SIZE_INCREASED:pe.ResizeReason.SCREEN_SIZE_DECREASED,i=screen.width),e.inkController.resize(n),n=pe.ResizeReason.WINDOW}),200),addEventListener("resize",this.resize)}},dettachResize:function(){this.inkController.resize!=I.prototype.resize&&removeEventListener("resize",this.resize)},isInputAllowed:function(e){if("pointerdown"!=e.type)return!0;var t=this.provider;if(t||(t=this.getInputProvider(e)),this.inactive.includes(t))return!1;if(t==pe.PointerType.MOUSE){if(0!=e.button)return!1}else if(t==pe.PointerType.PEN&&0==e.pressure)return!1;for(var n in this.suppressKeys)if(e[n])return!1;return!0},isInputExpected:function(e){var t=!1,n=this.inkController.getInkBuilder(e.pointerId);return n&&(t="pointerdown"==e.type?!n.phase:n.phase&&n.phase!=W.Phase.END),t},getSensorPoint:function(e){var t=void 0,n=this.inkController.getInkBuilder(e.pointerId);if(n){if(e.pointerId!=n.pointerID)throw new Error("Create sensor point failed, expected pointer with id: ".concat(n.pointerID,", found: ").concat(e.pointerId));if("pointerdown"==e.type){var r=this.getInputProvider(e);e.pointerType!=r?n.pointerType=r:delete n.pointerType}t=this.createSensorPoint(e,n.pointerType)}return t},createSensorPoint:function(e,t,n){var r=this;if(!(e instanceof PointerEvent))throw new Error("Unexpected event detected: ".concat(e.constructor.name,". Expected event should be instance of PointerEvent."));var i,o=this.getOffset(e),a={x:o.x,y:o.y,z:void 0,timestamp:Math.round(e.timeStamp),pressure:void 0,radiusX:void 0,radiusY:void 0,tiltX:void 0,tiltY:void 0,rotation:void 0};if(n?(Object.defineProperty(a,"phase",{value:n.phase,enumerable:!0}),i=n.pointer):(Object.defineProperty(a,"phase",{value:W.Phase[e.type.replace(/pointer|mouse|touch/g,"").replace(/down|start/,"BEGIN").replace(/move/,"UPDATE").replace(/up|end/,"END")],enumerable:!0}),i={id:e.pointerId,type:t||e.pointerType,button:void 0,buttons:void 0},Object.defineProperty(i,"provider",{get:function(){return H.Type[this.type.toUpperCase()]},enumerable:!0}),"pen"!=i.type&&"mouse"!=i.type||(i.button=e.button,i.buttons=e.buttons)),Object.defineProperty(a,"pointer",{value:i,enumerable:!0}),"pen"!=i.type&&"touch"!=i.type||(a.pressure=e.pressure,a.rotation=Math.toRadians(e.twist)),"pen"==i.type?(a.tiltX=e.tiltX,a.tiltY=e.tiltY):"touch"==i.type&&(a.radiusX=e.width/2,a.radiusY=e.height/2),!n){if(e.getPredictedEvents){var s,u=e.getPredictedEvents();if(u.length>0)Object.defineProperty(a,"predicted",{get:function(){return s||(s=u.map((function(e){return r.createSensorPoint(e,t,a)}))),s},enumerable:!0})}if("pen"==i.type&&e.getCoalescedEvents){var c,l=e.getCoalescedEvents();if(l.length>1)Object.defineProperty(a,"coalesced",{get:function(){return c||(c=l.map((function(e){return r.createSensorPoint(e,t,a)}))),c},enumerable:!0})}}return a},getOffset:function(e){var t=this.canvas.offsetParent;if(!t)return{x:0,y:0};"flex"==t.getStyle("display")&&(t=this.canvas);var n=this.inkController.transform,r=this.canvas.getStyle("transform");"none"==r?r=t.getStyle("transform"):t=this.canvas,r="none"==r?null:ae.fromMatrix(r);var i=Z.fromPoint({x:e.clientX,y:e.clientY}),o=t.getBoundingClientRect();if(r){var a=r.invert();i=i.transform(a),o=$.fromRect(o).transform(a)}var s={x:i.x-o.x,y:i.y-o.y};return n&&(s=Z.fromPoint(s).transform(n.invert())),s},getInputProvider:function(e){return e.pointerType==pe.PointerType.MOUSE&&e.pressure>0&&.5!==e.pressure?pe.PointerType.PEN:e.pointerType},begin:function(e){if(this.logSampleInput(e),this.isInputAllowed(e)&&(this.inkController.registerInputProvider(e.pointerId,e.isPrimary),this.isInputExpected(e))){this.provider||(this.provider=this.getInputProvider(e));var t=this.getSensorPoint(e);t&&this.inkController.begin(t,e)}},move:function(e){if(this.isInputAllowed(e)&&this.isInputExpected(e)){this.logSampleInput(e);var t=this.getSensorPoint(e);if(t){var n;if(this.inkController.getInkBuilder(e.pointerId).prediction&&e.getPredictedEvents){var r=e.getPredictedEvents();r.length>0&&(n=this.createSensorPoint(r.last,t.pointer.type,t))}this.inkController.move(t,n,e)}}},end:function(e){if(this.isInputAllowed(e)&&this.isInputExpected(e)){this.logSampleInput(e);var t=this.getSensorPoint(e);t&&this.inkController.end(t,e),delete this.provider}},abort:function(e){delete this.provider,this.inkController.abort(e.pointerId)},logSampleInput:function(e){this.debug&&(this.debug.move||this.debug.end)&&("pointermove"!=e.type||this.debug.move)&&("pointerup"!=e.type||this.debug.end)&&(e.pointerType!=this.getInputProvider(e)&&(e.pen=!0),"pointerup"==e.type&&this.table.length>0&&(console.log(this.table.toString()),this.table.clear()),this.table||(this.table=new le(["type","id","pointerType","x","y","pressure","radius","tilt","rotation","buttons","timestamp"])),this.table.insert({type:e.type,id:e.pointerId,pointerType:e.pen?"".concat(e.pointerType," / pen"):e.pointerType,x:e.clientX.toFixed(2),y:e.clientY.toFixed(2),pressure:e.pressure.toFixed(8),radius:(e.width/2).toFixed(2)+" / "+(e.height/2).toFixed(2),rotation:e.twist,tilt:isFinite(e.tiltX)?e.tiltX+" / "+e.tiltY:"",buttons:e.buttons,timestamp:e.timeStamp.toFixed(8)}),"pointermove"!=e.type&&(console.log(this.table.toString()),this.table.clear()))},PointerType:{PEN:"pen",MOUSE:"mouse",TOUCH:"touch"}};Object.defineEnum(pe,"ResizeReason",["WINDOW","ZOOM_IN","ZOOM_OUT","SCREEN_SIZE_INCREASED","SCREEN_SIZE_DECREASED","ORIENTATION"]),"object"===("undefined"==typeof window?"undefined":P(window))&&"undefined"==typeof PointerEvent&&(de.suppressKeys=pe.suppressKeys,de.ResizeReason=pe.ResizeReason,de.attachResize=pe.attachResize,de.dettachResize=pe.dettachResize,pe=de);var ye=pe;function ve(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var me=function(e){T(n,e);var t=ve(n);function n(e,r,i){var o,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return k(this,n),(o=t.call(this,e,r,i)).red=a.red,o.green=a.green,o.blue=a.blue,o.alpha=a.alpha,o.size=a.size||n.defaults.size,o.rotation=a.rotation||n.defaults.rotation,o.scaleX=a.scaleX||n.defaults.scaleX,o.scaleY=a.scaleY||n.defaults.scaleY,o.scaleZ=isFinite(i)?a.scaleZ||n.defaults.scaleZ:void 0,o.offsetX=a.offsetX||n.defaults.offsetX,o.offsetY=a.offsetY||n.defaults.offsetY,o.offsetZ=isFinite(i)?a.offsetZ||n.defaults.offsetZ:void 0,o.dX,o.dY,o}return S(n,[{key:"fill",value:function(e,t,r){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o={},a=e*r.length;r.forEach((function(e,r){return n.setProperty(o,e,t[a+r])})),this.x=o.x,this.y=o.y,this.z=o.z,this.red=isFinite(o.red)?o.red:i.red,this.green=isFinite(o.green)?o.green:i.green,this.blue=isFinite(o.blue)?o.blue:i.blue,this.alpha=isFinite(o.alpha)?o.alpha:i.alpha,this.size=o.size||i.size||n.defaults.size,this.rotation=o.rotation||i.rotation||n.defaults.rotation,this.scaleX=o.scaleX||i.scaleX||n.defaults.scaleX,this.scaleY=o.scaleY||i.scaleY||n.defaults.scaleY,this.scaleZ=isFinite(o.z)?o.scaleZ||i.scaleZ||n.defaults.scaleZ:void 0,this.offsetX=o.offsetX||i.offsetX||n.defaults.offsetX,this.offsetY=o.offsetY||i.offsetY||n.defaults.offsetY,this.offsetZ=isFinite(o.z)?o.offsetZ||i.offsetZ||n.defaults.offsetZ:void 0,this.dX=o.dX,this.dY=o.dY}},{key:"getProperty",value:function(e){switch(e){case n.Property.X:return this.x;case n.Property.Y:return this.y;case n.Property.Z:return this.z;case n.Property.RED:return this.red;case n.Property.GREEN:return this.green;case n.Property.BLUE:return this.blue;case n.Property.ALPHA:return this.alpha;case n.Property.SIZE:return this.size;case n.Property.ROTATION:return this.rotation;case n.Property.SCALE_X:return this.scaleX;case n.Property.SCALE_Y:return this.scaleY;case n.Property.SCALE_Z:return this.scaleZ;case n.Property.OFFSET_X:return this.offsetX;case n.Property.OFFSET_Y:return this.offsetY;case n.Property.OFFSET_Z:return this.offsetZ;case n.Property.D_X:return this.dX;case n.Property.D_Y:return this.dY;default:throw console.warn(e),new Error("Invalid property found")}}},{key:"setProperty",value:function(e,t){n.setProperty(this,e,t)}},{key:"transform",value:function(e){if(!(e instanceof ae))throw new Error("matrix is instance of ".concat(e.constructor.name," - it should be instance of Matrix. Use Matrix.fromMatrix method to convert."));var t=e.scaleX,n=e.rotation;this.transformSelf(e),this.size*=t,this.rotation+=n}},{key:"toArray",value:function(e){var t=this;return e.map((function(e){var n=t.getProperty(e);if(null==n||isNaN(n))throw new Error("Property ".concat(e.name," has invalid value ").concat(n));return n}))}},{key:"toJSON",value:function(){var e=this,t={};return n.Property.values.forEach((function(n){var r=e.getProperty(n);null!=r&&isFinite(r)&&(t[n.name]=e.getProperty(n))})),t}}],[{key:"createInstance",value:function(e,t,r){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=new n(0,0,e.includes(n.Property.Z)?0:void 0);return r&&o.fill(i,r,e,t),o}},{key:"setProperty",value:function(e,t,r){switch(t){case n.Property.X:e.x=r;break;case n.Property.Y:e.y=r;break;case n.Property.Z:e.z=r;break;case n.Property.RED:e.red=r;break;case n.Property.GREEN:e.green=r;break;case n.Property.BLUE:e.blue=r;break;case n.Property.ALPHA:e.alpha=r;break;case n.Property.SIZE:e.size=r;break;case n.Property.ROTATION:e.rotation=r;break;case n.Property.SCALE_X:e.scaleX=r;break;case n.Property.SCALE_Y:e.scaleY=r;break;case n.Property.SCALE_Z:e.scaleZ=r;break;case n.Property.OFFSET_X:e.offsetX=r;break;case n.Property.OFFSET_Y:e.offsetY=r;break;case n.Property.OFFSET_Z:e.offsetZ=r;break;case n.Property.D_X:e.dX=r;break;case n.Property.D_Y:e.dY=r;break;default:throw console.warn(t),new Error("Invalid property found")}}}]),n}(Z);function ge(e,t,n,r,i,o,a){try{var s=e[o](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,i)}function be(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){ge(o,r,i,a,s,"next",e)}function s(e){ge(o,r,i,a,s,"throw",e)}a(void 0)}))}}x(me,"defaults",{size:1,rotation:0,scaleX:1,scaleY:1,scaleZ:1,offsetX:0,offsetY:0,offsetZ:0}),Object.defineEnum(me,"Property",["X","Y","Z","RED","GREEN","BLUE","ALPHA","SIZE","ROTATION","SCALE_X","SCALE_Y","SCALE_Z","OFFSET_X","OFFSET_Y","OFFSET_Z","D_X","D_Y"]);var Ee={exports:{}};!function(e){var t=function(e){var t,n=Object.prototype,r=n.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function c(e,t,n,r){var i=t&&t.prototype instanceof v?t:v,o=Object.create(i.prototype),a=new T(r||[]);return o._invoke=function(e,t,n){var r=h;return function(i,o){if(r===d)throw new Error("Generator is already running");if(r===p){if("throw"===i)throw o;return O()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var s=I(a,n);if(s){if(s===y)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===h)throw r=p,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=d;var u=l(e,t,n);if("normal"===u.type){if(r=n.done?p:f,u.arg===y)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(r=p,n.method="throw",n.arg=u.arg)}}}(e,n,a),o}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var h="suspendedStart",f="suspendedYield",d="executing",p="completed",y={};function v(){}function m(){}function g(){}var b={};u(b,o,(function(){return this}));var E=Object.getPrototypeOf,P=E&&E(E(A([])));P&&P!==n&&r.call(P,o)&&(b=P);var k=g.prototype=v.prototype=Object.create(b);function w(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function S(e,t){function n(i,o,a,s){var u=l(e[i],e,o);if("throw"!==u.type){var c=u.arg,h=c.value;return h&&"object"==typeof h&&r.call(h,"__await")?t.resolve(h.__await).then((function(e){n("next",e,a,s)}),(function(e){n("throw",e,a,s)})):t.resolve(h).then((function(e){c.value=e,a(c)}),(function(e){return n("throw",e,a,s)}))}s(u.arg)}var i;this._invoke=function(e,r){function o(){return new t((function(t,i){n(e,r,t,i)}))}return i=i?i.then(o,o):o()}}function I(e,n){var r=e.iterator[n.method];if(r===t){if(n.delegate=null,"throw"===n.method){if(e.iterator.return&&(n.method="return",n.arg=t,I(e,n),"throw"===n.method))return y;n.method="throw",n.arg=new TypeError("The iterator does not provide a 'throw' method")}return y}var i=l(r,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,y;var o=i.arg;return o?o.done?(n[e.resultName]=o.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,y):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function x(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function R(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(x,this),this.reset(!0)}function A(e){if(e){var n=e[o];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,a=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return a.next=a}}return{next:O}}function O(){return{value:t,done:!0}}return m.prototype=g,u(k,"constructor",g),u(g,"constructor",m),m.displayName=u(g,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,u(e,s,"GeneratorFunction")),e.prototype=Object.create(k),e},e.awrap=function(e){return{__await:e}},w(S.prototype),u(S.prototype,a,(function(){return this})),e.AsyncIterator=S,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new S(c(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},w(k),u(k,s,"Generator"),u(k,o,(function(){return this})),u(k,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=A,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(R),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return s.type="throw",s.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var u=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(u&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),R(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;R(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:A(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),y}},e}(e.exports);try{regeneratorRuntime=t}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=t:Function("r","regeneratorRuntime = r")(t)}}(Ee);var Pe=Ee.exports;Object.defineProperty(globalThis,"DIGITAL_INK_ENV",{value:"AUTO",enumerable:!0,configurable:!0});var ke={version:E};Object.defineEnum(ke,"Type",["WEB","WORKER","NODE","SHELL"]),Object.defineEnum(ke,"Type2D",["SCREEN","OFFSCREEN"]),Object.defineEnum(ke,"TypeGL",["WEB","STACK"]),function(e){var t,n="BROWSER"!=DIGITAL_INK_ENV&&"object"===("undefined"==typeof process?"undefined":P(process))&&"function"==typeof require;t="object"===("undefined"==typeof window?"undefined":P(window))?"WEB":"function"==typeof importScripts?"WORKER":n?"NODE":"SHELL";var r="undefined"==typeof Screen?"OFFSCREEN":"SCREEN",i="undefined"==typeof WebGLRenderingContext?"STACK":"WEB";Object.defineProperty(ke,"commonJS",{value:n,enumerable:!0}),Object.defineProperty(ke,"type",{value:ke.Type[t],enumerable:!0}),Object.defineProperty(ke,"type2D",{value:ke.Type2D[r],enumerable:!0}),Object.defineProperty(ke,"typeGL",{value:ke.TypeGL[i],enumerable:!0})}();var we=void 0;ke.commonJS&&(we=require("systeminformation"));var Se=we;function Ie(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var xe=function(e){T(r,e);var t,n=Ie(r);function r(){var e;return k(this,r),(e=n.call(this)).props={},e}return S(r,[{key:"getMD5Message",value:function(){if(!Object.isFrozen(this))throw new Error("ID generation failed. Environment do not belongs to any InputContext yet");return["Environment",z.buildMD5Tokens(this.props)]}}],[{key:"createInstance",value:(t=be(Pe.mark((function e(){var t,n,i,o=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=o.length>0&&void 0!==o[0]?o[0]:{},(n=new r).props["wacom.ink.sdk.lang"]="js",n.props["wacom.ink.sdk.version"]=ke.version,n.props["runtime.type"]=ke.type.name,void 0!==Se){e.next=9;break}n.props["user.agent"]=navigator.userAgent,e.next=17;break;case 9:return e.next=11,Se.osInfo();case 11:i=e.sent,n.props["os.id"]=i.serial.toLowerCase(),n.props["os.name"]=i.codename,n.props["os.version"]=i.release,n.props["os.build"]=i.build,n.props["os.platform"]="".concat(i.distro," (").concat(i.platform," ").concat(i.arch,")");case 17:return Object.keys(t).forEach((function(e){return n.props[e]=t[e]})),e.abrupt("return",n);case 19:case"end":return e.stop()}}),e)}))),function(){return t.apply(this,arguments)})}]),r}(z);function Re(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return Te(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Te(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function Te(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Ae={DP:160,PICA:6,POINT:72,DIP:96,DPI:96*("undefined"==typeof window?1:window.devicePixelRatio)},Oe=function(){function e(){k(this,e)}return S(e,null,[{key:"getChannelResolution",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=1,i=e.getUnitMetric(t);return i!=e.Metric.NORMALIZED&&i!=e.Metric.LOGICAL&&(r=e[Ce[i.name].name][t.name]/n),r}},{key:"convert",value:function(t,n,r,i){return n?r*e[Ce[t.name].name][i.name]/n:r}},{key:"convertAny",value:function(t,n,r,i){return t*e[n.name][r.name]/i}},{key:"getUnitMetric",value:function(t){switch(t){case e.Unit.METER:case e.Unit.CENTIMETER:case e.Unit.MILLIMETER:case e.Unit.MICROMETER:case e.Unit.INCH:case e.Unit.DP:case e.Unit.PICA:case e.Unit.POINT:case e.Unit.DIP:case e.Unit.DPI:return e.Metric.LENGTH;case e.Unit.SECOND:case e.Unit.MILLISECOND:case e.Unit.MICROSECOND:case e.Unit.NANOSECOND:return e.Metric.TIME;case e.Unit.RADIAN:case e.Unit.DEGREE:return e.Metric.ANGLE;case e.Unit.NEWTON:return e.Metric.FORCE;case e.Unit.NORMALIZED:return e.Metric.NORMALIZED;case e.Unit.LOGICAL:return e.Metric.LOGICAL;default:throw console.warn(t),new Error("Invalid unit found")}}},{key:"getMetricUnits",value:function(t){switch(t){case e.Metric.LENGTH:return[e.Unit.METER,e.Unit.CENTIMETER,e.Unit.MILLIMETER,e.Unit.MICROMETER,e.Unit.INCH,e.Unit.DP,e.Unit.PICA,e.Unit.POINT,e.Unit.DIP,e.Unit.DPI];case e.Metric.TIME:return[e.Unit.SECOND,e.Unit.MILLISECOND,e.Unit.MICROSECOND,e.Unit.NANOSECOND];case e.Metric.ANGLE:return[e.Unit.RADIAN,e.Unit.DEGREE];case e.Metric.FORCE:return[e.Unit.NEWTON];case e.Metric.NORMALIZED:return[e.Unit.NORMALIZED];case e.Metric.LOGICAL:return[e.Unit.LOGICAL];case e.Metric.DIMENSIONLESS:return[];default:throw console.warn(t),new Error("Invalid metric found")}}},{key:"getUnit",value:function(e){return Ce[e.name]}},{key:"convertValue",value:function(t,n,r){return t*e[n.name][r.name]}}]),e}();x(Oe,"Units",{METER:{METER:1,CENTIMETER:100,MILLIMETER:1e3,MICROMETER:1e6,INCH:39.3700787402},CENTIMETER:{METER:.01,CENTIMETER:1,MILLIMETER:10,MICROMETER:1e4,INCH:.3937007874},MILLIMETER:{METER:.001,CENTIMETER:.1,MILLIMETER:1,MICROMETER:1e3,INCH:.0393700787},MICROMETER:{METER:1e-6,CENTIMETER:1e-4,MILLIMETER:.001,MICROMETER:1,INCH:393701e-10},INCH:{METER:.0254,CENTIMETER:2.54,MILLIMETER:25.4,MICROMETER:25400,INCH:1},SECOND:{SECOND:1,MILLISECOND:1e3,MICROSECOND:1e6,NANOSECOND:1e9},MILLISECOND:{SECOND:.001,MILLISECOND:1,MICROSECOND:1e3,NANOSECOND:1e6},MICROSECOND:{SECOND:1e-6,MILLISECOND:.001,MICROSECOND:1,NANOSECOND:1e3},NANOSECOND:{SECOND:1e-9,MILLISECOND:1e-6,MICROSECOND:.001,NANOSECOND:1},RADIAN:{RADIAN:1,DEGREE:57.2957795},DEGREE:{RADIAN:.0174532925,DEGREE:1},NEWTON:{NEWTON:1}}),Object.defineEnum(Oe,"Unit",["METER","CENTIMETER","MILLIMETER","MICROMETER","INCH","DP","PICA","POINT","DIP","DPI","SECOND","MILLISECOND","MICROSECOND","NANOSECOND","RADIAN","DEGREE","NEWTON","NORMALIZED","LOGICAL"]),Object.defineEnum(Oe,"Metric",["LENGTH","TIME","FORCE","ANGLE","NORMALIZED","LOGICAL","DIMENSIONLESS"]);var Ce=Object.freeze({LENGTH:Oe.Unit.METER,TIME:Oe.Unit.SECOND,FORCE:Oe.Unit.NEWTON,ANGLE:Oe.Unit.RADIAN,NORMALIZED:Oe.Unit.NORMALIZED,LOGICAL:Oe.Unit.LOGICAL});function De(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}!function(){var e,t=Oe.getMetricUnits(Oe.Metric.LENGTH).filter((function(e){return!(e.name in Ae)})),n=Re(t);try{for(n.s();!(e=n.n()).done;){var r=e.value,i=Oe.Units[r.name];for(var o in Ae)i[o]=i.INCH*Ae[o]}}catch(e){n.e(e)}finally{n.f()}for(var a in Ae){var s,u={},c=Re(t);try{for(c.s();!(s=c.n()).done;){var l=s.value;u[l.name]=Oe.Units.INCH[l.name]/Ae[a]}}catch(e){c.e(e)}finally{c.f()}for(var h in Ae)u[h]=Ae[h]/Ae[a];Oe.Units[a]=u}for(var f in Oe.Units)Oe[f]=Oe.Units[f]}(),Object.freeze(Oe);var Me=function(e){T(n,e);var t=De(n);function n(e,r,i,o,a){var s;if(k(this,n),s=t.call(this),r==n.Metric.DIMENSIONLESS)i=1;else if(isNaN(i)||!r){var u=n.getDefaultUnitDescriptorPerType(e);if(!r){if(!u)throw new Error("metric is required");r=u.metric}if(!i){if(!u)throw new Error("resolution is required");i=Oe.getChannelResolution(u.unit)}isNaN(o)&&(o=u.min),isNaN(a)&&(a=u.max)}return s.type=e,s.metric=r,s.resolution=i,s.min=o,s.max=a,e==n.Type.TIMESTAMP?s.precision=0:s.precision=2,Object.defineProperty(A(s),"name",{get:function(){return ce.getEnumValueName(this.type.substring(this.type.lastIndexOf("/")+1))}}),s}return S(n,[{key:"getMD5Message",value:function(){if(!this.context)throw new Error("ID generation failed. This channel do not belongs to SensorChannelsContext yet");if(!Object.isFrozen(this.context)&&!Object.isFrozen(this))throw new Error("ID generation failed. Underlying SensorChannelsContext do not belongs to any SensorContext yet");var e=[];return e.push("SensorChannel"),e.push(this.context.inkProvider?this.context.inkProvider.id:""),e.push(this.context.device.id),e.push(this.type),e.push(this.metric.name),e.push(this.resolution.toFixed(4)),e.push((this.min||0).toFixed(4)),e.push((this.max||0).toFixed(4)),e.push(this.precision.toString()),e}}],[{key:"getTypeName",value:function(e){return Object.keys(n.Type).find((function(t){return n.Type[t]==e}))}},{key:"getDefaultUnitDescriptorPerType",value:function(e){var t={};switch(e){case n.Type.X:case n.Type.Y:case n.Type.Z:case n.Type.RADIUS_X:case n.Type.RADIUS_Y:t.unit=Oe.Unit.DIP;break;case n.Type.TIMESTAMP:t.unit=Oe.Unit.MILLISECOND;break;case n.Type.PRESSURE:t.unit=Oe.Unit.NORMALIZED,t.min=0,t.max=1;break;case n.Type.ALTITUDE:t.unit=Oe.Unit.RADIAN,t.min=0,t.max=Math.PI/2;break;case n.Type.AZIMUTH:t.unit=Oe.Unit.RADIAN,t.min=-Math.PI,t.max=Math.PI;break;case n.Type.ROTATION:t.unit=Oe.Unit.RADIAN,t.min=0,t.max=2*Math.PI;break;default:t=null}return t.metric=Oe.getUnitMetric(t.unit),t}},{key:"createCustomChannel",value:function(e,t,r,i,o,a){var s=new n(e,r,i,o,a);return s.precision=t,s}},{key:"createDefaultInstance",value:function(e,t){var r=n.getDefaultUnitDescriptorPerType(e);r||console.error("SensorChannel: createDefaultInstance failed with ".concat(e," type"));var i=Oe.getChannelResolution(r.unit),o=new n(e,r.metric,i,r.min,r.max);return o.unit=r.unit,e!=n.Type.X&&e!=n.Type.Y||t.type!=H.Type.MOUSE||(o.precision=0),o}}]),n}(z);function Ne(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}Me.defaults=Object.freeze({PEN:["X","Y","TIMESTAMP","PRESSURE","ALTITUDE","AZIMUTH","ROTATION"],TOUCH:["X","Y","TIMESTAMP","PRESSURE","RADIUS_X","RADIUS_Y","ROTATION"],MOUSE:["X","Y","TIMESTAMP"]}),Me.Type={X:"will://input/3.0/channel/X",Y:"will://input/3.0/channel/Y",Z:"will://input/3.0/channel/Z",TIMESTAMP:"will://input/3.0/channel/Timestamp",PRESSURE:"will://input/3.0/channel/Pressure",RADIUS_X:"will://input/3.0/channel/RadiusX",RADIUS_Y:"will://input/3.0/channel/RadiusY",ALTITUDE:"will://input/3.0/channel/Altitude",AZIMUTH:"will://input/3.0/channel/Azimuth",ROTATION:"will://input/3.0/channel/Rotation"},Me.Metric=Oe.Metric;var Le=function(e){T(n,e);var t=Ne(n);function n(e,r){var i;k(this,n),(i=t.call(this)).device=e.freeze(),i.inkProvider=Object.freeze(r);var o,a,s=[];return Object.defineProperty(A(i),"channels",{get:function(){return s},set:function(e){var t=this;this.invalidateID(),s=[],e.forEach((function(e){return t.add(e)}))},enumerable:!0}),Object.defineProperty(A(i),"layout",{get:function(){return s.map((function(e){return e.name}))},set:function(e){this.invalidateID(),s=s.filter((function(t){return e.includes(t.name)}))},enumerable:!0}),Object.defineProperty(A(i),"samplingRate",{get:function(){return o},set:function(e){this.invalidateID(),o=e},enumerable:!0}),Object.defineProperty(A(i),"latency",{get:function(){return a},set:function(e){this.invalidateID(),a=e},enumerable:!0}),i}return S(n,[{key:"getMD5Message",value:function(){if(!Object.isFrozen(this))throw new Error("ID generation failed. SensorChannelsContext do not belongs to any SensorContext yet");var e=["SensorChannelsContext"].concat(ue(this.channels.map((function(e){return e.id}))));return e.push(this.samplingRate||""),e.push(this.latency||""),e.push(this.inkProvider?this.inkProvider.id:""),e.push(this.device.id),e}},{key:"add",value:function(e){if((e.type==Me.Type.X||e.type==Me.Type.Y)&&!this.inkProvider)throw new Error("inkProvider is not found. Required for ink group.");e.context=this,Object.freeze(e),this.channels.push(e)}},{key:"get",value:function(e){return this.channels.filter((function(t){return t.id==e})).first}}],[{key:"createDefaultInstance",value:function(e,t){var r=new n(e,t);return r.channels=Me.defaults[t.type.name].map((function(e){return Me.createDefaultInstance(Me.Type[e],t)})),r}}]),n}(z);function Be(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var _e=function(e){T(n,e);var t=Be(n);function n(){var e;k(this,n),e=t.call(this);var r=[];return Object.defineProperty(A(e),"channelsContexts",{get:function(){return r},set:function(e){this.invalidateID(),r=e},enumerable:!0}),e}return S(n,[{key:"getMD5Message",value:function(){if(!Object.isFrozen(this))throw new Error("ID generation failed. SensorContext do not belongs to any InputContext yet");return["SensorContext"].concat(ue(this.channelsContexts.map((function(e){return e.id}))))}},{key:"addContext",value:function(e){if(!this.channelsContexts.includes(e)){if(this.channelsContexts.some((function(t){return t.device==e.device})))throw new Error("Already exists channelsContext with device ".concat(e.device.id,". Device should be unique in the scope of SensorContext."));e.inkProvider&&(this.inkChannelsContext=e),Object.freeze(e),this.channelsContexts.push(e)}}},{key:"getContext",value:function(e){return this.channelsContexts.find((function(t){return t.id==e}))}},{key:"getContextByChannelID",value:function(e){return this.channelsContexts.find((function(t){return t.get(e)}))}}],[{key:"createDefaultInstance",value:function(e,t){var r=new n;return r.addContext(Le.createDefaultInstance(e,t)),r}}]),n}(z);function Fe(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Ue=function(e){T(n,e);var t=Fe(n);function n(e,r){var i;return k(this,n),(i=t.call(this)).environment=Object.freeze(e),i.sensorContext=Object.freeze(r),i}return S(n,[{key:"getMD5Message",value:function(){return["InputContext",this.environment.id,this.sensorContext.id]}},{key:"addChannelsContext",value:function(e){this.sensorContext.addContext(e)}}],[{key:"createDefaultInstance",value:function(e,t,r){return new n(e,_e.createDefaultInstance(t,r))}}]),n}(z),je=function(){function e(){k(this,e)}return S(e,null,[{key:"createTreeURI",value:function(e,t){return"uim:tree/".concat(t?"".concat(t,"/"):"").concat(e)}},{key:"createStrokeURI",value:function(e,t){return"uim:stroke/".concat(t?"".concat(t,"/"):"").concat(e)}},{key:"createSensorDataURI",value:function(e,t){return"uim:sensor/".concat(t?"".concat(t,"/"):"").concat(e)}},{key:"createNodeURI",value:function(t,n,r){if(!t)throw new Error("inkTree is required");var i="";return r&&(i="#frag=".concat(r.pointIndexStart,",").concat(r.pointIndexEnd),0==r.ts&&1==r.tf||(i+=",".concat(r.ts.toFixed(5),",").concat(r.tf.toFixed(5)))),"".concat(e.createNodeURISchema(t),"/").concat(n).concat(i)}},{key:"createNodeURISchema",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.name;return"uim:node".concat(e.id?"/".concat(e.id):"","/").concat(t)}},{key:"createNamedEntityURI",value:function(e,t){return"uim:ne/".concat(t?"".concat(t,"/"):"").concat(e)}}]),e}();function Ge(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}function Ye(e,t,n){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}var Xe=new WeakMap,ze=function(e){T(n,e);var t=Ge(n);function n(e,r){var i;return k(this,n),Ye(A(i=t.call(this,r)),Xe,{writable:!0,value:void 0}),Object.defineProperty(A(i),"uri",{get:function(){return B(A(i),Xe)||_(A(i),Xe,je.createSensorDataURI(i.id)),B(A(i),Xe)},enumerable:!0}),i.created=Date.now(),i.inkState=n.InkState.PLANE,i.context=e,i.streams=[],i}return S(n,[{key:"add",value:function(e){if(!this.context.sensorContext.getContextByChannelID(e.channels.first.id))throw new Error("SensorContext do not contains information about SensorChannelsContext corresponding with this stream");e.ink&&(this.inkStream=e),this.streams.push(e)}}]),n}(z);Object.defineEnum(ze,"InkState",["PLANE","HOVERING","IN_VOLUME","VOLUME_HOVERING"]);var Ve=function(){function e(t){k(this,e),this.channels=t,this.data=[],this.ignoredIndex=[],Object.defineProperty(this,"layout",{value:t.map((function(e){return e.name})),enumerable:!0}),Object.defineProperty(this,"stride",{value:this.layout.length,enumerable:!0}),Object.defineProperty(this,"length",{get:function(){return this.data.length/this.stride},enumerable:!0}),this.ink=this.layout.includes("X")&&this.layout.includes("Y")}return S(e,[{key:"add",value:function(e,t){var n=this;t&&this.ignoredIndex.push(this.length),this.channels.forEach((function(t){var r=ce.getPropName(t.name),i=e[r];if(t.type==Me.Type.ALTITUDE&&!("altitude"in e))throw new Error("SensorStream input data do not provides altitude");if(t.type==Me.Type.AZIMUTH&&!("azimuth"in e))throw new Error("SensorStream input data do not provides azimuth");n.data.push(i)}))}},{key:"get",value:function(e){if(e>=this.length||e<0)throw new Error("Index ".concat(e," out of range - (0, ").concat(this.length-1,")"));for(var t={},n=0;n<this.stride;n++){var r=this.channels[n],i=ce.getPropName(r.name),o=e*this.stride;t[i]=this.data[o+n]}return t}},{key:"getChannelData",value:function(e){var t=e==Me.Type.TIMESTAMP?new Uint32Array(this.length):new Float32Array(this.length),n=this.channels.findIndex((function(t){return t.type==e}));this.channels[n].name;for(var r=0;r<this.length;r++)t[r]=this.data[r*this.stride+n];return t}},{key:"getPipelineMapping",value:function(){var e=[];if(this.ignoredIndex.length>0)for(var t=0;t<this.length;t++)this.ignoredIndex.includes(t)||e.push(t);return e}}]),e}();function He(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var We=function(e){T(r,e);var t,n=He(r);function r(){var e;return k(this,r),(e=n.call(this)).props={},e.devices=[],e}return S(r,[{key:"freeze",value:function(){return Object.freeze(this.props),this}},{key:"getMD5Message",value:function(){if(!Object.isFrozen(this.props)&&!Object.isFrozen(this))throw new Error("ID generation failed. InputDevice do not belongs to any SensorChannelsContext yet");return["InputDevice",z.buildMD5Tokens(this.props)]}},{key:"link",value:function(e){if(!(e instanceof r))throw new Error("Implementation of InputDevice is required");this.devices.push(e)}},{key:"getInkInputProvider",value:function(e){return new H(e)}},{key:"getInkSensorContext",value:function(e){var t=this.getInkInputProvider(e);return _e.createDefaultInstance(this,t)}},{key:"openStream",value:function(e){var t=this;if(!this.environment)throw new Error("Environment is not configured for current InputDevice instance");var n=H.Type[e.pointer.type.toUpperCase()],i=this.getInkSensorContext(n),o=new Ue(this.environment,i);i.inkChannelsContext.layout=r.getLayout(e),this.sensorData=new ze(o),this.sensorData.add(new Ve(i.inkChannelsContext.channels)),this.sampleTimestamp=e.timestamp,this.devices.forEach((function(e){return e.openStream(t.sensorData)}))}},{key:"add",value:function(e,t){if(!this.sensorData)throw new Error("Open ink stream not found");e.timestamp-=this.sampleTimestamp,this.sensorData.inkStream.add(e,t)}},{key:"closeStream",value:function(e){var t=this.sensorData;return this.devices.forEach((function(t){return t.closeStream(e)})),this.sensorData=null,this.sampleTimestamp=0,e?null:t}}],[{key:"getLayout",value:function(e){var t=[];return Object.keys(Me.Type).forEach((function(n){var i,o=Me.Type[n];i=o==Me.Type.ALTITUDE?"tiltX":o==Me.Type.AZIMUTH?"tiltY":ce.getPropName(n),r.isValidInput(i,e)&&t.push(n)})),t}},{key:"isValidInput",value:function(e,t){var n=!1;return isFinite(t[e])&&(n=!0,"pressure"==e?(n=t.pressure>0)&&t.pointer&&("mouse"==t.pointer.type?n=!1:"touch"==t.pointer.type&&(n=.5!==t.pressure&&1!==t.pressure)):"tiltX"==e||"tiltY"==e?n=0!=t.tiltX||0!=t.tiltY:"radiusX"==e||"radiusY"==e?n=t.radiusX!=t.radiusY||parseInt(t.radiusX)!=t.radiusX:"rotation"==e&&(n=0!=t.rotation)),n}},{key:"createInstance",value:(t=be(Pe.mark((function e(t){var n,r,i,o,a,s,u=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=M(this,ue(Array.from(u).slice(1))),void 0!==Se){e.next=5;break}n.props["dev.graphics.resolution"]="".concat(screen.width,"x").concat(screen.height),e.next=22;break;case 5:return e.next=7,Se.system();case 7:return r=e.sent,e.next=10,Se.cpu();case 10:return i=e.sent,e.next=13,Se.graphics();case 13:o=e.sent,a=o.displays.filter((function(e){return e.main}))[0],s=o.controllers[0],n.props["dev.id"]=r.uuid.toLowerCase(),n.props["dev.manufacturer"]=r.manufacturer,n.props["dev.model"]=r.model,n.props["dev.cpu"]="".concat(i.manufacturer," ").concat(i.brand," ").concat(i.speed," - ").concat(i.cores," core(s)"),n.props["dev.graphics.display"]="".concat(a.model," ").concat(a.currentResX,"x").concat(a.currentResY," (").concat(a.pixeldepth," bit)"),n.props["dev.graphics.adapter"]="".concat(s.model," ").concat(s.vram," GB");case 22:return e.next=24,xe.createInstance(t);case 24:return n.environment=e.sent,e.abrupt("return",n);case 26:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})}]),r}(z),Ze={};if(ke.type==ke.Type.WEB)Ze.Image=globalThis.Image,Ze.ImageData=globalThis.ImageData,Ze.CanvasRenderingContext2D=globalThis.CanvasRenderingContext2D,void 0===globalThis.OffscreenCanvas?(Ze.OffscreenCanvas=function(e,t){var n=document.createElement("canvas");return n.width=e,n.height=t,n},Ze.OffscreenCanvasRenderingContext2D=globalThis.CanvasRenderingContext2D):(Ze.OffscreenCanvas=globalThis.OffscreenCanvas,Ze.OffscreenCanvasRenderingContext2D=globalThis.OffscreenCanvasRenderingContext2D);else if(void 0!==globalThis.OffscreenCanvas)Ze.Image=globalThis.Image,Ze.ImageData=globalThis.ImageData,Ze.OffscreenCanvas=globalThis.OffscreenCanvas,Ze.OffscreenCanvasRenderingContext2D=globalThis.OffscreenCanvasRenderingContext2D;else if(ke.commonJS){var qe=require("canvas"),Ke=qe.Canvas,Je=qe.CanvasRenderingContext2D,$e=qe.Image,Qe=qe.ImageData;Ze.Image=$e,Ze.ImageData=Qe,Ze.OffscreenCanvas=Ke,Ze.OffscreenCanvasRenderingContext2D=Je}else console.warn("Current env - ".concat(ke.type.name,", do not provides OffscreenCanvas support"));function et(e){this.clearRect(0,0,this.canvas.width,this.canvas.height),e&&(this.fillStyle=e,this.fillRect(0,0,this.canvas.width,this.canvas.height))}Ze.CanvasRenderingContext2D&&(Ze.CanvasRenderingContext2D.prototype.clearCanvas=et),Ze.OffscreenCanvasRenderingContext2D&&(Ze.OffscreenCanvasRenderingContext2D.prototype.clearCanvas=et);var tt,nt=Ze.Image,rt=Ze.ImageData,it=Ze.OffscreenCanvas,ot=Ze.CanvasRenderingContext2D,at=Ze.OffscreenCanvasRenderingContext2D;function st(e,t,n){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}var ut=new WeakMap,ct=function(){function e(){var t=this;if(k(this,e),st(this,ut,{writable:!0,value:{}}),tt)throw new Error("URIResolver instance already available");tt=this,Object.defineProperty(this,"items",{get:function(){return Object.values(B(t,ut))},enumerable:!0}),this.init.apply(this,arguments)}return S(e,[{key:"init",value:function(){throw new Error("URIResolver.init(...args) is abstract and should be implemented")}},{key:"get",value:function(e){return B(this,ut)[e]}},{key:"register",value:function(e,t){B(this,ut)[e]=t}},{key:"resolve",value:function(e){var t;if(e.includes("?")){var n=e.split("?")[0],r=B(this,ut)[n];if(r){var i=e.split("?")[1],o=[];i.split("&").forEach((function(e){var t=e.split("=")[1],n=parseFloat(t);isFinite(n)?t=n:"true"==t?t=!0:"false"==t&&(t=!1),o.push(t)})),t=function(){return r.apply(void 0,ue(Array.from(arguments).concat(o)))}}}else t=B(this,ut)[e];if(!t)throw new Error("Failed to resolve ".concat(e));return t}}]),e}();Object.defineProperty(ct,"instance",{get:function(){return tt}});var lt=function(){function e(){k(this,e)}return S(e,null,[{key:"encode",value:function(t){var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.Encoding.AUTO;if(r==e.Encoding.AUTO?r="undefined"==typeof Buffer?"undefined"!=typeof SharedArrayBuffer&&t.buffer instanceof SharedArrayBuffer?e.Encoding.NONE:e.Encoding.ARRAY:e.Encoding.BUFFER:r==e.Encoding.ARRAY&&t instanceof Array&&(r=e.Encoding.NONE),r==e.Encoding.NONE)n=t;else if(r==e.Encoding.ARRAY)n=t.toArray();else{if("undefined"==typeof Buffer)throw new Error("Buffer not found, unable to serialize. Please provide Buffer in global scope.");var i=Buffer.from(t.buffer);switch(r){case e.Encoding.BUFFER:n=i.toJSON();break;case e.Encoding.BASE64:n=i.toString("base64");break;default:throw new Error("Invalid encoding provided: ".concat(r.name))}}return{encoding:r.name,type:t.constructor.name,content:n}}},{key:"decode",value:function(t){var n,r=e.Encoding[t.encoding];if(r==e.Encoding.NONE)n=t.content;else if(r==e.Encoding.ARRAY)n=t.content.toFloat32Array();else{var i;switch(r){case e.Encoding.BUFFER:i="undefined"==typeof Buffer?t.content.data:Buffer.from(t.content);break;case e.Encoding.BASE64:i="undefined"==typeof Buffer?atob(t.content).toCharArray():Buffer.from(t.content,"base64");break;default:throw new Error("Invalid encoding provided: ".concat(r.name))}var o=new Uint8Array(i);n=new globalThis[t.type](o.buffer)}return n}},{key:"isTypedArrayData",value:function(e){return e&&e.encoding&&e.type&&e.type.endsWith("Array")}}]),e}();Object.defineEnum(lt,"Encoding",["AUTO","NONE","ARRAY","BUFFER","BASE64"]);var ht=function(){function e(t,n){k(this,e),this.name=t,!t||ce.isValidURL(t)||e.repetitionsCache.has(t)||(e.repetitionsCache.add(t),console.warn("The string ".concat(t," is not a well formed URI"))),Object.defineProperty(this,"value",{get:function(){if(!n){if(!this.name)throw new Error("Resource descriptor identifier not found. Cannot resolve resource content.");if("function"==typeof this.resolve&&(n=this.resolve(this.name)),!n){if(!ct.instance)throw new Error("Resource URI ".concat(this.name," cannot be resolved. URIResolver not implemented yet. Please implement and instantiate."));n=ct.instance.resolve(this.name)}if(!n)throw new Error("Resource URI ".concat(this.name," cannot be resolved. Please provide resource definition in URIResolver init implementation."))}return n},set:function(e){n=e},enumerable:!0})}return S(e,[{key:"toJSON",value:function(){var e=this.value;return ArrayBuffer.isTypedArray(e)?e=lt.encode(e,this.encoding):"function"==typeof e&&(e=e()),{name:this.name,value:e}}}],[{key:"fromJSON",value:function(t){var n=t.value;return lt.isTypedArrayData(n)&&(n=lt.decode(n)),new e(t.name,n)}},{key:"getInstance",value:function(t,n){return new e(n,t)}}]),e}();x(ht,"repetitionsCache",new Set);var ft=function(){function e(t){k(this,e),ce.isValidURL(t)||(ht.repetitionsCache.has(t)||(ht.repetitionsCache.add(t),console.warn("Brush URI ".concat(t," is not a well formed URI"))),t=this.constructor.onInvalidName(t)),Object.defineProperty(this,"id",{value:t}),Object.defineProperty(this,"uri",{value:t}),Object.defineProperty(this,"name",{value:t,enumerable:!0})}return S(e,[{key:"toJSON",value:function(){throw new Error("Brush.toJSON() should be implemented")}}],[{key:"fromJSON",value:function(e){throw new Error("static Brush.fromJSON() should be implemented")}},{key:"onInvalidName",value:function(e){return e}}]),e}(),dt=function(){function e(){k(this,e)}return S(e,null,[{key:"createCircle",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e.defaults.CIRCLE_PRECISION,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.defaults.CIRCLE_RADIUS,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{x:0,y:0};return e.createEllipse(t,n,n,r)}},{key:"createEllipse",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e.defaults.ELLIPSE_PRECISION,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.defaults.ELLIPSE_RADIUS_X,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.defaults.ELLIPSE_RADIUS_Y,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{x:0,y:0},o=[],a=2*Math.PI/t;if(n<=0)throw new Error("Invalid radius x found ".concat(n," > 0"));if(r<=0)throw new Error("Invalid radius y found ".concat(r," > 0"));for(var s=0;s<t;s++){var u=s*a,c=n*Math.cos(u),l=r*Math.sin(u);o.push(i.x+c,i.y+l)}return Float32Array.createSharedInstance(o)}},{key:"createStar",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e.defaults.STAR_POINTS,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.defaults.STAR_INTERNAL_RADIUS,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.defaults.STAR_RADIUS,i=[];if(r<=0)throw new Error("Invalid radius found ".concat(r," > 0"));if(n<=0)throw new Error("Invalid internal radius found ".concat(n," > 0"));if(n>r)throw new Error("Invalid internal radius found 0 < ".concat(n," < ").concat(r));for(var o=2*Math.PI/t,a=0;a<t;a++){var s=a*o,u=r*Math.cos(s),c=r*Math.sin(s),l=n*Math.cos(s+o/2),h=n*Math.sin(s+o/2);i.push(u,c,l,h)}return Float32Array.createSharedInstance(i)}}]),e}();x(dt,"defaults",{CIRCLE_PRECISION:20,CIRCLE_RADIUS:.5,ELLIPSE_PRECISION:20,ELLIPSE_RADIUS_X:.5,ELLIPSE_RADIUS_Y:.25,STAR_POINTS:5,STAR_RADIUS:.5,STAR_INTERNAL_RADIUS:.25});var pt=y?y.default||globalThis.poly2tri:{},yt=pt.SweepContext,vt=pt.Point,mt=v?v.default||globalThis.ClipperLib:{},gt=mt.Clipper,bt=mt.Paths,Et=mt.Path;mt.ClipType,mt.PolyType;var Pt=mt.PolyFillType,kt=function(){function e(t,n,r){k(this,e),Object.defineProperties(this,{path:{value:t,enumerable:!0},pointIndexStart:{value:n,enumerable:!0},pointIndexEnd:{value:r,enumerable:!0}}),this.validate()}return S(e,[{key:"validate",value:function(){if(this.pointIndexStart<0)throw new Error("Invalid fragment pointIndexStart ".concat(this.pointIndexStart," found. The value must be non-negative."));if(this.pointIndexEnd>this.path.length-1)throw new Error("Invalid fragment pointIndexEnd ".concat(this.pointIndexEnd," found. Last point in path index is ").concat(this.path.length-1,"."))}},{key:"toPath",value:function(){return this.path.slice(this)}},{key:"toString",value:function(){return"fragment(".concat(this.pointIndexStart," - ").concat(this.pointIndexEnd,")")}}]),e}(),wt=function(){function e(t,n,r){var i=this,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;if(k(this,e),this.red=t,this.green=n,this.blue=r,this.alpha=o,o<0||o>1)throw new Error("Invalid alpha ".concat(o," found. The value must be in the interval [0, 1]."));Object.defineProperty(this,"hex",{get:function(){return"#".concat(i.red.toString(16).pad(2,"0")).concat(i.green.toString(16).pad(2,"0")).concat(i.blue.toString(16).pad(2,"0")).concat(Math.round(255*i.alpha).toString(16).pad(2,"0"))},enumerable:!0})}return S(e,[{key:"premultiply",value:function(){return{red:this.red/255*this.alpha,green:this.green/255*this.alpha,blue:this.blue/255*this.alpha,alpha:this.alpha}}},{key:"equals",value:function(e){return e&&this.red==e.red&&this.green==e.green&&this.blue==e.blue&&this.alpha==e.alpha}},{key:"toRGB",value:function(){return 1==this.alpha?this:new e(this.red,this.green,this.blue)}},{key:"toRGBA",value:function(t){return new e(this.red,this.green,this.blue,t)}},{key:"toHSLA",value:function(){var e=this.red/255,t=this.green/255,n=this.blue/255,r=Math.min(e,t,n),i=Math.max(e,t,n),o=0,a=0,s=(i+r)/2;if(i!=r){var u=i-r;switch(a=u/(1-Math.abs(2*s-1)),i){case e:o=(t-n)/u%6;break;case t:o=(n-e)/u+2;break;case n:o=(e-t)/u+4}}return(o*=60)<0&&(o+=360),{hue:parseFloat(o.toFixed(0)),saturation:parseFloat((100*a).toFixed(2)),lightness:parseFloat((100*s).toFixed(2)),alpha:this.alpha}}},{key:"toArray",value:function(){return[this.red,this.green,this.blue,this.alpha]}},{key:"toJSON",value:function(){return{red:this.red,green:this.green,blue:this.blue,alpha:this.alpha}}},{key:"toString",value:function(){return 1==this.alpha?"rgb(".concat(this.red,", ").concat(this.green,", ").concat(this.blue,")"):"rgba(".concat(this.red,", ").concat(this.green,", ").concat(this.blue,", ").concat(this.alpha,")")}}],[{key:"postdivide",value:function(t,n,r,i){return new e(parseInt(255*t/i),parseInt(255*n/i),parseInt(255*r/i),i)}},{key:"isColor",value:function(e){return e&&isFinite(e.red)&&isFinite(e.green)&&isFinite(e.blue)}},{key:"fromColor",value:function(t){var n,r,i,o;if("string"==typeof t)if(t.startsWith("rgb"))t=t.substring(t.indexOf("(")+1,t.indexOf(")")).split(/,\s*/g),n=parseInt(t[0]),r=parseInt(t[1]),i=parseInt(t[2]),o=t[3]?parseInt(t[3]):1;else{if(!t.startsWith("#"))throw new Error("Unknown input found: ".concat(t,". Expected data starts with rgba, rgb or #."));t=t.substring(1),n=parseInt(t.substring(0,2),16),r=parseInt(t.substring(2,4),16),i=parseInt(t.substring(4,6),16),o=8==t.length?parseInt(t.substring(6,8),16)/255:1}else Array.isArray(t)?(n=t[0],r=t[1],i=t[2],o=t[3]):(n=t.red,r=t.green,i=t.blue,o=t.alpha);return new e(n,r,i,o)}},{key:"fromHSLA",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3?arguments[3]:void 0;t/=60,n/=100,r/=100;var o=(1-Math.abs(2*r-1))*n,a=o*(1-Math.abs(t%2-1)),s=0,u=0,c=0;t>=0&&t<1?(s=o,u=a):t>=1&&t<2?(s=a,u=o):t>=2&&t<3?(u=o,c=a):t>=3&&t<4?(u=a,c=o):t>=4&&t<5?(s=a,c=o):(s=o,c=a);var l=r-o/2;return s+=l,u+=l,c+=l,new e(Math.round(255*s),Math.round(255*u),Math.round(255*c),i)}},{key:"random",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return new e(Math.randomInt(0,255),Math.randomInt(0,255),Math.randomInt(0,255),t?Math.random():1)}}]),e}();wt.TRANSPERENT=new wt(0,0,0,0),wt.BLACK=new wt(0,0,0,1),wt.WHITE=new wt(255,255,255,1),wt.RED=new wt(255,0,0,1),wt.GREEN=new wt(0,255,0,1),wt.BLUE=new wt(0,0,255,1);var St=function(){function e(t,n){var r=this,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};k(this,e),this.layout=t,this.pointProps=n,this.sheet={};var o={};Object.defineProperties(o,{size:{get:this.getComputed.bind(this,"size"),set:this.setStyle.bind(this,"size"),enumerable:!0},red:{get:this.getComputed.bind(this,"red"),set:this.setStyle.bind(this,"red"),enumerable:!0},green:{get:this.getComputed.bind(this,"green"),set:this.setStyle.bind(this,"green"),enumerable:!0},blue:{get:this.getComputed.bind(this,"blue"),set:this.setStyle.bind(this,"blue"),enumerable:!0},alpha:{get:this.getComputed.bind(this,"alpha"),set:this.setStyle.bind(this,"alpha"),enumerable:!0},rotation:{get:this.getComputed.bind(this,"rotation"),set:this.setStyle.bind(this,"rotation"),enumerable:!0},scaleX:{get:this.getComputed.bind(this,"scaleX"),set:this.setStyle.bind(this,"scaleX"),enumerable:!0},scaleY:{get:this.getComputed.bind(this,"scaleY"),set:this.setStyle.bind(this,"scaleY"),enumerable:!0},scaleZ:{get:this.getComputed.bind(this,"scaleZ"),set:this.setStyle.bind(this,"scaleZ"),enumerable:!0},offsetX:{get:this.getComputed.bind(this,"offsetX"),set:this.setStyle.bind(this,"offsetX"),enumerable:!0},offsetY:{get:this.getComputed.bind(this,"offsetY"),set:this.setStyle.bind(this,"offsetY"),enumerable:!0},offsetZ:{get:this.getComputed.bind(this,"offsetZ"),set:this.setStyle.bind(this,"offsetZ"),enumerable:!0},color:{get:this.getComputed.bind(this,"color"),set:this.setStyle.bind(this,"color"),enumerable:!0},blendMode:{get:this.getComputed.bind(this,"blendMode"),set:this.setStyle.bind(this,"blendMode"),enumerable:!0},visibility:{get:this.getComputed.bind(this,"visibility"),set:this.setStyle.bind(this,"visibility"),enumerable:!0},reset:{value:function(e){e&&(i=e),r.clear(),Object.keys(i).forEach((function(e){return r.setStyle(e,i[e])}))}},clear:{value:this.clear.bind(this)}}),this.style=Object.freeze(o),this.style.reset(i)}return S(e,[{key:"setStyle",value:function(t,n){if(null==n&&(n=void 0),e.validate(this.layout,t,n),"color"==t&&n)return this.sheet.red=n.red,this.sheet.green=n.green,this.sheet.blue=n.blue,void(this.sheet.alpha=n.alpha);null==n?delete this.sheet[t]:this.sheet[t]=n}},{key:"getStyle",value:function(e){var t=this.sheet[e];return"visibility"==e?"boolean"!=typeof t&&(t=!0):"color"==e&&wt.isColor(this.sheet)&&(t=wt.fromColor(this.sheet)),t}},{key:"getComputed",value:function(e){var t=this.getStyle(e);if(null==t)if("color"==e){var n={red:isFinite(this.sheet.red)?this.sheet.red:this.pointProps.red,green:isFinite(this.sheet.green)?this.sheet.green:this.pointProps.green,blue:isFinite(this.sheet.blue)?this.sheet.blue:this.pointProps.blue,alpha:isFinite(this.sheet.alpha)?this.sheet.alpha:this.pointProps.alpha};wt.isColor(n)&&(t=wt.fromColor(n))}else t=this.pointProps[e];return t}},{key:"clear",value:function(){this.sheet={}}}],[{key:"validate",value:function(e,t,n,r){var i;if(n&&e.includes(me.Property[ce.getEnumValueName(t)])){if(!r)throw new Error("Property ".concat(t," value ").concat(n," is not applicable. This is a dynamic property and is part of the layout."));console.warn("Property ".concat(t," value ").concat(n," is not applicable. This is a dynamic property and is part of the layout.")),n=void 0}if("color"==t)!n||n instanceof wt||(i="Property ".concat(t," is not an instance of Color"));else if("blendMode"==t)""==n&&(n=void 0);else if("number"==typeof n)if("size"==t)n<0?i="Property ".concat(t," with value ").concat(n," is not allowed. Value should be a positive number."):0==n&&(n=void 0);else if("red"==t||"green"==t||"blue"==t||"alpha"==t){var o="alpha"==t?{min:0,max:1}:{min:0,max:255};n>=o.min&&n<=o.max||(i="Property ".concat(t," with value ").concat(n," is out of range. Allowd range: [").concat(o.min,", ").concat(o.max,"]."))}else"rotation"==t?0==n&&(n=void 0):"scattering"==t&&n<0&&(n=void 0);if(i)throw new Error(i);return n}}]),e}();function It(e,t,n){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}var xt=[me.Property.X,me.Property.Y],Rt=new WeakMap,Tt=new WeakMap,At=function(){function e(t){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:xt;if(k(this,e),It(this,Rt,{writable:!0,value:void 0}),It(this,Tt,{writable:!0,value:void 0}),_(this,Rt,t),Object.defineProperty(this,"points",{get:function(){return B(n,Rt)},set:function(e){if(B(n,Rt)instanceof Float32Array)throw new Error("Points setter is not accessible wehn points type is Float32Array.");_(n,Rt,e),n.validate()},enumerable:!0}),Object.defineProperty(this,"buffer",{get:function(){return B(n,Rt).buffer},set:function(e){if(Array.isArray(B(n,Rt)))throw new Error("Underlying points buffer is Array. This property is applicable for TypedArray only.");if("undefined"!=typeof SharedArrayBuffer&&B(n,Rt).buffer instanceof SharedArrayBuffer)throw new Error("Underlying buffer is SharedArrayBuffer and cannot be restored");if(B(n,Rt).buffer.byteLength>0)throw new Error("Cannot restore buffer when underlying buffer is not empty");if(e.byteLength/Float32Array.BYTES_PER_ELEMENT/n.stride!=n.length)throw new Error("Value exceeds expected memory length");_(n,Rt,new Float32Array(e))}}),!Object.isSealed(r))for(var o in r)void 0!==r[o]&&(r[o]=St.validate(i,o,r[o],!0));i.includes(me.Property.ROTATION)||"rotation"in r||(r.rotation=void 0),i.includes(me.Property.SIZE)||r.size||(r.size=1),Object.defineProperties(this,{stride:{value:i.length,enumerable:!0},layout:{value:Object.freeze(i),enumerable:!0},pointProps:{value:Object.seal(r),enumerable:!0}}),t instanceof Float32Array?Object.defineProperty(this,"length",{value:B(this,Rt).length/this.stride,enumerable:!0}):Object.defineProperty(this,"length",{get:function(){return B(n,Rt).length/n.stride},enumerable:!0}),i.forEach((function(e,t){var r=ce.getPropName(e.name,!0);Object.defineProperty(n,"setPoint".concat(r),{value:n.setPointPropertyValue.bind(n,t)}),Object.defineProperty(n,"getPoint".concat(r),{value:n.getPointPropertyValue.bind(n,t)})}))}return S(e,[{key:"validate",value:function(){if(this.points.length%this.stride!=0)throw new Error("Path length doesn't match the stride provided via the layout")}},{key:"setPointPropertyValue",value:function(e,t,n){if(isNaN(t))throw new Error("Point index is required");if(t>=this.length||t<0)throw new Error("Index ".concat(e," out of range - (0, ").concat(this.length-1,")"));if(isNaN(n))throw new Error("value is required");this.points[t*this.layout.length+e]=n}},{key:"getPointPropertyValue",value:function(e,t){if(isNaN(t))throw new Error("Point index is required");if(t>=this.length||t<0)throw new Error("Index ".concat(e," out of range - (0, ").concat(this.length-1,")"));return this.points[t*this.layout.length+e]}},{key:"setPoint",value:function(e,t){var n=this,r=e*this.stride;this.layout.forEach((function(e,i){return n.points[r+i]=t.getProperty(e)}))}},{key:"getPoint",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.pointProps;if(e>=this.length||e<0)throw new Error("Index ".concat(e," out of range - (0, ").concat(this.length-1,")"));return me.createInstance(this.layout,t,this.points,e)}},{key:"getPointRef",value:function(e){if(arguments.length>1&&void 0!==arguments[1]||this.pointProps,e>=this.length||e<0)throw new Error("Index ".concat(e," out of range - (0, ").concat(this.length-1,")"));return B(this,Tt)||_(this,Tt,me.createInstance(this.layout)),B(this,Tt).fill(e,this.points,this.layout,this.pointProps),B(this,Tt)}},{key:"getChannelData",value:function(e){var t=new([me.Property.RED,me.Property.GREEN,me.Property.BLUE].includes(e)?Uint8Array:Float32Array)(this.length),n=this.layout.indexOf(e);if(-1==n)throw new Error("Property ".concat(e.name," is not part from the spline layout ").concat(this.layout.map((function(e){return e.name})).join(", ")));for(var r=0;r<this.length;r++)t[r]=this.points[r*this.stride+n];return t}},{key:"transform",value:function(e){for(var t=e.scaleX,n=e.rotation,r=0;r<this.length;r++){var o=r*this.stride,a=i.vec4.fromValues(this.getPointX(r),this.getPointY(r),0,1);i.vec4.transformMat4(a,a,e.value);for(var s=0;s<this.stride;s++){var u=o+s;switch(this.layout[s]){case me.Property.X:this.points[u]=a[0]/a[3];break;case me.Property.Y:this.points[u]=a[1]/a[3];break;case me.Property.Z:this.points[u]=a[2]/a[3];break;case me.Property.ROTATION:this.points[u]+=n;break;case me.Property.SIZE:this.points[u]*=t}}}this.layout.includes(me.Property.ROTATION)||(this.pointProps.rotation=0==n?void 0:n)}},{key:"clone",value:function(){return new e(B(this,Rt).clone(),Object.clone(this.pointProps),this.layout.slice())}},{key:"getFragment",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.length-1;return new kt(this,e,t)}},{key:"slice",value:function(t){return new e(this.slicePoints(t.pointIndexStart,t.pointIndexEnd),Object.clone(this.pointProps),this.layout.slice())}},{key:"validateFragment",value:function(e,t){if(e<0)throw new Error("Invalid fragment pointIndexStart ".concat(e," found. The value must be non-negative."));if(t>this.length-1)throw new Error("Invalid fragment pointIndexEnd ".concat(t," found. Last point in path index is ").concat(this.length-1,"."))}},{key:"slicePoints",value:function(e,t){var n;if(this.validateFragment(e,t),"undefined"!=typeof SharedArrayBuffer&&this.buffer instanceof SharedArrayBuffer){var r=this.points.subarray(e*this.stride,(t+1)*this.stride),i=new SharedArrayBuffer(r.length*Float32Array.BYTES_PER_ELEMENT);(n=new Float32Array(i)).set(r)}else n=this.points.slice(e*this.stride,(t+1)*this.stride);return n}},{key:"toSVGPath",value:function(){for(var e=[],t=0;t<this.length;t++)e.push("".concat(this.getPointX(t),",").concat(this.getPointY(t)));return"M ".concat(e.join(" L ")," Z")}},{key:"toJSON",value:function(){return{type:"Path",points:lt.encode(B(this,Rt),this.encoding),pointProps:this.pointProps,layout:this.layout.map((function(e){return e.name}))}}}],[{key:"fromJSON",value:function(t){if("Path"!=t.type)throw new Error("Path deserialization failed. JSON type is ".concat(t.type,", expected Path."));return new e(lt.decode(t.points),t.pointProps,t.layout.map((function(e){return me.Property[e]})))}},{key:"fromRect",value:function(t,n){return new e([t.left,t.top,t.right,t.top,t.right,t.bottom,t.left,t.bottom,t.left,t.top],n)}},{key:"createInstance",value:function(t,n,r){return new e(t,n,r)}},{key:"createSharedInstance",value:function(t,n,r){return new e(Float32Array.createSharedInstance(t),n,r)}}]),e}();function Ot(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return Ct(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ct(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function Ct(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Dt=-32767,Mt=65534,Nt=function(){function e(t,n){if(k(this,e),!Array.isArray(t))throw new Error("Unexpected polygons type found");var r=Mt/(n.width+1e-16),i=Mt/(n.height+1e-16),o=Math.floor(Math.min(r,i));if(0==o)throw new Error("Insufficent clipper range - (".concat(Dt," - ").concat(32767,"), scale failed - scaleX: ").concat(r,", scaleY: ").concat(i));var a=n.left+32767/o,s=n.top+32767/o;this.solution=new bt,this.bounds=n,this.transform={scale:o,offsetX:a,offsetY:s},this.subject=this.apply(t)}return S(e,[{key:"convertPoint",value:function(e){var t=(e.x-this.transform.offsetX)*this.transform.scale,n=(e.y-this.transform.offsetY)*this.transform.scale;return{X:t<0?Math.ceil(t):Math.floor(t),Y:n<0?Math.ceil(n):Math.floor(n)}}},{key:"containsPoint",value:function(e){return gt.PointInPolygon(this.convertPoint(e),this.solution)}},{key:"apply",value:function(e){var t,n=new bt,r=Ot(e);try{for(r.s();!(t=r.n()).done;){for(var i=t.value,o=new Et,a=i.shape,s=0;s<a.length;s++)o.push(this.convertPoint({x:a.getPointX(s),y:a.getPointY(s)}));n.push(o)}}catch(e){r.e(e)}finally{r.f()}return n}},{key:"toPaths",value:function(){var e=[];this.lastPoint={};var t,n=Ot(this.solution);try{for(n.s();!(t=n.n()).done;){var r=t.value;if(0!=r.length){var i=this.flatPath(r);i.length>0&&e.push(i)}}}catch(e){n.e(e)}finally{n.f()}return e}},{key:"flatPath",value:function(e){var t,n=[],r=Ot(e);try{for(r.s();!(t=r.n()).done;){var i=t.value;this.lastPoint.X==i.X&&this.lastPoint.Y==i.Y||(n.push(i.X/this.transform.scale+this.transform.offsetX,i.Y/this.transform.scale+this.transform.offsetY),this.lastPoint=i)}}catch(e){r.e(e)}finally{r.f()}return n.length<6&&(console.warn("Invalid contour found: [".concat(n.join(", "),"]")),n.clear()),n}}]),e}(),Lt=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:2;k(this,e),this.stride=t}return S(e,[{key:"sort",value:function(e,t){return this.sortArrayPart(e,0,e.length-this.stride,t),e}},{key:"partition",value:function(e,t,n,r){for(var i=e[n],o=e[n+1],a=t-this.stride,s=t;s<n;s+=2)r?r(i,o,e[s],e[s+1])&&(a+=this.stride,this.swap(e,a,s)):(i>e[s]||i==e[s]&&o>e[s+1])&&(a+=this.stride,this.swap(e,a,s));return this.swap(e,a+this.stride,n),a+this.stride}},{key:"swap",value:function(e,t,n){var r=e[t],i=e[t+1];return e[t]=e[n],e[t+1]=e[n+1],e[n]=r,e[n+1]=i,e}},{key:"sortArrayPart",value:function(e,t,n,r){if(t<n){var i=this.partition(e,t,n,r);this.sortArrayPart(e,t,i-this.stride,r),this.sortArrayPart(e,i+this.stride,n,r)}}}]),e}();function Bt(e,t,n,r,i,o){return(n-e)*(o-t)-(r-t)*(i-e)}function _t(e,t,n,r,i,o){var a=e-n,s=i-n,u=a*(o-r)-s*(t-r);u*=u;var c=(a=i-n)*a+(s=o-r)*s;return c>0?Math.sqrt(u/c):Math.sqrt((n-e)*(n-e)+(r-t)*(r-t))}var Ft=Object.freeze({__proto__:null,vector:function(e,t){var n,r={};return Object.defineProperty(r,"x",{value:t.x-e.x,enumerable:!0}),Object.defineProperty(r,"y",{value:t.y-e.y,enumerable:!0}),Object.defineProperty(r,"length",{get:function(){return isNaN(n)&&(n=Math.sqrt(r.x*r.x+r.y*r.y)),n},enumerable:!0}),r},angle:function(e,t){var n=e.x*t.x+e.y*t.y,r=e.x*t.y-e.y*t.x;return Math.atan2(r,n)},cross:Bt,perpendicularDistance:_t}),Ut=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Float32Array;k(this,e),this.ArrayType=t,this.quickSort=new Lt}return S(e,[{key:"monotoneChain",value:function(e){if(e.length<=0)return new this.ArrayType;this.quickSort.sort(e);for(var t=new this.ArrayType(e.length),n=0,r=0;r<e.length;r+=2){for(;n>=4&&Bt(t[n-4],t[n-3],t[n-2],t[n-1],e[r],e[r+1])<=0;)n-=2;t[n]=e[r],t[n+1]=e[r+1],n+=2}t=t.slice(0,n);var i,o=new this.ArrayType(e.length);n=0;for(var a=e.length-2;a>=0;a-=2){for(;n>=4&&Bt(o[n-4],o[n-3],o[n-2],o[n-1],e[a],e[a+1])<=0;)n-=2;o[n]=e[a],o[n+1]=e[a+1],n+=2}if(o=o.slice(0,n-2),this.ArrayType==Float32Array){var s=o.length+t.length;(i=Float32Array.createSharedInstance(s)).set(o),i.set(t,o.length)}else i=o.concat(t);return i}}]),e}();function jt(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return Gt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Gt(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function Gt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Yt=function(){function e(t){var n,r=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(k(this,e),!(t instanceof At))throw new Error("Expected shape type is Path. Use createInstance or createSharedInstance Polygon methods to allocate instance.");if(i.some((function(e){return!(e instanceof At)})))throw new Error("Expected hole type is Path. Use createInstance or createSharedInstance Polygon methods to allocate instance.");this.holesDirection=e.PointsDirection.CLOCKWISE,Object.defineProperty(this,"shape",{value:t,enumerable:!0}),Object.defineProperty(this,"holes",{value:i,enumerable:!0}),Object.defineProperty(this,"contours",{value:[t].concat(ue(i)),enumerable:!0}),Object.defineProperty(this,"ArrayType",{value:t.points instanceof Float32Array?Float32Array:Array}),Object.defineProperty(this,"bounds",{get:function(){return $.ofPolygon(r)},enumerable:!0}),Object.defineProperty(this,"vertices",{get:function(){return n||(n=r.triangulate()),n},set:function(e){return n=e},enumerable:!0}),Object.defineProperty(this,"verticesValue",{get:function(){return n}})}return S(e,[{key:"clone",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=this.shape.clone(t),r=this.holes.map((function(e){return e.clone(t)})),i=new e(n,r);return this.verticesValue&&(i.vertices=this.vertices.slice()),i}},{key:"fit",value:function(e){var t,n=this.bounds,r=e.width/n.width,i=e.height/n.height,o=r>0&&i>0?Math.min(r,i):Math.max(r,i),a=jt(this.contours);try{for(a.s();!(t=a.n()).done;)for(var s=t.value,u=0;u<s.length;u++)s.setPointX(u,s.getPointX(u)*o),s.setPointY(u,s.getPointY(u)*o)}catch(e){a.e(e)}finally{a.f()}}},{key:"center",value:function(){var e,t=this.bounds,n=jt(this.contours);try{for(n.s();!(e=n.n()).done;)for(var r=e.value,i=0;i<r.length;i++)r.setPointX(i,r.getPointX(i)-t.center.x),r.setPointY(i,r.getPointY(i)-t.center.y)}catch(e){n.e(e)}finally{n.f()}}},{key:"transform",value:function(e){this.contours.forEach((function(t){return t.transform(e)}))}},{key:"intersects",value:function(t){if(!(t instanceof e))throw new Error("Expected 'poly' type is Polygon");(this.holes.length>0||t.holes.length)&&console.warn("Polygon intersection is for contours only. Holes are ignored.");for(var n=this.shape,r=t.shape,i=0;i<2;i++)for(var o=0==i?n:r,a=0;a<o.length;a++){for(var s=a+1==o.length?0:a+1,u=o.getPointX(a),c=o.getPointY(a),l=o.getPointX(s),h=o.getPointY(s)-c,f=u-l,d=Number.POSITIVE_INFINITY,p=Number.NEGATIVE_INFINITY,y=0;y<n.length;y++){var v=h*n.getPointX(y)+f*n.getPointY(y);v<d&&(d=v),v>p&&(p=v)}for(var m=Number.POSITIVE_INFINITY,g=Number.NEGATIVE_INFINITY,b=0;b<r.length;b++){var E=h*r.getPointX(b)+f*r.getPointY(b);E<m&&(m=E),E>g&&(g=E)}if(p<m||g<d)return!1}return!0}},{key:"containsPoint",value:function(e){var t=!1,n=this.shape;this.holes.length>0&&console.warn("Polygon intersection is for contours only. Holes are ignored.");for(var r=0,i=n.length-1;r<n.length;i=r++)n.getPointY(r)>e.y!=n.getPointY(i)>e.y&&e.x<(n.getPointX(i)-n.getPointX(r))*(e.y-n.getPointY(r))/(n.getPointY(i)-n.getPointY(r))+n.getPointX(r)&&(t=!t);return t}},{key:"triangulate",value:function(){var e,t,n=[],r=jt(this.contours);try{for(r.s();!(e=r.n()).done;){for(var i=e.value,o=[],a=0;a<i.length;a++){var s=new vt(i.getPointX(a),i.getPointY(a));if(a>0){if(o.last.x==s.x&&o.last.y==s.y)continue;if(a==i.length-1&&o.first.x==s.x&&o.first.y==s.y)continue}o.push(s)}n.push(o)}}catch(e){r.e(e)}finally{r.f()}try{t=new yt(n.shift())}catch(e){return console.error(e),new Float32Array}for(var u=0,c=n;u<c.length;u++){var l=c[u];try{t.addHole(l)}catch(e){return console.error(e),new Float32Array}}try{t.triangulate()}catch(e){return console.warn(e),new Float32Array}var h,f=t.getTriangles(),d=Float32Array.createSharedInstance(6*f.length),p=0,y=jt(f);try{for(y.s();!(h=y.n()).done;){var v,m=jt(h.value.getPoints());try{for(m.s();!(v=m.n()).done;){var g=v.value;d[p++]=g.x,d[p++]=g.y}}catch(e){m.e(e)}finally{m.f()}}}catch(e){y.e(e)}finally{y.f()}return d}},{key:"convex",value:function(){return this.buildConvex(this.shape.points)}},{key:"union",value:function(e){var t=Array.of.apply(Array,ue(this.shape.points).concat(ue(e.shape.points)));return this.buildConvex(t)}},{key:"buildConvex",value:function(t){this.convexHullProducer||(this.convexHullProducer=new Ut(this.ArrayType));var n=this.convexHullProducer.monotoneChain(t);return this.ArrayType==Float32Array?e.createSharedInstance(n):e.createInstance(n)}},{key:"simplify",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.1,n=new Nt([this],this.bounds);return n.subject=gt.SimplifyPolygons(n.subject,Pt.pftNonZero),n.solution=gt.CleanPolygons(n.subject,t*n.transform.scale),1==n.subject.length&&0==n.solution.first.length&&(n.solution=n.subject),e.toPolygonArray(n.toPaths())}},{key:"simplifyRamerDouglasPeucker",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.1;if(t<=0)throw new Error("epsilon expected value > 0");this.epsilon=t;var n,r=this.simplifyPath(this.shape),i=[],o=jt(this.holes);try{for(o.s();!(n=o.n()).done;){var a=n.value,s=this.simplifyPath(a);s.length>0&&i.push(s)}}catch(e){o.e(e)}finally{o.f()}return this.ArrayType==Float32Array?e.createSharedInstance(r,i):e.createInstance(r,i)}},{key:"simplifyPath",value:function(e){if(e.length<3)return e.points;var t=Array.of.apply(Array,ue(e.points).concat([e.getPointX(0),e.getPointY(0)])),n=this.simplifyPolyline(t);return n.length<8?t.slice(0,t.length-2):n.slice(0,n.length-2)}},{key:"simplifyPolyline",value:function(e){if(e.length<4)return e;for(var t=0,n=0,r=2;r<e.length-2;r+=2){var i=_t(e[r],e[r+1],e[0],e[1],e[e.length-2],e[e.length-1]);i>t&&(n=r,t=i)}if(t>this.epsilon){var o=this.simplifyPolyline(e.slice(0,n+2)),a=this.simplifyPolyline(e.slice(n,e.length));return o.concat(a.slice(2,a.length))}return[e[0],e[1],e[e.length-2],e[e.length-1]]}},{key:"toSVGPath",value:function(){return this.contours.map((function(e){return e.toSVGPath()})).join(" ")}},{key:"toJSON",value:function(){return{type:"Polygon",shape:this.shape.toJSON(),holes:this.holes.map((function(e){return e.toJSON()})),holesDirection:this.holesDirection.name,vertices:this.verticesValue}}}],[{key:"fromJSON",value:function(t){if("Polygon"!=t.type)throw new Error("Polygon deserialization failed. JSON type is ".concat(t.type,", expected Polygon."));var n=new e(At.fromJSON(t.shape),t.holes.map((function(e){return At.fromJSON(e)})));return n.holesDirection=e.PointsDirection[t.holesDirection],n.vertices=t.vertices,n}},{key:"fromRect",value:function(t){return e.createInstance([t.left,t.top,t.right,t.top,t.right,t.bottom,t.left,t.bottom,t.left,t.top])}},{key:"createInstance",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return new e(At.createInstance(t),n.map((function(e){return At.createInstance(e)})))}},{key:"createSharedInstance",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=new e(At.createSharedInstance(t),n.map((function(e){return At.createSharedInstance(e)})));return Object.defineProperty(r,"encoding",{get:function(){return r.shape.encoding},set:function(e){r.contours.forEach((function(t){return t.encoding=e}))},enumerable:!0}),r}}]),e}();Object.defineEnum(Yt,"PointsDirection",["CLOCKWISE","COUNTERCLOCKWISE"]);var Xt=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;k(this,e),this.size=n,Object.defineProperty(this,"descriptor",{value:{shape:void 0},enumerable:!0}),Object.defineProperty(this,"shape",{get:function(){if(!t){if("function"==typeof(t=this.descriptor.shape.value)&&(t=t()),(Array.isArray(t)||t instanceof Float32Array)&&(t=Yt.createSharedInstance(t)),!(t instanceof Yt))throw new Error("Expected shape type is Polygon");e.fitShape(t)}return t},set:function(n){if(!n)throw new Error("BrushPrototype: shape not found");"string"==typeof n?n=new ht(n):n instanceof Yt||n instanceof Float32Array||Array.isArray(n)?n=ht.getInstance(n):n instanceof ht||(n=new ht(n.name,n.value)),t=null,this.descriptor.shape=n,this.descriptor.shape.resolve=e.resolve},enumerable:!0}),this.shape=t}return S(e,[{key:"toJSON",value:function(){return this.shape.encoding=this.encoding,{shape:{name:this.descriptor.shape.name,value:this.shape.toJSON()},size:this.size}}}],[{key:"fromJSON",value:function(t){return new e({name:t.shape.name,value:Yt.fromJSON(t.shape.value)},t.size)}},{key:"create",value:function(t){for(var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=t,o=arguments.length,a=new Array(o>2?o-2:0),s=2;s<o;s++)a[s-2]=arguments[s];switch(t){case e.Type.CIRCLE:n=dt.createCircle.apply(dt,a),i+="?precision=".concat(a[0]||dt.defaults.CIRCLE_PRECISION,"&radius=").concat(a[1]||dt.defaults.CIRCLE_RADIUS);break;case e.Type.ELLIPSE:n=dt.createEllipse.apply(dt,a),i+="?precision=".concat(a[0]||dt.defaults.ELLIPSE_PRECISION,"&radiusX=").concat(a[1]||dt.defaults.ELLIPSE_RADIUS_X,"&radiusY=").concat(a[2]||dt.defaults.ELLIPSE_RADIUS_Y);break;case e.Type.STAR:n=dt.createStar.apply(dt,a),i+="?points=".concat(a[0]||dt.defaults.STAR_POINTS,"&internalRadius=").concat(a[1]||dt.defaults.STAR_INTERNAL_RADIUS,"&radius=").concat(a[2]||dt.defaults.STAR_RADIUS);break;default:console.error("Brush2D: createShape fails with ".concat(t," type"))}return new e({name:i,shape:n},r)}},{key:"resolve",value:function(t){var n,r=t.split("?"),i=r.first;if(Object.values(e.Type).includes(i)){var o=r.last.split("&"),a={};switch(o.forEach((function(e){a[e.substring(0,e.indexOf("="))]=e.substring(e.indexOf("=")+1)})),i){case e.Type.CIRCLE:var s=a.precision?parseInt(a.precision):void 0,u=a.radius?parseFloat(a.radius):1;n=dt.createCircle(s,u);break;case e.Type.ELLIPSE:var c=a.precision?parseInt(a.precision):void 0,l=a.radiusX?parseFloat(a.radiusX):void 0,h=a.radiusY?parseFloat(a.radiusY):void 0;n=dt.createEllipse(c,l,h);break;case e.Type.STAR:var f=a.points?parseInt(a.points):void 0,d=a.radius?parseFloat(a.radius):void 0,p=a.internalRadius?parseFloat(a.internalRadius):void 0;n=dt.createStar(f,p,d);break;default:console.error("Brush2D: createShape fails with ".concat(i," type"))}}return n}},{key:"fitShape",value:function(t){if(!(t instanceof Yt))throw new Error("Expected shape type is Polygon");t.center(),t.fit(e.SHAPE_FRAME)}}]),e}();function zt(e){return Vt.apply(this,arguments)}function Vt(){return Vt=be(Pe.mark((function e(t){var n,r,i,o,a,s,u=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=u.length>1&&void 0!==u[1]?u[1]:"binary",r=u.length>2&&void 0!==u[2]?u[2]:{},!(t instanceof Uint8Array)){e.next=4;break}return e.abrupt("return",t);case 4:return e.next=6,fetch(t,Object.assign({mode:"no-cors"},r));case 6:if(o=e.sent,"json"!=n){e.next=13;break}return e.next=10,o.json();case 10:i=e.sent,e.next=36;break;case 13:if("text"!=n){e.next=19;break}return e.next=16,o.text();case 16:i=e.sent,e.next=36;break;case 19:if("binary"!=n){e.next=26;break}return e.next=22,o.arrayBuffer();case 22:a=e.sent,i=new Uint8Array(a),e.next=36;break;case 26:return e.next=28,o.blob();case 28:if(s=e.sent,"base64"!=n){e.next=35;break}return e.next=32,qt(s);case 32:i=e.sent,e.next=36;break;case 35:i=s;case 36:return e.abrupt("return",i);case 37:case"end":return e.stop()}}),e)}))),Vt.apply(this,arguments)}function Ht(e){return Wt.apply(this,arguments)}function Wt(){return Wt=be(Pe.mark((function e(t){var n,r,i=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=i.length>1&&void 0!==i[1]?i[1]:"binary",r=i.length>2&&void 0!==i[2]?i[2]:{},e.abrupt("return",zt(t,n,r));case 3:case"end":return e.stop()}}),e)}))),Wt.apply(this,arguments)}function Zt(){return Zt=be(Pe.mark((function e(t){var n,r,i=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=i.length>1&&void 0!==i[1]?i[1]:{},e.next=3,fetch(t,Object.assign({method:"HEAD",cache:"no-store"},n));case 3:return r=e.sent,e.abrupt("return",200==r.status);case 5:case"end":return e.stop()}}),e)}))),Zt.apply(this,arguments)}function qt(e){return new Promise((function(t,n){var r=new FileReader;r.onloadend=function(){return t("data:"==r.result?"":r.result)},r.onerror=n,r.readAsDataURL(e)}))}function Kt(e){return new Promise((function(t,n){var r,i=new nt;i.crossOrigin="anonymous",i.onload=function(){if(ke.type2D==ke.Type2D.OFFSCREEN){var e=new it(i.width,i.height);e.getContext("2d").drawImage(i,0,0),t(e)}else r&&URL.revokeObjectURL(r),t(i)},i.onerror=n,"string"==typeof e?i.src=e:ke.type2D==ke.Type2D.OFFSCREEN?e instanceof Uint8Array?i.src=Buffer.from(e):e instanceof it?t(e):i.src=e:(e instanceof Uint8Array&&(e.byteLength!=e.buffer.byteLength&&(e=e.slice()),e=e.buffer),e instanceof ArrayBuffer&&(e=new Blob([e],{type:"image/png"})),r=URL.createObjectURL(e),i.src=r)}))}function Jt(e){return $t.apply(this,arguments)}function $t(){return($t=be(Pe.mark((function e(t){var n;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("string"!=typeof t&&"undefined"!=typeof createImageBitmap){e.next=6;break}return e.next=3,Kt(t);case 3:n=e.sent,e.next=15;break;case 6:if(!(t instanceof ArrayBuffer||t instanceof Uint8Array)){e.next=12;break}return e.next=9,createImageBitmap(new Blob([t],{type:"image/png"}));case 9:n=e.sent,e.next=15;break;case 12:return e.next=14,createImageBitmap(t);case 14:n=e.sent;case 15:return e.abrupt("return",n);case 16:case"end":return e.stop()}}),e)})))).apply(this,arguments)}x(Xt,"SHAPE_FRAME",new $(-.5,-.5,1,1)),Xt.Type={ELLIPSE:"will://brush/3.0/shape/Ellipse",CIRCLE:"will://brush/3.0/shape/Circle",STAR:"will://brush/3.0/shape/Star"};var Qt=Object.freeze({__proto__:null,readFile:zt,loadFile:Ht,exists:function(e){return Zt.apply(this,arguments)},dataURL:qt,loadImage:Jt,saveAs:function(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"application/octet-stream";if(e instanceof Blob)n=URL.createObjectURL(e);else{var i;if(e instanceof ArrayBuffer)i=[e];else if(e.buffer)e.byteLength<e.buffer.byteLength&&(e=new Uint8Array(e)),i=[e.buffer];else if(e instanceof Array)i=e;else{if("string"!=typeof e)throw new Error("content expected type not found");i=[e]}var o=new Blob(i,{type:r});n=URL.createObjectURL(o)}var a=document.createElement("a");a.href=n,a.download=t,a.appendChild(document.createTextNode(t)),a.style.display="none",document.body.appendChild(a),a.click(),setTimeout((function(){URL.revokeObjectURL(n),a.remove()}),911)}});function en(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var tn=function(e){T(r,e);var t,n=en(r);function r(e,t,i){var o,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;return k(this,r),o=n.call(this,e),isFinite(i)&&(a=i,i=void 0),a<=0&&(console.warn("Invalid spacing found ".concat(a,". It should be positive number.")),a=1),Object.defineProperty(A(o),"shape",{get:function(){return t},set:function(e){if(e instanceof Float32Array&&(e=new Xt(e)),e instanceof Xt&&(e=[e]),e.some((function(e){return!(e instanceof Xt)})))throw console.warn(e),new Error("Brush2D: Invalid shape found");e.sort(ce.comparator({sortBy:"size",sortOrder:"asc"})),t=e},enumerable:!0}),o.shape=t,o.fill=i,o.spacing=a,o}return S(r,[{key:"configure",value:(t=be(Pe.mark((function e(t){var n;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.pattern&&this.fill){e.next=2;break}return e.abrupt("return");case 2:if(t instanceof ot||t instanceof at){e.next=4;break}throw new Error("ctx is not instance of CanvasRenderingContext2D or OffscreenCanvasRenderingContext2D");case 4:return e.next=6,Jt(this.fill);case 6:n=e.sent,this.pattern=t.createPattern(n,"repeat");case 8:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"selectShape",value:function(e){for(var t,n=1;n<this.shape.length;n++)if(this.shape[n].size>e){t=this.shape[n-1];break}return t||(t=this.shape.last),t.shape}},{key:"toJSON",value:function(){var e=this;return{type:"Brush2D",name:this.name,spacing:this.spacing,shape:this.shape.map((function(t){return t.encoding=e.encoding,t.toJSON()}))}}}],[{key:"fromJSON",value:function(e){var t=1==e.shape.length?Xt.fromJSON(e.shape[0]):e.shape.map((function(e){return Xt.fromJSON(e)}));return new r(e.name,t,e.spacing)}}]),r}(ft),nn=function(){function e(){k(this,e),this.state={}}return S(e,[{key:"isLayoutPart",value:function(t){if(e.posProps.includes(t))return this.inputLayout.includes(t.name);if(this.brush instanceof tn&&e.colorProps.includes(t))return!1;if(e.excludedProps.includes(t))return!1;var n=!1,r=ce.getPropName(t.name),i=this.dynamics[r];if(i&&!i.disabled)if(i.dependencies&&i.dependencies.length>0){var o=this.inputLayout.map((function(e){return Me.Type[e]}));n=i.dependencies.filter((function(e){return o.includes(e)})).length>0}else n=!0;return n}},{key:"calculate",value:function(t,n,r){var i=this;return this.point=n.createPathPoint(this.statics),this.calculateProperty(me.Property.SIZE,t,n,r),e.colorProps.forEach((function(e){return i.calculateProperty(e,t,n,r)})),e.transformProps.forEach((function(e){return i.calculateTransform(e,t,n,r)})),this.point}},{key:"calculateProperty",value:function(t,n,r,i){if(this.layout.includes(t)){var o,a=ce.getPropName(t.name),s=this.dynamics[a],u=this.state[a];if("function"==typeof u.resolve)o=u.resolve(n,r,i);else if(this.inputLayout.includes("PRESSURE")){var c=r.pressure||n.pressure/2;o=e.mapTo(c,u.pressure,s.value)}else{var l=r.speed(n,i);0==l&&n&&(l=n.velocity),r.velocity=l,o=e.mapTo(l,u.velocity,s.value)}this.point[ce.getPropName(t.name)]=o}}},{key:"calculateTransform",value:function(t,n,r,i){if(this.layout.includes(t)){var o,a=ce.getPropName(t.name),s=this.dynamics[a],u=this.state[a];if("function"==typeof u.resolve)o=u.resolve(n,r,i);else{var c=s.dependencies||[];if(t==me.Property.ROTATION)if(c.includes(Me.Type.ROTATION)&&this.inputLayout.includes("ROTATION"))o=r.rotation;else{if(!c.includes(Me.Type.AZIMUTH)||!this.inputLayout.includes("AZIMUTH"))throw new Error("Property ".concat(t.name," is not configured properly. Dependencies are expected or resolve handler."));o=r.computeNearestAzimuthAngle(n)}else if(c.includes(Me.Type.ALTITUDE)&&this.inputLayout.includes("ALTITUDE")){r.cosAltitude||(r.cosAltitude=Math.cos(r.altitude));var l=r.cosAltitude;o=e.mapTo(l,u.altitude,s.value)}else{if(t!=me.Property.SCALE_X&&t!=me.Property.SCALE_Y||!c.includes(Me.Type.RADIUS_X)&&!c.includes(Me.Type.RADIUS_Y)||!this.inputLayout.includes("RADIUS_X")||!this.inputLayout.includes("RADIUS_Y"))throw new Error("Property ".concat(t.name," is not configured properly. Dependencies are expected or resolve handler."));var h=t==me.Property.SCALE_X?"radiusX":"radiusY",f=r[h];o=e.mapTo(f,u[h],s.value)}}if(isNaN(o))throw new Error("Property ".concat(t.name," has no value"));this.point[ce.getPropName(t.name)]=o}}},{key:"reset",value:function(t,n,r){var i=this,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};if(this.brush=n,this.dynamics=o,this.statics={},this.color=new wt(0,0,0),this.inputLayout=We.getLayout(t),this.layout=me.Property.values.filter((function(e){return i.isLayoutPart(e)})),this.debug&&console.log(t.pointer.type,this.inputLayout,this.layout.map((function(e){return e.name})),t),"size"in a){if(this.isLayoutPart(me.Property.SIZE))throw new Error("Size should exist only in dynamics or statics");this.statics.size=a.size}else{if(!this.isLayoutPart(me.Property.SIZE))throw new Error("Size not found. Should be set through dynamics or statics.");if(0==t.pressure&&"pen"==t.pointer.type)throw new Error("Hover point detected. Should be handeled manually.")}e.colorProps.forEach((function(e){var t=ce.getPropName(e.name);i.isLayoutPart(e)?i.color[t]=r[t]:(i.statics[t]=isFinite(a[t])?a[t]:r[t],i.color[t]=i.statics[t])})),e.transformProps.forEach((function(e){if(!i.isLayoutPart(e)){var t=ce.getPropName(e.name);isFinite(a[t])&&(i.statics[t]=a[t])}})),this.resetState(t)}},{key:"resetState",value:function(t){var n=this;this.state={},[me.Property.SIZE].concat(e.colorProps).forEach((function(t){if(n.layout.includes(t)){var r=ce.getPropName(t.name),i=n.dynamics[r],o={};if(i.resolve)o.resolve=e.resolveAction(i.resolve);else{if(!i.value)throw new Error("PathPointContext: dynamics ".concat(r," value property not found"));if(i.value.min>i.value.max)throw new Error("PathPointContext: dynamics ".concat(r," invalid value range found: ").concat(i.value.min," - ").concat(i.value.max));var a=n.clonePropertySettings(i.velocity,0,4e3);a.remap=e.resolveAction(a.remap||i.value.remap);var s=n.clonePropertySettings(i.pressure,0,1);s.remap=e.resolveAction(s.remap||i.value.remap),o.velocity=e.validateRange(r,a,{min:0,max:3e4}),o.pressure=e.validateRange(r,s,{min:0,max:1})}n.state[r]=o}})),e.transformProps.forEach((function(r){if(n.layout.includes(r)){var i=ce.getPropName(r.name),o=n.dynamics[i],a={};if(o.resolve)a.resolve=e.resolveAction(o.resolve);else if(o.dependencies){if(o.dependencies.includes(Me.Type.ALTITUDE)){if(!o.value)throw new Error("PathPointContext: dynamics ".concat(i," value property not found"));if(o.value.min>o.value.max)throw new Error("PathPointContext: dynamics ".concat(i," invalid value range found: ").concat(o.value.min," - ").concat(o.value.max));var s=n.clonePropertySettings(o.altitude,0,Math.PI/2);s.remap=e.resolveAction(s.remap||o.value.remap),a.altitude=e.validateRange(i,s,{min:0,max:Math.PI/2})}if(r==me.Property.SCALE_X&&o.dependencies.includes(Me.Type.RADIUS_X)||r==me.Property.SCALE_Y&&o.dependencies.includes(Me.Type.RADIUS_Y)){if(!o.value)throw new Error("PathPointContext: dynamics ".concat(i," value property not found"));var u=r==me.Property.SCALE_X?"radiusX":"radiusY",c=o[u]||{},l=t[u]<=1?0:1,h=t[u]<=1?1:50,f=t[u]<=1?{min:0,max:1,remap:c.remap}:c,d=n.clonePropertySettings(f,l,h);d.remap=e.resolveAction(d.remap||o.value.remap),a[u]=e.validateRange(i,d,{min:l,max:h})}}n.state[i]=a}}))}},{key:"clonePropertySettings",value:function(e,t,n){var r;return e?("min"in(r=Object.clone(e))||(r.min=t),"max"in r||(r.max=n)):r={min:t,max:n},r}}],[{key:"excludedProps",get:function(){return[me.Property.D_X,me.Property.D_Y]}},{key:"posProps",get:function(){return[me.Property.X,me.Property.Y,me.Property.Z]}},{key:"colorProps",get:function(){return[me.Property.RED,me.Property.GREEN,me.Property.BLUE,me.Property.ALPHA]}},{key:"transformProps",get:function(){return[me.Property.ROTATION,me.Property.SCALE_X,me.Property.SCALE_Y,me.Property.SCALE_Z,me.Property.OFFSET_X,me.Property.OFFSET_Y,me.Property.OFFSET_Z]}},{key:"resolveAction",value:function(e){if(e)return"string"==typeof e?e=new ht(e):"function"==typeof e?e=ht.getInstance(e):e instanceof ht||(e=new ht(e.name,e.value)),e.value}},{key:"validateRange",value:function(e,t,n){if(t.min<n.min||t.max>n.max)throw new Error("".concat(e," config is out of range - (").concat(t.min,", ").concat(t.max,"), expected values interval - (").concat(n.min,", ").concat(n.max,")"));if(t.min>t.max)throw new Error("".concat(e," min ").concat(t.min," exceeds max ").concat(t.max));return t}},{key:"mapTo",value:function(e,t,n){var r=(ce.clamp(e,t)-t.min)/(t.max-t.min);return t.remap&&(r=t.remap(r)),n.min+r*(n.max-n.min)}}]),e}();function rn(e,t,n){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}var on=new WeakMap,an=function(){function e(){var t=this;k(this,e),rn(this,on,{writable:!0,value:void 0}),this.keepAllData=!1,Object.defineProperty(this,"allData",{get:function(){if(!t.keepAllData)throw new Error("All data is not accumulated. By default keepAllData property is false.");return B(t,on)||_(t,on,new t.constructor.ARRAY_TYPE),t.getOutput(B(t,on),e.OutputType.ALL_DATA)},enumerable:!0})}return S(e,[{key:"build",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.OutputType.PROCESSOR,r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return console.warn("use process instead"),this.process(t,n,r)}},{key:"process",value:function(t){var n,r,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.OutputType.PROCESSOR,o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];switch(i){case e.OutputType.ADDITION:n=this.add(t,o);break;case e.OutputType.PREDICTION:n=this.predict(t);break;case e.OutputType.PROCESSOR:this.reset(),n=this.processImpl(t,i);break;default:throw new Error("Unexpected OutputType found. Allowed type is oneof(ADDITION, PREDICTION, PROCESSOR)")}i!=e.OutputType.PREDICTION&&(this.keepAllData&&(B(this,on)||_(this,on,new this.constructor.ARRAY_TYPE),n instanceof Yt?B(this,on).push(n):i==e.OutputType.PROCESSOR?_(this,on,n):(r=B(this,on)).push.apply(r,ue(n))));return this.debug&&console.log(this.constructor.name,i.name,o,n),this.getOutput(n,i)}},{key:"add",value:function(t){return this.processImpl(t,e.OutputType.ADDITION)}},{key:"predict",value:function(t){return this.processImpl(t,e.OutputType.PREDICTION)}},{key:"processImpl",value:function(e,t){throw new Error("Abstract method processImpl(input, type) of DataSequenceProcessor should be implemented")}},{key:"getOutput",value:function(e,t){return e}},{key:"reset",value:function(){B(this,on)&&_(this,on,new this.constructor.ARRAY_TYPE)}}]),e}();function sn(){}x(an,"ARRAY_TYPE",Array),Object.defineEnum(an,"OutputType",["ADDITION","PREDICTION","ALL_DATA","PROCESSOR"]),sn.BlendMode={SOURCE_OVER:"source-over",DESTINATION_OVER:"destination-over",DESTINATION_IN:"destination-in",DESTINATION_OUT:"destination-out",LIGHTER:"lighter",COPY:"copy",MIN:"MIN",MAX:"MAX",DIRECT_SOURCE_OUT:"DIRECT_SOURCE_OUT",DIRECT_DESTINATION_OUT:"DIRECT_DESTINATION_OUT"};var un=sn.BlendMode,cn=function(){function e(t){var n,r,i=this;if(k(this,e),!t)throw new Error("GL context is not available in current environment");this.gl=t,this.program=null,this.programs=[],Object.defineProperty(this,"blendMode",{get:function(){return n},set:function(e){if(!e)throw new Error("blendMode is required");n!=e&&(i.activeBlendMode(e),n=e)},enumerable:!0}),Object.defineProperty(this,"transform",{get:function(){return r&&!r.isIdentity?r:null},set:function(e){e!=r&&(r=e,i.program&&i.program.onContextChange())}}),Object.defineProperty(e,"VERTEX_SHADER_PRECISION",{value:this.getSupportedFloatPrecision(this.gl.VERTEX_SHADER),enumerable:!0,configurable:!0}),Object.defineProperty(e,"FRAGMENT_SHADER_PRECISION",{value:this.getSupportedFloatPrecision(this.gl.FRAGMENT_SHADER),enumerable:!0,configurable:!0}),Object.defineProperty(e,"VERTEX_BATCH_SIZE",{value:1e3,enumerable:!0})}return S(e,[{key:"init",value:function(e,t){this.gl.disable(this.gl.DITHER),this.gl.disable(this.gl.BLEND),this.gl.disable(this.gl.STENCIL_TEST),this.gl.disable(this.gl.DEPTH_TEST),this.gl.disable(this.gl.SCISSOR_TEST),this.gl.activeTexture(this.gl.TEXTURE0),this.blendMinMaxExt=this.gl.getExtension("EXT_blend_minmax"),this.blendMaxFallback=!this.gl.MAX&&!this.blendMinMaxExt,this.blendMode=un.COPY,this.resize(e,t),this.scatterMethodRandomSeed=parseInt(Date.now()/1e3),this.onChange();for(var n=0;n<this.programs.length;n++)this.programs[n].init()}},{key:"getSupportedFloatPrecision",value:function(e){return this.gl.getShaderPrecisionFormat(e,this.gl.HIGH_FLOAT).precision>0?"highp":this.gl.getShaderPrecisionFormat(e,this.gl.MEDIUM_FLOAT).precision>0?"mediump":"lowp"}},{key:"random",value:function(){return this.scatterMethodRandomSeed=1103515245*this.scatterMethodRandomSeed+12345&Number.MAX_INT32,this.scatterMethodRandomSeed/Number.MAX_INT32}},{key:"setUniforms",value:function(e){var t=i.mat4.create();e?i.mat4.ortho(t,this.graphicsBox.left,this.graphicsBox.right,this.graphicsBox.top,this.graphicsBox.bottom,1,-1):i.mat4.ortho(t,this.graphicsBox.left,this.graphicsBox.right,this.graphicsBox.bottom,this.graphicsBox.top,1,-1),this.graphicsSpaceToFramebufferSpaceT=t,this.onChange()}},{key:"resize",value:function(e,t){this.gl.viewport(e.x,e.y,e.width,e.height),this.bounds=e,this.graphicsBox=t,this.clipRect=t}},{key:"activeBlendMode",value:function(e){switch(e){case un.COPY:this.gl.disable(this.gl.BLEND);break;case un.SOURCE_OVER:this.gl.enable(this.gl.BLEND),this.gl.blendEquation(this.gl.FUNC_ADD),this.gl.blendFunc(this.gl.ONE,this.gl.ONE_MINUS_SRC_ALPHA);break;case un.DESTINATION_OVER:this.gl.enable(this.gl.BLEND),this.gl.blendEquation(this.gl.FUNC_ADD),this.gl.blendFunc(this.gl.ONE_MINUS_DST_ALPHA,this.gl.ONE);break;case un.DESTINATION_IN:this.gl.enable(this.gl.BLEND),this.gl.blendEquation(this.gl.FUNC_ADD),this.gl.blendFunc(this.gl.ZERO,this.gl.SRC_ALPHA);break;case un.DESTINATION_OUT:this.gl.enable(this.gl.BLEND),this.gl.blendEquation(this.gl.FUNC_ADD),this.gl.blendFunc(this.gl.ZERO,this.gl.ONE_MINUS_SRC_ALPHA);break;case un.DESTINATION_IN_MULTIPLY:this.gl.enable(this.gl.BLEND),this.gl.blendEquation(this.gl.FUNC_ADD),this.gl.blendFunc(this.gl.ZERO,this.gl.SRC_COLOR);break;case un.DESTINATION_OUT_NO_ALPHA:this.gl.enable(this.gl.BLEND),this.gl.blendEquation(this.gl.FUNC_ADD),this.gl.blendFunc(this.gl.ZERO,this.gl.ONE_MINUS_SRC_COLOR);break;case un.LIGHTER:this.gl.enable(this.gl.BLEND),this.gl.blendEquation(this.gl.FUNC_ADD),this.gl.blendFunc(this.gl.ONE,this.gl.ONE);break;case un.MIN:this.gl.enable(this.gl.BLEND),this.gl.MIN?this.gl.blendEquation(this.gl.MIN):this.blendMinMaxExt?this.gl.blendEquation(this.blendMinMaxExt.MIN_EXT):(this.gl.blendEquation(this.gl.FUNC_ADD),this.gl.blendFunc(this.gl.ONE,this.gl.ONE_MINUS_SRC_ALPHA));break;case un.MAX:this.gl.enable(this.gl.BLEND),this.gl.MAX?this.gl.blendEquation(this.gl.MAX):this.blendMinMaxExt?this.gl.blendEquation(this.blendMinMaxExt.MAX_EXT):(this.gl.blendEquation(this.gl.FUNC_ADD),this.gl.blendFunc(this.gl.ONE,this.gl.ONE_MINUS_SRC_ALPHA));break;case un.DIRECT_SOURCE_OUT:this.gl.enable(this.gl.BLEND),this.gl.blendEquation(this.gl.FUNC_SUBTRACT),this.gl.blendFunc(this.gl.ONE,this.gl.ONE);break;case un.DIRECT_DESTINATION_OUT:this.gl.enable(this.gl.BLEND),this.gl.blendEquation(this.gl.FUNC_REVERSE_SUBTRACT),this.gl.blendFunc(this.gl.ONE,this.gl.ONE);break;default:throw new Error("Unsupported blend mode: ".concat(e))}}},{key:"clearColorBuffer",value:function(e){this.gl.clearColor(e.red*e.alpha/255,e.green*e.alpha/255,e.blue*e.alpha/255,e.alpha),this.gl.clear(this.gl.COLOR_BUFFER_BIT)}},{key:"scissors",value:function(e){if(e&&!(e instanceof J))throw new TypeError("rect must be undefined or instanceof RectGL");var t=this.gl.isEnabled(this.gl.SCISSOR_TEST);e?(t||this.gl.enable(this.gl.SCISSOR_TEST),this.gl.scissor(e.x,e.y,e.width,e.height)):t&&this.gl.disable(this.gl.SCISSOR_TEST)}},{key:"isProgramActive",value:function(e){return this.program==e}},{key:"activateProgram",value:function(e){this.isProgramActive(e)?e.contextChanged&&(e.onContextChange(),e.contextChanged=!1):(this.program&&this.program.onDeactivate(),this.gl.useProgram(e.program),this.program=e,e.onActivate(),e.onContextChange(),e.contextChanged=!1)}},{key:"generateBuffersExt",value:function(e,t,n,r){var i=this.gl,o=i.createFramebuffer();i.bindFramebuffer(i.FRAMEBUFFER,o);var a=i.createTexture();return i.activeTexture(i.TEXTURE0),i.bindTexture(i.TEXTURE_2D,a),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_S,i.CLAMP_TO_EDGE),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_T,i.CLAMP_TO_EDGE),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MAG_FILTER,n),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MIN_FILTER,n),i.texImage2D(i.TEXTURE_2D,0,i.RGBA,e,t,0,i.RGBA,r,null),i.framebufferTexture2D(i.FRAMEBUFFER,i.COLOR_ATTACHMENT0,i.TEXTURE_2D,a,0),i.clearColor(0,0,0,0),i.clear(i.COLOR_BUFFER_BIT),{framebuffer:o,texture:a}}},{key:"genFrameAndRenders",value:function(e,t,n){var r=this.gl.createFramebuffer(),i=this.gl.createRenderbuffer();return this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,r),this.gl.bindRenderbuffer(this.gl.RENDERBUFFER,i),this.gl.renderbufferStorage(this.gl.RENDERBUFFER,e,t,n),this.gl.framebufferRenderbuffer(this.gl.FRAMEBUFFER,this.gl.COLOR_ATTACHMENT0,this.gl.RENDERBUFFER,i),{framebuffer:r,renderbuffer:i}}},{key:"deleteBuffers",value:function(e,t){t&&this.gl.deleteTexture(t),e&&this.gl.deleteFramebuffer(e)}},{key:"deleteFrameAndRender",value:function(e,t){t&&this.gl.deleteRenderbuffer(t),e&&this.gl.deleteFramebuffer(e)}},{key:"onChange",value:function(){for(var e=0;e<this.programs.length;e++)this.programs[e].contextChanged=!0}},{key:"enableStencilBufferForBlending",value:function(e){this.gl.enable(this.gl.STENCIL_TEST),this.gl.stencilMask(4294967295),this.gl.clearStencil(0),this.gl.clear(this.gl.STENCIL_BUFFER_BIT),this.isStencilBufferAvailable()||this.attachStencilBufferForBlending(e)}},{key:"isStencilBufferAvailable",value:function(){return this.gl.getFramebufferAttachmentParameter(this.gl.FRAMEBUFFER,this.gl.STENCIL_ATTACHMENT,this.gl.FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE)==this.gl.RENDERBUFFER}},{key:"attachStencilBufferForBlending",value:function(e){var t=this.gl.getParameter(this.gl.FRAMEBUFFER_BINDING);if(e.framebuffer==t){var n=this.gl.createRenderbuffer();this.gl.bindRenderbuffer(this.gl.RENDERBUFFER,n),this.gl.renderbufferStorage(this.gl.RENDERBUFFER,this.gl.STENCIL_INDEX8,e.width,e.height),this.gl.framebufferRenderbuffer(this.gl.FRAMEBUFFER,this.gl.STENCIL_ATTACHMENT,this.gl.RENDERBUFFER,n),e.renderbuffer=n,e.releaseRenderbuffer=!0}}},{key:"drawArrays",value:function(e,t,n){this.blendMode==un.MAX&&this.blendMaxFallback?this.drawBlendMaxFallback(this.gl.drawArrays.bind(this.gl,e,t,n)):this.gl.drawArrays(e,t,n)}},{key:"drawElements",value:function(e,t,n,r){this.blendMode==un.MAX&&this.blendMaxFallback?this.drawBlendMaxFallback(this.gl.drawElements.bind(this.gl,e,t,n,r)):this.gl.drawElements(e,t,n,r)}},{key:"drawBlendMaxFallback",value:function(e){this.gl.enable(this.gl.STENCIL_TEST),this.gl.stencilFunc(this.gl.NOTEQUAL,1,1),this.gl.stencilOp(this.gl.REPLACE,this.gl.REPLACE,this.gl.REPLACE),this.blendMode=un.DIRECT_DESTINATION_OUT,e(),this.gl.stencilFunc(this.gl.NOTEQUAL,2,1),this.gl.stencilOp(this.gl.REPLACE,this.gl.REPLACE,this.gl.REPLACE),this.inkGLContext.blendMode=un.LIGHTER,e(),this.gl.disable(this.gl.STENCIL_TEST)}}],[{key:"random",value:function(e){return(1103515245*e+12345&Number.MAX_INT32)/Number.MAX_INT32}},{key:"logGLError",value:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=e.getError();r>0&&(e.constructor.prototype&&(t=Object.keys(e.constructor.prototype).filter((function(t){return e[t]===r})).join(" | ")),console.error("WebGL".concat(n?" ":"").concat(n,": ").concat(r).concat(t?" - ".concat(t):"")))}}]),e}();x(cn,"ANTIALIASING",{passesSqrt:4,passes:16,spread:1.25,step:.3125,weight:1.01/16}),x(cn,"IDENTITY_MATRIX",i.mat4.create());var ln=function(){function e(){k(this,e)}var t,n;return S(e,[{key:"setTransform",value:function(e){this.matrix=e}},{key:"getExportCanvas",value:function(e){throw new Error("Abstract method getExportCanvas of Layer inheritor should be implemented")}},{key:"toBlob",value:(n=be(Pe.mark((function e(t){var n,r,i,o=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=o.length>1&&void 0!==o[1]?o[1]:"image/png",r=o.length>2&&void 0!==o[2]?o[2]:.92,"undefined"!=typeof Blob){e.next=8;break}if("undefined"!=typeof Buffer){e.next=7;break}throw new Error("Current environment do not have neither Blob nor Buffer support.");case 7:throw new Error("This method is not compliant in underlying environment. Use `toBuffer` instead.");case 8:if(!(i=this.getExportCanvas(t)).toBlob){e.next=13;break}return e.abrupt("return",new Promise((function(e,t){return i.toBlob(e,n,r)})));case 13:return e.abrupt("return",i.convertToBlob({type:n,quality:r}));case 14:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"toBuffer",value:(t=be(Pe.mark((function e(t){var n,r,i,o,a,s=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=s.length>1&&void 0!==s[1]?s[1]:"image/png",r=s.length>2&&void 0!==s[2]?s[2]:{},"undefined"!=typeof Buffer){e.next=8;break}if("undefined"!=typeof Blob){e.next=7;break}throw new Error("Current environment do not have neither Blob nor Buffer support.");case 7:throw new Error("This method is not compliant in underlying environment. Use `toBlob` instead.");case 8:return i=this.getExportCanvas(t),r.filters&&(o=Array.isArray(r.filters)?r.filters.slice():[r.filters],a=0,o.forEach((function(e){a|=i["PNG_FILTER_".concat(e.name)]})),Object.assign({},r,{filters:a})),e.abrupt("return",new Promise((function(e,t){return i.toBuffer((function(n,r){return n?t(n):e(r)}),n,r)})));case 11:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})}],[{key:"getDefaultSize",value:function(e,t){var n={};return"undefined"==typeof screen?(n.width=e,n.height=t):navigator.maxTouchPoints?(n.width=Math.max(screen.width,screen.height),n.height=n.width):(n.width=screen.width,n.height=screen.height),n}}]),e}();function hn(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return fn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return fn(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function fn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}Object.defineEnum(ln,"PNGFilterType",["NO","ALL","NONE","SUB","UP","AVG","PAETH"]);var dn=function(){function e(t,n){k(this,e),Object.defineProperty(this,"ctx",{value:t,enumerable:!0}),Object.defineProperty(this,"value",{value:n,enumerable:!0}),Object.defineProperty(this,"texture",{value:n,enumerable:!0})}var t;return S(e,[{key:"update",value:(t=be(Pe.mark((function e(t,n){var r,i,o,a,s,u,c;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!Array.isArray(t)){e.next=26;break}r=[],i=hn(t),e.prev=4,i.s();case 6:if((o=i.n()).done){e.next=14;break}return a=o.value,e.next=10,Jt(a);case 10:s=e.sent,r.push(s);case 12:e.next=6;break;case 14:e.next=19;break;case 16:e.prev=16,e.t0=e.catch(4),i.e(e.t0);case 19:return e.prev=19,i.f(),e.finish(19);case 22:this.completeMipMap(r),this.fill(r,n),e.next=31;break;case 26:return u=t,e.next=29,Jt(u);case 29:c=e.sent,this.fill(c);case 31:case"end":return e.stop()}}),e,this,[[4,16,19,22]])}))),function(e,n){return t.apply(this,arguments)})},{key:"completeMipMap",value:function(e){if(e.sort((function(e,t){return t.width-e.width})),1!=e.last.width)for(var t=e.last.width;t>1;){t/=2;var n=new it(e.last.width/2,e.last.height/2);n.getContext("2d").drawImage(e.last,0,0,n.width,n.height),e.push(n)}}},{key:"fill",value:function(e,t){var n=this,r=this.ctx,i=this.value;if(r.bindTexture(r.TEXTURE_2D,i),r.pixelStorei(r.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0),Array.isArray(e)){var o=e;this.size=[],o.forEach((function(e,t){r.texImage2D(r.TEXTURE_2D,t,r.RGBA,r.RGBA,r.UNSIGNED_BYTE,e),n.size.push({width:e.width,height:e.height}),e.close&&e.close()})),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MIN_FILTER,t?r.LINEAR_MIPMAP_LINEAR:r.LINEAR_MIPMAP_NEAREST),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MAG_FILTER,r.LINEAR)}else r.texImage2D(r.TEXTURE_2D,0,r.RGBA,r.RGBA,r.UNSIGNED_BYTE,e),this.size={width:e.width,height:e.height},e.close&&e.close();r.pixelStorei(r.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),r.bindTexture(r.TEXTURE_2D,null),this.logError(this.ctx,i.name)}},{key:"readPixels",value:function(){var e=this.ctx,t=this.value,n=function(n,r){var i=new Uint8Array(n.width*n.height*4);return e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,t,r),e.readPixels(0,0,n.width,n.height,e.RGBA,e.UNSIGNED_BYTE,i),i},r=e.createFramebuffer();e.bindFramebuffer(e.FRAMEBUFFER,r);var i=Array.isArray(this.size)?this.size.map(n):n(this.size,0);return e.deleteFramebuffer(r),i}},{key:"logError",value:function(){var e=this,t=this.ctx.getError();if(t>0){var n=Object.keys(this.ctx.constructor.prototype).filter((function(n){return e.ctx[n]===t})).join(" | ");console.error("WebGL error - ".concat(this.texture.name,": ").concat(t," - ").concat(n))}}}],[{key:"createInstance",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t.CLAMP_TO_EDGE,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t.NEAREST,i=t.createTexture();return t.bindTexture(t.TEXTURE_2D,i),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,n),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,n),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,r),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,r),t.bindTexture(t.TEXTURE_2D,null),new e(t,i)}}]),e}();function pn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var yn=function(e){T(n,e);var t=pn(n);function n(e){var r,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(k(this,n),(r=t.call(this)).inkGLContext=e,r.gl=e.gl,r.scaleFactor=i.scaleFactor||1,r.flipY=!!i.flipY,r.useTextureStorage=!1,i.renderbuffer){if(!i.framebuffer)throw new Error("`framebuffer` is required when `renderbuffer` available");var o=r.initWithGLBuffers(i.framebuffer,i.renderbuffer);r.flipY=!0,r.ownGLResources=!!i.ownGLResources,r.setDimensions(o.width,o.height)}else if(i.framebuffer){var a=r.initWithGLFramebuffer(i.framebuffer);r.flipY=!0,r.ownGLResources=!!i.ownGLResources,r.setDimensions(a.width,a.height)}else if(i.texture){if(!(i.texture instanceof WebGLTexture))throw new Error("`texture` is not instance of WebGLTexture");var s;if(r.texture=i.texture,r.useTextureStorage=!0,i.width>0&&i.height>0)s={width:i.width,height:i.height};else if(r.texture.image)s={width:r.texture.image.width,height:r.texture.image.height};else{if(!r.texture.size)throw new Error("`width` and `height` are required when `texture` is available");s=r.texture.size}r.ownGLResources=!!i.ownGLResources,r.setDimensions(s.width,s.height)}else if(r.setDimensions(i.width,i.height),i.display)r.framebuffer=null,r.renderbuffer=null,r.texture=null;else if(r.useTextureStorage=!i.useBuffersStorage,r.ownGLResources=!0,r.useTextureStorage){var u=r.inkGLContext.generateBuffersExt(r.storageWidth,r.storageHeight,r.gl.LINEAR,r.gl.UNSIGNED_BYTE);r.framebuffer=u.framebuffer,r.texture=u.texture}else{var c=r.inkGLContext.genFrameAndRenders(r.gl.RGBA8||r.gl.RGBA4,r.storageWidth,r.storageHeight);r.framebuffer=c.framebuffer,r.renderbuffer=c.renderbuffer}return r.releaseRenderbuffer=!1,r.deleted=!1,r}return S(n,[{key:"initWithGLFramebuffer",value:function(e){if(!(e instanceof WebGLFramebuffer))throw new Error("`framebuffer` is not instance of WebGLFramebuffer");var t,n=this.gl.getParameter(this.gl.RENDERBUFFER_BINDING);if(this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,e),this.gl.getError()==this.gl.INVALID_OPERATION)throw new Error("Invalid framebuffer");if(this.gl.checkFramebufferStatus(this.gl.FRAMEBUFFER)!=this.gl.FRAMEBUFFER_COMPLETE)throw new Error("Incomplete framebuffer");if(this.gl.getFramebufferAttachmentParameter(this.gl.FRAMEBUFFER,this.gl.COLOR_ATTACHMENT0,this.gl.FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE)!=this.gl.RENDERBUFFER)throw new Error("Renderbuffer attachment not found");return t=this.gl.getFramebufferAttachmentParameter(this.gl.FRAMEBUFFER,this.gl.COLOR_ATTACHMENT0,this.gl.FRAMEBUFFER_ATTACHMENT_OBJECT_NAME),this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,n),this.initWithGLBuffers(e,t)}},{key:"initWithGLBuffers",value:function(e,t){if(!(e instanceof WebGLFramebuffer))throw new Error("`framebuffer` is not instance of WebGLFramebuffer");if(!(t instanceof WebGLRenderbuffer))throw new Error("`renderbuffer` is not instance of WebGLRenderbuffer");var n=this.gl.getParameter(this.gl.RENDERBUFFER_BINDING);this.gl.bindRenderbuffer(this.gl.RENDERBUFFER,t);var r=this.gl.getRenderbufferParameter(this.gl.RENDERBUFFER,this.gl.RENDERBUFFER_WIDTH),i=this.gl.getRenderbufferParameter(this.gl.RENDERBUFFER,this.gl.RENDERBUFFER_HEIGHT);return this.gl.bindRenderbuffer(this.gl.RENDERBUFFER,n),this.framebuffer=e,this.renderbuffer=t,{width:r,height:i}}},{key:"setDimensions",value:function(e,t){var n=Math.ceil(e*this.scaleFactor),r=Math.ceil(t*this.scaleFactor),i=new J(0,0,e,t),o=new J(0,0,n,r);Object.defineProperties(this,{width:{value:e,enumerable:!0,configurable:!0},height:{value:t,enumerable:!0,configurable:!0},graphicsBounds:{value:i,enumerable:!0,configurable:!0},storageWidth:{value:n,enumerable:!0,configurable:!0},storageHeight:{value:r,enumerable:!0,configurable:!0},storageBounds:{value:o,enumerable:!0,configurable:!0}})}},{key:"resize",value:function(e,t){e>0&&t>0&&(this.width!=e||this.height!=t)&&(this.setDimensions(e,t),this.useTextureStorage?(this.gl.bindTexture(this.gl.TEXTURE_2D,this.texture),this.gl.texImage2D(this.gl.TEXTURE_2D,0,this.gl.RGBA,this.storageWidth,this.storageHeight,0,this.gl.RGBA,this.gl.UNSIGNED_BYTE,null)):this.renderbuffer&&(this.gl.bindRenderbuffer(this.gl.RENDERBUFFER,this.renderbuffer),this.gl.renderbufferStorage(this.gl.RENDERBUFFER,this.gl.RGBA8||this.gl.RGBA4,this.storageWidth,this.storageHeight)))}},{key:"fillTexture",value:function(e){if(!this.texture)throw new Error("Underlying layer is not texture based");new dn(this.gl,this.texture).fill(e)}},{key:"delete",value:function(){this.ownGLResources&&(this.useTextureStorage?this.inkGLContext.deleteBuffers(this.framebuffer,this.texture):this.inkGLContext.deleteFrameAndRender(this.framebuffer,this.renderbuffer)),this.releaseRenderbuffer&&this.renderbuffer&&(this.gl.deleteRenderbuffer(this.renderbuffer),this.renderbuffer=null,this.releaseRenderbuffer=!1),this.deleted=!0}},{key:"deleteLater",value:function(){var e=this;setTimeout((function(){return e.delete()}),0)}},{key:"isDeleted",value:function(){return this.deleted}}]),n}(ln),vn=function(){function e(t){k(this,e),this.gl=t.gl,this.inkGLContext=t,this.program=this.createProgram(this.compileShader(this.constructor.getVertexShader(),this.gl.VERTEX_SHADER),this.compileShader(this.constructor.getFragmentShader(this.gl.getContextAttributes().premultipliedAlpha),this.gl.FRAGMENT_SHADER)),this.contextChanged=!0}return S(e,[{key:"init",value:function(){}},{key:"compileShader",value:function(e,t){var n=this.gl.createShader(t);if(this.gl.shaderSource(n,e),this.gl.compileShader(n),!this.gl.getShaderParameter(n,this.gl.COMPILE_STATUS))throw new Error("".concat(this.constructor.name," could not compile ").concat(t==this.gl.VERTEX_SHADER?"VERTEX":"FRAGMENT"," shader: ").concat(this.gl.getShaderInfoLog(n)));return n}},{key:"createProgram",value:function(e,t){var n=this.gl.createProgram();if(this.gl.attachShader(n,e),this.gl.attachShader(n,t),this.gl.linkProgram(n),!this.gl.getProgramParameter(n,this.gl.LINK_STATUS))throw new Error("".concat(this.constructor.name," failed to link: ").concat(this.gl.getProgramInfoLog(n)));return n}},{key:"onActivate",value:function(){}},{key:"onDeactivate",value:function(){}},{key:"onContextChange",value:function(){}},{key:"activate",value:function(){this.inkGLContext.activateProgram(this)}}]),e}();function mn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var gn=function(e){T(n,e);var t=mn(n);function n(e){var r;return k(this,n),(r=t.call(this,e)).buffer=r.gl.createBuffer(),r}return S(n,[{key:"init",value:function(){this.a_position=this.gl.getAttribLocation(this.program,"a_position"),this.u_color=this.gl.getUniformLocation(this.program,"u_color"),this.u_projectionMatrix=this.gl.getUniformLocation(this.program,"u_projectionMatrix"),this.u_viewMatrix=this.gl.getUniformLocation(this.program,"u_viewMatrix")}},{key:"onContextChange",value:function(){this.gl.uniform4f(this.u_color,this.color.red,this.color.green,this.color.blue,this.color.alpha),this.gl.uniformMatrix4fv(this.u_projectionMatrix,!1,this.inkGLContext.graphicsSpaceToFramebufferSpaceT),this.gl.uniformMatrix4fv(this.u_viewMatrix,!1,this.inkGLContext.transform?this.inkGLContext.transform.value:cn.IDENTITY_MATRIX)}},{key:"onDeactivate",value:function(){this.gl.disableVertexAttribArray(this.a_position),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,null)}},{key:"drawVertices",value:function(e,t,n){var r,i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=arguments.length>5&&void 0!==arguments[5]&&arguments[5];this.color=n.premultiply(),i&&!a?(t=this.antialias(t),r=un.LIGHTER):(Array.isArray(t)&&(t=new Float32Array(t)),r=a?un.MAX:un.SOURCE_OVER);var s=t.length/2;this.activate(),this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,e),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,this.buffer),this.gl.bufferData(this.gl.ARRAY_BUFFER,t,this.gl.STATIC_DRAW),this.gl.enableVertexAttribArray(this.a_position),this.gl.vertexAttribPointer(this.a_position,2,this.gl.FLOAT,!1,0,0),o&&(this.inkGLContext.blendMode=un.DIRECT_DESTINATION_OUT,this.gl.drawArrays(this.gl.TRIANGLES,0,s)),this.inkGLContext.blendMode=r,this.gl.drawArrays(this.gl.TRIANGLES,0,s)}},{key:"antialias",value:function(e){for(var t=cn.ANTIALIASING,n=t.passesSqrt,r=t.passes,i=t.spread,o=t.step,a=t.weight,s=new Float32Array(e.length*r),u=0,c=0;c<n;c++)for(var l=0;l<n;l++)for(var h=c*o-(i-o)/2,f=l*o-(i-o)/2,d=0;d<e.length;d+=2)s[u++]=e[d]+h,s[u++]=e[d+1]+f;return this.color={red:this.color.red*a,green:this.color.green*a,blue:this.color.blue*a,alpha:this.color.alpha*a},s}}],[{key:"getVertexShader",value:function(){return"\n\t\t\tprecision ".concat(cn.VERTEX_SHADER_PRECISION," float;\n\n\t\t\tuniform mat4 u_projectionMatrix;\n\t\t\tuniform mat4 u_viewMatrix;\n\t\t\tuniform lowp vec4 u_color;\n\n\t\t\tattribute highp vec4 a_position;\n\n\t\t\tvarying lowp vec4 v_color;\n\n\t\t\tvoid main() {\n\t\t\t\tvec4 viewPosition = u_viewMatrix * a_position;\n\n\t\t\t\tgl_Position = u_projectionMatrix * viewPosition;\n\t\t\t\tv_color = u_color;\n\t\t\t}\n\t\t")}},{key:"getFragmentShader",value:function(){return"\n\t\t\tvarying lowp vec4 v_color;\n\n\t\t\tvoid main() {\n\t\t\t\tgl_FragColor = v_color;\n\t\t\t}\n\t\t"}}]),n}(vn);function bn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var En=16,Pn=En*Float32Array.BYTES_PER_ELEMENT,kn=function(e){T(n,e);var t=bn(n);function n(e){var r;return k(this,n),(r=t.call(this,e)).vao=r.gl.createVertexArray(),r.positionBuffer=r.gl.createBuffer(),r.colorBuffer=r.gl.createBuffer(),r.matrixBuffer=r.gl.createBuffer(),r}return S(n,[{key:"init",value:function(){this.a_position=this.gl.getAttribLocation(this.program,"a_position"),this.a_color=this.gl.getAttribLocation(this.program,"a_color"),this.a_matrix=this.gl.getAttribLocation(this.program,"a_matrix"),this.u_viewMatrix=this.gl.getUniformLocation(this.program,"u_viewMatrix")}},{key:"onContextChange",value:function(){this.gl.uniformMatrix4fv(this.u_viewMatrix,!1,this.inkGLContext.transform?this.inkGLContext.transform.value:cn.IDENTITY_MATRIX)}},{key:"onDeactivate",value:function(){this.gl.disableVertexAttribArray(this.a_position),this.gl.disableVertexAttribArray(this.a_color),this.gl.disableVertexAttribArray(this.a_matrix),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,null),this.gl.bindVertexArray(null)}},{key:"drawVertices",value:function(e,t,n){var r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=arguments.length>5&&void 0!==arguments[5]&&arguments[5];n=n.premultiply(),Array.isArray(t)&&(t=new Float32Array(t));var s,u,c,l=1,h=t.length/2,f=this.inkGLContext.graphicsSpaceToFramebufferSpaceT;if(r&&!a){var d=cn.ANTIALIASING,p=d.passesSqrt,y=d.passes,v=d.spread,m=d.step,g=d.weight,b=[n.red*g,n.green*g,n.blue*g,n.alpha*g];l=y,s=new Float32Array(4*l),u=new Float32Array(l*En);for(var E=0;E<l;++E)for(var P=0;P<4;P++)s[4*E+P]=b[P];for(var k=0;k<p;k++)for(var w=0;w<p;w++){for(var S=p*k+w,I=S*En,x=I*Float32Array.BYTES_PER_ELEMENT,R=0;R<En;R++)u[I+R]=f[R];var T=new Float32Array(u.buffer,x,En),A=k*m-(v-m)/2,O=w*m-(v-m)/2;i.mat4.translate(T,T,i.vec4.fromValues(A,O,0,1))}c=un.LIGHTER}else s=new Float32Array([n.red,n.green,n.blue,n.alpha]),u=f,c=a?un.MAX:un.SOURCE_OVER;this.activate(),this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,e),this.gl.bindVertexArray(this.vao),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,this.positionBuffer),this.gl.bufferData(this.gl.ARRAY_BUFFER,t,this.gl.STATIC_DRAW),this.gl.enableVertexAttribArray(this.a_position),this.gl.vertexAttribPointer(this.a_position,2,this.gl.FLOAT,!1,0,0),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,this.colorBuffer),this.gl.bufferData(this.gl.ARRAY_BUFFER,s,this.gl.STATIC_DRAW),this.gl.enableVertexAttribArray(this.a_color),this.gl.vertexAttribPointer(this.a_color,4,this.gl.FLOAT,!1,0,0),this.gl.vertexAttribDivisor(this.a_color,1),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,this.matrixBuffer),this.gl.bufferData(this.gl.ARRAY_BUFFER,u,this.gl.STATIC_DRAW);for(var C=0;C<4;C++){var D=this.a_matrix+C,M=16*C;this.gl.enableVertexAttribArray(D),this.gl.vertexAttribPointer(D,4,this.gl.FLOAT,!1,Pn,M),this.gl.vertexAttribDivisor(D,1)}o&&(this.inkGLContext.blendMode=un.DIRECT_DESTINATION_OUT,this.gl.drawArraysInstanced(this.gl.TRIANGLES,0,h,l)),this.inkGLContext.blendMode=c,this.gl.drawArraysInstanced(this.gl.TRIANGLES,0,h,l)}}],[{key:"getVertexShader",value:function(){return"#version 300 es\n\t\t\tin vec4 a_position;\n\t\t\tin vec4 a_color;\n\t\t\tin mat4 a_matrix;\n\n\t\t\tuniform mat4 u_viewMatrix;\n\n\t\t\tout vec4 v_color;\n\n\t\t\tvoid main() {\n\t\t\t\tvec4 viewPosition = u_viewMatrix * a_position;\n\n\t\t\t\tgl_Position = a_matrix * viewPosition;\n\t\t\t\tv_color = a_color;\n\t\t\t}\n\t\t"}},{key:"getFragmentShader",value:function(){return"#version 300 es\n\t\t\tprecision highp float;\n\n\t\t\tin vec4 v_color;\n\n\t\t\tout vec4 outColor;\n\n\t\t\tvoid main() {\n\t\t\t\toutColor = v_color;\n\t\t\t}\n\t\t"}}]),n}(vn);function wn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Sn=function(e){T(n,e);var t=wn(n);function n(e){var r;return k(this,n),(r=t.call(this,e)).destBuffer=r.gl.createBuffer(),r.srcBuffer=r.gl.createBuffer(),r}return S(n,[{key:"init",value:function(){this.a_position=this.gl.getAttribLocation(this.program,"a_position"),this.a_srcPosition=this.gl.getAttribLocation(this.program,"a_srcPosition"),this.u_texture=this.gl.getUniformLocation(this.program,"u_texture"),this.u_projectionMatrix=this.gl.getUniformLocation(this.program,"u_projectionMatrix"),this.u_textureMatrix=this.gl.getUniformLocation(this.program,"u_textureMatrix")}},{key:"onDeactivate",value:function(){this.gl.bindBuffer(this.gl.ARRAY_BUFFER,null)}},{key:"drawTexture",value:function(e,t,n,r,i){this.activate(),this.gl.disable(this.gl.DEPTH_TEST),this.gl.disable(this.gl.STENCIL_TEST),this.gl.activeTexture(this.gl.TEXTURE0),this.gl.bindTexture(this.gl.TEXTURE_2D,e),this.gl.uniform1i(this.u_texture,0),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,this.destBuffer),this.gl.bufferData(this.gl.ARRAY_BUFFER,t,this.gl.DYNAMIC_DRAW),this.gl.enableVertexAttribArray(this.a_position),this.gl.vertexAttribPointer(this.a_position,2,this.gl.FLOAT,!1,0,0),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,this.srcBuffer),this.gl.bufferData(this.gl.ARRAY_BUFFER,n,this.gl.DYNAMIC_DRAW),this.gl.enableVertexAttribArray(this.a_srcPosition),this.gl.vertexAttribPointer(this.a_srcPosition,2,this.gl.FLOAT,!1,0,0),this.gl.uniformMatrix4fv(this.u_projectionMatrix,!1,r),this.gl.uniformMatrix4fv(this.u_textureMatrix,!1,i),this.inkGLContext.drawArrays(this.gl.TRIANGLE_STRIP,0,4)}}],[{key:"getVertexShader",value:function(){return"\n\t\t\tprecision ".concat(cn.VERTEX_SHADER_PRECISION," float;\n\n\t\t\tuniform mat4 u_projectionMatrix;\n\t\t\tuniform mat4 u_textureMatrix;\n\t\t\tattribute highp vec4 a_position;\n\t\t\tattribute highp vec4 a_srcPosition;\n\t\t\tvarying highp vec2 v_textureCoordinate;\n\n\t\t\tvoid main() {\n\t\t\t\tgl_Position = u_projectionMatrix * a_position;\n\t\t\t\tv_textureCoordinate = (u_textureMatrix * a_srcPosition).xy;\n\t\t\t}\n\t\t")}},{key:"getFragmentShader",value:function(){return"\n\t\t\tprecision ".concat(cn.FRAGMENT_SHADER_PRECISION," float;\n\n\t\t\tvarying vec2 v_textureCoordinate;\n\t\t\tuniform lowp sampler2D u_texture;\n\n\t\t\tvoid main() {\n\t\t\t\tgl_FragColor = texture2D(u_texture, v_textureCoordinate);\n\t\t\t}\n\t\t")}}]),n}(vn);function In(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return xn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return xn(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function xn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Rn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Tn=function(e){T(c,e);var t,n,r,i,o,a,s,u=Rn(c);function c(e,t,n){var r,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};k(this,c),(r=u.call(this,e)).spacing=i.spacing||.15,r.scattering=i.scattering||0,r.rotationMode=i.rotationMode||c.RotationMode.RANDOM;var a=i.blendMode||un.SOURCE_OVER,s=void 0;return Object.defineProperty(A(r),"blendMode",{get:function(){return a},set:function(e){if(!e)throw new Error("BrushGL blendMode is required");a=e}}),Object.defineProperty(A(r),"particleSettings",{get:function(){return{spacing:r.spacing,scattering:r.scattering,blendMode:r.blendMode,rotationMode:r.rotationMode}},enumerable:!0}),Object.defineProperty(A(r),"fillTextureSettings",{get:function(){return{randomize:r.randomizeFill,size:r.fillTextureSize,offset:r.fillTextureOffset}},enumerable:!0}),Object.defineProperty(A(r),"descriptor",{value:{shape:void 0,fill:void 0},enumerable:!0}),Object.defineProperty(A(r),"shape",{get:function(){return Array.isArray(r.descriptor.shape)?r.descriptor.shape.map((function(e){return e.value})):r.descriptor.shape.value},enumerable:!0}),Object.defineProperty(A(r),"fill",{get:function(){return r.descriptor.fill.value},enumerable:!0}),Object.defineProperty(A(r),"encoding",{get:function(){return s},set:function(e){s=e,Array.isArray(r.descriptor.shape)?r.descriptor.shape.forEach((function(t){return t.encoding=e})):r.descriptor.shape.encoding=e,r.descriptor.fill.encoding=e},enumerable:!0}),r.updateShape(t),r.updateFill(n,o),r}return S(c,[{key:"updateShape",value:(s=be(Pe.mark((function e(t){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Array.isArray(t)?t=t.map((function(e){return"string"==typeof e?ht.getInstance(e):e instanceof ht?e:new ht(e.name,e.value)})):"string"==typeof t?t=ht.getInstance(t):t instanceof ht||(t=new ht(t.name,t.value)),this.descriptor.shape=t,!this.ctx){e.next=5;break}return e.next=5,this.configureShape();case 5:case"end":return e.stop()}}),e,this)}))),function(e){return s.apply(this,arguments)})},{key:"updateFill",value:(a=be(Pe.mark((function e(t){var n,r=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=r.length>1&&void 0!==r[1]?r[1]:{},this.randomizeFill=!("randomize"in n)||n.randomize,this.fillTextureSize=n.size,this.fillTextureOffset=n.offset||{x:0,y:0},!Array.isArray(t)){e.next=6;break}throw new Error("Mipmap is not compatible whith fill texture");case 6:if("string"==typeof t?t=ht.getInstance(t):t instanceof ht||(t=new ht(t.name,t.value)),this.descriptor.fill=t,!this.ctx){e.next=11;break}return e.next=11,this.configureFill();case 11:case"end":return e.stop()}}),e,this)}))),function(e){return a.apply(this,arguments)})},{key:"configure",value:(o=be(Pe.mark((function e(t){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.ctx=t,e.next=3,this.configureShape();case 3:return e.next=5,this.configureFill();case 5:case"end":return e.stop()}}),e,this)}))),function(e){return o.apply(this,arguments)})},{key:"configureShape",value:(i=be(Pe.mark((function e(){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.shapeTexture||(this.shapeTexture=dn.createInstance(this.ctx,this.ctx.CLAMP_TO_EDGE,this.ctx.LINEAR)),e.next=3,this.shapeTexture.update(this.shape,this.spacing<=1);case 3:case"end":return e.stop()}}),e,this)}))),function(){return i.apply(this,arguments)})},{key:"configureFill",value:(r=be(Pe.mark((function e(){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.fillTexture||(this.fillTexture=dn.createInstance(this.ctx,this.ctx.REPEAT,this.ctx.NEAREST)),e.next=3,this.fillTexture.update(this.fill);case 3:this.fillTextureSize||(this.fillTextureSize=this.fillTexture.size);case 4:case"end":return e.stop()}}),e,this)}))),function(){return r.apply(this,arguments)})},{key:"getShapeBinary",value:(n=be(Pe.mark((function e(){var t,n,r,i,o,a;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!Array.isArray(this.shape)){e.next=24;break}n=[],r=In(this.shape),e.prev=3,r.s();case 5:if((i=r.n()).done){e.next=13;break}return o=i.value,e.next=9,Ht(o);case 9:a=e.sent,n.push(a);case 11:e.next=5;break;case 13:e.next=18;break;case 15:e.prev=15,e.t0=e.catch(3),r.e(e.t0);case 18:return e.prev=18,r.f(),e.finish(18);case 21:t=n,e.next=27;break;case 24:return e.next=26,Ht(this.shape);case 26:t=e.sent;case 27:return e.abrupt("return",t);case 28:case"end":return e.stop()}}),e,this,[[3,15,18,21]])}))),function(){return n.apply(this,arguments)})},{key:"getFillBinary",value:(t=be(Pe.mark((function e(){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Ht(this.fill);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"toJSON",value:function(){return{type:"BrushGL",name:this.name,shape:Array.isArray(this.descriptor.shape)?this.descriptor.shape.map((function(e){return e.toJSON()})):this.descriptor.shape.toJSON(),fill:this.descriptor.fill.toJSON(),particleSettings:{spacing:this.spacing,scattering:this.scattering,blendMode:this.blendMode.name,rotationMode:this.rotationMode.name},fillTextureSettings:{randomize:this.randomizeFill,size:this.fillTextureSize,offset:this.fillTextureOffset}}}},{key:"equals",value:function(e){return e==this&&e.shapeTexture==this.shapeTexture&&e.fillTexture==this.fillTexture}},{key:"delete",value:function(){this.deleteShape(),this.deleteFill(),delete this.ctx}},{key:"deleteShape",value:function(){this.shapeTexture&&(this.ctx.deleteTexture(this.shapeTexture.texture),delete this.shapeTexture)}},{key:"deleteFill",value:function(){this.fillTexture&&(this.ctx.deleteTexture(this.fillTexture.texture),delete this.fillTexture)}}],[{key:"fromJSON",value:function(e){e.particleSettings.blendMode=un[e.particleSettings.blendMode],e.particleSettings.rotationMode=c.RotationMode[e.particleSettings.rotationMode];var t=Array.isArray(e.shape)?e.shape.map((function(e){return ht.fromJSON(e)})):ht.fromJSON(e.shape),n=ht.fromJSON(e.fill);return new c(e.name,t,n,e.particleSettings,e.fillTextureSettings)}}]),c}(ft);function An(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}Object.defineEnum(Tn,"RotationMode",["NONE","RANDOM","TRAJECTORY"]);var On=function(e){T(n,e);var t=An(n);function n(e){var r;return k(this,n),(r=t.call(this,e)).buffer=r.gl.createBuffer(),r.indexBuffer=r.gl.createBuffer(),r}return S(n,[{key:"init",value:function(){this.a_coordinates=this.gl.getAttribLocation(this.program,"a_coordinates"),this.a_xys=this.gl.getAttribLocation(this.program,"a_xys"),this.a_spriteRotation=this.gl.getAttribLocation(this.program,"a_spriteRotation"),this.a_spriteScaleAndOffset=this.gl.getAttribLocation(this.program,"a_spriteScaleAndOffset"),this.a_color=this.gl.getAttribLocation(this.program,"a_color"),this.a_velocity=this.gl.getAttribLocation(this.program,"a_velocity"),this.a_transformParams=this.gl.getAttribLocation(this.program,"a_transformParams"),this.u_projectionMatrix=this.gl.getUniformLocation(this.program,"u_projectionMatrix"),this.u_viewMatrix=this.gl.getUniformLocation(this.program,"u_viewMatrix"),this.u_fillTextureSize=this.gl.getUniformLocation(this.program,"u_fillTextureSize"),this.u_fillTextureOffset=this.gl.getUniformLocation(this.program,"u_fillTextureOffset"),this.u_shapeTexture=this.gl.getUniformLocation(this.program,"u_shapeTexture"),this.u_fillTexture=this.gl.getUniformLocation(this.program,"u_fillTexture")}},{key:"setBrush",value:function(e){this.brush=e,this.brushChanged=!0}},{key:"onActivate",value:function(){this.brushChanged=!0,this.gl.bindBuffer(this.gl.ARRAY_BUFFER,this.buffer),this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER,this.indexBuffer);var e=19*Float32Array.BYTES_PER_ELEMENT;this.gl.vertexAttribPointer(this.a_coordinates,2,this.gl.FLOAT,!1,e,0*Float32Array.BYTES_PER_ELEMENT),this.gl.vertexAttribPointer(this.a_xys,3,this.gl.FLOAT,!1,e,2*Float32Array.BYTES_PER_ELEMENT),this.gl.vertexAttribPointer(this.a_spriteRotation,1,this.gl.FLOAT,!1,e,5*Float32Array.BYTES_PER_ELEMENT),this.gl.vertexAttribPointer(this.a_spriteScaleAndOffset,4,this.gl.FLOAT,!1,e,6*Float32Array.BYTES_PER_ELEMENT),this.gl.vertexAttribPointer(this.a_color,4,this.gl.FLOAT,!1,e,10*Float32Array.BYTES_PER_ELEMENT),this.gl.vertexAttribPointer(this.a_velocity,2,this.gl.FLOAT,!1,e,14*Float32Array.BYTES_PER_ELEMENT),this.gl.vertexAttribPointer(this.a_transformParams,3,this.gl.FLOAT,!1,e,16*Float32Array.BYTES_PER_ELEMENT),this.gl.enableVertexAttribArray(this.a_coordinates),this.gl.enableVertexAttribArray(this.a_xys),this.gl.enableVertexAttribArray(this.a_spriteRotation),this.gl.enableVertexAttribArray(this.a_spriteScaleAndOffset),this.gl.enableVertexAttribArray(this.a_color),this.gl.enableVertexAttribArray(this.a_velocity),this.gl.enableVertexAttribArray(this.a_transformParams)}},{key:"onContextChange",value:function(){this.gl.uniformMatrix4fv(this.u_projectionMatrix,!1,this.inkGLContext.graphicsSpaceToFramebufferSpaceT),this.gl.uniformMatrix4fv(this.u_viewMatrix,!1,this.inkGLContext.transform?this.inkGLContext.transform.value:cn.IDENTITY_MATRIX)}},{key:"onDeactivate",value:function(){this.gl.disableVertexAttribArray(this.a_coordinates),this.gl.disableVertexAttribArray(this.a_xys),this.gl.disableVertexAttribArray(this.a_spriteRotation),this.gl.disableVertexAttribArray(this.a_spriteScaleAndOffset),this.gl.disableVertexAttribArray(this.a_color),this.gl.disableVertexAttribArray(this.a_velocity),this.gl.disableVertexAttribArray(this.a_transformParams),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,null)}},{key:"updateTextures",value:function(){this.brushChanged&&(this.brushChanged=!1,this.gl.uniform2f(this.u_fillTextureOffset,this.brush.fillTextureOffset.x,this.brush.fillTextureOffset.y),this.gl.uniform2f(this.u_fillTextureSize,this.brush.fillTextureSize.width,this.brush.fillTextureSize.height),this.gl.activeTexture(this.gl.TEXTURE0),this.gl.bindTexture(this.gl.TEXTURE_2D,this.brush.shapeTexture.value),this.gl.uniform1i(this.u_shapeTexture,0),this.gl.activeTexture(this.gl.TEXTURE1),this.gl.bindTexture(this.gl.TEXTURE_2D,this.brush.fillTexture.value),this.gl.uniform1i(this.u_fillTexture,1))}},{key:"drawSprites",value:function(e){var t;this.activate(),this.updateTextures();for(var n=Math.ceil(e.length/cn.VERTEX_BATCH_SIZE),r=0;r<n;r++){t=this.drawSpritesBatch(e,r).union(t)}return t}},{key:"drawSpritesBatch",value:function(e,t){var n,r=this,i=t*cn.VERTEX_BATCH_SIZE,o=i+cn.VERTEX_BATCH_SIZE;o>e.length&&(o=i+e.length%cn.VERTEX_BATCH_SIZE);for(var a=o-i,s=new Float32Array(76*a),u=new Uint16Array(6*a),c=this.brush.rotationMode==Tn.RotationMode.TRAJECTORY?1:0,l=me.createInstance(e.layout),h=function(t){l.fill(t,e.points,e.layout,e.style);var o=76*(t-i),a=J.calculateBrushGLSegmentBounds(l,r.brush.scattering);n=a.union(n);var u=l.red/255*l.alpha,h=l.green/255*l.alpha,f=l.blue/255*l.alpha;0==l.dX&&0==l.dY&&(l.dX=2*r.inkGLContext.random()-1,l.dY=2*r.inkGLContext.random()-1);var d=r.brush.rotationMode==Tn.RotationMode.RANDOM?2*r.inkGLContext.random()*Math.PI:0,p=(2*r.inkGLContext.random()-1)*r.brush.scattering;J.SQURE.forEach((function(e,t){var n=o+19*t;s[n]=e.x,s[n+1]=e.y,s[n+2]=l.x,s[n+3]=l.y,s[n+4]=l.size,s[n+5]=l.rotation,s[n+6]=l.scaleX,s[n+7]=l.scaleY,s[n+8]=l.offsetX,s[n+9]=l.offsetY,s[n+10]=u,s[n+11]=h,s[n+12]=f,s[n+13]=l.alpha,s[n+14]=l.dX,s[n+15]=l.dY,s[n+16]=c,s[n+17]=d,s[n+18]=p}))},f=i;f<o;f++)h(f);for(var d=0,p=0;p<u.length;p+=6)u[p]=d,u[p+1]=d+1,u[p+2]=d+2,u[p+3]=d+2,u[p+4]=d+1,u[p+5]=d+3,d+=4;return this.gl.bufferData(this.gl.ARRAY_BUFFER,s,this.gl.STATIC_DRAW),this.gl.bufferData(this.gl.ELEMENT_ARRAY_BUFFER,u,this.gl.STATIC_DRAW),this.inkGLContext.drawElements(this.gl.TRIANGLES,u.length,this.gl.UNSIGNED_SHORT,0),n}}],[{key:"getVertexShader",value:function(){return"\n\t\t\tprecision ".concat(cn.VERTEX_SHADER_PRECISION," float;\n\n\t\t\tuniform mat4 u_projectionMatrix;\n\t\t\tuniform mat4 u_viewMatrix;\n\t\t\tuniform vec2 u_fillTextureSize;\n\t\t\tuniform vec2 u_fillTextureOffset;\n\n\t\t\tattribute lowp vec4 a_color;\n\t\t\t/* (x,y) is the center. z is the offset by x, w is the offset by y */\n\t\t\tattribute highp vec2 a_coordinates;\n\t\t\t/* the velocity at that point. It is the derivative of the function */\n\t\t\tattribute vec2 a_velocity;\n\t\t\t/* 1-shape trajectory rotation coficient, 2-shape randrom rotation angle, 3-scattering value */\n\t\t\tattribute vec3 a_transformParams;\n\n\t\t\t/* sprite rotation angle */\n\t\t\tattribute highp float a_spriteRotation;\n\t\t\t/* sprite's: 1-scaleX, 2-scaleY, 3-offsetX, 4-offsetY */\n\t\t\tattribute highp vec4 a_spriteScaleAndOffset;\n\n\t\t\tvarying lowp vec4 v_color;\n\t\t\tattribute vec3 a_xys;\n\t\t\tvarying highp vec2 v_shapeTexturePosition;\n\t\t\tvarying highp vec2 v_fillTexturePosition;\n\n\t\t\tmat2 trajectoryRotate(vec2 vNormal, float value) {\n\t\t\t\tvec2 base = vec2(1.0, 0.0);\n\n\t\t\t\tvec2 vNormalR = mix(base, vNormal, value);\n\t\t\t\tvNormalR = normalize(vNormalR);\n\n\t\t\t\tfloat cosfi = dot(vNormalR, base);\n\t\t\t\tfloat sinfi = vNormalR.x * base.y - vNormalR.y * base.x;\n\n\t\t\t\treturn mat2(\n\t\t\t\t\tcosfi, -sinfi,\n\t\t\t\t\tsinfi, cosfi\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tmat2 angleRotate2(float fi) {\n\t\t\t\tfloat sinfi = sin(fi);\n\t\t\t\tfloat cosfi = cos(fi);\n\n\t\t\t\treturn mat2(\n\t\t\t\t\tcosfi, -sinfi,\n\t\t\t\t\tsinfi, cosfi\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tmat2 scale2(float sx, float sy) {\n\t\t\t\treturn mat2(sx, 0.0, 0.0, sy);\n\t\t\t}\n\n\t\t\tmat3 rotate2h(float fi) {\n\t\t\t\tfloat sinfi = sin(fi);\n\t\t\t\tfloat cosfi = cos(fi);\n\n\t\t\t\treturn mat3(\n\t\t\t\t\tcosfi,  sinfi, 0.0,\n\t\t\t\t\t-sinfi, cosfi, 0.0,\n\t\t\t\t\t0.0,    0.0,   1.0\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tmat3 scale2h(float sx, float sy) {\n\t\t\t\treturn mat3(\n\t\t\t\t\tsx,  0.0, 0.0,\n\t\t\t\t\t0.0, sy,  0.0,\n\t\t\t\t\t0.0, 0.0, 1.0\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tmat3 translate2h(float tx, float ty) {\n\t\t\t\treturn mat3(\n\t\t\t\t\t1.0, 0.0, 0.0,\n\t\t\t\t\t0.0, 1.0, 0.0,\n\t\t\t\t\ttx,  ty,  1.0\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tvec2 orthoOffset(vec2 vNormal, float value) {\n\t\t\t\tvec2 vno = vec2(vNormal.y * value, vNormal.x * value);\n\t\t\t\treturn vno;\n\t\t\t}\n\n\t\t\tvoid main() {\n\t\t\t\t// Calculate radius from size\n\t\t\t\tfloat halfSize = a_xys.z / 2.0;\n\n\t\t\t\tvec2 vNormal = normalize(a_velocity);\n\n\t\t\t\t// Because this applied to the texture, the y reversed because the texture coordinate system y is increasing downwards.\n\t\t\t\tvNormal.y = -vNormal.y;\n\n\t\t\t\tfloat spriteRotation = a_spriteRotation;\n\t\t\t\tvec4 spriteScaleAndOffset = a_spriteScaleAndOffset;\n\t\t\t\tspriteScaleAndOffset.y = -spriteScaleAndOffset.y;\n\t\t\t\tspriteScaleAndOffset.w = -spriteScaleAndOffset.w;\n\n\t\t\t\tmat2 productRotateMatrix = angleRotate2(a_transformParams.y) * trajectoryRotate(vNormal, a_transformParams.x);\n\t\t\t\tvec2 scatterOffset = orthoOffset(vNormal, a_transformParams.z * halfSize);\n\t\t\t\tvec2 transformedCoordinates = productRotateMatrix * a_coordinates;\n\n\t\t\t\tvec3 coordinatesH = vec3(transformedCoordinates.x, transformedCoordinates.y, 1.0);\n\t\t\t\tcoordinatesH = rotate2h(spriteRotation) * translate2h(spriteScaleAndOffset.z, spriteScaleAndOffset.w) * scale2h(spriteScaleAndOffset.x * halfSize, spriteScaleAndOffset.y * halfSize) * coordinatesH;\n\t\t\t\tvec2 coordinates = coordinatesH.xy;\n\n\t\t\t\tvec2 offset = coordinates + scatterOffset;\n\t\t\t\tvec4 position = vec4(a_xys.x + offset.x, a_xys.y + offset.y, 0.0, 1.0);\n\n\t\t\t\tv_color = a_color;\n\n\t\t\t\tvec4 viewPosition = u_viewMatrix * position;\n\t\t\t\tgl_Position = u_projectionMatrix * viewPosition;\n\t\t\t\tv_fillTexturePosition = position.xy / u_fillTextureSize + u_fillTextureOffset;\n\t\t\t\tv_shapeTexturePosition = (a_coordinates + 1.0) * 0.5; /* [-1;1] -> [0;1] */\n\t\t\t}\n\t\t")}},{key:"getFragmentShader",value:function(e){return"\n\t\t\tuniform lowp sampler2D u_shapeTexture;\n\t\t\tuniform lowp sampler2D u_fillTexture;\n\n\t\t\t/* lowp seems to lead to an overflow on PowerVR SGX540, so use mediump instead :)*/\n\t\t\tvarying mediump vec4 v_color;\n\t\t\tvarying highp vec2 v_shapeTexturePosition;\n\t\t\tvarying highp vec2 v_fillTexturePosition;\n\n\t\t\tvoid main() {\n\t\t\t\tlowp vec4 c = v_color * texture2D(u_shapeTexture, v_shapeTexturePosition) * texture2D(u_fillTexture, v_fillTexturePosition);\n\n\t\t\t\tgl_FragColor = ".concat(e?"c":"vec4(c.x / c.w, c.y / c.w, c.z / c.w, c.w * 255.0)",";\n\t\t\t}\n\t\t")}}]),n}(vn),Cn=function(){function e(t,n){k(this,e);var r=t.getContext("webgl2",n)||t.getContext("webgl",n);if(this.debug){var i=new Proxy(r,{get:function(e,t,n){var r;return r="function"==typeof e[t]?Reflect.get.apply(Reflect,arguments):e[t],"getError"!=t&&cn.logGLError(e,t),r}});Object.getOwnPropertyNames(r.constructor.prototype).forEach((function(e){var t=r[e];"function"==typeof t&&(i[e]=t.bind(r))})),r=i}this.gl=r,this.glVersion=r.drawArraysInstanced?2:1,this.inkGLContext=new cn(r);var o=r.drawArraysInstanced?kn:gn;this.simpleProgram=new o(this.inkGLContext),this.textureProgram=new Sn(this.inkGLContext),this.spriteProgram=new On(this.inkGLContext),this.inkGLContext.programs=[this.simpleProgram,this.textureProgram,this.spriteProgram],this.currentTarget=null,this.inkGLContext.init(new J(0,0,0,0),new J(0,0,0,0))}return S(e,[{key:"createLayer",value:function(e,t){return new yn(this.inkGLContext,e,t)}},{key:"drawLayerWithTransform",value:function(e,t,n){if(this.currentTarget){var r=e.graphicsBounds.toQuad(),i=e.graphicsBounds.toQuad(t);this.drawLayer(e,r,i,n)}}},{key:"drawLayer",value:function(e,t,n,r){if(this.currentTarget&&e.useTextureStorage){var o=i.mat4.create(),a=i.mat4.create(),s=this.currentTarget;s.flipY?i.mat4.ortho(a,0,s.width,s.height,0,1,-1):i.mat4.ortho(a,0,s.width,0,s.height,1,-1),e.flipY?i.mat4.ortho(o,-e.width,e.width,2*e.height,0,0,1):i.mat4.ortho(o,-e.width,e.width,-e.height,e.height,0,1),this.inkGLContext.blendMode=r,this.textureProgram.drawTexture(e.texture,n,t,a,o)}}},{key:"setTarget",value:function(e,t){if(e&&!(e instanceof yn))throw new TypeError("layer must be unset or instanceof InkLayer");if(t&&!(t instanceof J))throw new TypeError("clipRect must be unset or instanceof RectGL");if(e){this.currentTarget=e;var n=new J(0,0,e.storageWidth,e.storageHeight),r=new J(0,0,e.width,e.height);this.inkGLContext.resize(n,r),this.inkGLContext.setUniforms(e.flipY),t?this.setTargetClipRect(t):this.disableTargetClipRect(),this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,e.framebuffer)}}},{key:"clearColor",value:function(e){this.inkGLContext.clearColorBuffer(e)}},{key:"setTargetClipRect",value:function(e){if(e=e.floor().intersect(this.currentTarget.graphicsBounds),this.currentTarget.flipY)e=new J(e.x,this.currentTarget.storageHeight-e.top,e.width,e.height);else{var t=this.currentTarget.scaleFactor;e=new J(e.x*t,e.y*t,e.width*t,e.height*t)}this.inkGLContext.clipRect=e,this.inkGLContext.scissors(e)}},{key:"disableTargetClipRect",value:function(){var e=this.inkGLContext;e.scissors(null),e.clipRect=e.graphicsBox}},{key:"drawStroke",value:function(e,t,n){return this.currentTarget?0==t.length?null:(this.inkGLContext.blendMaxFallback&&e.blendMode==un.MAX&&this.inkGLContext.enableStencilBufferForBlending(this.currentTarget),this.inkGLContext.transform=this.currentTarget.matrix,e instanceof Tn?this.drawGL(e,t,n):this.draw2D(e,t,n)):null}},{key:"drawGL",value:function(e,t,n){var r;n&&(r=this.inkGLContext.scatterMethodRandomSeed,this.inkGLContext.scatterMethodRandomSeed=n.randomSeed),this.spriteProgram.setBrush(e),this.inkGLContext.blendMode=e.blendMode;var i=this.spriteProgram.drawSprites(t);return n&&(n.randomSeed=this.inkGLContext.scatterMethodRandomSeed,this.inkGLContext.scatterMethodRandomSeed=r),this.clipDirtyArea(i)}},{key:"draw2D",value:function(e,t,n){return this.fill(t,n,!0,t.segment)}},{key:"fill",value:function(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=this.clipDirtyArea(e.bounds.toGLRect());if(i){var o=e.vertices;o.length>0?this.simpleProgram.drawVertices(this.currentTarget.framebuffer,o,t,n,r):i=void 0}return i}},{key:"clipDirtyArea",value:function(e){var t=e,n=this.inkGLContext.clipRect;return this.inkGLContext.transform&&(t=t.transform(this.inkGLContext.transform)),(t=n.intersect(t))&&(t=t.ceil(),1==this.glVersion&&(t=J.ofEdges(t.left-1,t.bottom-1,t.right+1,t.top+1))),t}},{key:"readPixels",value:function(e){var t=this.currentTarget;if(e){if(!(e instanceof J))throw new TypeError("box must be instance of RectGL")}else e=t.graphicsBounds;t.flipY&&(e=new J(e.x,t.height-e.y-e.height,e.width,e.height)),this.setTarget(t);var n=new Uint8Array(e.width*e.height*4);return this.gl.readPixels(parseInt(e.x*t.scaleFactor),parseInt(e.y*t.scaleFactor),parseInt(e.width*t.scaleFactor),parseInt(e.height*t.scaleFactor),this.gl.RGBA,this.gl.UNSIGNED_BYTE,n),n}},{key:"writePixels",value:function(e,t){if(e&&!(e instanceof J))throw new TypeError("rect must be instance of RectGL");var n=this.currentTarget;if(e||(e=new J(0,0,n.width,n.height)),!n.useTextureStorage)throw new Error("writePixels layer without texture is not supported!");n.flipY&&(e=new J(e.x,n.height-e.y-e.height,e.width,e.height));var r=new J(e.x*n.scaleFactor,e.y*n.scaleFactor,e.width*n.scaleFactor,e.height*n.scaleFactor);this.gl.finish(),this.gl.activeTexture(this.gl.TEXTURE0),this.gl.bindTexture(this.gl.TEXTURE_2D,n.texture),this.gl.texSubImage2D(this.gl.TEXTURE_2D,0,parseInt(r.x),parseInt(r.y),parseInt(r.width),parseInt(r.height),this.gl.RGBA,this.gl.UNSIGNED_BYTE,t)}}]),e}();function Dn(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=C(e)););return e}function Mn(){return Mn="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var r=Dn(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},Mn.apply(this,arguments)}function Nn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Ln=function(e,t){T(r,e);var n=Nn(r);function r(){var e;k(this,r);for(var t=arguments.length,i=new Array(t),o=0;o<t;o++)i[o]=arguments[o];if(e=n.call.apply(n,[this].concat(i)),i.some((function(e){return!(e instanceof Yt)})))throw new Error("Expected data item type is Polygon");return Object.defineProperty(A(e),"bounds",{get:function(){var t;return e.length>0&&(e.forEach((function(e){return t=e.bounds.union(t)})),t=t.ceil()),t},enumerable:!0}),Object.defineProperty(A(e),"vertices",{get:function(){return e.triangulate()},enumerable:!0}),e}return S(r,[{key:"clone",value:function(){return M(this.constructor,ue(this.map((function(e){return e.clone()}))))}},{key:"push",value:function(){for(var e,t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];if(n.some((function(e){return!(e instanceof Yt)})))throw new Error("Expected data item type is Polygon");(e=Mn(C(r.prototype),"push",this)).call.apply(e,[this].concat(n))}},{key:"triangulate",value:function(){var e;return(e=Float32Array.createSharedInstance()).concat.apply(e,ue(this.map((function(e){return e.vertices}))))}},{key:"transform",value:function(e){this.forEach((function(t){return t.transform(e)}))}},{key:"toJSON",value:function(){return{type:"PolygonArray",polygons:this.map((function(e){return e.toJSON()}))}}}],[{key:t,get:function(){return Array}},{key:"fromJSON",value:function(e){if("PolygonArray"!=e.type)throw new Error("PolygonArray deserialization failed. JSON type is ".concat(e.type,", expected PolygonArray."));return M(r,ue(e.polygons.map((function(e){return Yt.fromJSON(e)}))))}},{key:"fromPathsData",value:function(e){return M(r,ue(e.map((function(e){return Yt.createInstance(e)}))))}}]),r}(N(Array),Symbol.species);Yt.toPolygonArray=Ln.fromPathsData;var Bn={palettes:{}},_n=["AliceBlue","#F0F8FF","AntiqueWhite","#FAEBD7","Aqua","#00FFFF","Aquamarine","#7FFFD4","Azure","#F0FFFF","Beige","#F5F5DC","Bisque","#FFE4C4","Black","#000000","BlanchedAlmond","#FFEBCD","Blue","#0000FF","BlueViolet","#8A2BE2","Brown","#A52A2A","BurlyWood","#DEB887","CadetBlue","#5F9EA0","Chartreuse","#7FFF00","Chocolate","#D2691E","Coral","#FF7F50","CornflowerBlue","#6495ED","Cornsilk","#FFF8DC","Crimson","#DC143C","Cyan","#00FFFF","DarkBlue","#00008B","DarkCyan","#008B8B","DarkGoldenRod","#B8860B","DarkGray","#A9A9A9","DarkGreen","#006400","DarkKhaki","#BDB76B","DarkMagenta","#8B008B","DarkOliveGreen","#556B2F","DarkOrange","#FF8C00","DarkOrchid","#9932CC","DarkRed","#8B0000","DarkSalmon","#E9967A","DarkSeaGreen","#8FBC8F","DarkSlateBlue","#483D8B","DarkSlateGray","#2F4F4F","DarkTurquoise","#00CED1","DarkViolet","#9400D3","DeepPink","#FF1493","DeepSkyBlue","#00BFFF","DimGray","#696969","DodgerBlue","#1E90FF","FireBrick","#B22222","FloralWhite","#FFFAF0","ForestGreen","#228B22","Fuchsia","#FF00FF","Gainsboro","#DCDCDC","GhostWhite","#F8F8FF","Gold","#FFD700","GoldenRod","#DAA520","Gray","#808080","Green","#008000","GreenYellow","#ADFF2F","HoneyDew","#F0FFF0","HotPink","#FF69B4","IndianRed","#CD5C5C","Indigo","#4B0082","Ivory","#FFFFF0","Khaki","#F0E68C","Lavender","#E6E6FA","LavenderBlush","#FFF0F5","LawnGreen","#7CFC00","LemonChiffon","#FFFACD","LightBlue","#ADD8E6","LightCoral","#F08080","LightCyan","#E0FFFF","LightGoldenRodYellow","#FAFAD2","LightGray","#D3D3D3","LightGreen","#90EE90","LightPink","#FFB6C1","LightSalmon","#FFA07A","LightSeaGreen","#20B2AA","LightSkyBlue","#87CEFA","LightSlateGray","#778899","LightSteelBlue","#B0C4DE","LightYellow","#FFFFE0","Lime","#00FF00","LimeGreen","#32CD32","Linen","#FAF0E6","Magenta","#FF00FF","Maroon","#800000","MediumAquaMarine","#66CDAA","MediumBlue","#0000CD","MediumOrchid","#BA55D3","MediumPurple","#9370DB","MediumSeaGreen","#3CB371","MediumSlateBlue","#7B68EE","MediumSpringGreen","#00FA9A","MediumTurquoise","#48D1CC","MediumVioletRed","#C71585","MidnightBlue","#191970","MintCream","#F5FFFA","MistyRose","#FFE4E1","Moccasin","#FFE4B5","NavajoWhite","#FFDEAD","Navy","#000080","OldLace","#FDF5E6","Olive","#808000","OliveDrab","#6B8E23","Orange","#FFA500","OrangeRed","#FF4500","Orchid","#DA70D6","PaleGoldenRod","#EEE8AA","PaleGreen","#98FB98","PaleTurquoise","#AFEEEE","PaleVioletRed","#DB7093","PapayaWhip","#FFEFD5","PeachPuff","#FFDAB9","Peru","#CD853F","Pink","#FFC0CB","Plum","#DDA0DD","PowderBlue","#B0E0E6","Purple","#800080","RebeccaPurple","#663399","Red","#FF0000","RosyBrown","#BC8F8F","RoyalBlue","#4169E1","SaddleBrown","#8B4513","Salmon","#FA8072","SandyBrown","#F4A460","SeaGreen","#2E8B57","SeaShell","#FFF5EE","Sienna","#A0522D","Silver","#C0C0C0","SkyBlue","#87CEEB","SlateBlue","#6A5ACD","SlateGray","#708090","Snow","#FFFAFA","SpringGreen","#00FF7F","SteelBlue","#4682B4","Tan","#D2B48C","Teal","#008080","Thistle","#D8BFD8","Tomato","#FF6347","Turquoise","#40E0D0","Violet","#EE82EE","Wheat","#F5DEB3","White","#FFFFFF","WhiteSmoke","#F5F5F5","Yellow","#FFFF00","YellowGreen","#9ACD32"],Fn={red:["LightSalmon","Salmon","DarkSalmon","LightCoral","IndianRed","Crimson","FireBrick","Red","DarkRed"],orange:["Coral","Tomato","OrangeRed","Gold","Orange","DarkOrange"],yellow:["LightYellow","LemonChiffon","LightGoldenRodYellow","PapayaWhip","Moccasin","PeachPuff","PaleGoldenRod","Khaki","DarkKhaki","Yellow"],green:["LawnGreen","Chartreuse","LimeGreen","Lime","ForestGreen","Green","DarkGreen","GreenYellow","YellowGreen","SpringGreen","MediumSpringGreen","LightGreen","PaleGreen","DarkSeaGreen","MediumSeaGreen","SeaGreen","Olive","DarkOliveGreen","OliveDrab"],cyan:["LightCyan","Cyan","Aqua","Aquamarine","MediumAquaMarine","PaleTurquoise","Turquoise","MediumTurquoise","DarkTurquoise","LightSeaGreen","CadetBlue","DarkCyan","Teal"],blue:["PowderBlue","LightBlue","LightSkyBlue","SkyBlue","DeepSkyBlue","LightSteelBlue","DodgerBlue","CornflowerBlue","SteelBlue","RoyalBlue","Blue","MediumBlue","DarkBlue","Navy","MidnightBlue","MediumSlateBlue","SlateBlue","DarkSlateBlue"],purple:["Lavender","Thistle","Plum","Violet","Orchid","Fuchsia","Magenta","MediumOrchid","MediumPurple","BlueViolet","DarkViolet","DarkOrchid","DarkMagenta","Purple","RebeccaPurple","Indigo"],pink:["Pink","LightPink","HotPink","DeepPink","PaleVioletRed","MediumVioletRed"],white:["White","Snow","HoneyDew","MintCream","Azure","AliceBlue","GhostWhite","WhiteSmoke","SeaShell","Beige","OldLace","FloralWhite","Ivory","AntiqueWhite","Linen","LavenderBlush","MistyRose"],gray:["Gainsboro","LightGray","Silver","DarkGray","Gray","DimGray","LightSlateGray","SlateGray","DarkSlateGray","Black"],brown:["Cornsilk","BlanchedAlmond","Bisque","NavajoWhite","Wheat","BurlyWood","Tan","RosyBrown","SandyBrown","GoldenRod","Peru","DarkGoldenRod","Chocolate","SaddleBrown","Sienna","Brown","Maroon"]},Un={};for(var jn=function(e){var t=ce.getEnumValueName(_n[e]);Object.defineProperty(Bn,t,{get:function(){return function(e,t){return Un[e]||(Un[e]=wt.fromColor(t)),Un[e]}(t,_n[e+1])},enumerable:!0})},Gn=0;Gn<_n.length;Gn+=2)jn(Gn);for(var Yn in Fn){var Xn=Fn[Yn];Bn.palettes[Yn]={scale:Xn};for(var zn=function(e){var t=ce.getEnumValueName(Xn[e]);Object.defineProperty(Bn.palettes[Yn],t,{get:function(){return Bn[t]},enumerable:!0})},Vn=0;Vn<Xn.length;Vn++)zn(Vn)}function Hn(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return Wn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Wn(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function Wn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Zn=function(){function e(t){k(this,e),Object.defineProperty(this,"ctx",{value:t,enumerable:!0})}return S(e,[{key:"clearCanvas",value:function(){this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height)}},{key:"setTransform",value:function(e){this.ctx.setTransform(e.a,e.b,e.c,e.d,e.tx,e.ty)}},{key:"drawRect",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Bn.GRAY.toRGBA(.3);this.ctx.strokeStyle=t.toString(),this.ctx.strokeRect(e.left,e.top,e.width,e.height)}},{key:"fillRect",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Bn.GRAY.toRGBA(.3);this.ctx.fillStyle=t.toString(),this.ctx.fillRect(e.left,e.top,e.width,e.height)}},{key:"drawPoint",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Bn.BLACK;this.ctx.beginPath(),this.ctx.arc(e.x,e.y,t,0,2*Math.PI),this.renderStyle({type:"stroke",style:n})}},{key:"fillPoint",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Bn.BLACK;this.ctx.beginPath(),this.ctx.arc(e.x,e.y,t,0,2*Math.PI),this.renderStyle({type:"fill",style:n})}},{key:"drawEllipse",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Bn.BLACK;this.ctx.beginPath(),this.ctx.ellipse(e.x,e.y,t,n,0,0,2*Math.PI),this.renderStyle({type:"stroke",style:r})}},{key:"fillEllipse",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Bn.BLACK;this.ctx.beginPath(),this.ctx.ellipse(e.x,e.y,t,n,0,0,2*Math.PI),this.renderStyle({type:"fill",style:r})}},{key:"drawShape",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Bn.DARK_MAGENTA.toRGBA(.5);this.renderShape(e,{type:"stroke",style:t})}},{key:"fillShape",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Bn.ORANGE.toRGBA(.6);this.renderShape(e,{type:"fill",style:t})}},{key:"renderShape",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e instanceof At)this.ctx.beginPath(),this.renderPath(e);else if(e instanceof Yt)this.renderPolygon(e);else{if(!(e instanceof Ln))throw new Error("Unexpected shape type found");this.ctx.beginPath();var n,r=Hn(e);try{for(r.s();!(n=r.n()).done;){var i=n.value;this.renderPolygon(i,{segment:!0})}}catch(e){r.e(e)}finally{r.f()}}this.renderStyle(t)}},{key:"renderPolygon",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};n.segment||this.ctx.beginPath(),this.renderPath(e.shape),e.holes.forEach((function(n){return t.renderPath(n,{holesDirection:e.holesDirection})})),n.segment||this.renderStyle(n)}},{key:"renderPath",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=!t.holesDirection||t.holesDirection==Yt.PointsDirection.CLOCKWISE;if(n){this.ctx.moveTo(e.getPointX(0),e.getPointY(0));for(var r=1;r<e.length;r++)this.ctx.lineTo(e.getPointX(r),e.getPointY(r))}else{this.ctx.moveTo(e.getPointX(e.length-1),e.getPointY(e.length-1));for(var i=e.length-2;i>=0;i--)this.ctx.lineTo(e.getPointX(i),e.getPointY(i))}this.ctx.closePath(),this.renderStyle(t)}},{key:"renderStyle",value:function(e){if(e.type){if("fill"!=e.type&&"stroke"!=e.type)throw new Error("Option type should be oneof(stroke, fill)");e.style&&(this.ctx["".concat(e.type,"Style")]=e.style instanceof wt?e.style.toString():e.style),this.ctx[e.type]()}}}]),e}();function qn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Kn=function(e){T(n,e);var t=qn(n);function n(e){return k(this,n),t.call(this,e.getContext("2d"))}return S(n,[{key:"clear",value:function(){var e=this.ctx.canvas.toRect().transform(this.ctx.getTransform().invert());this.ctx.clearRect(e.left,e.top,e.width,e.height),this.clearCanvas()}},{key:"refresh",value:function(){if(!this.suppressRefresh){var e=[];n.allocateSegments(e,this.data,0),this.clear(),this.drawRange(e)}}},{key:"drawRange",value:function(e){var t=this;e.forEach((function(e){return t.drawRect(e.bounds,Bn.MAGENTA)}))}}],[{key:"allocateSegments",value:function(e,t,r){if(t&&(t.bounds&&e.push(t),t.children))if(10!==r)for(var i=0;i<t.children.length;i++)n.allocateSegments(e,t.children[i],r+1);else console.warn("depth 10")}},{key:"getInstance",value:function(e,t){if(e){var r=new n(e);return Object.defineProperty(r,"data",{get:function(){return t.data},enumerable:!0}),r}}}]),n}(Zn);function Jn(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var $n=function(e){T(n,e);var t=Jn(n);function n(){var e,r;k(this,n);for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=t.call.apply(t,[this].concat(o)),"undefined"!=typeof document&&("loading"==document.readyState?addEventListener("DOMContentLoaded",(function(t){return r=Kn.getInstance(document.getElementById("rbush"),A(e))})):r=Kn.getInstance(document.getElementById("rbush"),A(e))),Object.defineProperty(A(e),"canvas",{get:function(){return e.debug?r:null},enumerable:!0}),e}return S(n,[{key:"toBBox",value:function(e){return{minX:e.bounds.left,minY:e.bounds.top,maxX:e.bounds.right,maxY:e.bounds.bottom}}},{key:"compareMinX",value:function(e,t){return e.bounds.x-t.bounds.x}},{key:"compareMinY",value:function(e,t){return e.bounds.y-t.bounds.y}},{key:"search",value:function(e){return Mn(C(n.prototype),"search",this).call(this,{minX:e.left,minY:e.top,maxX:e.right,maxY:e.bottom})}},{key:"find",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.data,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];if(t){if(t.stroke){var i=!0;Object.keys(e).forEach((function(n){i=i&&t[n]==e[n]})),i&&r.push(t)}if(t.children){if(6!==n){for(var o=0;o<t.children.length;o++)this.find(e,t.children[o]||null,n+1,r);return r}console.warn("depth 6")}}}},{key:"load",value:function(e){Array.isArray(e)||(e=[e]),0!=e.length&&(Mn(C(n.prototype),"load",this).call(this,e),this.canvas&&this.canvas.refresh())}},{key:"unload",value:function(e){var t=this;Array.isArray(e)||(e=[e]),e.forEach((function(e){return t.remove(e)})),this.canvas&&this.canvas.refresh()}}]),n}(m.default),Qn=g?g.default||globalThis.JSZip:{},er=new TextEncoder,tr={fourCCLength:4,versionLength:3,chunkDescriptorLength:4+Uint32Array.BYTES_PER_ELEMENT,fourCC:{RIFF:er.encode("RIFF"),LIST:er.encode("LIST"),LIST_ITEM:er.encode("LI  "),HEAD:er.encode("HEAD"),META:er.encode("META"),DATA:er.encode("DATA"),TXT:er.encode("TEXT"),JSON:er.encode("JSON")}};function nr(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return rr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return rr(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function rr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}Object.defineEnum(tr,"ContentType",["BINARY","PROTO","JSON","TEXT","LIST"]),Object.defineEnum(tr,"CompressionType",["NONE","ZIP","LZMA"]);var ir=function(){function e(t){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint8Array([0,0,0]);k(this,e),this.textEncoder=new TextEncoder,Object.defineProperty(this,"format",{get:function(){return t},set:function(e){if(!e)throw new Error("format is expected");if("string"==typeof e&&(e=n.textEncoder.encode(e)),!(e instanceof Uint8Array))throw new Error("format expected type is Uint8Array");if(e.length!=tr.fourCCLength)throw new Error("format expected length is ".concat(tr.fourCCLength));t=e}}),Object.defineProperty(this,"version",{get:function(){return r},set:function(e){if(e){if("string"==typeof e&&(e=new Uint8Array(e.split("."))),!(e instanceof Uint8Array))throw new Error("version expected type is Uint8Array");if(e.length!=tr.versionLength)throw new Error("version expected length is ".concat(tr.versionLength))}r=e}}),Object.defineProperty(this,"length",{get:function(){return n.descriptors.length}}),this.format=t,this.version=r,this.reset()}var t,n,r;return S(e,[{key:"add",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(t=Object.assign({},t,{data:e})).data&&(this.validateChunkDescriptor(t),Object.equals(t.fourCC,tr.fourCC.META)?this.descriptors.unshift(t):this.descriptors.push(t))}},{key:"validateChunkDescriptor",value:function(e){var t=this;if(e.fourCC||(e.fourCC=tr.fourCC.DATA),e.version||(e.version=new Uint8Array([0,0,0])),!e.contentType){if(!(e.data instanceof ArrayBuffer||ArrayBuffer.isTypedArray(e.data)))throw new Error("descriptor contentType auto detect failed - should be provided");e.contentType=tr.ContentType.BINARY}if(Array.isArray(e.data)&&(e.list=!0),e.compressionType||(e.compressionType=tr.CompressionType.NONE),"string"==typeof e.fourCC&&(e.fourCC=this.textEncoder.encode(e.fourCC)),"string"==typeof e.version&&(e.version=new Uint8Array(e.version.split("."))),!(e.fourCC instanceof Uint8Array))throw new Error("Invalid fourCC type - ".concat(e.fourCC,", expected string or Uint8Array"));if(e.fourCC.length!=tr.fourCCLength)throw new Error("Invalid fourCC: [".concat(e.fourCC,"]"));if(!(e.version instanceof Uint8Array))throw new Error("Invalid chunk version - ".concat(e.version,", expected string or Uint8Array"));if(e.version.length!=tr.versionLength)throw new Error("Invalid version: [".concat(e.version,"]"));if(Object.equals(e.fourCC,tr.fourCC.RIFF))throw new Error("RIFF fourCC is reserved");if(Object.equals(e.fourCC,tr.fourCC.META)){if(e.contentType==tr.ContentType.BINARY)throw new Error("Binary meta is not allowed");if(e.list)throw new Error("Multi-value meta is not allowed");if(this.descriptors.some((function(e){return Object.equals(e.fourCC,tr.fourCC.META)})))throw new Error("Meta chunk is found. More than 1 meta chunk is not allowed.")}e.list&&e.data.forEach((function(n,r){var i=Object.assign({},e,{data:n});i.fourCC=tr.fourCC.LIST_ITEM,i.list=Array.isArray(n),e.data[r]=i,t.validateChunkDescriptor(i)}))}},{key:"encode",value:(r=be(Pe.mark((function e(){var t;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!=this.descriptors.length){e.next=2;break}throw new Error("Chunks not found. Build process failed.");case 2:return e.next=4,this.build();case 4:return t=e.sent,this.reset(),e.abrupt("return",t);case 7:case"end":return e.stop()}}),e,this)}))),function(){return r.apply(this,arguments)})},{key:"build",value:(n=be(Pe.mark((function e(){var t,n,r,i,o,a,s,u,c,l,h,f,d,p;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!=this.descriptors.length){e.next=2;break}throw new Error("RIFF build failed. Chunks not found.");case 2:t=0,n=[],r=0,i=nr(this.descriptors),e.prev=6,i.s();case 8:if((o=i.n()).done){e.next=17;break}return a=o.value,e.next=12,this.buildChunk(a);case 12:s=e.sent,n.push(s),r+=s.length;case 15:e.next=8;break;case 17:e.next=22;break;case 19:e.prev=19,e.t0=e.catch(6),i.e(e.t0);case 22:return e.prev=22,i.f(),e.finish(22);case 25:for(u=this.buildHeadChunk(n),r+=u.length,n.unshift(u),c=tr.fourCCLength+r,l=tr.fourCC.RIFF.length+Uint32Array.BYTES_PER_ELEMENT+c,h=new ArrayBuffer(l),this.content=new Uint8Array(h),this.contentView=new DataView(h),this.content.set(tr.fourCC.RIFF,t),t+=tr.fourCC.RIFF.length,this.contentView.setUint32(t,c,!0),t+=Uint32Array.BYTES_PER_ELEMENT,this.content.set(this.format,t),t+=this.format.length,f=0,d=n;f<d.length;f++)p=d[f],this.appendChunk(p,t),t+=p.length;return e.abrupt("return",this.content);case 41:case"end":return e.stop()}}),e,this,[[6,19,22,25]])}))),function(){return n.apply(this,arguments)})},{key:"buildChunk",value:(t=be(Pe.mark((function e(t){var n,r,i,o,a=this;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.data,!t.list){e.next=7;break}return e.next=4,Promise.all(n.map(function(){var e=be(Pe.mark((function e(t){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,a.buildChunk(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()));case 4:n=e.sent,e.next=21;break;case 7:t.contentType==tr.ContentType.JSON&&(n=JSON.stringify(n)),e.t0=t.compressionType,e.next=e.t0===tr.CompressionType.NONE?11:e.t0===tr.CompressionType.ZIP?13:e.t0===tr.CompressionType.LZMA?19:20;break;case 11:return t.contentType!=tr.ContentType.JSON&&t.contentType!=tr.ContentType.TEXT||(n=this.textEncoder.encode(n)),e.abrupt("break",21);case 13:return(r=new Qn).file("data",n),e.next=17,r.generateAsync({type:"uint8array",compression:"DEFLATE",compressionOptions:{level:9}});case 17:return n=e.sent,e.abrupt("break",21);case 19:throw new Error("LZMA compression is not supported yet");case 20:throw new Error("Invalid compression type: ".concat(t.compressionType));case 21:return i=0,o=0,t.list?(o=n.map((function(e){return e.length})).reduce((function(e,t){return e+t}),0),i=o,o+=tr.chunkDescriptorLength,o+=t.fourCC.length):(i=n.length,o+=i,o+=i%2,o+=tr.chunkDescriptorLength),this.debug&&console.log("buildChunk",this.readFourCC(t.fourCC),i,o,t.data),e.abrupt("return",Object.assign({},t,{data:n,size:i,length:o}));case 26:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"buildHeadChunk",value:function(e){var t=this.version.length+1+e.length*tr.chunkDescriptorLength,n=t%2,r=tr.fourCCLength+Uint32Array.BYTES_PER_ELEMENT+t+n,i=new Uint8Array(t+n),o=this.version.length+1;return i.set(this.version,0),e.forEach((function(e,t){var n=new Uint8Array(8);n.set(e.version,0),n.set([e.contentType.value],3),n.set([e.compressionType.value],4),i.set(n,o+t*tr.chunkDescriptorLength)})),{fourCC:tr.fourCC.HEAD,data:i,size:t,length:r}}},{key:"appendChunk",value:function(e,t){if(this.debug&&console.log("appendChunk",this.readFourCC(e.fourCC),e.size,e.length),e.list&&(this.content.set(tr.fourCC.LIST,t),t+=tr.fourCCLength,this.contentView.setUint32(t,e.size+e.fourCC.length,!0),t+=Uint32Array.BYTES_PER_ELEMENT),this.content.set(e.fourCC,t),t+=e.fourCC.length,e.list){var n,r=nr(e.data);try{for(r.s();!(n=r.n()).done;){var i=n.value;this.appendChunk(i,t),t+=i.length}}catch(e){r.e(e)}finally{r.f()}}else this.contentView.setUint32(t,e.size,!0),t+=Uint32Array.BYTES_PER_ELEMENT,this.content.set(e.data,t)}},{key:"readFourCC",value:function(e){return this.textDecoder||(this.textDecoder=new TextDecoder),this.textDecoder.decode(e)}},{key:"reset",value:function(){this.descriptors=[],this.content=null,this.contentView=null}}]),e}();function or(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return ar(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ar(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function ar(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function sr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var ur={contentType:tr.ContentType.BINARY,compressionType:tr.CompressionType.NONE},cr=function(e){T(n,e);var t=sr(n);function n(){return k(this,n),t.apply(this,arguments)}return S(n)}(N(Array)),lr=function(){function e(){k(this,e),this.textDecoder=new TextDecoder}var t,n,r;return S(e,[{key:"decode",value:(r=be(Pe.mark((function e(t,n){var r,i,o,a,s,u,c,l,h,f,d,p,y;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("undefined"!=typeof Buffer&&t instanceof Buffer&&(t=new Uint8Array(t)),n instanceof Uint8Array&&(n=this.textDecoder.decode(n)),i=(r=0)+tr.fourCC.RIFF.length,this.content=t,this.contentView=new DataView(t.buffer),this.descriptor={},this.onChunkDecoded||(this.descriptor.chunks=[],this.descriptor.asProps=function(){var e,t={},n=or(this.chunks);try{for(n.s();!(e=n.n()).done;){var r=e.value,i=r.fourCC.toLowerCase();if(t[i])if(t[i]instanceof cr)t[i].push(r.value);else{var o=new cr;o.push(t[i]),o.push(r.value),t[i]=o}else t[i]=r.value}}catch(e){n.e(e)}finally{n.f()}return t}),Object.equals(this.content.subarray(r,i),tr.fourCC.RIFF)){e.next=10;break}throw new Error("Invalid RIFF fourCC");case 10:if(i=(r=i)+Uint32Array.BYTES_PER_ELEMENT,(o=this.contentView.getUint32(r,!0)+i)%2==0){e.next=15;break}throw new Error("Invalid RIFF file size");case 15:if(o==this.content.length){e.next=17;break}throw new Error("Incomplete RIFF file");case 17:i=(r=i)+tr.fourCCLength,this.descriptor.format=this.textDecoder.decode(this.content.subarray(r,i)),r=i,a=[],s=0;case 23:if(!(r<o)){e.next=64;break}if(u=this.content.subarray(r,r+tr.fourCCLength),!Object.equals(u,tr.fourCC.HEAD)){e.next=31;break}c=this.decodeHead(r),r+=c.length,a=c.descriptors,e.next=62;break;case 31:if(l=void 0,Object.equals(u,tr.fourCC.LIST)?(h=this.content.subarray(r+tr.chunkDescriptorLength,r+tr.chunkDescriptorLength+tr.fourCCLength),l=this.textDecoder.decode(h)):l=this.textDecoder.decode(u),n&&l!=n){e.next=59;break}if(f=this.readChunk(r),r+=f.length,d=a[s]||ur,p=void 0,!Object.equals(u,tr.fourCC.LIST)){e.next=44;break}return e.next=41,this.decodeList(f.data,d);case 41:p=e.sent,e.next=47;break;case 44:return e.next=46,this.decodeChunk(f.data,d);case 46:p=e.sent;case 47:if(!n){e.next=51;break}return e.abrupt("return",p);case 51:if(!this.onChunkDecoded){e.next=56;break}return e.next=54,this.onChunkDecoded(l,p,this.descriptor);case 54:e.next=57;break;case 56:this.descriptor.chunks.push({fourCC:l,value:p});case 57:e.next=61;break;case 59:y=this.readChunkDimensionality(r),r+=y.length;case 61:s++;case 62:e.next=23;break;case 64:return e.abrupt("return",this.onChunkDecoded?void 0:this.descriptor);case 65:case"end":return e.stop()}}),e,this)}))),function(e,t){return r.apply(this,arguments)})},{key:"decodeHead",value:function(e){var t=this.readChunk(e),n={size:t.size,length:t.length,descriptors:[]},r="".concat(t.data[0],".").concat(t.data[1],".").concat(t.data[2]);"0.0.0"!=r&&(this.descriptor.version=r);for(var i=(t.size+t.size%2-4)/8,o=0;o<i;o++){var a=4+o*tr.chunkDescriptorLength,s=t.data.slice(a,a+8);r="".concat(s[0],".").concat(s[1],".").concat(s[2]);var u={contentType:tr.ContentType[s[3]],compressionType:tr.CompressionType[s[4]]};"0.0.0"!=r&&(u.version=r),n.descriptors.push(u)}return n}},{key:"readChunk",value:function(e){var t,n=this.content.subarray(e,e+tr.fourCCLength),r=this.readChunkDimensionality(e);if(this.debug&&console.log("readChunk",this.textDecoder.decode(n),r.size,r.length),r.size>0){var i=r.byteOffset+r.size;if(t=this.content.subarray(r.byteOffset,i),t=new Uint8Array(t,t.byteOffset,t.length),Object.equals(n,tr.fourCC.LIST)){var o=[],a=0;for(e+=tr.chunkDescriptorLength,e+=tr.fourCCLength;a<r.size-tr.fourCCLength;){var s=this.readChunk(e);a+=s.length,e+=s.length,o.push(s.data)}t=o}}else t=new Uint8Array(0);return{data:t,size:r.size,length:r.length}}},{key:"readChunkDimensionality",value:function(e){e+=tr.fourCCLength;var t=this.contentView.getUint32(e,!0),n=tr.fourCCLength+Uint32Array.BYTES_PER_ELEMENT+t,r=n%2;return{byteOffset:e+Uint32Array.BYTES_PER_ELEMENT,size:t,length:n+r}}},{key:"decodeList",value:(n=be(Pe.mark((function e(t,n){var r=this;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Promise.all(t.map(function(){var e=be(Pe.mark((function e(t){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!Array.isArray(t)){e.next=6;break}return e.next=3,r.decodeList(t,n);case 3:case 8:return e.abrupt("return",e.sent);case 6:return e.next=8,r.decodeChunk(t,n);case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()));case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)}))),function(e,t){return n.apply(this,arguments)})},{key:"decodeChunk",value:(t=be(Pe.mark((function e(t,n){var r,i;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:e.t0=n.compressionType,e.next=e.t0===tr.CompressionType.NONE?3:e.t0===tr.CompressionType.ZIP?5:e.t0===tr.CompressionType.LZMA?18:19;break;case 3:return r=n.contentType==tr.ContentType.JSON||n.contentType==tr.ContentType.TEXT?this.textDecoder.decode(t):t,e.abrupt("break",20);case 5:return e.next=7,Qn.loadAsync(t);case 7:if(i=e.sent,n.contentType!=tr.ContentType.JSON&&n.contentType!=tr.ContentType.TEXT){e.next=14;break}return e.next=11,i.file("data").async("string");case 11:r=e.sent,e.next=17;break;case 14:return e.next=16,i.file("data").async("uint8array");case 16:r=e.sent;case 17:return e.abrupt("break",20);case 18:throw new Error("LZMA compression is not supported yet");case 19:throw new Error("Invalid compression type: ".concat(n.compressionType));case 20:return n.contentType==tr.ContentType.JSON&&(r=JSON.parse(r)),e.abrupt("return",r);case 22:case"end":return e.stop()}}),e,this)}))),function(e,n){return t.apply(this,arguments)})}]),e}(),hr=function(){function e(){k(this,e)}return S(e,[{key:"calculatePrecision",value:function(e,t){throw new Error("PrecisionCalculator.calculatePrecision(data, property) should be implemented")}}]),e}();function fr(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return dr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return dr(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function dr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var pr=["position","size","rotation","scale","offset"],yr={position:0,size:4,rotation:8,scale:12,offset:16},vr=function(){function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;k(this,e),this.precisions=n;var r,i=fr(pr);try{var o=function(){var e=r.value;Object.defineProperty(t,e,{get:function(){return t.get(e)},set:function(n){return t.set(e,n)},enumerable:!0})};for(i.s();!(r=i.n()).done;)o()}catch(e){i.e(e)}finally{i.f()}Object.defineProperty(this,"factors",{get:function(){return Object.assign.apply(Object,[{}].concat(ue(pr.map((function(e){return x({},e,Math.pow(10,t[e]))})))))},enumerable:!0})}return S(e,[{key:"get",value:function(e){return this.precisions>>yr[e]&15}},{key:"set",value:function(e,t){if(t>15||t<0)throw new Error("Invalid '".concat(e,"' precision value ").concat(t," found. The value must be in the interval [0, 15]."));if(t>this[e])throw new Error("PrecisionSchema '".concat(e,"' update failed. Update value ").concat(t," > ").concat(this[e]," - update value should be less than current value."));t!=this[e]&&(this.precisions=this.precisions&~(15<<yr[e])|t<<yr[e])}},{key:"update",value:function(e){var t=this;pr.forEach((function(n){var r=e[n];r<t[n]&&(t[n]=r)}))}},{key:"decode",value:function(){var e=this;return Object.assign.apply(Object,[{}].concat(ue(pr.map((function(t){return x({},t,e[t])})))))}}],[{key:"encode",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=0;return pr.forEach((function(n){var r=e[n]||0;if(r>15||r<0)throw new Error("Invalid '".concat(n,"' precision value ").concat(r," found. The value must be in the interval [0, 15]."));t=t&~(15<<yr[n])|r<<yr[n]})),t}}]),e}();function mr(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return gr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return gr(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function gr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var br=function(){function e(){k(this,e)}return S(e,null,[{key:"l2Norm",value:function(e){var t,n=0,r=mr(e);try{for(r.s();!(t=r.n()).done;){var i=t.value;n+=i*i}}catch(e){r.e(e)}finally{r.f()}return Math.sqrt(n)}},{key:"rmse",value:function(e,t){for(var n=0,r=0;r<e.length;r++){var i=e[r]-t[r];n+=i*i}return Math.sqrt(n/e.length)}},{key:"variance",value:function(e){if(e.length<=1)return 0;var t,n=this.average(e),r=0,i=mr(e);try{for(i.s();!(t=i.n()).done;){var o=t.value-n;r+=o*o}}catch(e){i.e(e)}finally{i.f()}return r/(e.length-1)}},{key:"average",value:function(e){var t,n=0,r=mr(e);try{for(r.s();!(t=r.n()).done;){n+=t.value}}catch(e){r.e(e)}finally{r.f()}return n/e.length}},{key:"calculateError",value:function(e,t){for(var n=Math.pow(10,t),r=new Float32Array(e.length),i=0;i<r.length;i++){var o=Math.round(e[i]*n);if(o>Number.MAX_INT32||o<-Number.MAX_INT32)return NaN;r[i]=o}for(var a=new Float32Array(e.length),s=0;s<r.length;s++)a[s]=r[s]/n;return this.rmse(e,a)}},{key:"isZero",value:function(e){return Math.abs(e)<Number.EPSILON}}]),e}();function Er(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Pr=function(e){T(n,e);var t=Er(n);function n(e,r){var i;return k(this,n),(i=t.call(this)).layout=e,i.pathPointCalculator=r,i.inputBuffer=[],i.prediction=!0,i}return S(n,[{key:"togglePrediction",value:function(e){console.warn("PathProducer togglePrediction method is deprecated. Use InkBuilder instance prediction property to configure prediction behaviour."),this.prediction=e}},{key:"addImpl",value:function(e,t){if(e.phase!=this.phase)throw new Error("The phase of the addition (".concat(e.phase.name,") doesn't match the phase of the PathProducer (").concat(this.phase.name,")"));var n=[],r=[];e&&this.inputBuffer.push(e);var i=this.inputBuffer.length>=3?this.inputBuffer[this.inputBuffer.length-3]:null,o=this.inputBuffer.length>=2?this.inputBuffer[this.inputBuffer.length-2]:null,a=this.inputBuffer.length>=1?this.inputBuffer[this.inputBuffer.length-1]:null,s=this.calculate(i,o,a);return e&&s&&n.push.apply(n,ue(s.toArray(this.layout))),this.phase==W.Phase.END?(s=this.calculate(o,a,null))&&n.push.apply(n,ue(s.toArray(this.layout))):this.prediction&&(this.phase==W.Phase.UPDATE||t)&&(s=this.calculate(o,a,t))&&(r.push.apply(r,ue(s.toArray(this.layout))),(s=this.calculate(a,t,null))&&r.push.apply(r,ue(s.toArray(this.layout)))),{added:n,predicted:r}}},{key:"calculate",value:function(e,t,n){return t?this.pathPointCalculator(e,t,n):null}},{key:"reset",value:function(){Mn(C(n.prototype),"reset",this).call(this),this.inputBuffer.clear()}}]),n}(W);function kr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}function wr(e,t,n){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}var Sr=[-6e-6,-139e-6,-185e-6,414e-6,.002357,.003357,-.003135,-.023928,-.042909,-.017858,.096525,.254692,.347072,.26881,.114933],Ir=new WeakMap,xr=new WeakMap,Rr=function(e){T(n,e);var t=kr(n);function n(e){var r,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:15;return k(this,n),wr(A(r=t.call(this)),Ir,{writable:!0,value:[]}),wr(A(r),xr,{writable:!0,value:0}),r.filter=Sr.slice(),Object.defineProperty(A(r),"movingAverageWindowSize",{get:function(){return i},set:function(e){i=e,this.filter.length!=e&&(this.filter=n.resample(Sr,e)),this.predictionPointsCount=4*e/15,this.windowSize=this.filter.length},enumerable:!0}),r.dimsCount=e,r.movingAverageWindowSize=i,r}return S(n,[{key:"add",value:function(e,t){return t?this.project(e):this.addSequence(e)}},{key:"processImpl",value:function(e){return this.project(e)}},{key:"project",value:function(e){if(e.length%this.dimsCount!=0)throw new Error("Points size ('".concat(e.length,"') must be multiple of the dimensions count ('").concat(this.dimsCount,"')."));if(0==e.length)return[];var t=[],n=B(this,Ir).slice(),r=e.slice(0,e.length-this.dimsCount),i=this.addSequence(r);t.push.apply(t,ue(i));for(var o=e.slice(e.length-this.dimsCount,e.length),a=this.predictionPointsCount,s=0;s<a;s++){var u=this.addPoint(o);a-s<=B(this,xr)&&t.push.apply(t,ue(u))}return _(this,Ir,n),t}},{key:"addSequence",value:function(e){if(e.length%this.dimsCount!=0)throw new Error("Points size ('".concat(e.length,"') must be multiple of the dimensions count ('").concat(this.dimsCount,"')."));for(var t=[],n=e.length/this.dimsCount,r=0;r<n;r++){var i=this.addPoint(e.slice(r*this.dimsCount,(r+1)*this.dimsCount));t.push.apply(t,ue(i))}return _(this,xr,B(this,xr)+n),t}},{key:"addPoint",value:function(e){var t;for((t=B(this,Ir)).push.apply(t,ue(e));B(this,Ir).length<this.windowSize*this.dimsCount;){var n;(n=B(this,Ir)).unshift.apply(n,ue(B(this,Ir).slice(0,this.dimsCount)))}for(;B(this,Ir).length>this.windowSize*this.dimsCount;)_(this,Ir,B(this,Ir).slice(this.dimsCount));return this.filterBuffer()}},{key:"filterBuffer",value:function(){for(var e=[],t=0;t<this.windowSize;t++)for(var n=0;n<this.dimsCount;n++)isNaN(e[n])&&(e[n]=0),e[n]+=B(this,Ir)[t*this.dimsCount+n]*this.filter[t];return e}},{key:"reset",value:function(){Mn(C(n.prototype),"reset",this).call(this),_(this,xr,0),B(this,Ir).clear()}}],[{key:"resample",value:function(e,t){for(var n=new Float32Array(t),r=e.length/t,i=0,o=0;o<t;o++){var a=(e.length-1)*o/(t-1),s=Math.floor(a),u=Math.ceil(a),c=a-s,l=r*(e[s]*(1-c)+e[u]*c);i+=l,n[o]=l}for(var h=1/i,f=0;f<t;f++)n[f]*=h;return n}}]),n}(an),Tr=function(){function e(t,n){var r=this;k(this,e),t>0&&0==n?(this.segmentIndex=t-1,this.t=1):(this.segmentIndex=t,this.t=n),Object.defineProperty(this,"index",{get:function(){return console.warn("SplineParameter index => segmentIndex"),r.segmentIndex}})}return S(e,[{key:"equals",value:function(e){return this.segmentIndex==e.segmentIndex&&this.t==e.t}},{key:"toString",value:function(){return"spline-parameter(".concat(this.segmentIndex,", ").concat(this.t,")")}},{key:"toJSON",value:function(){return{segmentIndex:this.segmentIndex,t:this.t}}}],[{key:"fromJSON",value:function(t){return new e(t.segmentIndex,t.t)}},{key:"calcMiddleOfSegment",value:function(t,n){var r=.5*(t.t+n.t+(n.segmentIndex-t.segmentIndex)),i=Math.trunc(r),o=r-i;return new e(t.segmentIndex+i,o)}},{key:"areDistantEnough",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.01,r=t.t+(t.segmentIndex-e.segmentIndex);return r-e.t>n}}]),e}();function Ar(e,t,n){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}var Or=new WeakMap,Cr=function(){function e(t,n,r,i,o){var a=this;k(this,e),Ar(this,Or,{writable:!0,value:void 0}),1==r&&(n++,r=0),0==o&&(i--,o=1),Object.defineProperties(this,{spline:{value:t,enumerable:!0},segmentIndexStart:{value:n,enumerable:!0},segmentIndexEnd:{value:i,enumerable:!0},ts:{value:r,enumerable:!0},tf:{value:o,enumerable:!0}}),Object.defineProperty(this,"pointIndexStart",{value:n,enumerable:!0}),Object.defineProperty(this,"pointIndexEnd",{value:i+3,enumerable:!0}),Object.defineProperty(this,"length",{value:this.pointIndexEnd-this.pointIndexStart+1,enumerable:!0}),Object.defineProperty(this,"id",{get:function(){return B(a,Or)||_(a,Or,U.generate()),B(a,Or)},set:function(e){if(B(a,Or))throw new Error("id is immutable");_(a,Or,e)}}),this.validate()}return S(e,[{key:"validate",value:function(){var e=this.length-3;if(this.pointIndexStart<0)throw new Error("Invalid fragment pointIndexStart ".concat(this.pointIndexStart," found. The value must be non-negative."));if(this.pointIndexEnd>this.spline.length-1)throw new Error("Invalid fragment pointIndexEnd ".concat(this.pointIndexEnd," found. Last point in spline index is ").concat(this.spline.length-1,"."));if(this.ts<0||this.ts>=1)throw new Error("Invalid fragment ts ".concat(this.ts," found. The value must be in the interval [0, 1)."));if(this.tf<=0||this.tf>1)throw new Error("Invalid fragment tf ".concat(this.tf," found. The value must be in the interval (0, 1]."));if(e<1)throw new Error("Invalid fragment points range {".concat(this.pointIndexStart,", ").concat(this.pointIndexEnd,"} found. At least 4 points are needed to define spline."));if(1==e&&this.ts>this.tf)throw new Error("Invalid fragment T range ".concat(this.ts," - ").concat(this.tf," found. Spline has only one segment and ts <= tf."))}},{key:"union",value:function(t){var n=[this,t];n.sort(e.compare);var r=n.first,i=n.last,o=r.segmentIndexEnd,a=r.tf;if(1==a&&(o++,a=0),o!=i.segmentIndexStart||a!=i.ts)throw new Error("Fragments ".concat(r," and ").concat(i," are not adjacent."));var s=new e(this.spline,r.segmentIndexStart,r.ts,i.segmentIndexEnd,i.tf);return _(s,Or,B(this,Or)),this.inside&&(s.inside=this.inside),s}},{key:"overlaps",value:function(e){if(e.spline!=this.spline)return!1;var t=e.segmentIndexStart<this.segmentIndexEnd||e.segmentIndexStart==this.segmentIndexEnd&&e.ts<=this.tf,n=e.segmentIndexEnd>this.segmentIndexStart||e.segmentIndexEnd==this.segmentIndexStart&&e.tf>=this.ts;return t&&n}},{key:"toString",value:function(){return"fragment(".concat(this.segmentIndexStart,", ").concat(this.ts," - ").concat(this.segmentIndexEnd,", ").concat(this.tf,")")}}],[{key:"compare",value:function(e,t){return e.segmentIndexStart<t.segmentIndexStart?-1:e.segmentIndexStart>t.segmentIndexStart?1:e.ts<t.ts?-1:e.ts>t.ts?1:0}},{key:"getInstance",value:function(t,n,r){return new e(t,n.segmentIndex,n.t,r.segmentIndex,r.t)}}]),e}();function Dr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Mr=function(e){T(n,e);var t=Dr(n);function n(e,r,i){var o,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1;return k(this,n),(o=t.call(this,r,i,e)).points instanceof Float32Array?Object.defineProperty(A(o),"segmentsCount",{value:o.length-3,configurable:!0}):Object.defineProperty(A(o),"segmentsCount",{get:function(){return o.length-3},configurable:!0}),o.ts=a,o.tf=s,Object.defineProperty(A(o),"bounds",{get:function(){return $.ofSpline(A(o),o.pointProps.scattering).ceil()},enumerable:!0}),Object.defineProperty(A(o),"color",{get:function(){return wt.isColor(o.pointProps)?wt.fromColor(o.pointProps):void 0},set:function(e){if(!e)throw new Error("Spline color cannot be removed");if(!(e instanceof wt))throw new Error("Expected value should be Color instance");"red"in o.pointProps&&(o.pointProps.red=e.red),"green"in o.pointProps&&(o.pointProps.green=e.green),"blue"in o.pointProps&&(o.pointProps.blue=e.blue),"alpha"in o.pointProps&&(o.pointProps.alpha=e.alpha)},enumerable:!0}),o.validate(),o}return S(n,[{key:"validate",value:function(){var e=this.points instanceof Float32Array;if(Mn(C(n.prototype),"validate",this).call(this),!this.layout.includes(me.Property.X))throw new Error("Layout doesn't contains required properties X");if(!this.layout.includes(me.Property.Y))throw new Error("Layout doesn't contains required properties Y");if(e&&0==this.points.length)throw new Error("Empty spline is not allowed");if(this.ts<0||this.ts>=1)throw new Error("Invalid spline ts ".concat(this.ts," found. The value must be in the interval [0, 1)."));if(this.tf<=0||this.tf>1)throw new Error("Invalid spline tf ".concat(this.tf," found. The value must be in the interval (0, 1]."));if(1==this.segmentsCount&&this.ts>this.tf)throw new Error("Invalid spline t range ".concat(this.ts," - ").concat(this.tf," found. Spline has only one segment and ts <= tf."));if(e&&this.segmentsCount<1)throw new Error("Incompleted spline found. Spline is defined with at least 4 control points.")}},{key:"clone",value:function(){return new n(this.layout.slice(),this.points.clone(),Object.clone(this.pointProps),this.ts,this.tf)}},{key:"slice",value:function(e){var t=this.slicePoints(e.pointIndexStart,e.pointIndexEnd),r=new n(this.layout.slice(),t,Object.clone(this.pointProps),e.ts,e.tf);return r.id=e.id,r}},{key:"getSegment",value:function(e){var t=e,n=e+3,r=0==t?this.ts:0,i=n+1==this.length?this.tf:1;return this.slice({pointIndexStart:t,pointIndexEnd:n,ts:r,tf:i})}},{key:"getFragment",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.ts,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.segmentsCount-1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:this.tf,i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o=e,a=i?n-3:n;return new Cr(this,o,t,a,r)}},{key:"toPlainPath",value:function(){for(var e=new At([]),t=0;t<this.length;t++)e.points.push(this.getPointX(),this.getPointY());return e}},{key:"toJSON",value:function(){return{type:"Spline",id:this.id,layout:this.layout.map((function(e){return e.name})),points:lt.encode(this.points,this.encoding),pointProps:this.pointProps,ts:this.ts,tf:this.tf}}}],[{key:"fromJSON",value:function(e){var t=lt.decode(e.points),r=new n(e.layout.map((function(e){return me.Property[e]})),t,e.pointProps,e.ts,e.tf);return r.id=e.id,r}},{key:"fromRect",value:function(e,t){return new n(void 0,[e.left,e.top,e.left,e.top,e.right,e.top,e.right,e.bottom,e.left,e.bottom,e.left,e.top,e.left,e.top],t)}},{key:"createInstance",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],i=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;return new n(e,r,t,i,o)}},{key:"createSharedInstance",value:function(e,t,r,i,o){return new n(e,Float32Array.createSharedInstance(t),r,i,o)}}]),n}(At);function Nr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}function Lr(e,t,n){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}var Br=new WeakMap,_r=new WeakMap,Fr=function(e){T(n,e);var t=Nr(n);function n(e){var r;return k(this,n),Lr(A(r=t.call(this)),Br,{writable:!0,value:void 0}),Lr(A(r),_r,{writable:!0,value:void 0}),r.layout=e,r.pathPointProps={},r}return S(n,[{key:"add",value:function(e,t){B(this,Br)||_(this,Br,Mr.createInstance(this.layout,this.pathPointProps)),0==B(this,Br).points.length&&e.length>0&&e.unshift.apply(e,ue(e.slice(0,this.layout.length))),t&&(e.length>=this.layout.length?e.push.apply(e,ue(e.slice(e.length-this.layout.length,e.length))):e.push.apply(e,ue(this.getLastPart())));var n=this.getFirstPart();return B(this,Br).points=n.concat(e),e}},{key:"predict",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];if(0==e.length)return e;B(this,_r)?B(this,_r).points.clear():_(this,_r,Mr.createInstance(this.layout,this.pathPointProps));var t=B(this,_r).points,n=this.getFirstPart();for(t.push.apply(t,ue(n)),t.push.apply(t,ue(e)),t.push.apply(t,ue(t.slice(t.length-this.layout.length,t.length)));t.length<4*this.layout.length;)t.unshift(t.slice(0,this.layout.length));return t}},{key:"processImpl",value:function(e){var t=[];return e.length>0&&(t.push.apply(t,ue(e.slice(0,this.layout.length))),t.push.apply(t,ue(e)),e.length>=this.layout.length&&t.push.apply(t,ue(e.slice(e.length-this.layout.length,e.length)))),e}},{key:"getOutput",value:function(e,t){if(t==n.OutputType.ADDITION){if(B(this,Br).points.length>=4*this.layout.length)return B(this,Br)}else if(t==n.OutputType.PREDICTION){if(B(this,_r).points.length>0)return B(this,_r)}else if(e.length>0)return Mr.createSharedInstance(this.layout,e,this.pathPointProps)}},{key:"getFirstPart",value:function(){return B(this,Br).points.slice(Math.max(0,B(this,Br).points.length-3*this.layout.length),B(this,Br).points.length)}},{key:"getLastPart",value:function(){return B(this,Br).points.slice(B(this,Br).points.length-this.layout.length,B(this,Br).points.length)}},{key:"reset",value:function(){Mn(C(n.prototype),"reset",this).call(this),_(this,Br,null),_(this,_r,null)}}]),n}(an);function Ur(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var jr=function(e){T(n,e);var t=Ur(n);function n(e,r,i){var o,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];k(this,n),o=t.call(this,r,i,e),a.length>0&&!Object.isFrozen(a.first)&&a.forEach((function(e){return Object.freeze(e)})),Object.defineProperty(A(o),"splineParameters",{value:Object.freeze(a),enumerable:!0});var s=new St(e,i);return Object.defineProperty(A(o),"style",{get:function(){return s.style},set:function(e){return s.style.reset(e)},enumerable:!0}),Object.defineProperty(A(o),"color",{get:function(){return o.style.color},set:function(e){if(!e)throw new Error("Spline color cannot be removed");if(!(e instanceof wt))throw new Error("Expected value should be Color instance");"red"in o.pointProps&&(o.pointProps.red=e.red),"green"in o.pointProps&&(o.pointProps.green=e.green),"blue"in o.pointProps&&(o.pointProps.blue=e.blue),"alpha"in o.pointProps&&(o.pointProps.alpha=e.alpha)},enumerable:!0}),Object.defineProperty(A(o),"bounds",{get:function(){return $.ofSpline(A(o),o.pointProps.scattering).ceil()},enumerable:!0}),o.validate(),o}return S(n,[{key:"validate",value:function(){var e=this.points instanceof Float32Array;if(Mn(C(n.prototype),"validate",this).call(this),!this.layout.includes(me.Property.X))throw new Error("Layout doesn't contains required properties X");if(!this.layout.includes(me.Property.Y))throw new Error("Layout doesn't contains required properties Y");if(e&&0==this.points.length)throw new Error("Empty spline is not allowed")}},{key:"clone",value:function(){return new n(this.layout.slice(),this.points.clone(),Object.clone(this.pointProps),this.splineParameters.slice())}},{key:"slice",value:function(e){var t=this.slicePoints(e.pointIndexStart,e.pointIndexEnd);return new n(this.layout.slice(),t,Object.clone(this.pointProps),this.splineParameters.slice())}},{key:"getPoint",value:function(e){return Mn(C(n.prototype),"getPoint",this).call(this,e,this.style)}},{key:"getPointRef",value:function(e){return Mn(C(n.prototype),"getPointRef",this).call(this,e,this.style)}},{key:"getPointSegmentIndex",value:function(e){return this.splineParameters[e]?this.splineParameters[e].index:void 0}},{key:"getPointT",value:function(e){return this.splineParameters[e]?this.splineParameters[e].t:void 0}},{key:"getPointParameter",value:function(e){return this.splineParameters[e]}},{key:"toJSON",value:function(){return{type:"InterpolatedSpline",layout:this.layout.map((function(e){return e.name})),points:lt.encode(this.points,this.encoding),pointProps:this.pointProps,splineParameters:this.splineParameters}}}],[{key:"fromJSON",value:function(e){var t=lt.decode(e.points);return new n(e.layout.map((function(e){return me.Property[e]})),t,e.pointProps,e.splineParameters.map(Tr.fromJSON))}},{key:"fromRect",value:function(e,t){throw new Error("InterpolatedSpline.fromRect is not supported. Try Spline.fromRect and interpolate with particular Spline interpolator.")}},{key:"createInstance",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return new n(e,r,t)}},{key:"createSharedInstance",value:function(e,t,r,i){return new n(e,Float32Array.createSharedInstance(t),r,i)}}]),n}(At);function Gr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}function Yr(e,t,n){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}var Xr=i.mat4.fromValues(0,-.5,1,-.5,1,0,-2.5,1.5,0,.5,2,-1.5,0,0,-.5,.5),zr=new WeakMap,Vr=new WeakMap,Hr=new WeakMap,Wr=function(e){T(n,e);var t=Gr(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return k(this,n),Yr(A(e=t.call(this)),zr,{writable:!0,value:void 0}),Yr(A(e),Vr,{writable:!0,value:void 0}),Yr(A(e),Hr,{writable:!0,value:void 0}),e.calculateDerivates=r,e.keepSplineParameters=i,e.state={segmentIndex:-1,lastPointPosition:void 0,lastPointSize:0},e}return S(n,[{key:"initState",value:function(e){var t=this;this.state.layout={},e.layout.forEach((function(e,n){t.state.layout[e.name]={index:n,polynomials:i.vec4.create()}})),this.keepSplineParameters?this.state.splineParameters?this.state.splineParameters.clear():this.state.splineParameters=[]:delete this.state.splineParameters,this.splineLayout=e.layout,this.pathPointProps=Object.clone(e.pointProps),this.scattering&&(this.pathPointProps.scattering=this.scattering),this.layout=this.calculateDerivates?e.layout.concat([me.Property.D_X,me.Property.D_Y]):e.layout,this.state.ready=!0}},{key:"predict",value:function(e){if(!e)return[];this.state.ready||this.initState(e),B(this,Vr)?B(this,Vr).points.clear():_(this,Vr,jr.createInstance(this.layout,this.pathPointProps)),this.path=B(this,Vr);var t=Object.clone(this.state);return delete this.state.splineParameters,this.resetState(),this.discretize(e),this.state=t,this.path.points}},{key:"processImpl",value:function(e,t){return e?(e instanceof Cr&&(e=(r=e).spline),this.state.ready||this.initState(e),t==n.OutputType.ADDITION?(B(this,zr)?B(this,zr).points.clear():_(this,zr,jr.createInstance(this.layout,this.pathPointProps)),this.path=B(this,zr)):(_(this,Hr,jr.createInstance(this.layout,this.pathPointProps)),this.path=B(this,Hr)),this.discretize(e,r),this.path.points):[];var r}},{key:"getOutput",value:function(e,t){if(0!=e.length){if(t==n.OutputType.PROCESSOR||t==n.OutputType.ALL_DATA){var r=this.state.splineParameters;return r&&(r=r.slice()),jr.createSharedInstance(this.layout,e,this.pathPointProps,r)}return this.path}}},{key:"calculateInterpolatedPoint",value:function(e,t,n){var r=this;this.initState(e);var o=new me(0,0,this.splineLayout.includes(me.Property.Z)?0:void 0),a=i.vec4.fromValues(1,n,n*n,n*n*n);return this.calculatePolynomials(e,t),this.splineLayout.forEach((function(e){var t=i.vec4.dot(r.state.layout[e.name].polynomials,a);o[ce.getPropName(e.name)]=t})),o}},{key:"discretize",value:function(e,t){throw new Error("This method is abstract and should be implemented")}},{key:"storeLastPoint",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.state.lastPointPosition=new Z(this.getPropValue(me.Property.X,e,t),this.getPropValue(me.Property.Y,e,t),this.getPropValue(me.Property.Z,e,t)),this.state.lastPointSize=this.getPropValue(me.Property.SIZE,e,t)}},{key:"getPropValue",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return this.state.layout[e.name]?t[n+this.state.layout[e.name].index]:void 0}},{key:"calculatePolynomials",value:function(e,t){var n=this,r=e.points,o=this.splineLayout.length*(t+0),a=this.splineLayout.length*(t+1),s=this.splineLayout.length*(t+2),u=this.splineLayout.length*(t+3);this.splineLayout.forEach((function(e,t){var c=i.vec4.fromValues(r[o+t],r[a+t],r[s+t],r[u+t]);i.vec4.transformMat4(n.state.layout[e.name].polynomials,c,Xr)}))}},{key:"samplePoint",value:function(e){var t=this,n=[],r=i.vec4.fromValues(1,e,e*e,e*e*e);return this.splineLayout.forEach((function(e){var o=i.vec4.dot(t.state.layout[e.name].polynomials,r);n.push(o)})),this.calculateDerivates&&(n.push(this.getDerivativeOf(this.state.layout.X.polynomials,r)),n.push(this.getDerivativeOf(this.state.layout.Y.polynomials,r))),n}},{key:"getDerivativeOf",value:function(e,t){var n=i.vec4.fromValues(e[1],2*e[2],3*e[3],0);return i.vec4.dot(n,t)}},{key:"keepSegmentT",value:function(e){this.state.splineParameters&&this.state.splineParameters.push(new Tr(this.state.segmentIndex,e))}},{key:"resetState",value:function(){this.state.segmentIndex=-1}},{key:"reset",value:function(){Mn(C(n.prototype),"reset",this).call(this),this.state.ready=!1,this.state.lastPointPosition=void 0,this.state.lastPointSize=0,this.resetState(),_(this,zr,null),_(this,Vr,null),_(this,Hr,null)}}]),n}(an);function Zr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var qr=function(e){T(n,e);var t=Zr(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.1,i=arguments.length>1?arguments[1]:void 0,o=arguments.length>2?arguments[2]:void 0;return k(this,n),(e=t.call(this,i,o)).spacing=r,e}return S(n,[{key:"split",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:8,n=this.spacing;this.spacing=1,this.splitCount=t;var r=this.process(e);return this.spacing=n,delete this.splitCount,r}},{key:"discretize",value:function(e,t){var n,r,o=this.path.points,a=this.splitCount,s=Math.max(1,10*(this.spacing>1?1:this.spacing)),u=0,c=e.segmentsCount-1,l=e.ts,h=e.tf;t&&(u=t.segmentIndexStart,c=t.segmentIndexEnd,l=t.ts,h=t.tf,this.state.segmentIndex=u-1);for(var f=u;f<c+1;f++){if(this.state.segmentIndex++,this.calculatePolynomials(e,f),isNaN(this.splitCount)){n||(n=me.createInstance(e.layout),r=me.createInstance(e.layout)),n.fill(f+1,e.points,e.layout,e.pointProps),r.fill(f+2,e.points,e.layout,e.pointProps);var d=i.vec2.distance(n.value,r.value),p=this.pathPointProps.size;this.state.layout.SIZE&&(p=Math.min(n.size,r.size)),a=Math.floor(s*(d/p)/this.spacing)+1}for(var y=1/a,v=0;v<=a;v++){var m=!this.state.lastPointPosition,g=v/a;if(0==f&&g<l){if(!(g+y>=l))continue;g=l,m=this.spacing<=1}if(f==c&&g>=h){if(!(g<h+y))continue;g=h,m=this.lastSegment&&this.spacing<=1}if(!(f>0&&0==g)){var b=this.samplePoint(g);if(!m&&this.state.lastPointPosition){var E=new Z(this.getPropValue(me.Property.X,b),this.getPropValue(me.Property.Y,b),this.getPropValue(me.Property.Z,b)),P=this.state.lastPointPosition.vec.squaredDistance(this.state.lastPointPosition.value,E.value),k=(this.state.layout.SIZE?(this.state.lastPointSize+b[this.state.layout.SIZE.index])/2:this.pathPointProps.size)*this.spacing;m=P>=k*k}m&&(o.push.apply(o,ue(b)),this.storeLastPoint(b),this.keepSegmentT(g))}}}}}]),n}(Wr),Kr=function(){function e(t){k(this,e),this.key=t,this.height=1}return S(e,[{key:"leftRotate",value:function(){var t=this.right,n=t.left;return t.left=this,this.right=n,this.height=Math.max(e.height(this.left),e.height(this.right))+1,t.height=Math.max(e.height(t.left),e.height(t.right))+1,t}},{key:"rightRotate",value:function(){var t=this.left,n=t.right;return t.right=this,this.left=n,this.height=Math.max(e.height(this.left),e.height(this.right))+1,t.height=Math.max(e.height(t.left),e.height(t.right))+1,t}},{key:"getBalanceFactor",value:function(){return e.height(this.left)-e.height(this.right)}}],[{key:"height",value:function(e){return e?e.height:0}},{key:"minValue",value:function(e){if(e){for(var t=e;t.left;)t=t.left;return t.key}}},{key:"maxValue",value:function(e){if(e){for(var t=e;t.right;)t=t.right;return t.key}}}]),e}(),Jr=function(){function e(){k(this,e),this.count=0,this.hasKey=!1,this.root}return S(e,[{key:"min",value:function(){return Kr.minValue(this.root)}},{key:"max",value:function(){return Kr.maxValue(this.root)}},{key:"add",value:function(e){return this.hasKey=!1,this.root=this.insertNode(this.root,e),this.hasKey||this.count++,!this.hasKey}},{key:"insertNode",value:function(e,t){if(!e)return new Kr(t);if(t<e.key)e.left=this.insertNode(e.left,t);else{if(!(t>e.key))return this.hasKey=!0,e;e.right=this.insertNode(e.right,t)}if(!this.hasKey){e.height=1+Math.max(Kr.height(e.left),Kr.height(e.right));var n=e.getBalanceFactor();if(n>1){if(t<e.left.key)return e.rightRotate();if(t>e.left.key)return e.left=e.left.leftRotate(),e.rightRotate()}else if(n<-1){if(t>e.right.key)return e.leftRotate();if(t<e.right.key)return e.right=e.right.rightRotate(),e.leftRotate()}}return e}},{key:"contains",value:function(e){return this.containsNode(this.root,e)}},{key:"containsNode",value:function(e,t){return!!e&&(t<e.key?this.containsNode(e.left,t):!(t>e.key)||this.containsNode(e.right,t))}},{key:"printTree",value:function(){if(this.root)for(var e=[this.root],t=this.root.height;e.length>0;){var n=e.shift();t!=n.height&&console.log("-"),console.log("".concat(n.key," with height: ").concat(n.height,", balance: ").concat(n.getBalanceFactor())),t=n.height;var r=n.left,i=n.right;r&&e.push(r),i&&e.push(i)}}},{key:"toArray",value:function(){var t=[];return e.fillArray(t,this.root),t}}],[{key:"fillArray",value:function(e,t){t&&(this.fillArray(e,t.left),e.push(t.key),this.fillArray(e,t.right))}}]),e}(),$r=function(){function e(){var t=this;k(this,e),this.tree=new Jr,Object.defineProperty(this,"length",{get:function(){return t.tree.count},enumerable:!0})}return S(e,[{key:"clear",value:function(){this.tree=new Jr}},{key:"add",value:function(e){return this.tree.add(e)}},{key:"includes",value:function(e){return this.tree.contains(e)}},{key:"min",value:function(){return this.tree.min()}},{key:"max",value:function(){return this.tree.max()}},{key:"toArray",value:function(){return this.tree.toArray()}}]),e}();function Qr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var ei=function(e){T(n,e);var t=Qr(n);function n(e,r){var i;return k(this,n),(i=t.call(this,e,r)).state.lastSegmentIndex=-1,i.state.lastPointRotation=0,i.state.lastPointT=0,i.state.absAccumulatedErrorPos=0,i.state.absAccumulatedErrorS=0,i.setT=new $r,i.samples=[],Object.defineProperty(A(i),"errorThreshold",{get:function(){return i.error},set:function(e){i.error=e,i.errorDistSq=i.error*i.error,i.error10=10*i.error},enumerable:!0}),i.errorThreshold=.15,i}return S(n,[{key:"discretize",value:function(e,t){var n=this.path.points,r=0,i=e.segmentsCount-1,o=e.ts,a=e.tf;t&&(r=t.segmentIndexStart,i=t.segmentIndexEnd,o=t.ts,a=t.tf,this.state.segmentIndex=r-1);for(var s=r;s<i+1;s++){this.state.segmentIndex++,this.calculatePolynomials(e,s);var u=this.calculateTValues(s==i,o,a);n.push.apply(n,ue(this.samplePoints(u))),u.length>0&&(this.resetAccumulatedErrors(),this.storeLastPoint(n))}}},{key:"samplePoints",value:function(e){var t=this;return this.samples.clear(),e.toArray().forEach((function(e){var n;t.keepSegmentT(e),(n=t.samples).push.apply(n,ue(t.samplePoint(e)))})),this.samples}},{key:"storeLastPoint",value:function(e){var t=e.length-this.layout.length;Mn(C(n.prototype),"storeLastPoint",this).call(this,e,t),this.state.lastPointRotation=this.getPropValue(me.Property.ROTATION,e,t),this.state.lastPointT=this.setT.max(),this.state.lastSegmentIndex=this.state.segmentIndex}},{key:"calculateTValues",value:function(e,t,n){var r=0==this.state.segmentIndex?t:0,i=e?n:1;return this.setT.clear(),this.getTForPos(r,i),this.state.layout.SIZE&&this.getTForCubic(r,i,this.state.layout.SIZE.polynomials,this.error),this.mustAddStartT()&&this.setT.add(r),e&&this.setT.add(i),this.state.layout.ROTATION&&this.getTForRotation(r,i),this.setT}},{key:"mustAddStartT",value:function(){if(this.state.lastSegmentIndex<0)return!0;var e=this.state.lastPointT-(this.state.segmentIndex-this.state.lastSegmentIndex),t=this.setT.length>0?this.setT.min():1,n=this.getPosErrorAtT0(t,this.state.lastPointPosition);if(this.state.absAccumulatedErrorPos+=Math.abs(n),this.state.layout.SIZE){var r=this.getErrorAtT0(this.state.layout.SIZE.polynomials,t,e,this.state.lastPointSize);this.state.absAccumulatedErrorS+=Math.abs(r)}return this.state.absAccumulatedErrorPos>this.errorDistSq||this.state.absAccumulatedErrorS>this.error}},{key:"getPosErrorAtT0",value:function(e,t){var n=this.getTPoint(e),r=this.getTPoint(0);return this.minDistanceSq(t,n,r)}},{key:"getErrorAtT0",value:function(e,t,n,r){var i=n,o=r,a=t,s=this.cubicCalc(e,a),u=this.cubicCalc(e,0),c=o+(0-i)*(s-o)/(a-i);return Math.abs(u-c)}},{key:"getTForPos",value:function(e,t){var n=this.getTPoint(e),r=this.getTPoint(t),i=this.subdividePos(n,r);if(i.split)this.subdivideRecursivePos(n,i),this.setT.add(i.t),this.subdivideRecursivePos(i,r);else{var o=this.subdividePos(n,i),a=this.subdividePos(i,r);o.split&&(this.subdivideRecursivePos(n,o),this.setT.add(o.t),this.subdivideRecursivePos(o,i)),(o.split||a.split)&&this.setT.add(i.t),a.split&&(this.subdivideRecursivePos(i,a),this.setT.add(a.t),this.subdivideRecursivePos(a,r))}}},{key:"subdivideRecursivePos",value:function(e,t){var n=this.subdividePos(e,t);n.split&&(this.subdivideRecursivePos(e,n),this.setT.add(n.t),this.subdivideRecursivePos(n,t))}},{key:"subdividePos",value:function(e,t){var n=.5*(e.t+t.t),r=this.getTPoint(n),i=this.minDistanceSq(e,t,r),o=e.add(t).scaleSelf(.5),a=r.subtract(o).absSelf();return r.split=i>this.errorDistSq||a.x>this.error10||a.y>this.error10,this.state.layout.Z&&(r.split=r.split||a.z>this.error10),r}},{key:"getTForCubic",value:function(e,t,n,r){var i={v:this.cubicCalc(n,e),t:e},o={v:this.cubicCalc(n,t),t:t},a=this.subdivide(i,o,n);if(a.diff>r)this.subdivideRecursive(i,a,n,r),this.setT.add(a.t),this.subdivideRecursive(a,o,n,r);else{var s=this.subdivide(i,a,n),u=this.subdivide(a,o,n);s.diff>r&&(this.subdivideRecursive(i,s,n,r),this.setT.add(s.t),this.subdivideRecursive(s,a,n,r)),(s.diff>r||u.diff>r)&&this.setT.add(a.t),u.diff>r&&(this.subdivideRecursive(a,u,n,r),this.setT.add(u.t),this.subdivideRecursive(u,o,n,r))}}},{key:"subdivideRecursive",value:function(e,t,n,r){var i=this.subdivide(e,t,n);i.diff>r&&(this.subdivideRecursive(e,i,n,r),this.setT.add(i.t),this.subdivideRecursive(i,t,n,r))}},{key:"subdivide",value:function(e,t,n){var r=.5*(e.t+t.t),i=this.cubicCalc(n,r),o=.5*(e.v+t.v);return{v:i,t:r,diff:Math.abs(i-o)}}},{key:"getTForRotation",value:function(e,t){var n=this.state.layout.ROTATION.polynomials,r=this.state.lastPointRotation;this.state.lastSegmentIndex<0&&(r=this.cubicCalc(n,e));for(var i=.25*(t-e),o=0;o<4;o++){var a=e+o*i,s=this.cubicCalc(n,a);Math.abs(s-r)>.06&&(this.setT.add(a),r=s)}}},{key:"minDistanceSq",value:function(e,t,n){var r=n.vec,i=r.squaredDistance(e.value,t.value);if(0==i)return r.squaredDistance(n.value,e.value);var o=Math.max(0,Math.min(1,r.dot(n.subtract(e).value,t.subtract(e).value)/i)),a=t.subtract(e).scale(o).add(e);return r.squaredDistance(n.value,a.value)}},{key:"getTPoint",value:function(e){var t=new Z(this.cubicCalc(this.state.layout.X.polynomials,e),this.cubicCalc(this.state.layout.Y.polynomials,e),this.state.layout.Z?this.cubicCalc(this.state.layout.Z.polynomials,e):void 0);return t.t=e,t}},{key:"cubicCalc",value:function(e,t){return e[0]+e[1]*t+e[2]*t*t+e[3]*t*t*t}},{key:"resetAccumulatedErrors",value:function(){this.state.absAccumulatedErrorPos=0,this.state.absAccumulatedErrorS=0}},{key:"resetState",value:function(){Mn(C(n.prototype),"resetState",this).call(this),this.state.lastSegmentIndex=-1,this.state.lastPointT=0,this.state.lastPointRotation=0,this.resetAccumulatedErrors()}}]),n}(Wr);function ti(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return ni(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ni(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function ni(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function ri(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var ii=function(e){T(n,e);var t=ri(n);function n(){var e,r;k(this,n);for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=t.call.apply(t,[this].concat(o)),Object.defineProperty(A(e),"encoding",{get:function(){return r},set:function(t){r=t,e.forEach((function(e){return e.encoding=t}))},enumerable:!0}),e}return S(n,[{key:"union",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.1,n=new Nt(this,this.bounds);n.subject=gt.SimplifyPolygons(n.subject,Pt.pftNonZero),n.solution=gt.CleanPolygons(n.subject,t*n.transform.scale),1==n.subject.length&&0==n.solution.first.length&&(n.solution=n.subject);var r=n.toPaths();return e?Yt.createSharedInstance(r.first,r.slice(1)):Yt.createInstance(r.first,r.slice(1))}},{key:"intersects",value:function(e){e instanceof Yt&&(e=[e]);var t,n=ti(this);try{for(n.s();!(t=n.n()).done;){var r,i=t.value,o=ti(e);try{for(o.s();!(r=o.n()).done;){var a=r.value;if(i.intersects(a))return{poly1:i,poly2:a}}}catch(e){o.e(e)}finally{o.f()}}}catch(e){n.e(e)}finally{n.f()}return null}},{key:"toJSON",value:function(){var e=Mn(C(n.prototype),"toJSON",this).call(this);return e.type="InkPath2D",e}}],[{key:"fromJSON",value:function(e){if("InkPath2D"!=e.type)throw new Error("InkPath2D deserialization failed. JSON type is ".concat(e.type,", expected InkPath2D."));return M(n,ue(e.polygons.map((function(e){return Yt.fromJSON(e)}))))}}]),n}(Ln);function oi(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}function ai(e,t,n){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}var si=new WeakMap,ui=new WeakMap,ci=new WeakMap,li=new WeakMap,hi=function(e){T(n,e);var t=oi(n);function n(e){var r;return k(this,n),ai(A(r=t.call(this)),si,{writable:!0,value:void 0}),ai(A(r),ui,{writable:!0,value:void 0}),ai(A(r),ci,{writable:!0,value:void 0}),ai(A(r),li,{writable:!0,value:void 0}),r.brush=e,r}return S(n,[{key:"processImpl",value:function(e,t){return t==n.OutputType.ADDITION?(B(this,si)||_(this,si,new ii),_(this,li,B(this,si))):t==n.OutputType.PREDICTION?(B(this,ui)||_(this,ui,new ii),_(this,li,B(this,ui))):(B(this,ci)||_(this,ci,new ii),_(this,li,B(this,ci))),B(this,li).clear(),this.generatePolygons(e),B(this,li)}},{key:"generatePolygons",value:function(e){if(!e)return B(this,li);for(var t=0;t<e.length;t++){var n=e.getPointRef(t),r=this.applyBrush(n);B(this,li).push(r)}}},{key:"applyBrush",value:function(e){for(var t=this.createTransform(e),n=this.brush.selectShape(t.maxScale).shape,r=Float32Array.createSharedInstance(2*n.length),o=0;o<n.length;o++){var a=o*n.stride,s=i.vec2.fromValues(n.getPointX(o),n.getPointY(o));i.vec2.transformMat2d(s,s,t),r[a]=s[0],r[a+1]=s[1]}return Yt.createSharedInstance(r)}},{key:"createTransform",value:function(e){if(isNaN(e.size))throw new Error("Size information not found");var t=i.mat2d.create(),n=e.size*e.scaleX,r=e.size*e.scaleY,o=Math.max(n,r);return i.mat2d.translate(t,t,i.vec2.fromValues(e.x,e.y)),i.mat2d.rotate(t,t,e.rotation),i.mat2d.translate(t,t,i.vec2.fromValues(e.offsetX,e.offsetY)),i.mat2d.scale(t,t,i.vec2.fromValues(n,r)),t.maxScale=o,t}},{key:"reset",value:function(){Mn(C(n.prototype),"reset",this).call(this),B(this,si)&&B(this,si).clear(),B(this,ui)&&B(this,ui).clear(),B(this,ci)&&_(this,ci,new ii)}}]),n}(an);function fi(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return di(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return di(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function di(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function pi(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}function yi(e,t,n){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}x(hi,"ARRAY_TYPE",ii);var vi=new WeakMap,mi=new WeakMap,gi=new WeakMap,bi=new WeakMap,Ei=new WeakMap,Pi=function(e){T(n,e);var t=pi(n);function n(){var e;return k(this,n),yi(A(e=t.call(this)),vi,{writable:!0,value:void 0}),yi(A(e),mi,{writable:!0,value:void 0}),yi(A(e),gi,{writable:!0,value:void 0}),yi(A(e),bi,{writable:!0,value:void 0}),yi(A(e),Ei,{writable:!0,value:void 0}),e}return S(n,[{key:"add",value:function(e,t){return B(this,vi)||_(this,vi,new ii),_(this,bi,B(this,vi)),B(this,bi).clear(),this.buildConvexHulls(e,!0),B(this,bi)}},{key:"processImpl",value:function(e,t){if(!(e instanceof ii)){if(!(e instanceof Yt))throw new Error("ConvexHullChainProducer build 'input' type missmatch, expected type is oneof(Polygon, InkPath2D)");e=[e]}return t==n.OutputType.PREDICTION?(B(this,mi)||_(this,mi,new ii),_(this,bi,B(this,mi))):(B(this,gi)||_(this,gi,new ii),_(this,bi,B(this,gi))),B(this,bi).clear(),this.buildConvexHulls(e),B(this,bi)}},{key:"buildConvexHulls",value:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=B(this,Ei),i=fi(e);try{for(i.s();!(t=i.n()).done;){var o=t.value;if(r||1==e.length){var a=r?r.union(o):o.convex();B(this,bi).push(a)}r=o}}catch(e){i.e(e)}finally{i.f()}n&&e.length>0&&_(this,Ei,e.last)}},{key:"reset",value:function(){Mn(C(n.prototype),"reset",this).call(this),_(this,Ei,null),B(this,vi)&&B(this,vi).clear(),B(this,mi)&&B(this,mi).clear(),B(this,gi)&&B(this,gi).clear()}}]),n}(an);function ki(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return wi(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return wi(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function wi(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}x(Pi,"ARRAY_TYPE",ii),"function"==typeof Worker&&(Worker.prototype.on=function(e,t){this["on".concat(e)]=function(n){var r="message"==e?n.data:n;t(r)}}),"function"==typeof DedicatedWorkerGlobalScope&&(DedicatedWorkerGlobalScope.prototype.on=function(e,t){this["on".concat(e)]=function(n){var r="message"==e?n.data:n;t(r)}});var Si=!1,Ii=function(){function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.WorkerType.CLASSIC;if(k(this,e),!Si)throw new Error("Constructor is private, use static method getInstance instead.");this.name=t,this.type=r,n&&n.startsWith("file://")&&(n=n.replace("file://","")),this.src=n,this.workers=[],this.transferables=[],this.status=e.Status.CLOSED,this.resolver={};var i=0;Object.defineProperty(this,"nextID",{get:function(){return String(i++)},enumerable:!0,configurable:!0})}var t,n,r;return S(e,[{key:"open",value:(r=be(Pe.mark((function t(){var n,r,i,o,a,s,u,c,l=this,h=arguments;return Pe.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=h.length>0&&void 0!==h[0]?h[0]:this.src,this.status==e.Status.CLOSED){t.next=3;break}throw new Error("".concat(this.name," worker cannot be opened. Current status is ").concat(this.status.name,"."));case 3:if(n){t.next=5;break}throw new Error("".concat(this.name," worker location is not defined."));case 5:if("function"!=typeof Worker){t.next=10;break}r=Worker,i=navigator.hardwareConcurrency||1,t.next=19;break;case 10:return t.next=12,import("os");case 12:return o=t.sent,t.next=15,import("worker_threads");case 15:a=t.sent,s=a.Worker,r=s,i=o.cpus().length;case 19:for(this.ready=0,u=function(e){var t=l.name+e,i=new r(n,{type:l.type,name:t,workerData:{name:t}});i.name=e,i.on("message",(function(e){return"INIT"==e.action?l.confirmWorkerReady():l.recieve(e)})),i.on("error",(function(t){return l.recieveError(t,e)})),l.workers.push(i)},c=0;c<i;c++)u(c);return this.status=e.Status.OPEN_IN_PROGRESS,t.abrupt("return",new Promise((function(e,t){l.workers.forEach((function(e,t){return e.postMessage({action:"INIT",worker:t})})),l.resolve=e})));case 24:case"end":return t.stop()}}),t,this)}))),function(){return r.apply(this,arguments)})},{key:"confirmWorkerReady",value:function(){this.ready++,this.ready==this.workers.length&&(this.resolve(),delete this.ready,delete this.resolve,this.status=e.Status.OPEN)}},{key:"close",value:function(){this.workers.forEach((function(e){return e.terminate()})),this.workers.clear(),this.status=e.Status.CLOSED}},{key:"broadcast",value:(n=be(Pe.mark((function t(n,r){var i=this;return Pe.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.status==e.Status.OPEN){t.next=2;break}throw new Error("ThreadBridge is not opened yet. Current status is ".concat(this.status.name,". Use open first."));case 2:return t.abrupt("return",new Promise((function(e,t){i.resolver[r]=e;var o,a=ki(i.workers);try{for(a.s();!(o=a.n()).done;){var s=o.value,u=i.buildRequestMessage(n,r);if(!u)break;i.send(s.name,u)}}catch(e){a.e(e)}finally{a.f()}})));case 3:case"end":return t.stop()}}),t,this)}))),function(e,t){return n.apply(this,arguments)})},{key:"broadcastMessage",value:(t=be(Pe.mark((function t(n){var r=this;return Pe.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.status==e.Status.OPEN){t.next=2;break}throw new Error("ThreadBridge is not opened yet. Current status is ".concat(this.status.name,". Use open first."));case 2:if(n.actionID){t.next=4;break}throw new Error("message actionID is required");case 4:return t.abrupt("return",new Promise((function(e,t){r.resolver[n.actionID]=e;var i,o=ki(r.workers);try{for(o.s();!(i=o.n()).done;){var a=i.value;r.send(a.name,n)}}catch(e){o.e(e)}finally{o.f()}})));case 5:case"end":return t.stop()}}),t,this)}))),function(e){return t.apply(this,arguments)})},{key:"send",value:function(t,n){if(this.status!=e.Status.OPEN)throw new Error("ThreadBridge is not opened yet. Current status is ".concat(this.status.name,". Use open first."));if(!n)throw new Error("message is required");this.workers[t].postMessage(n,this.transferables),this.transferables.clear()}},{key:"buildRequestMessage",value:function(e,t){throw new Error("ThreadBridge.buildRequestMessage(action, actionID) is abstract and should be implemented")}},{key:"recieve",value:function(e){throw new Error("ThreadBridge.recieve(message) is abstract and should be implemented")}},{key:"resolve",value:function(e,t){this.resolver[e](t),delete this.resolver[e]}},{key:"recieveError",value:function(e,t){console.warn("".concat(this.name," worker ").concat(t,": ").concat(e.message)),e.filename||console.error(e)}}],[{key:"getInstance",value:function(){return this.instance||(Si=!0,this.instance=new this,Si=!1),this.instance}}]),e}();function xi(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}Object.defineEnum(Ii,"Status",["OPEN","OPEN_IN_PROGRESS","CLOSED"]),Ii.WorkerType={CLASSIC:"classic",MODULE:"module"};var Ri=function(e){T(r,e);var t,n=xi(r);function r(){var e;return k(this,r),(e=n.call(this,r.WORKER_NAME,r.buildWorkerURL(),r.buildWorkerURL().contains("/wacom-src/")?Ii.WorkerType.MODULE:Ii.WorkerType.CLASSIC)).state={},e}return S(r,[{key:"build",value:(t=be(Pe.mark((function e(t,n,r){var i;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=this.nextID,this.state[i]={type:n,input:t,output:new ii,queue:ue(t.slice()),lastPolygon:r,expected:t.length,processed:0},e.next=4,this.broadcast("BUILD",i);case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}}),e,this)}))),function(e,n,r){return t.apply(this,arguments)})},{key:"buildRequestMessage",value:function(e,t){var n={action:e,actionID:t},r=this.state[t];if("BUILD"!=e)throw new Error("Unknow data action found: ".concat(e));var i=r.queue.shift();if(i){var o,a=r.input.indexOf(i),s=0==a?r.lastPolygon:r.input[a-1];return s?o=Array.of.apply(Array,ue(s.shape.points).concat(ue(i.shape.points))):(s=i,(i=r.queue.shift())?(o=Array.of.apply(Array,ue(s.shape.points).concat(ue(i.shape.points))),r.updateIndex=!0,r.expected--,a++):o=s.shape.points),n.index=a,n.data=o,n}}},{key:"recieve",value:function(e){var t=this.state[e.actionID],n=t.updateIndex?e.index-1:e.index;if(t.output[n]=Yt.createSharedInstance(e.data),t.processed++,t.processed==t.expected){var r;if(delete this.state[e.actionID],this.keepAllData&&t.type==an.OutputType.ADDITION)this.path||(this.path=new ii),(r=this.path).push.apply(r,ue(t.output));this.resolve(e.actionID,t.output)}else{var i=this.buildRequestMessage(e.action,e.actionID);i&&this.send(e.worker,i)}}}],[{key:"buildWorkerURL",value:function(){if("function"!=typeof DedicatedWorkerGlobalScope){var e=void 0===l?"undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("digital-ink-min.js",document.baseURI).href:l;return(e=e.substring(0,e.lastIndexOf("/"))).endsWith("workers")||(e+="/workers"),e+="/".concat(r.WORKER_NAME,".js")}}}]),r}(Ii);function Ti(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}x(Ri,"WORKER_NAME","ConvexHullProvider");var Ai=function(e){T(i,e);var t,n,r=Ti(i);function i(){var e;return k(this,i),(e=r.call(this)).lastPolygon,e.convexHullProducer=Ri.getInstance(),Object.defineProperty(A(e),"closed",{get:function(){return e.convexHullProducer.status==Ii.Status.CLOSED},enumerable:!0}),e}return S(i,[{key:"open",value:(n=be(Pe.mark((function e(){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.convexHullProducer.open();case 2:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"close",value:function(){this.convexHullProducer.close()}},{key:"process",value:(t=be(Pe.mark((function e(t){var n,r,o,a,s=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=s.length>1&&void 0!==s[1]?s[1]:i.OutputType.PROCESSOR,r=!(s.length>2&&void 0!==s[2])||s[2],o=this.lastPolygon,n==i.OutputType.ADDITION&&t.length>0&&(this.lastPolygon=t.last),r&&(this.lastPolygon=null),e.next=7,this.convexHullProducer.build(t,n,o);case 7:return a=e.sent,e.abrupt("return",this.getOutput(a));case 9:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"reset",value:function(){Mn(C(i.prototype),"reset",this).call(this),this.lastPolygon=null}}],[{key:"buildWorkerURL",value:function(){return Ri.buildWorkerURL()}}]),i}(an);function Oi(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}x(Ai,"ARRAY_TYPE",ii);var Ci=function(e){T(n,e);var t=Oi(n);function n(){return k(this,n),t.apply(this,arguments)}return S(n,[{key:"predict",value:function(e){return console.warn("Prediction merge is not recommended"),e}},{key:"processImpl",value:function(e,t){return this.merge(e,t==n.OutputType.PROCESSOR)}},{key:"merge",value:function(e,t){if(0!=e.length)return e.union(t)}}]),n}(an);function Di(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}x(Ci,"ARRAY_TYPE",ii);var Mi=function(e){T(n,e);var t=Di(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.1;return k(this,n),(e=t.call(this)).epsilon=r,e}return S(n,[{key:"predict",value:function(e){return console.warn("Prediction simplify is not recommended"),e}},{key:"processImpl",value:function(e){var t=this;return e instanceof Yt?e.simplifyRamerDouglasPeucker(this.epsilon):M(e.constructor,ue(e.map((function(e){return e.simplify(t.epsilon)}))))}}]),n}(an);function Ni(){}x(Mi,"ARRAY_TYPE",ii),Object.defineEnum(Ni,"Stage",["PATH_PRODUCER","SMOOTHER","SPLINE_PRODUCER","SPLINE_INTERPOLATOR","BRUSH_APPLIER","CONVEX_HULL_CHAIN_PRODUCER","POLYGON_MERGER","POLYGON_SIMPLIFIER"]);var Li=Ni.Stage,Bi=W.Phase,_i=an.OutputType,Fi=Object.freeze({__proto__:null,Stage:Li,Phase:Bi,OutputType:_i,PathProducer:Pr,Smoother:Rr,SplineProducer:Fr,DistanceBasedInterpolator:qr,CurvatureBasedInterpolator:ei,BrushApplier:hi,ConvexHullChainProducer:Pi,ConvexHullChainProducerAsync:Ai,PolygonMerger:Ci,PolygonSimplifier:Mi}),Ui=function(){function e(t){var n=this;k(this,e),this.phase=t.phase;var r=isNaN(t.altitude)||isNaN(t.azimuth)?void 0:{altitude:t.altitude,azimuth:t.azimuth};Object.defineProperty(this,"x",{value:t.x,enumerable:!0}),Object.defineProperty(this,"y",{value:t.y,enumerable:!0}),Object.defineProperty(this,"z",{value:t.z,enumerable:!0}),Object.defineProperty(this,"timestamp",{value:t.timestamp,enumerable:!0,writable:!0}),Object.defineProperty(this,"force",{value:t.pressure,enumerable:!0}),Object.defineProperty(this,"pressure",{value:t.pressure,enumerable:!0}),Object.defineProperty(this,"rotation",{value:t.rotation,enumerable:!0}),Object.defineProperty(this,"radiusX",{value:t.radiusX,enumerable:!0}),Object.defineProperty(this,"radiusY",{value:t.radiusY,enumerable:!0}),Object.defineProperty(this,"altitude",{get:function(){return r||(r=n.computeTilt(t)||{}),r.altitude},enumerable:!0}),Object.defineProperty(this,"azimuth",{get:function(){return r||(r=n.computeTilt(t)||{}),r.azimuth},enumerable:!0}),t.pointer&&Object.defineProperty(this,"pointer",{value:t.pointer,enumerable:!0}),this.computedAzimuth=void 0}return S(e,[{key:"createPathPoint",value:function(e){return new me(this.x,this.y,this.z,e)}},{key:"computeTilt",value:function(e){if(!isNaN(e.tiltX)&&!isNaN(e.tiltY)){var t=e.tiltX,n=e.tiltY,r=Math.tan(Math.toRadians(t)),i=Math.tan(Math.toRadians(n)),o=Math.sqrt(r*r+i*i);return{altitude:Math.atan2(1,o),azimuth:Math.atan2(i,r)}}}},{key:"speed",value:function(t,n){var r={x:0,y:0,time:0};return(r=t&&!n?this.minus(t):n&&!t?n.minus(this):n.minus(t)).time>0?e.getMagnitude(r.x,r.y)/(r.time/1e3):(0==r.time||console.warn("Speed out of range: ".concat(r.time,"ms")),0)}},{key:"computeNearestAzimuthAngle",value:function(e){var t;if(isNaN(this.azimuth))return 0;if(e){if(isNaN(e.azimuth))return 0;var n=2*Math.PI,r=e.computedAzimuth||e.azimuth,i=parseInt(r/n),o=(t=this.azimuth+i*n)-r;o>=Math.PI?t-=n:o<-Math.PI&&(t+=n)}else t=this.azimuth;return this.computedAzimuth=t,t}},{key:"minus",value:function(e){return{x:this.x-e.x,y:this.y-e.y,time:this.timestamp-e.timestamp}}}],[{key:"getMagnitude",value:function(e,t){return Math.sqrt(e*e+t*t)}}]),e}();Object.defineEnum(Ui,"Property",["X","Y","Z","PHASE","TIMESTAMP","PRESSURE","RADIUS_X","RADIUS_Y","ALTITUDE","AZIMUTH","ROTATION"]);var ji=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];k(this,e),this.accumulatedAddition=t,this.lastPrediction=n,this.first=!1,this.last=!1}return S(e,[{key:"add",value:function(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];e==W.Phase.BEGIN?this.reset(!0):e==W.Phase.END&&(this.last=!0),t&&(n=this.accumulatedAddition).push.apply(n,ue(t)),this.lastPrediction=r}},{key:"clone",value:function(){var t=new e(this.accumulatedAddition.slice(),this.lastPrediction.slice());return t.first=this.first,t.last=this.last,t}},{key:"reset",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.first=e,this.last=!1,this.accumulatedAddition.clear(),this.lastPrediction.clear()}}]),e}();function Gi(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return Yi(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Yi(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function Yi(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Xi(e,t,n){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}var zi=[Li.SMOOTHER,Li.POLYGON_MERGER,Li.POLYGON_SIMPLIFIER],Vi=[Li.SPLINE_PRODUCER,Li.SPLINE_INTERPOLATOR,Li.BRUSH_APPLIER,Li.CONVEX_HULL_CHAIN_PRODUCER,Li.POLYGON_MERGER,Li.POLYGON_SIMPLIFIER],Hi=new WeakMap,Wi=function(){function e(){var t=this;k(this,e),Xi(this,Hi,{writable:!0,value:void 0}),this.layout=[me.Property.X,me.Property.Y],this.pathSegment=new ji,this.pathProducer=new Pr(this.layout),this.smoother=new Rr(this.layout.length),this.splineProducer=new Fr(this.layout),this.distanceInterpolator=new qr,this.curvatureInterpolator=new ei,this.brushApplier=new hi,this.polygonMerger=new Ci,this.polygonSimplifier=new Mi,this.splineProducer.keepAllData=!0,this.phase=void 0,this.pointerID=void 0,this.concatSegments=!1,this.lastPipelineStage=void 0,this.excludedPipelineStages=[],this.configured=!1,Object.defineProperty(this,"allData",{get:function(){var e={};return t.lastPipelineStage&&(t.smoother.keepAllData&&(e.smootherPoints=t.smoother.allData),t.splineInterpolator.keepAllData&&t.lastPipelineStage.value>Li.SPLINE_INTERPOLATOR.value&&(e.interpolatedSpline=t.splineInterpolator.allData),t.brushApplier.keepAllData&&t.lastPipelineStage.value>Li.BRUSH_APPLIER.value&&(e.shapesPath=t.brushApplier.allData),t.convexHullChainProducer.keepAllData&&t.lastPipelineStage.value>Li.CONVEX_HULL_CHAIN_PRODUCER.value&&(e.convexPath=t.convexHullChainProducer.allData)),e}}),Object.defineProperty(this,"prediction",{get:function(){return t.pathProducer.prediction},set:function(e){return t.pathProducer.prediction=e},enumerable:!0})}return S(e,[{key:"configure",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this.reset(this.pointerID),e.onBuildComplete)throw new Error("[InkBuilderSettings] onBuildComplete property is deprecated. Use InkBuilder instance onComplete property to set callback.");if("mergePrediction"in e&&console.warn("[InkBuilderSettings] 'mergePrediction' property is deprecated. Do not affects PolygonMerger behaviour."),!e.brush)throw new Error("[InkBuilderSettings] brush property is required");if(e.excludedPipelineStages){if(!Array.isArray(e.excludedPipelineStages))throw new Error("Expected type of excludedPipelineStages is Array instance");var t=e.excludedPipelineStages.filter((function(e){return!zi.includes(e)}));t.length>0&&console.warn("[InkBuilderSettings] excludedPipelineStages property includes steps which cannot be excluded: ".concat(t.map((function(e){return e.name})).join(", "))),this.excludedPipelineStages=e.excludedPipelineStages.slice()}if(!this.excludedPipelineStages.includes(Li.SMOOTHER)&&e.movingAverageWindowSize&&(this.smoother.movingAverageWindowSize=e.movingAverageWindowSize),e.lastPipelineStage){if(!Vi.includes(e.lastPipelineStage))throw new Error("[InkBuilderSettings] lastPipelineStage property expects one of: ".concat(Vi.map((function(e){return e.name})).join(", ")));if(this.excludedPipelineStages.includes(e.lastPipelineStage))throw new Error("[InkBuilderSettings] lastPipelineStage ".concat(e.lastPipelineStage.name," is disabled, check excludedPipelineStages configuration"));if(e.brush instanceof Tn&&e.lastPipelineStage!=Li.SPLINE_INTERPOLATOR)throw new Error("[InkBuilderSettings] lastPipelineStage ".concat(e.lastPipelineStage.name," is not compatible with provided brush"));this.lastPipelineStage=e.lastPipelineStage}switch(this.brush=e.brush,this.brush instanceof tn&&(this.brushApplier.brush=this.brush),this.lastPipelineStage||(this.brush instanceof tn?(this.brush.spacing>1?this.lastPipelineStage=Li.BRUSH_APPLIER:this.excludedPipelineStages.includes(Li.POLYGON_SIMPLIFIER)&&this.excludedPipelineStages.includes(Li.POLYGON_MERGER)?this.lastPipelineStage=Li.CONVEX_HULL_CHAIN_PRODUCER:this.lastPipelineStage=Li.POLYGON_MERGER,this.lastPipelineStage==Li.POLYGON_MERGER&&(this.concatSegments=Boolean(e.concatSegments))):this.lastPipelineStage=Li.SPLINE_INTERPOLATOR),this.lastPipelineStage==Li.SPLINE_INTERPOLATOR||this.lastPipelineStage==Li.BRUSH_APPLIER?(this.splineInterpolator=this.distanceInterpolator,this.splineInterpolator.spacing=this.brush.spacing,this.splineInterpolator.scattering=this.brush.scattering,this.splineInterpolator.calculateDerivates=this.brush instanceof Tn):(this.splineInterpolator=this.curvatureInterpolator,this.splineInterpolator.errorThreshold=e.errorThreshold||.15),this.splineInterpolator.keepSplineParameters=!!e.keepSplineParameters,this.splineInterpolator.keepAllData=!1,this.brushApplier.keepAllData=!1,this.convexHullChainProducer.keepAllData=!1,this.polygonMerger.keepAllData=!1,this.polygonSimplifier.keepAllData=!1,this.lastPipelineStage){case Li.SPLINE_PRODUCER:break;case Li.SPLINE_INTERPOLATOR:this.splineInterpolator.keepAllData=!0;break;case Li.BRUSH_APPLIER:this.brushApplier.keepAllData=!0;break;case Li.CONVEX_HULL_CHAIN_PRODUCER:this.convexHullChainProducer.keepAllData=!0;break;case Li.POLYGON_MERGER:this.polygonMerger.keepAllData=!0;break;case Li.POLYGON_SIMPLIFIER:this.polygonSimplifier.keepAllData=!0;break;default:throw console.warn(this.lastPipelineStage),new Error("[InkBuilderSettings] Invalid lastPipelineStage found")}if(this.lastPipelineStage==Li.POLYGON_SIMPLIFIER&&(console.warn("[InkBuilderSettings] Pipeline stage POLYGON_SIMPLIFIER is deprecated. POLYGON_MERGER stage is recommended as last stage."),this.polygonSimplifier.epsilon=e.epsilon||.1),e.keepAllData){e.keepAllData.includes(this.lastPipelineStage)&&(console.warn("[InkBuilderSettings] keepAllData contains last pipeline stage ".concat(this.lastPipelineStage,". Duplicate is dropped.")),e.keepAllData.remove(this.lastPipelineStage)),e.keepAllData.includes(Li.PATH_PRODUCER)&&(console.warn("[InkBuilderSettings] keepAllData contains stage ".concat(Li.PATH_PRODUCER,", sensor input is accessible through InputDevice output - SensorData. Dropped from keepAllData.")),e.keepAllData.remove(Li.PATH_PRODUCER)),e.keepAllData.includes(Li.SPLINE_PRODUCER)&&(console.warn("[InkBuilderSettings] keepAllData contains stage ".concat(Li.SPLINE_PRODUCER,". Use getSpline() method to acceess spline data. Dropped from keepAllData.")),e.keepAllData.remove(Li.SPLINE_PRODUCER));var n,r=Gi(e.keepAllData);try{for(r.s();!(n=r.n()).done;){var i=n.value;if(this.excludedPipelineStages.includes(i))throw new Error("[InkBuilderSettings] keepAllData contains stage ".concat(i,", configured as stage in excludedPipelineStages."));switch(i){case Li.SMOOTHER:this.smoother.keepAllData=!0;break;case Li.SPLINE_INTERPOLATOR:this.splineInterpolator.keepAllData=!0;break;case Li.BRUSH_APPLIER:this.brushApplier.keepAllData=!0;break;case Li.CONVEX_HULL_CHAIN_PRODUCER:this.convexHullChainProducer.keepAllData=!0;break;default:throw console.warn(i),new Error("Invalid stage found")}}}catch(e){r.e(e)}finally{r.f()}_(this,Hi,e.keepAllData)}else _(this,Hi,[]);if(e.pathPointCalculator&&(this.calculator=e.pathPointCalculator,this.pathProducer.pathPointCalculator=e.pathPointCalculator),!e.layout)throw new Error("[InkBuilderSettings] layout property is required");var o=e.pathPointProps||{};if(this.layout=e.layout,this.brush instanceof tn){if(this.layout.includes(me.Property.RED))throw new Error("RED layout channel is not supported for non particles strokes");if(this.layout.includes(me.Property.GREEN))throw new Error("GREEN layout channel is not supported for non particles strokes");if(this.layout.includes(me.Property.BLUE))throw new Error("BLUE layout channel is not supported for non particles strokes");if(this.layout.includes(me.Property.ALPHA))throw new Error("ALPHA layout channel is not supported for non particles strokes")}if(!this.layout.includes(me.Property.RED)&&isNaN(o.red))throw new Error("Stroke color red channel information is required. Please provide via layout or through configure settings via pathPointProps property.");if(!this.layout.includes(me.Property.GREEN)&&isNaN(o.green))throw new Error("Stroke color green channel information is required. Please provide via layout or through configure settings via pathPointProps property.");if(!this.layout.includes(me.Property.BLUE)&&isNaN(o.blue))throw new Error("Stroke color blue channel information is required. Please provide via layout or through configure settings via pathPointProps property.");if(!this.layout.includes(me.Property.ALPHA)&&isNaN(o.alpha))throw new Error("Stroke color alpha channel information is required. Please provide via layout or through configure settings via pathPointProps property.");this.pathProducer.layout=this.layout,this.smoother.dimsCount=this.layout.length,this.splineProducer.layout=this.layout,this.splineProducer.pathPointProps=o,this.configured=!0}},{key:"add",value:function(e,t){if(!this.configured)throw new Error("InkBuilder instance is not configured yet, use configure method to configure the instance.");if(!this.calculator)throw new Error("InkBuilder instance is not configured properly, pathPointCalculator property is required");if(!e.phase)throw new Error("SensorPoint phase is not found");this.phase=e.phase;var n,r=new Ui(e);t&&(this.prediction?n=new Ui(t):console.warn("Prediction sensor point is available, but ignored, prediction is disabled")),this.device&&(this.phase==Pr.Phase.BEGIN&&this.device.openStream(e),this.device.add(r),this.phase==Pr.Phase.END&&(this.sensorData=this.device.closeStream()));var i=this.pathProducer.add(this.phase,r,n);this.pathSegment.add(this.phase,i.added,i.predicted)}},{key:"ignore",value:function(e){if(!e.phase)throw new Error("SensorPoint phase is not found");this.device&&e&&e.phase==Pr.Phase.UPDATE&&this.device.add(new Ui(e),!0)}},{key:"build",value:function(){throw new Error("InkBuilderAbstract.build() is abstract and should be implemented")}},{key:"processSegment",value:function(e,t,n){throw new Error("InkBuilderAbstract.processSegment(path, type, lastSegment) is abstract and should be implemented")}},{key:"getSensorData",value:function(){return this.sensorData}},{key:"getSpline",value:function(){return this.splineProducer.allData}},{key:"getAllData",value:function(){if(0!=B(this,Hi).length){var e,t={},n=Gi(B(this,Hi));try{for(n.s();!(e=n.n()).done;){var r=e.value;switch(r){case Li.SMOOTHER:t.smoother=this.smoother.allData;break;case Li.SPLINE_INTERPOLATOR:t.interpolatedSpline=this.splineInterpolator.allData;break;case Li.BRUSH_APPLIER:t.shapesPath=this.brushApplier.allData;break;case Li.CONVEX_HULL_CHAIN_PRODUCER:t.convexPath=this.convexHullChainProducer.allData;break;default:throw console.warn(r),new Error("Invalid stage found")}}}catch(e){n.e(e)}finally{n.f()}return t}}},{key:"getInkPath",value:function(){var e,t;switch(this.lastPipelineStage){case Li.SPLINE_PRODUCER:return void console.warn("Pipeline stage SPLINE_PRODUCER is configured as lastPipelineStage. Ink Path is a result from Spline processing.");case Li.SPLINE_INTERPOLATOR:e=this.splineInterpolator.allData;break;case Li.BRUSH_APPLIER:e=this.brushApplier.allData;break;case Li.CONVEX_HULL_CHAIN_PRODUCER:e=this.convexHullChainProducer.allData;break;case Li.POLYGON_MERGER:e=this.polygonMerger.allData;break;case Li.POLYGON_SIMPLIFIER:e=this.polygonSimplifier.allData;break;default:throw console.warn(this.lastPipelineStage),new Error("Invalid lastPipelineStage found")}this.concatSegments&&(this.lastPipelineStage!=Li.POLYGON_MERGER&&this.lastPipelineStage!=Li.POLYGON_SIMPLIFIER||(t=this.polygonMerger.process(e)),this.lastPipelineStage==Li.POLYGON_SIMPLIFIER&&(t=this.polygonSimplifier.process(t)),t&&(e=new ii(t)));return e}},{key:"abort",value:function(){this.device&&this.device.closeStream(!0),this.reset()}},{key:"reset",value:function(e){this.pointerID=e,this.phase=void 0,this.concatSegments=!1,this.lastPipelineStage=void 0,this.excludedPipelineStages.clear(),this.sensorData=void 0,this.pathProducer.reset(),this.smoother.reset(),this.splineProducer.reset(),this.distanceInterpolator.reset(),this.curvatureInterpolator.reset(),this.brushApplier.reset(),this.convexHullChainProducer.reset(),this.polygonMerger.reset(),this.polygonSimplifier.reset(),this.configured=!1}}]),e}();function Zi(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}Wi.Phase=Pr.Phase;var qi=function(e){T(n,e);var t=Zi(n);function n(){var e;return k(this,n),(e=t.call(this)).convexHullChainProducer=new Pi,e}return S(n,[{key:"build",value:function(){var e=this.buildSegment();return e.phase=this.phase,e.pointerID=this.pointerID,this.onComplete&&this.onComplete(e),this.phase==Bi.END&&(delete this.phase,delete this.pipeline),e}},{key:"buildSegment",value:function(){var e={};return this.pathSegment.accumulatedAddition.length>0&&(e.added=this.processSegment(this.pathSegment.accumulatedAddition,_i.ADDITION,this.pathSegment.last),e.pipeline=this.pipeline,e.added&&(e.added.segment=!0)),this.prediction&&this.pathSegment.lastPrediction.length>0&&(e.predicted=this.processSegment(this.pathSegment.lastPrediction,_i.PREDICTION,this.pathSegment.last),e.predicted&&(e.predicted.segment=!0)),this.pathSegment.reset(),e}},{key:"processSegment",value:function(e,t,n){if(this.excludedPipelineStages.includes(Li.SMOOTHER)||(e=this.smoother.process(e,t,n)),e=this.splineProducer.process(e,t,n))return this.lastPipelineStage==Li.SPLINE_PRODUCER?e:this.processSpline(e,t,n)}},{key:"processSpline",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:_i.PROCESSOR,n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];t==_i.ADDITION&&(this.pipeline={}),t==_i.ADDITION&&(this.pipeline.spline=e);var r,i=this.splineInterpolator.process(e,t,n);if(this.lastPipelineStage==Li.SPLINE_INTERPOLATOR)return i;if(i)return t==_i.ADDITION&&(this.pipeline.interpolatedSpline=i),i=this.brushApplier.process(i,t,n),this.lastPipelineStage==Li.BRUSH_APPLIER?i:(t==_i.ADDITION&&(this.pipeline.shapesPath=i),i=this.convexHullChainProducer.process(i,t,n),this.lastPipelineStage==Li.CONVEX_HULL_CHAIN_PRODUCER?i:(t==_i.ADDITION&&(this.pipeline.convexPath=i),t==_i.PREDICTION?i:this.excludedPipelineStages.includes(Li.POLYGON_MERGER)||(r=this.polygonMerger.process(i,t,n),this.lastPipelineStage!=Li.POLYGON_MERGER)?(this.excludedPipelineStages.includes(Li.POLYGON_SIMPLIFIER)||(r=this.polygonSimplifier.process(r,t,n)),new ii(r)):new ii(r)));if(t==_i.PROCESSOR)throw new Error("InkBuilder processSpline failed for spline",e)}}]),n}(Wi),Ki=function(){function e(){k(this,e),this.queue=Promise.resolve(),this.thenables=[]}var t;return S(e,[{key:"then",value:function(e,t,n){var r=this;return this.thenables.push(e),this.queue=this.queue.then((function(){return r.thenables.shift(),e.canceled?Promise.resolve():e.apply(void 0,arguments)})),t&&this.then((function(e){return t(e,n)})),this}},{key:"catch",value:function(e){return this.queue=this.queue.catch(e),this}},{key:"cancel",value:function(){this.thenables.forEach((function(e){return e.canceled=!0}))}},{key:"isEmpty",value:function(){return 0==this.thenables.length}}],[{key:"serial",value:(t=be(Pe.mark((function t(n,r){var i;return Pe.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=new e,n.forEach((function(e,t){return i.then(e,r,t)})),t.abrupt("return",i.queue);case 3:case"end":return t.stop()}}),t)}))),function(e,n){return t.apply(this,arguments)})}]),e}();function Ji(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var $i,Qi,eo=function(e){T(s,e);var t,n,r,i,o,a=Ji(s);function s(){var e;return k(this,s),(e=a.call(this)).convexHullChainProducer=new Ai,Object.defineProperty(A(e),"closed",{get:function(){return e.convexHullChainProducer.closed},enumerable:!0}),e.queue=new Ki,e}return S(s,[{key:"open",value:(o=be(Pe.mark((function e(){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.convexHullChainProducer.open();case 2:case"end":return e.stop()}}),e,this)}))),function(){return o.apply(this,arguments)})},{key:"close",value:function(){this.convexHullChainProducer.close()}},{key:"onComplete",value:function(e){throw new Error("InkBuilderAbstract.onComplete(pathSegment) is abstract and should be implemented")}},{key:"build",value:function(){var e=this;if(!this.buildPhase||this.phase==Bi.END){var t=this.phase;this.queue.then((function(){return e.buildPhase=t,e.buildSegment()})).then((function(n){e.buildPhase=null,n.phase=t,n.pointerID=e.pointerID,e.onComplete(n),t==Bi.END&&(delete e.phase,delete e.pipeline)}))}}},{key:"buildChain",value:(i=be(Pe.mark((function e(){var t;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.buildSegment();case 2:return(t=e.sent).phase=this.phase,t.pointerID=this.pointerID,this.onComplete(t),this.phase==Bi.END&&delete this.phase,e.abrupt("return",t);case 8:case"end":return e.stop()}}),e,this)}))),function(){return i.apply(this,arguments)})},{key:"buildSegment",value:(r=be(Pe.mark((function e(){var t;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t={},!(this.pathSegment.accumulatedAddition.length>0)){e.next=7;break}return e.next=4,this.processSegment(this.pathSegment.accumulatedAddition,_i.ADDITION,this.pathSegment.last);case 4:t.added=e.sent,t.pipeline=this.pipeline,t.added&&(t.added.segment=!0);case 7:if(!(this.prediction&&this.pathSegment.lastPrediction.length>0)){e.next=12;break}return e.next=10,this.processSegment(this.pathSegment.lastPrediction,_i.PREDICTION,this.pathSegment.last);case 10:t.predicted=e.sent,t.predicted&&(t.predicted.segment=!0);case 12:return this.pathSegment.reset(),e.abrupt("return",t);case 14:case"end":return e.stop()}}),e,this)}))),function(){return r.apply(this,arguments)})},{key:"processSegment",value:(n=be(Pe.mark((function e(t,n,r){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.excludedPipelineStages.includes(Li.SMOOTHER)||(t=this.smoother.process(t,n,r)),t=this.splineProducer.process(t,n,r)){e.next=4;break}return e.abrupt("return");case 4:if(this.lastPipelineStage!=Li.SPLINE_PRODUCER){e.next=6;break}return e.abrupt("return",t);case 6:return e.abrupt("return",this.processSpline(t,n,r));case 7:case"end":return e.stop()}}),e,this)}))),function(e,t,r){return n.apply(this,arguments)})},{key:"processSpline",value:(t=be(Pe.mark((function e(t){var n,r,i,o,a=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=a.length>1&&void 0!==a[1]?a[1]:_i.PROCESSOR,r=!(a.length>2&&void 0!==a[2])||a[2],n==_i.ADDITION&&(this.pipeline={}),n==_i.ADDITION&&(this.pipeline.spline=t),i=this.splineInterpolator.process(t,n,r),this.lastPipelineStage!=Li.SPLINE_INTERPOLATOR){e.next=7;break}return e.abrupt("return",i);case 7:if(i){e.next=11;break}if(n!=_i.PROCESSOR){e.next=10;break}throw new Error("InkBuilderAsync processSpline failed for spline",t);case 10:return e.abrupt("return");case 11:if(n==_i.ADDITION&&(this.pipeline.interpolatedSpline=i),i=this.brushApplier.process(i,n,r),this.lastPipelineStage!=Li.BRUSH_APPLIER){e.next=15;break}return e.abrupt("return",i);case 15:return n==_i.ADDITION&&(this.pipeline.shapesPath=i),e.next=18,this.convexHullChainProducer.process(i,n,r);case 18:if(i=e.sent,this.lastPipelineStage!=Li.CONVEX_HULL_CHAIN_PRODUCER){e.next=21;break}return e.abrupt("return",i);case 21:if(n==_i.ADDITION&&(this.pipeline.convexPath=i),n!=_i.PREDICTION){e.next=24;break}return e.abrupt("return",i);case 24:if(this.excludedPipelineStages.includes(Li.POLYGON_MERGER)){e.next=28;break}if(o=this.polygonMerger.process(i,n,r),this.lastPipelineStage!=Li.POLYGON_MERGER){e.next=28;break}return e.abrupt("return",new ii(o));case 28:return this.excludedPipelineStages.includes(Li.POLYGON_SIMPLIFIER)||(o=this.polygonSimplifier.process(o,n,r)),e.abrupt("return",new ii(o));case 30:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"abort",value:function(){this.buildPhase=null,this.queue.cancel(),Mn(C(s.prototype),"abort",this).call(this)}}]),s}(Wi);function to(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}function no(e,t,n){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}var ro=new WeakMap,io=new WeakMap,oo=new WeakMap,ao=new WeakMap,so=new WeakMap,uo=new WeakMap,co=new WeakMap,lo=new WeakMap,ho=function(e){T(r,e);var t,n=to(r);function r(e,t,i,o){var a;k(this,r),no(A(a=n.call(this,t.id)),ro,{writable:!0,value:void 0}),no(A(a),io,{writable:!0,value:void 0}),no(A(a),oo,{writable:!0,value:void 0}),no(A(a),ao,{writable:!0,value:void 0}),no(A(a),so,{writable:!0,value:void 0}),no(A(a),uo,{writable:!0,value:r.RenderMode.SOURCE_OVER}),no(A(a),co,{writable:!0,value:void 0}),no(A(a),lo,{writable:!0,value:r.CompressionType.AUTO}),t.id||(t.id=a.id),_(A(a),ao,o);var s=new St(t.layout,t.pointProps);Object.defineProperty(A(a),"target",{get:function(){return console.warn("Stroke 'target' property is deprecated. Do not affects Stroke behaviour.")},set:function(e){return console.warn("Stroke 'target' property is deprecated. Do not affects Stroke behaviour.")}});var u=!0;return Object.defineProperty(A(a),"layout",{value:t.layout,enumerable:!0}),Object.defineProperty(A(a),"points",{get:function(){return t.points},enumerable:!0}),Object.defineProperty(A(a),"pointProps",{value:t.pointProps,enumerable:!0}),Object.defineProperty(A(a),"style",{value:s.style,enumerable:!0}),Object.defineProperty(A(a),"ts",{value:t.ts,enumerable:!0}),Object.defineProperty(A(a),"tf",{value:t.tf,enumerable:!0}),Object.defineProperty(A(a),"stride",{value:t.stride,enumerable:!0}),Object.defineProperty(A(a),"length",{value:t.length,enumerable:!0}),Object.defineProperty(A(a),"segmentsCount",{value:t.segmentsCount,enumerable:!0}),Object.defineProperty(A(a),"color",{get:function(){return s.style.color},set:function(e){t.color=e,B(A(a),io)&&(B(A(a),io).color=e)},enumerable:!0}),Object.defineProperty(A(a),"sensorData",{get:function(){return B(A(a),ao)},set:function(e){if(B(A(a),ao))throw new Error("sensorData is immutable");_(A(a),ao,e)}}),Object.defineProperty(A(a),"spline",{value:t,enumerable:!0}),Object.defineProperty(A(a),"path",{get:function(){return B(A(a),io)||a.buildPath(),B(A(a),io)},set:function(e){_(A(a),io,e),B(A(a),io)instanceof jr&&(B(A(a),io).style=s.style),_(A(a),oo,null)},enumerable:!0}),Object.defineProperty(A(a),"bounds",{get:function(){return B(A(a),oo)||_(A(a),oo,a.path.bounds),a.matrix?B(A(a),oo).transform(a.matrix).ceil():B(A(a),oo)},set:function(e){return _(A(a),oo,e)},enumerable:!0}),Object.defineProperty(A(a),"descriptor",{value:{brush:{}},enumerable:!0}),Object.defineProperty(A(a),"brush",{get:function(){return e||(e=a.descriptor.brush.value),e},set:function(n){if(u||(a.path=null),"string"==typeof n?n=new ht(n):n instanceof tn||n instanceof Tn?n=new ht(n.name,n):n instanceof ht||(n=new ht(n.name,n.value)),n instanceof Tn&&!t.randomSeed)throw new Error("Spline do not provides randomSeed. Raster rendering requires it.");e=null,a.descriptor.brush=n},enumerable:!0}),Object.defineProperty(A(a),"randomSeed",{get:function(){return B(A(a),so)},set:function(e){if(B(A(a),so))throw new Error("randomSeed is immutable");_(A(a),so,e)},enumerable:!0}),Object.defineProperty(A(a),"renderMode",{get:function(){return B(A(a),uo)},set:function(e){if(!e)throw new Error("Stroke renderMode is required");if(!ce.isValidURL(e))throw new Error("The renderMode ".concat(e," is not a well formed URI"));_(A(a),uo,e)}}),Object.defineProperty(A(a),"blendMode",{get:function(){return r.RenderMode.getBlendMode(B(A(a),uo))},set:function(e){if(!a.blendMode)throw new Error("Override user defined renderMode '".concat(B(A(a),uo),"' is not allowed."));_(A(a),uo,r.RenderMode.get(e))}}),Object.defineProperty(A(a),"precisionSchema",{get:function(){return B(A(a),co)},set:function(e){if(B(A(a),co))throw new Error("precisionSchema is immutable, precisionSchema.update(schema) is an alternative");if(e){if(!(e instanceof vr))throw new Error("Expected precisionSchema type is PrecisionSchema");_(A(a),co,e),_(A(a),lo,r.CompressionType.COMPUTED)}else _(A(a),lo,r.CompressionType.NONE)}}),Object.defineProperty(A(a),"compressionType",{get:function(){return B(A(a),lo)},set:function(e){if(!e)throw new Error("Stroke compressionType is required");if(B(A(a),lo)==r.CompressionType.COMPUTED&&e==r.CompressionType.NONE)throw new Error("compressionType NONE is not applicable for compressed stroke");_(A(a),lo,e)}}),Object.defineProperty(A(a),"uri",{get:function(){return B(A(a),ro)||_(A(a),ro,je.createStrokeURI(a.id)),B(A(a),ro)},enumerable:!0}),a.brush=e,a.path=i,a.sensorDataOffset=0,a.sensorDataMapping=[],u=!1,a}return S(r,[{key:"buildPath",value:function(){if(this.pathProceessInProgress)throw new Error("Init process in progress. Await init stroke.");$i||($i=new qi),$i.configure(this.buildInkBuilderSettings()),this.path=$i.processSpline(this.spline)}},{key:"init",value:(t=be(Pe.mark((function e(t){var n,r,i,o;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.pathProceessInProgress=!0,Qi){e.next=6;break}if(!(Qi=new eo).closed){e.next=6;break}return e.next=6,Qi.open();case 6:return Qi.configure(this.buildInkBuilderSettings(t)),e.next=9,Qi.processSpline(this.spline);case 9:this.path=e.sent,Object.keys(Qi.allData).length>0&&(n=Qi.allData,r=n.interpolatedSpline,i=n.shapesPath,o=n.convexPath,this.pipeline={},r&&(this.pipeline.interpolatedSpline=r),i&&(this.pipeline.shapesPath=i),o&&(this.pipeline.convexPath=o)),delete this.pathProceessInProgress;case 12:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"buildInkBuilderSettings",value:function(e){return r.onPipeline?r.onPipeline(this):Object.assign({},{brush:this.brush,layout:this.layout,pathPointProps:this.pointProps},e)}},{key:"invalidateBounds",value:function(){_(this,oo,null)}},{key:"clone",value:function(){var e,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];B(this,io)&&(e=t?B(this,io):B(this,io).clone());var i=this.spline.clone();n&&(i.id=this.id);var o=new r(this.descriptor.brush,i,e,this.sensorData);return o.randomSeed=B(this,so),o.renderMode=B(this,uo),o.sensorDataOffset=this.sensorDataOffset,o.sensorDataMapping=this.sensorDataMapping.clone(),o}},{key:"getSensorPoint",value:function(e){if(e>=this.length||e<0)throw new Error("Index ".concat(e," out of range - (0, ").concat(this.length-1,")"));var t;if(0==this.sensorDataOffset&&e>0&&e--,this.sensorData){var n,r=this.sensorData.inkStream;if(r)this.sensorDataMapping.length>0?n=e>=this.sensorDataMapping.length?this.sensorDataMapping.last:this.sensorDataMapping[e]:(n=this.sensorDataOffset+e)>=r.length&&(n=r.length-1),(t=r.get(n)).index=n,t.timespan=t.timestamp,t.timestamp+=this.sensorData.created}return t}},{key:"getPoint",value:function(e){return this.spline.getPoint(e)}},{key:"setPoint",value:function(e,t){var n=this,r=e*this.stride;this.layout.forEach((function(e,i){return n.points[r+i]=t.getProperty(e)}))}},{key:"pointAt",value:function(e){return this.getPoint(e)}},{key:"getSegment",value:function(e){return this.spline.getSegment(e)}},{key:"getAverageWidth",value:function(){var e=0;if(this.layout.includes(me.Property.SIZE)){for(var t=0,n=0;n<this.length;n++)t+=this.getPointRef(n).size;e=t/this.length}else e=this.pointProps.size;return e}},{key:"split",value:function(e){var t=this,n=e.map((function(e){return t.slice(e)}));return n.includes(this)?void 0:n}},{key:"slice",value:function(e){if(0==e.pointIndexStart&&e.pointIndexEnd+1==this.length&&e.ts==this.ts&&e.tf==this.tf)return this;var t=this.spline.slice(e),n=new r(this.descriptor.brush,t,void 0,this.sensorData);if(n.randomSeed=B(this,so),n.renderMode=B(this,uo),this.sensorData){var i,o=e.pointIndexStart;if(0==this.sensorDataOffset&&e.pointIndexStart>0&&(o=e.pointIndexStart-1),n.sensorDataOffset=this.sensorDataOffset+o,this.sensorDataMapping.length>0)i=e.pointIndexEnd>this.sensorDataMapping.length?this.sensorDataMapping.length:0==n.sensorDataOffset?e.pointIndexEnd:e.pointIndexEnd+1,n.sensorDataMapping=this.sensorDataMapping.slice(o,i);else n.sensorDataMapping=[]}return n}},{key:"transform",value:function(e){if(e||(e=this.matrix,this.matrix=null),e){if(this.spline.transform(e),B(this,io)&&this.path.transform(e),this.pipeline){var t=this.pipeline,n=t.interpolatedSpline,i=t.shapesPath,o=t.convexPath;n&&n.transform(e),i&&i.transform(e),o&&o.transform(e)}B(this,lo)!=r.CompressionType.NONE&&(_(this,co,void 0),_(this,lo,r.CompressionType.AUTO)),_(this,oo,null)}}},{key:"setTransform",value:function(e){this.matrix=e}}],[{key:"createInstance",value:function(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=arguments.length>4?arguments[4]:void 0,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0,s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:1,u=i.id,c=i.color;u&&delete i.id,c&&(delete i.color,i=Object.assign({},i),n.includes(me.Property.RED)||(i.red=c.red),n.includes(me.Property.GREEN)||(i.green=c.green),n.includes(me.Property.BLUE)||(i.blue=c.blue),n.includes(me.Property.ALPHA)||(i.alpha=c.alpha));var l=Mr.createSharedInstance(n,t,i,a,s);l.id=u;var h=new r(e,l);return h.randomSeed=o,h}},{key:"validatePath",value:function(e){if(!e)return!1;if(0==e.length)return!1;if(e instanceof ii)return!0;if(Array.isArray(e))throw new Error("path should be instance of InkPath2D");var t=!1,n=0,r=!1,i=e.pointProps,o=i.size,a=i.red,s=i.green,u=i.blue,c=i.alpha;if(!(e instanceof jr)){var l=e.points.length,h=e.layout.length;r=0==l||l<4*h,n=l%h}return 0!=n?console.error("The points array (length: ".concat(e.points.length,") does not refer to provided layout (").concat(e.layout.map((function(e){return e.name})).join(", "),")")):r?console.error("Less than needed minimum of points passed (At least 4 points are needed to define a path)!"):!e.layout.includes(me.Property.SIZE)&&isNaN(o)?console.error("Either the size property must be set or the path layout must include a SIZE property"):!e.layout.includes(me.Property.RED)&&isNaN(a)?console.error("Either the color property must be set or the path layout must include a RED property"):!e.layout.includes(me.Property.GREEN)&&isNaN(s)?console.error("Either the color property must be set or the path layout must include a GREEN property"):!e.layout.includes(me.Property.BLUE)&&isNaN(u)?console.error("Either the color property must be set or the path layout must include a BLUE property"):!e.layout.includes(me.Property.ALPHA)&&isNaN(c)?console.error("Either the color property must be set or the path layout must include a ALPHA property"):t=!0,t}},{key:"decodeInkPath",value:function(e){if("InkPath2D"==e.type)return ii.fromJSON(e);if("InterpolatedSpline"==e.type)return jr.fromJSON(e);throw new Error("Decode ink path faild. Cannot identify type: ".concat(e.type))}}]),r}(z);function fo(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return po(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return po(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function po(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}ho.RenderMode=Object.assign.apply(Object,[{}].concat(ue(Object.keys(un).map((function(e){return x({},e,"will://rasterization/3.0/blend-mode/".concat(ce.getPropName(e,!0)))}))))),ho.RenderMode.get=function(e){return ho.RenderMode[ce.getEnumValueName(e.replace(/-/g,"_"))]},ho.RenderMode.getBlendMode=function(e){return un[Object.keys(ho.RenderMode).filter((function(t){return ho.RenderMode[t]==e})).first]},ho.Target={},Object.defineProperty(ho.Target,"2D",{get:function(){return console.warn("Stroke 'Target[2D]' enum is deprecated")}}),Object.defineProperty(ho.Target,"GL",{get:function(){return console.warn("Stroke 'Target[GL]' enum is deprecated")}}),Object.defineEnum(ho,"CompressionType",["AUTO","NONE","COMPUTED"]);var yo=function(){function e(){var t;k(this,e),this.statistics={},this.statisticCounts={},Object.defineProperty(this,"calculator",{get:function(){return t},set:function(e){if(t&&!(t instanceof hr))throw new Error("calculator should be instance of PrecisionCalculator");t=e},enumerable:!0})}return S(e,[{key:"determinePrecisions",value:function(e){this.reset();var t,n=fo(e);try{for(n.s();!(t=n.n()).done;){var r=t.value;this.updatePrecisionSchema(r)}}catch(e){n.e(e)}finally{n.f()}this.printStats()}},{key:"updatePrecisionSchema",value:function(e){var t=this.determinePrecisionSchema(e);e.precisionSchema?e.precisionSchema.update(t):e.precisionSchema=t}},{key:"determinePrecisionSchema",value:function(t){if(t.compressionType==ho.CompressionType.AUTO){if(!this.calculator)throw new Error("PrecisionDetector calculator property is required");var n,r=Number.MAX_SAFE_INTEGER,i=Number.MAX_SAFE_INTEGER,o=Number.MAX_SAFE_INTEGER,a=Number.MAX_SAFE_INTEGER,s=Number.MAX_SAFE_INTEGER,u=fo(t.layout);try{for(u.s();!(n=u.n()).done;){var c=n.value,l=void 0,h=t.spline.getChannelData(c),f=this.calculator.calculatePrecision(h,c);switch(c){case me.Property.X:case me.Property.Y:case me.Property.Z:l=e.StatType.POSITION,r=Math.min(f,r);break;case me.Property.SIZE:l=e.StatType.SIZE,i=Math.min(f,i);break;case me.Property.ROTATION:l=e.StatType.ROTATION,o=Math.min(f,o);break;case me.Property.SCALE_X:case me.Property.SCALE_Y:case me.Property.SCALE_Z:l=e.StatType.SCALE,a=Math.min(f,a);break;case me.Property.OFFSET_X:case me.Property.OFFSET_Y:case me.Property.OFFSET_Z:l=e.StatType.OFFSET,s=Math.min(f,s);break;default:continue}if(this.debug){var d=br.calculateError(h,f);this.addSampleStatistics(l,f,d,Math.min.apply(Math,ue(h)),Math.max.apply(Math,ue(h)))}}}catch(e){u.e(e)}finally{u.f()}r==Number.MAX_SAFE_INTEGER&&(r=0),i==Number.MAX_SAFE_INTEGER&&(i=0),o==Number.MAX_SAFE_INTEGER&&(o=0),a==Number.MAX_SAFE_INTEGER&&(a=0),s==Number.MAX_SAFE_INTEGER&&(s=0);var p=vr.encode({position:r,size:i,rotation:o,scale:a,offset:s});return new vr(p)}}},{key:"addSampleStatistics",value:function(t,n,r,i,o){if(this.debug){t!=e.StatType.TOTAL&&this.addSampleStatistics(e.StatType.TOTAL,n,r,i,o);var a=t.name;a in this.statistics||(this.statistics[a]={},this.statisticCounts[a]=0),n in this.statistics[a]||(this.statistics[a][n]={samplesCount:0,totalError:0,minError:Number.MAX_SAFE_INTEGER,maxError:0,sampleMinVal:Number.MAX_SAFE_INTEGER,sampleMaxVal:Number.MIN_SAFE_INTEGER}),this.statistics[a][n].samplesCount+=1,this.statistics[a][n].totalError+=r,this.statistics[a][n].minError=Math.min(this.statistics[a][n].minError,r),this.statistics[a][n].maxError=Math.max(this.statistics[a][n].maxError,r),this.statistics[a][n].sampleMinVal=Math.min(this.statistics[a][n].sampleMinVal,i),this.statistics[a][n].sampleMaxVal=Math.max(this.statistics[a][n].sampleMaxVal,o),this.statisticCounts[a]+=1}}},{key:"printStats",value:function(){var e=this;this.debug&&(Object.keys(this.statistics).forEach((function(t){Object.values(e.statistics[t]).forEach((function(n){n.meanTotalError=n.samplesCount>0?n.totalError/n.samplesCount:NaN,n.usage=e.statisticCounts[t]>0?n.samplesCount/e.statisticCounts[t]*100:NaN}));var n=Object.keys(e.statistics[t]).sort((function(e,t){return e>t}));console.log("***********************************************************************"),n.forEach((function(n){var r=e.statistics[t][n];console.log("Stat Type: ".concat(t," | Precision: ").concat(n)),console.log(" -> Count: ".concat(r.samplesCount," (").concat(r.usage,"%)")),console.log(" -> Min/Max Error: ".concat(r.minError," / ").concat(r.maxError)),console.log(" -> Sample Min/Max Value: ".concat(r.sampleMinVal," / ").concat(r.sampleMaxVal)),console.log(" -> TotalError: ".concat(r.totalError," / ").concat(r.meanTotalError," (mean)"))}))})),console.log("***********************************************************************"))}},{key:"reset",value:function(){this.statistics={},this.statisticCounts={}}}]),e}();Object.defineEnum(yo,"StatType",["TOTAL","POSITION","SIZE","ROTATION","SCALE","OFFSET"]);var vo=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.Type.UNKNOWN,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};k(this,e),this.uri=t,this.type=n,this.ontologyType,this.instance,this.internalID=r.internalID,this.externalModelID=r.modelID,this.isGloballyUnique=!!r.isGloballyUnique,this.belongsToCurrentModel=!!r.belongsToCurrentModel,this.triples=[],this.referencedEntities=[],this.referencedByEntities=[]}return S(e,[{key:"printDebugInfo",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";console.log("".concat(e,"----------------------------------------------------------------")),console.log("".concat(e,"EntityURI: ").concat(this.uri)),console.log("".concat(e,"Type: ").concat(this.type)),console.log("".concat(e,"OntologyType: ").concat(this.ontologyType)),console.log("".concat(e,"InternalEntityID: ").concat(this.internalID)),console.log("".concat(e,"ExternalModelID: ").concat(this.externalModelID)),console.log("".concat(e,"EntityInstance:"),this.instance),console.log("".concat(e,"IsGloballyUnique: ").concat(this.isGloballyUnique)),console.log("".concat(e,"BelongsToCurrentModel: ").concat(this.belongsToCurrentModel)),console.log("".concat(e,"ReferencedEntities: ").concat(this.referencedEntities.length)),this.referencedEntities.forEach((function(t){return console.log("".concat(e," -> ").concat(t.uri))})),console.log("".concat(e,"ReferencedByEntities: ").concat(this.referencedByEntities.length)),this.referencedByEntities.forEach((function(t){return console.log("".concat(e," -> ").concat(t.uri))}))}}]),e}();Object.defineEnum(vo,"Type",["UNKNOWN","NODE","STROKE","NAMED_ENTITY"]);var mo,go=function(){function e(t){var n;k(this,e),this.model=t,this.entities={},this.entityDetector=(x(n={},vo.Type.NODE,{regEx:new RegExp(/^(uim:node)(\/[a-zA-Z0-9-]{36})?(\/[a-zA-Z0-9-_]{1,})?\/([a-zA-Z0-9-]{36})(#chunk=[0-9]+,[0-9]+)?$/),entityIDGroupIndex:0,modelIDGroupIndex:2}),x(n,vo.Type.STROKE,{regEx:new RegExp(/^(uim:stroke)(\/[a-zA-Z0-9-]{36})?(\/)([a-zA-Z0-9-]{36})$/),entityIDGroupIndex:4,modelIDGroupIndex:2}),x(n,vo.Type.NAMED_ENTITY,{regEx:new RegExp(/^(uim:ne)(\/[a-zA-Z0-9-]{36})?(\/)([a-zA-Z0-9-]{36})$/),entityIDGroupIndex:0,modelIDGroupIndex:2}),n)}return S(e,[{key:"clean",value:function(){this.analyze(),this.cleanupInternals(),this.cleanupUnusedEntities()}},{key:"analyze",value:function(){var t=this,n={};this.model.knowledgeGraph.forEach((function(e){var r=n[e.subject];r||(r=t.determineEntityBasedOnURI(e.subject),n[e.subject]=r),r.triples.includes(e)||r.triples.push(e)})),Object.values(n).forEach((function(e){if(e.internalID&&e.belongsToCurrentModel)switch(e.type){case vo.Type.NODE:e.instance=t.model.getNode(e.internalID);break;case vo.Type.STROKE:e.instance=t.model.getStroke(e.internalID)}})),Object.values(n).forEach((function(t){t.triples.forEach((function(e){var r=n[e.object];r&&(t.referencedEntities.includes(r)||t.referencedEntities.push(r),r.referencedByEntities.includes(t)||r.referencedByEntities.push(t))}));var r=t.triples.filter((function(t){return t.predicate==e.CommonRDF.HAS_TYPE||"@"==t.predicate})).first;r&&(t.ontologyType=r.object)})),this.entities=n,this.debug&&this.printStats()}},{key:"determineEntityBasedOnURI",value:function(e){for(var t=vo.Type.UNKNOWN,n={},r=0,i=Object.keys(this.entityDetector);r<i.length;r++){var o=i[r];t=vo.Type[o];var a=this.entityDetector[o],s=a.regEx.exec(e);if(s){var u=s[a.entityIDGroupIndex],c=s[a.modelIDGroupIndex];if(!u)throw new Error("Failed to extract internal ID");c&&(c=c.substring(1)),n={internalID:u,externalModelId:c,isGloballyUnique:!!c,belongsToCurrentModel:!c||this.model.id&&this.model.id==c};break}}return new vo(e,t,n)}},{key:"cleanupInternals",value:function(){var e=new Set,t=[vo.Type.NODE,vo.Type.STROKE];Object.values(this.entities).forEach((function(n){t.includes(n.type)&&n.belongsToCurrentModel&&!n.instance&&e.add(n.uri)})),e.size>0&&(this.debug&&console.log("[KnowledgeGraphAnalyzer] Cleanup internals - ".concat(e.size,": ").concat(Array.from(e).join(", "))),this.model.knowledgeGraph.remove((function(t){return e.has(t.subject)||e.has(t.object)})),this.analyze())}},{key:"cleanupUnusedEntities",value:function(){var e=new Set;Object.values(this.entities).forEach((function(t){t.type!=vo.Type.NAMED_ENTITY||!t.belongsToCurrentModel||t.referencedByEntities.length>0||e.add(t.uri)})),e.size>0&&(this.debug&&console.log("[KnowledgeGraphAnalyzer] Cleanup unused entities - ".concat(e.size,": ").concat(Array.from(e).join(", "))),this.model.knowledgeGraph.remove((function(t){return e.has(t.subject)||e.has(t.object)})),this.analyze())}},{key:"printStats",value:function(){var e=0,t=Object.values(this.entities);console.log("[KnowledgeGraphAnalyzer] Print ".concat(t.length," entities analysis")),t.forEach((function(n){return n.printDebugInfo("[".concat(++e,"/").concat(t.length,"] "))}))}}]),e}();function bo(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}go.CommonRDF={HAS_TYPE:"http://www.w3.org/1999/02/22-rdf-syntax-ns#type",LOCALE:"http://ogp.me/ns#locale"},Object.defineProperty(globalThis,"DIGITAL_INK_DEBUG",{get:function(){return mo},set:function(e){mo=e||{},ye.debug=mo.InputListener,W.prototype.debug=mo.Pipeline,an.prototype.debug=mo.Pipeline,nn.prototype.debug=mo.PathPointContext,$n.prototype.debug=mo.RTree,Cn.prototype.debug=mo.GL,ir.prototype.debug=mo.RIFF,lr.prototype.debug=mo.RIFF,yo.prototype.debug=mo.PrecisionDetector,go.prototype.debug=mo.KnowledgeGraphAnalyzer},enumerable:!0,configurable:!0});var Eo=function(e){T(i,e);var t,n,r=bo(i);function i(){var e;return k(this,i),(e=r.call(this,i.WORKER_NAME,i.buildWorkerURL(),Ii.WorkerType.CLASSIC)).actions={},e}return S(i,[{key:"importBrushes",value:(n=be(Pe.mark((function e(t){var n;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=this.nextID,this.actions[n]={expected:this.workers.length},e.next=4,this.broadcastMessage({action:"IMPORT_BRUSHES",actionID:n,brushes:t.map((function(e){return e.toJSON()}))});case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"build",value:(t=be(Pe.mark((function e(t,n){var r;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!=t.length){e.next=2;break}return e.abrupt("return");case 2:return r=this.nextID,this.actions[r]={settings:this.encodeSettings(n),strokes:t,queue:t.slice(),expected:t.length},this.updateProgress(0,t.length),e.next=7,this.broadcast("BUILD",r);case 7:return e.abrupt("return",e.sent);case 8:case"end":return e.stop()}}),e,this)}))),function(e,n){return t.apply(this,arguments)})},{key:"encodeSettings",value:function(e){if(e)return(e=Object.assign({},e)).excludedPipelineStages&&(e.excludedPipelineStages=e.excludedPipelineStages.map((function(e){return e.name}))),e.lastPipelineStage&&(e.lastPipelineStage=e.lastPipelineStage.name),e.keepAllData&&(e.keepAllData=e.keepAllData.map((function(e){return e.name}))),e}},{key:"buildRequestMessage",value:function(e,t){var n={action:e,actionID:t},r=this.actions[t];if("BUILD"!=e)throw new Error("Unknow data action found: ".concat(e));var i=r.queue.shift();if(i)return i.spline.points.buffer instanceof ArrayBuffer&&this.transferables.push(i.spline.points.buffer),i.spline.encoding=lt.Encoding.NONE,n.index=r.strokes.indexOf(i),n.brushName=i.brush.name,n.spline=i.spline.toJSON(),n.settings=r.settings,n}},{key:"recieve",value:function(e){var t=this.actions[e.actionID];switch(t.expected--,e.action){case"IMPORT_BRUSHES":break;case"BUILD":if(this.update(e.actionID,e.index,e.path,e.splineBuffer,e.pipeline),t.expected>0){var n=this.buildRequestMessage(e.action,e.actionID);n&&this.send(e.worker,n)}break;default:throw new Error("Unknow data action found: ".concat(e.action))}0==t.expected&&(delete this.actions[e.actionID],this.resolve(e.actionID))}},{key:"update",value:function(e,t,n,r,i){var o=this.actions[e],a=o.strokes[t];r instanceof ArrayBuffer&&(a.spline.buffer=r),a.path=ho.decodeInkPath(n),i&&(i.interpolatedSpline&&(i.interpolatedSpline=jr.fromJSON(i.interpolatedSpline)),i.shapesPath&&(i.shapesPath=ii.fromJSON(i.shapesPath)),i.convexPath&&(i.convexPath=ii.fromJSON(i.convexPath)),a.pipeline=i);var s=100*(o.strokes.length-o.expected)/o.strokes.length;this.updateProgress(s,o.expected,a)}},{key:"updateProgress",value:function(e,t,n){}}],[{key:"buildWorkerURL",value:function(){if(("undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("digital-ink-min.js",document.baseURI).href).contains("/wacom-src/"))return"/node_modules/digital-ink/workers/".concat(i.WORKER_NAME,".js");if("function"!=typeof DedicatedWorkerGlobalScope){var e=void 0===l?"undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("digital-ink-min.js",document.baseURI).href:l;return(e=e.substring(0,e.lastIndexOf("/"))).endsWith("workers")||(e+="/workers"),e+="/".concat(i.WORKER_NAME),"undefined"==typeof navigator?e+=".mjs":e+=".js",e}}}]),i}(Ii);function Po(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}x(Eo,"WORKER_NAME","InkPathProvider");var ko=function(e){T(n,e);var t=Po(n);function n(e){var r;return k(this,n),r=t.call(this),Object.defineProperty(A(r),"surface",{value:e.canvas,enumerable:!0}),Object.defineProperty(A(r),"ctx",{value:e,enumerable:!0}),Object.defineProperty(A(r),"renderingContext",{value:new Zn(e),enumerable:!0}),Object.defineProperty(A(r),"bounds",{get:function(){return new $(0,0,r.width,r.height)},enumerable:!0}),r.ctx.getContextAttributes||(r.ctx.getContextAttributes=function(){return{}}),r}return S(n,[{key:"width",get:function(){return this.surface.width}},{key:"height",get:function(){return this.surface.height}},{key:"clear",value:function(e,t){if(t){if(wt.isColor(e))throw new Error("`clear` first argument should be Rectangle")}else wt.isColor(e)&&(t=e,e=null);e||(e=this.bounds),this.ctx.clearRect(e.left,e.top,e.width,e.height),t&&(this.ctx.fillStyle=t.toString(),this.ctx.fillRect(e.left,e.top,e.width,e.height))}},{key:"draw",value:function(e){return this.drawStroke(e.brush,e.path,e.color,e.matrix)}},{key:"drawStroke",value:function(e,t,n,r){if(!ho.validatePath(t))return null;if(!(e instanceof tn))throw new Error("Incompatible brush found. It should be Brush2D instance.");r?this.matrix&&(r=this.matrix.multiply(r)):r=this.matrix;var i=t.bounds;if(i&&(r&&(i=i.transform(r)),i=i.ceil()),i=this.bounds.intersect(i)){if(this.ctx.save(),r?this.ctx.setTransform(r.a,r.b,r.c,r.d,r.tx,r.ty):(this.ctx.rect(i.left,i.top,i.width,i.height),this.ctx.clip()),t instanceof ii)this.renderingContext.fillShape(t,n),e.pattern&&this.renderingContext.fillShape(t,e.pattern);else{if(!(t instanceof jr))throw new Error("drawStroke 'path' type missmatch, expected oneof(InkPath2D, InterpolatedSpline)");this.drawSpline(t,n)}this.ctx.restore()}return i}},{key:"drawSpline",value:function(e,t){t||(t=e.color),this.ctx.fillStyle="rgb(".concat(t.red,", ").concat(t.green,", ").concat(t.blue,")");for(var n=0;n<e.length;n++){var r=e.getPointRef(n),i=r.size/2;this.ctx.beginPath(),this.ctx.arc(r.x,r.y,i,0,2*Math.PI),this.ctx.closePath(),this.ctx.fill()}}},{key:"fill",value:function(e,t){var n;if($.isRect(e))n=e;else{if("number"==typeof e[0]&&e.length%2==0&&(e=Yt.createInstance(e)),e instanceof Yt)e=new Ln(e);else if(!(e instanceof Ln))throw new Error("fill shape type missmatch, expected oneof(PolygonArray, Polygon, Rect)");n=e.bounds}return(n=this.bounds.intersect(n))&&(n=n.ceil(),$.isRect(e)?(this.ctx.fillStyle=t.toString(),this.ctx.fillRect(e.left,e.top,e.width,e.height)):(this.ctx.save(),this.ctx.rect(n.left,n.top,n.width,n.height),this.ctx.clip(),this.renderingContext.fillShape(e,t),this.ctx.restore())),n}},{key:"blend",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(t.mode||(t.mode=un.SOURCE_OVER),t.transform&&t.destinationRect)throw new Error("`destinationRect` is not applicable with `transform`");if(t.sourceRect&&!t.destinationRect)throw new Error("With `sourceRect`, `destinationRect` is required");if(t.destinationRect&&!t.sourceRect)throw new Error("With `destinationRect`, `sourceRect`is required");t.rect&&(t.sourceRect=t.rect,t.destinationRect=t.rect),this.ctx.save(),t.transform?this.ctx.setTransform(t.transform.a,t.transform.b,t.transform.c,t.transform.d,t.transform.tx,t.transform.ty):t.clipRect&&(this.ctx.rect(t.clipRect.left,t.clipRect.top,t.clipRect.width,t.clipRect.height),this.ctx.clip()),this.ctx.globalCompositeOperation=t.mode,t.sourceRect&&t.destinationRect?this.ctx.drawImage(e.ctx.canvas,t.sourceRect.left,t.sourceRect.top,t.sourceRect.width,t.sourceRect.height,t.destinationRect.left,t.destinationRect.top,t.destinationRect.width,t.destinationRect.height):this.ctx.drawImage(e.ctx.canvas,0,0),this.ctx.restore()}},{key:"createImageData",value:function(e,t,n){return new rt(e,t,n)}},{key:"getImageData",value:function(e){e||(e=this.bounds);var t=this.ctx.getImageData(e.x,e.y,e.width,e.height);return t.x=e.x,t.y=e.y,t}},{key:"putImageData",value:function(e,t,n){if(!(e instanceof rt))throw new Error("putImageData 'imageData' parameter is not instance of ImageData");(isNaN(t)||isNaN(n))&&(t=e.x||0,n=e.y||0),this.ctx.putImageData(e,t,n)}},{key:"readPixels",value:function(e){return this.getImageData(e).data}},{key:"writePixels",value:function(e,t){if(!(e instanceof Uint8Array||e instanceof Uint8ClampedArray))throw new Error("writePixels 'data' parameter is not instance of Uint8Array or Uint8ClampedArray");t||(t=this.bounds),e instanceof Uint8Array&&(e=new Uint8ClampedArray(e.buffer)),this.putImageData(this.createImageData(e,t.width,t.height),t.x,t.y)}},{key:"getExportCanvas",value:function(e){var t=this.surface;return e&&(e=e.intersect(this.bounds).ceil(),(t=new it(e.width,e.height)).getContext("2d").putImageData(this.getImageData(e),0,0)),t}},{key:"resize",value:function(e,t){this.surface.width=e,this.surface.height=t}}]),n}(ln);function wo(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var So=function(e){T(n,e);var t=wo(n);function n(e){return k(this,n),t.call(this,e)}return S(n,[{key:"createLayer",value:function(e,t){var r;r=e&&t?{width:e,height:t}:e&&e.width&&e.height?e:n.getDefaultSize(this.width,this.height);var i=new it(r.width,r.height);return new ko(i.getContext("2d",this.ctx.getContextAttributes()))}}],[{key:"createInstance",value:function(e,t,r,i){if(!e||"function"!=typeof e.getContext)throw new Error("CanvasRenderingContext2D context provider is required");if(t>0&&r>0&&(e.width=t,e.height=r),!(e.width>0&&e.height>0))throw new Error("width and height are required and should be positive whole numbers");return new n(e.getContext("2d",i))}}]),n}(ko),Io=function(){function e(t,n){var r=this;k(this,e),this.canvas=t,this.options=n,this.layer=t.createLayer(n),this.preliminaryLayer=void 0,this.restart=!0,this.runtime=!1,this.brush=void 0,this.color=void 0,this.backgroundColor=wt.TRANSPERENT,this.blendMode=un.SOURCE_OVER,this.matrix=void 0,this.strokeBounds=void 0,this.updatedArea=void 0,Object.defineProperty(this,"settings",{get:function(){return{brush:r.brush,color:r.color,backgroundColor:r.backgroundColor,blendMode:r.blendMode,matrix:r.matrix,randomSeed:r.initialRandomSeed}},enumerable:!0})}return S(e,[{key:"requestComposeLayer",value:function(){if(!this.restart)throw new Error("Operation is not supported for incomplete composition");var e=this.layer;return this.layer=this.canvas.createLayer(this.options),this.matrix&&this.layer.setTransform(this.matrix),this.reset(!0),e}},{key:"configure",value:function(e){e.brush&&(this.brush=e.brush),e.color&&(this.color=e.color),e.backgroundColor&&(this.backgroundColor=e.backgroundColor),e.blendMode&&(this.blendMode=e.blendMode),"transform"in e&&this.setTransform(e.transform),this.restart=!0}},{key:"setTransform",value:function(e){this.matrix=e,this.layer.setTransform(e),this.preliminaryLayer&&this.preliminaryLayer.setTransform(e)}},{key:"draw",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];e instanceof ho?(this.reset(!1),this.drawStroke(e),this.restart=!0):(this.restart&&this.reset(!0),this.drawSegment(e,t),t&&(this.restart=!0))}},{key:"drawStroke",value:function(e){throw new Error("StrokeRenderer.drawStroke(stroke) should be implemented")}},{key:"drawSegment",value:function(e){throw new Error("StrokeRenderer.drawSegment(path, endStroke) should be implemented")}},{key:"drawPreliminary",value:function(e){throw new Error("StrokeRenderer.drawPreliminary(path) should be implemented")}},{key:"abort",value:function(){this.restart=!0}},{key:"reset",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.runtime=e,this.incompleteStrokeBounds=null,this.strokeBounds=null,this.updatedArea=null,this.preliminaryDirtyArea=null,this.layer.clear(this.backgroundColor),this.preliminaryLayer&&this.preliminaryLayer.clear(this.backgroundColor),this.restart=!1}},{key:"validate",value:function(e){if(!this.brush)throw new Error("StrokeRenderer requires 'brush' to be configured");return e&&e.length>0}},{key:"blendUpdatedArea",value:function(e){throw new Error("StrokeRenderer.blendUpdatedArea(layer) should be implemented")}},{key:"blendStroke",value:function(e,t,n){throw new Error("StrokeRenderer.blendStroke(layer, dirtyArea, blendMode) should be implemented")}},{key:"toStroke",value:function(e,t){throw new Error("StrokeRenderer.toStroke(builder) should be implemented")}}]),e}();function xo(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return Ro(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ro(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function Ro(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function To(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Ao=function(e){T(n,e);var t=To(n);function n(e,r){var i;return k(this,n),(i=t.call(this,e,r)).alphaLayer=void 0,i}return S(n,[{key:"drawStroke",value:function(e){var t=e.blendMode;if(!(e.brush instanceof tn))throw new Error("Incompatible brush found. It should be Brush2D instance.");if(t){this.blendMode=e.style.blendMode||t;var n=this.layer.draw(e);this.strokeBounds=n,this.updatedArea=n}else console.warn("Cannot process ".concat(e.renderMode," render mode. Requres custom processing."))}},{key:"drawSegment",value:function(e){if(!this.color)throw new Error("StrokeRenderer requires 'color' to be configured");if(this.validate(e)){var t=this.layer.drawStroke(this.brush,e,this.color.toRGB(),e.matrix);t&&(this.incompleteStrokeBounds=t.union(this.incompleteStrokeBounds),this.strokeBounds=t.union(this.strokeBounds),this.updatedArea=this.incompleteStrokeBounds.union(this.preliminaryDirtyArea))}else this.updatedArea=this.preliminaryDirtyArea;this.blendWithPreliminaryLayer=!1,this.preliminaryDirtyArea=null}},{key:"drawPreliminary",value:function(e){this.validate(e)&&(this.preliminaryLayer||(this.preliminaryLayer=this.canvas.createLayer(),this.preliminaryLayer.setTransform(this.matrix)),this.updatedArea&&this.preliminaryLayer&&(this.preliminaryLayer.clear(this.updatedArea),this.preliminaryLayer.blend(this.layer,{mode:un.SOURCE_OVER,rect:this.updatedArea})),this.preliminaryDirtyArea=this.preliminaryLayer.drawStroke(this.brush,e,this.color.toRGB(),e.matrix),this.updatedArea=$.union(this.preliminaryDirtyArea,this.updatedArea),this.preliminaryLayer&&(this.blendWithPreliminaryLayer=!0))}},{key:"reset",value:function(e){Mn(C(n.prototype),"reset",this).call(this,e),this.runtime&&this.color.alpha<1&&(this.alphaLayer?this.alphaLayer.resize(this.layer.width,this.layer.height):this.alphaLayer=this.canvas.createLayer(this.layer.width,this.layer.height))}},{key:"blendUpdatedArea",value:function(e){if(this.updatedArea){var t=e||this.canvas,n=this.blendWithPreliminaryLayer?this.preliminaryLayer:this.layer,r=t.bounds.intersect(this.updatedArea);r&&(this.color.alpha<1?(this.alphaLayer.clear(),this.alphaLayer.ctx.globalAlpha=1,this.alphaLayer.blend(t,{rect:r}),this.alphaLayer.ctx.globalAlpha=this.color.alpha,this.alphaLayer.blend(n,{mode:this.blendMode,rect:r}),t.clear(r),t.blend(this.alphaLayer,{rect:r})):t.blend(n,{mode:this.blendMode,rect:r})),this.incompleteStrokeBounds=null,this.updatedArea=null}}},{key:"blendStroke",value:function(e,t,n){if(this.strokeBounds){n||(n=this.blendMode);var r=this.layer,i=e||this.canvas;(t=i.bounds.intersect(t||this.strokeBounds))&&(this.runtime&&this.color.alpha<1?(this.alphaLayer.clear(),this.alphaLayer.ctx.globalAlpha=this.color.alpha,this.alphaLayer.blend(r,{rect:t}),i.blend(this.alphaLayer,{mode:n,rect:t})):i.blend(r,{mode:n,rect:t}))}}},{key:"blendStrokes",value:function(e,t){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=arguments.length>3?arguments[3]:void 0;if(0!=e.length){var o,a,s,u=e.first.renderMode,c=[],l=function(){var e=ho.RenderMode.getBlendMode(u);n.strokeBounds=a,a=null,n.blendStroke(t,r.rect,e),o=n.strokeBounds.union(o),n.reset()},h=function(){if(!i)throw new Error("strokeRendererGL should be provided for WebGL strokes rasterization");i.reset(),s=i.blendStrokes(c,t,r),o=s.union(o),c.clear()};i&&(i.layer.resize(this.layer.width,this.layer.height),i.setTransform(this.matrix)),this.reset();var f,d=xo(e);try{for(d.s();!(f=d.n()).done;){var p=f.value;p.brush instanceof tn?(c.length>0&&(h(),u=p.renderMode),u!=p.renderMode&&(l(),u=p.renderMode),a=p.bounds.union(a),this.drawStroke(p)):(a&&l(),c.push(p))}}catch(e){d.e(e)}finally{d.f()}return c.length>0?h():l(),this.restart=!0,o}}},{key:"toStroke",value:function(e,t){if(!this.brush)throw new Error("StrokeRenderer brush is not configured");!0===t&&(t=void 0,console.warn("StrokeRenderer2D.toStroke 'simplify' param is deprecated. Do not affects InkPath. InkBuilderSettings 'concatSegments' should be configured to affect ink path properly."));var n=e.getSensorData(),r=e.getSpline(),i=e.getInkPath(),o=e.getAllData();r.id=t;var a=new ho(this.brush,r,i,n);return n&&(a.sensorDataMapping=n.inkStream.getPipelineMapping()),o&&(a.pipeline=o),this.blendMode!=un.SOURCE_OVER&&(a.renderMode=ho.RenderMode.get(this.blendMode)),a}},{key:"delete",value:function(){console.warn("Not applicable with 2D API")}}]),n}(Io),Oo=void 0;ke.typeGL==ke.TypeGL.STACK&&(ke.commonJS?Oo=require("gl"):console.warn("Current env - ".concat(ke.type.name,", do not provides WebGL support")));var Co=Oo,Do=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:300,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:150;k(this,e),this.width=t,this.height=n}return S(e,[{key:"getContext",value:function(){var e=arguments.length>1?arguments[1]:void 0;if(!this.context){var t=this.width,n=this.height;this.context=Co(t,n,e),this.context.canvas=this;var r=this.context.getExtension("STACKGL_resize_drawingbuffer");Object.defineProperty(this,"width",{get:function(){return t},set:function(e){t=e,r.resize(t,n)},enumerable:!0}),Object.defineProperty(this,"height",{get:function(){return n},set:function(e){n=e,r.resize(t,n)},enumerable:!0})}return this.context}},{key:"destroy",value:function(){this.context&&(this.context.getExtension("STACKGL_destroy_context").destroy(),delete this.context)}}]),e}(),Mo=Co?Do:void 0,No=function(){function e(t){k(this,e),this.randomSeed=t||parseInt(Date.now()/1e3)}return S(e,[{key:"copyTo",value:function(e){e.randomSeed=this.randomSeed}}]),e}();function Lo(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Bo=function(e){T(n,e);var t=Lo(n);function n(e,r){var i;return k(this,n),i=t.call(this,e.inkGLContext,r),Object.defineProperty(A(i),"inkContext",{value:e,enumerable:!0}),Object.defineProperty(A(i),"bounds",{get:function(){return $.fromGLRect(i.graphicsBounds)},enumerable:!0}),i}return S(n,[{key:"clear",value:function(e,t){if(t){if(wt.isColor(e))throw new Error("`clear` first argument should be Rect")}else wt.isColor(e)?(t=e,e=null):t=wt.TRANSPERENT;var n;if($.isRect(e)){if(!e.intersect(this.bounds))return;n=$.fromRect(e).toGLRect()}Array.isArray(e)||e instanceof Float32Array?this.fill(e,t,!1):(this.inkContext.setTarget(this,n),this.inkContext.clearColor(t)),n&&this.inkContext.disableTargetClipRect()}},{key:"draw",value:function(e){var t,n=e.brush;return n instanceof Tn?isFinite(e.randomSeed)&&(t=new No(e.randomSeed)):t=e.color,this.drawStroke(n,e.path,t)}},{key:"drawStroke",value:function(e,t,n){if(!ho.validatePath(t))return null;this.inkContext.setTarget(this);var r=this.inkContext.drawStroke(e,t,n);return $.fromGLRect(r)}},{key:"fill",value:function(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if($.isRect(e))e=Yt.fromRect(e),e=new Ln(e);else if("number"==typeof e[0]&&e.length%2==0&&(e=Yt.createInstance(e)),e instanceof Yt)e=new Ln(e);else if(!(e instanceof Ln))throw new Error("fill shape type missmatch, expected oneof(PolygonArray, Polygon, Rect)");this.inkContext.setTarget(this);var r=this.inkContext.fill(e,t,n);return $.fromGLRect(r)}},{key:"blend",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(t.mode||(t.mode=un.SOURCE_OVER),t.rect&&!t.transform&&(t.sourceRect=t.rect,t.destinationRect=t.rect),t.transform&&t.destinationRect)throw new Error("`destinationRect` is not applicable with `transform`");if(t.sourceRect&&!t.destinationRect)throw new Error("With `sourceRect`, `destinationRect` is required");if(t.destinationRect&&!t.sourceRect)throw new Error("With `destinationRect`, `sourceRect`is required");if(this.inkContext.setTarget(this,$.isRect(t.clipRect)?$.fromRect(t.clipRect).toGLRect():void 0),"boolean"==typeof t.flipY&&(e.flipY=t.flipY),t.transform&&t.rect){var n=$.fromRect(t.rect).toGLRect().toQuad(),r=$.fromRect(t.rect).toGLRect().toQuad(t.transform);this.inkContext.drawLayer(e,n,r,t.mode)}else t.transform?this.inkContext.drawLayerWithTransform(e,t.transform,t.mode):t.sourceRect&&t.destinationRect?this.inkContext.drawLayer(e,$.fromRect(t.sourceRect).toGLRect().toQuad(),$.fromRect(t.destinationRect).toGLRect().toQuad(),t.mode):this.inkContext.drawLayerWithTransform(e,null,t.mode)}},{key:"createImageData",value:function(e,t,n){return e instanceof Uint8Array&&(e=new Uint8ClampedArray(e.buffer)),new rt(e,t,n)}},{key:"getImageData",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];e||(e=this.bounds);var n=this.createImageData(this.readPixels(e,t),e.width,e.height);return n.x=e.x,n.y=e.y,n}},{key:"putImageData",value:function(e,t,n){if(!(e instanceof rt))throw new Error("putImageData 'imageData' parameter is not instance of ImageData");(isNaN(t)||isNaN(n))&&(t=e.x||0,n=e.y||0),this.writePixels(e.data,new $(t,n,e.width,e.height))}},{key:"readPixels",value:function(e,t){e&&(e=$.fromRect(e).toGLRect()),this.inkContext.setTarget(this);var n=this.inkContext.readPixels(e);if(t)for(var r=0;r<n.length;r+=4){var i=n[r+3];n[r]=parseInt(255*n[r]/i),n[r+1]=parseInt(255*n[r+1]/i),n[r+2]=parseInt(255*n[r+2]/i)}return n}},{key:"writePixels",value:function(e,t){if(!(e instanceof Uint8Array||e instanceof Uint8ClampedArray))throw new Error("writePixels 'data' parameter is not instance of Uint8Array or Uint8ClampedArray");t&&(t=$.fromRect(t).toGLRect()),this.inkContext.setTarget(this),this.inkContext.writePixels(t,e)}},{key:"getExportCanvas",value:function(e){e=e?e.intersect(this.bounds).ceil():this.bounds;var t=new it(e.width,e.height);return t.getContext("2d").putImageData(this.getImageData(e,!0),0,0),t}}]),n}(yn);function _o(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Fo=function(e){T(r,e);var t,n=_o(r);function r(e){var t,i;k(this,r),i=n.call(this,e,{display:!0,width:e.inkGLContext.gl.canvas.width,height:e.inkGLContext.gl.canvas.height,flipY:ke.typeGL==ke.TypeGL.WEB}),Object.defineProperty(A(i),"surface",{value:e.inkGLContext.gl.canvas,enumerable:!0}),Object.defineProperty(A(i),"ctx",{value:e.inkGLContext.gl,enumerable:!0}),Object.defineProperty(A(i),"MAX_SIZE",{value:i.ctx.getParameter(i.ctx.MAX_TEXTURE_SIZE),enumerable:!0});var o=i.ctx.getContextAttributes();if("function"==typeof requestAnimationFrame&&!o.preserveDrawingBuffer){var a=i.createLayer();Object.defineProperty(A(i),"backbuffer",{value:a,enumerable:!0});i.frameID=requestAnimationFrame((function e(n){i.present&&(delete i.present,Mn((t=A(i),C(r.prototype)),"blend",t).call(t,a,{mode:un.COPY})),i.frameID=requestAnimationFrame(e)}))}return i}return S(r,[{key:"createLayer",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.width,n=e.height;if(t&&n){if(t>this.MAX_SIZE)throw new Error("Max width exceeded: ".concat(t," found, ").concat(this.MAX_SIZE," allowed"));if(n>this.MAX_SIZE)throw new Error("Max height exceeded: ".concat(n," found, ").concat(this.MAX_SIZE," allowed"))}else{var i=r.getDefaultSize(this.width,this.height);e.width=i.width,e.height=i.height}return new Bo(this.inkContext,e)}},{key:"clear",value:function(e,t){this.backbuffer?(this.backbuffer.clear(e,t),this.present=!0):Mn(C(r.prototype),"clear",this).call(this,e,t)}},{key:"draw",value:function(e,t){var n;return this.backbuffer?(n=this.backbuffer.draw(e,t),this.present=!0):n=Mn(C(r.prototype),"draw",this).call(this,e,t),n}},{key:"drawStroke",value:function(e,t,n){var i;return this.backbuffer?(i=this.backbuffer.drawStroke(e,t,n),this.present=!0):i=Mn(C(r.prototype),"drawStroke",this).call(this,e,t,n),i}},{key:"fill",value:function(e,t,n){var i;return this.backbuffer?(i=this.backbuffer.fill(e,t,n),this.present=!0):i=Mn(C(r.prototype),"fill",this).call(this,e,t,n),i}},{key:"blend",value:function(e,t){this.backbuffer?(this.backbuffer.blend(e,t),this.present=!0):Mn(C(r.prototype),"blend",this).call(this,e,t)}},{key:"readPixels",value:function(e,t){return this.backbuffer?this.backbuffer.readPixels(e,t):Mn(C(r.prototype),"readPixels",this).call(this,e,t)}},{key:"writePixels",value:function(e,t){this.backbuffer?(this.backbuffer.writePixels(e,t),this.present=!0):Mn(C(r.prototype),"writePixels",this).call(this,e,t)}},{key:"toBlob",value:(t=be(Pe.mark((function e(){var t,n,i,o=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=o.length>0&&void 0!==o[0]?o[0]:this.bounds,n=o.length>1?o[1]:void 0,i=o.length>2?o[2]:void 0,!this.backbuffer){e.next=9;break}return e.next=6,this.backbuffer.toBlob(t,n,i);case 6:case 11:return e.abrupt("return",e.sent);case 9:return e.next=11,Mn(C(r.prototype),"toBlob",this).call(this,t,n,i);case 12:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"resize",value:function(e,t){Mn(C(r.prototype),"resize",this).call(this,e,t),this.surface.width=e,this.surface.height=t}},{key:"destroy",value:function(){var e=this;this.delete(),ct.instance&&ct.instance.items.forEach((function(t){t instanceof Tn&&t.ctx==e.ctx&&t.delete()}));var t=this.ctx.getExtension("WEBGL_lose_context");t&&t.loseContext()}},{key:"delete",value:function(){"number"==typeof this.frameID&&cancelAnimationFrame(this.frameID),Mn(C(r.prototype),"delete",this).call(this),this.backbuffer&&this.backbuffer.delete()}},{key:"deleteLater",value:function(){return Mn(C(r.prototype),"deleteLater",this).call(this),this.backbuffer&&this.backbuffer.deleteLater(),this}}],[{key:"createInstance",value:function(e,t,n,i){if(!e||"function"!=typeof e.getContext)throw new Error("WebGL context provider is required");if(t>0&&n>0&&(e.width=t,e.height=n),!(e.width>0&&e.height>0))throw new Error("width and height are required and should be positive whole numbers");return new r(new Cn(e,i))}}]),r}(Bo);function Uo(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return jo(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return jo(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function jo(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Go(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Yo=function(e){T(n,e);var t=Go(n);function n(e,r){var i;return k(this,n),(i=t.call(this,e,r)).layerOptions=r,i.bitmapLayer=void 0,i.randomSeed=void 0,i.initialRandomSeed=void 0,i}return S(n,[{key:"configure",value:function(e){Mn(C(n.prototype),"configure",this).call(this,e),this.brush instanceof Tn&&isFinite(e.randomSeed)&&(this.initialRandomSeed=e.randomSeed)}},{key:"draw",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(this.layer.isDeleted())throw new Error("StrokeRenderer cannot draw, it is already deleted");Mn(C(n.prototype),"draw",this).call(this,e,t)}},{key:"drawStroke",value:function(e){var t=e.blendMode;if(t){this.blendMode=e.style.blendMode||t;var n=this.layer.draw(e);this.strokeBounds=n,this.updatedArea=n}else console.warn("Cannot process ".concat(e.renderMode," render mode. Requres custom processing."))}},{key:"drawSegment",value:function(e){if(this.validate(e)){e.color&&this.color&&!e.color.equals(this.color)&&(e.color=this.color);var t=this.brush instanceof Tn?this.strokeLastRendererdDrawContext:this.color,n=this.layer.drawStroke(this.brush,e,t);n&&(this.incompleteStrokeBounds=n.union(this.incompleteStrokeBounds),this.strokeBounds=n.union(this.strokeBounds),this.updatedArea=this.incompleteStrokeBounds.union(this.preliminaryDirtyArea))}else this.updatedArea=this.preliminaryDirtyArea;this.blendWithPreliminaryLayer=!1,this.preliminaryDirtyArea=null}},{key:"drawPreliminary",value:function(e){if(this.validate(e)){var t=null;this.brush instanceof Tn?(this.strokeLastRendererdDrawContext.copyTo(this.strokePrelimLastRenderedDrawContext),t=this.strokePrelimLastRenderedDrawContext):t=this.color,this.preliminaryLayer||(this.preliminaryLayer=this.canvas.createLayer(this.layerOptions),this.preliminaryLayer.setTransform(this.matrix)),this.updatedArea&&this.preliminaryLayer&&this.preliminaryLayer.blend(this.layer,{mode:un.COPY,rect:this.updatedArea}),e.color&&this.color&&!e.color.equals(this.color)&&(e.color=this.color),this.preliminaryDirtyArea=this.preliminaryLayer.drawStroke(this.brush,e,t),this.updatedArea=$.union(this.preliminaryDirtyArea,this.updatedArea),this.preliminaryLayer&&(this.blendWithPreliminaryLayer=!0)}}},{key:"reset",value:function(){Mn(C(n.prototype),"reset",this).call(this),this.bitmapLayer&&(this.bitmapLayer.clear(this.backgroundColor),this.bitmapLayer.resize(this.layer.width,this.layer.height)),this.brush instanceof Tn?(this.strokeLastRendererdDrawContext=new No(this.initialRandomSeed),this.strokePrelimLastRenderedDrawContext||(this.strokePrelimLastRenderedDrawContext=new No),delete this.initialRandomSeed,this.randomSeed=this.strokeLastRendererdDrawContext.randomSeed):delete this.randomSeed}},{key:"blendUpdatedArea",value:function(e){if(this.updatedArea){var t=this.blendWithPreliminaryLayer?this.preliminaryLayer:this.layer,n=e||this.canvas,r=n.bounds.intersect(this.updatedArea);if(r){if(n instanceof ko)throw new Error("Runtime blendig requires OffscreenLayerGL target");n.blend(t,{mode:this.blendMode,rect:r})}this.incompleteStrokeBounds=null,this.updatedArea=null}}},{key:"blendStroke",value:function(e,t,n){if(this.strokeBounds){n||(n=this.blendMode);var r=this.layer,i=e||this.canvas;(t=i.bounds.intersect(t||this.strokeBounds))&&(i instanceof ko?this.blendStroke2D(i,{mode:n,rect:t}):i.blend(r,{mode:n,rect:t}))}}},{key:"blendStroke2D",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.rect||t.sourceRect||this.layer.bounds,r=this.layer.getImageData(n,!0);if(!this.bitmapLayer){var i=new it(this.layer.width,this.layer.height);this.bitmapLayer=new ko(i.getContext("2d"))}this.bitmapLayer.putImageData(r,n.x,n.y),e.blend(this.bitmapLayer,t)}},{key:"blendStrokes",value:function(e,t){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(0!=e.length){var i,o,a=e.first.renderMode,s=function(){var e=ho.RenderMode.getBlendMode(a);n.strokeBounds=o,o=null,n.blendStroke(t,r.rect,e),i=n.strokeBounds.union(i),n.reset()};this.reset();var u,c=Uo(e);try{for(c.s();!(u=c.n()).done;){var l=u.value;(a!=l.renderMode||l.brush instanceof tn&&l!=e.first)&&(s(l),a=l.renderMode),o=l.bounds.union(o),this.drawStroke(l)}}catch(e){c.e(e)}finally{c.f()}return s(),this.restart=!0,i}}},{key:"toStroke",value:function(e,t){var n=e.getSensorData(),r=e.getSpline(),i=e.getInkPath(),o=e.getAllData();r.id=t;var a=new ho(this.brush,r,i,n);return n&&(a.sensorDataMapping=n.inkStream.getPipelineMapping()),o&&(a.pipeline=o),a.randomSeed=this.randomSeed,this.blendMode!=un.SOURCE_OVER&&(a.renderMode=ho.RenderMode.get(this.blendMode)),a}},{key:"delete",value:function(){this.layer.delete(),this.preliminaryLayer&&this.preliminaryLayer.delete()}}]),n}(Io),Xo=function(){function e(t,n,r){var i,o,a=this;k(this,e),this.listeners=[];var s=function(e,t){Object.defineProperty(a,e,{get:function(){return t},set:function(n){if(!n&&"object"!=e)throw new Error("SemanticTriple ".concat(e," is required"));t=n,o=i,i=void 0,a.listeners.slice().forEach((function(e){return e(o,a)}))},enumerable:!0})};s("subject",t),s("predicate",n),s("object",r),Object.defineProperty(this,"hashCode",{get:function(){return i||(i=F(a.toString())),i},enumerable:!0}),this.subject=t,this.predicate=n,this.object=r}return S(e,[{key:"equals",value:function(e){return this.subject==e.subject&&this.predicate==e.predicate&&this.object==e.object}},{key:"subsets",value:function(e){if(!e||!e.subject&&!e.predicate&&!e.object)throw new Error("Illegal partialStatement found: at least onoeof(subject, predicate, object) is required");var t=!0;return e.subject&&(t=this.subject==e.subject),t&&e.predicate&&(t=this.predicate==e.predicate),t&&e.object&&(t=this.object==e.object),t}},{key:"addListener",value:function(e){this.listeners.push(e)}},{key:"removeListener",value:function(e){this.listeners.remove(e)}},{key:"toString",value:function(){return"triple(".concat(this.subject,", ").concat(this.predicate,", ").concat(this.object,")")}},{key:"toJSON",value:function(){return{subject:this.subject,predicate:this.predicate,object:this.object}}}],[{key:"hashCode",value:function(t){return t instanceof e?t.hashCode:F(e.prototype.toString.call(t))}}]),e}();function zo(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Vo=function(e,t){T(r,e);var n=zo(r);function r(){var e,t,i;k(this,r),i=n.call(this);var o={};return Object.defineProperty(A(i),"items",{value:o}),Object.defineProperty(A(i),"update",{value:function(e,n){delete o[e],i.items.hasOwnProperty(n.hashCode)?(n.removeListener(i.update),Mn((t=A(i),C(r.prototype)),"remove",t).call(t,n)):o[n.hashCode]=n}}),(e=i).add.apply(e,arguments),i}return S(r,[{key:"add",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(var r=0,i=t;r<i.length;r++){var o=i[r];o instanceof Xo?this.push(o):this.addTriple(o.subject,o.predicate,o.object)}}},{key:"addTriple",value:function(e,t,n){this.push(new Xo(e,t,n))}},{key:"push",value:function(){if(arguments.length>1)this.add.apply(this,arguments);else{var e=arguments.length<=0?void 0:arguments[0];if(e instanceof Xo){var t=e.hashCode;this.items.hasOwnProperty(t)||(this.items[t]=e,Mn(C(r.prototype),"push",this).call(this,e),e.addListener(this.update))}else this.addTriple(e.subject,e.predicate,e.object)}}},{key:"remove",value:function(){for(var e,t=this,n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];"function"==typeof i[0]&&(i=this.filter(i[0])),(i=i.map((function(e){return e instanceof Xo?e:t.items[Xo.hashCode(e)]}))).forEach((function(e){e&&(e.removeListener(t.update),delete t.items[e.hashCode])})),(e=Mn(C(r.prototype),"remove",this)).call.apply(e,[this].concat(ue(i)))}},{key:"clear",value:function(){var e=this;Mn(C(r.prototype),"clear",this).call(this),Object.keys(this.items).forEach((function(t){e.items[t].removeListener(e.update),delete e.items[t]}))}},{key:"clone",value:function(){var e=new r;return this.forEach((function(t){return e.addTriple(t.subject,t.predicate,t.object)})),e}},{key:"search",value:function(e){return console.warn("TripleStore search method is deprecated. Use filter instead."),this.filter((function(t){var n=!0;return Object.keys(e).forEach((function(r){n=n&&t[r]==e[r]})),n}))}}],[{key:t,get:function(){return Array}}]),r}(N(Array),Symbol.species),Ho=b?b.default||globalThis.protobuf:{},Wo=S((function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:e.Type.STROKE,r=arguments.length>1?arguments[1]:void 0;if(k(this,e),n==e.Type.SENSOR_DATA)throw new Error("Sensor data trees are not supported yet");if(r){var i=/^[a-zA-Z][a-zA-Z0-9-_]{1,62}[a-zA-Z0-9]$/;if(!i.test(r))throw new Error('Ink tree name "'.concat(r,'" is not valid. Allowed symbols are ').concat(i.toString(),"."))}else r=n==e.Type.STROKE?"main":"sdm";Object.defineProperty(this,"type",{value:n}),Object.defineProperty(this,"name",{get:function(){return r},set:function(n){if(e.reservedViewNames.includes(r))throw new Error("Tree name ".concat(r," update is not allowed. Reserved names are immutable."));if(e.reservedViewNames.includes(n))throw new Error("Tree name ".concat(n," is not allowed. It is reserved."));var i=je.createNodeURISchema(t),o=je.createNodeURISchema(t,n);t.model.knowledgeGraph.forEach((function(e){e.subject.startsWith(i)&&(e.subject=e.subject.replace(i,o)),e.object.startsWith(i)&&(e.object=e.object.replace(i,o))})),t.model.views[n]=t.model.views[r],delete t.model.views[r],r=n},enumerable:!0}),Object.defineProperty(this,"uri",{get:function(){return je.createTreeURI(r)}})}));x(Wo,"reservedViewNames",["main","sdm","hwr","ner"]),Object.defineEnum(Wo,"Type",["STROKE","SENSOR_DATA"]);var Zo=function(){function e(){k(this,e)}return S(e,null,[{key:"encodePathPointProperties",value:function(t,n){var r=n.$type;for(var i in r.fields){var o=void 0;if("color"==i)o=e.rgba(t.red,t.green,t.blue,t.alpha);else{if(("size"==i||i.startsWith("scale"))&&1==t[i])continue;o=t[i]}isFinite(o)&&(n[i]=o)}return n}},{key:"decodePathPointProperties",value:function(t,n){if(t){if(!n)throw new Error("Color property requires layout");var r=t.$type,i={};for(var o in r.fields)if("color"==o){var a=e.fromRGBA(t.color);n.includes(me.Property.RED)||(i.red=a.red),n.includes(me.Property.GREEN)||(i.green=a.green),n.includes(me.Property.BLUE)||(i.blue=a.blue),n.includes(me.Property.ALPHA)||(i.alpha=a.alpha)}else if(t.hasOwnProperty(o)){if(o.startsWith("scale")&&1==t[o])continue;i[o]=t[o]}return i}}},{key:"rgba",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return(255&e)<<24|(255&t)<<16|(255&n)<<8|255&(r=Math.round(255*r))}},{key:"fromRGBA",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=e>>24&255,n=e>>16&255,r=e>>8&255,i=(255&e)/255;return{red:t,green:n,blue:r,alpha:i}}}]),e}(),qo={evolution:["3.0.0","3.1.0"],"3.1.0":{schema:{nested:{UIM_3_1_0:{options:{optimize_for:"SPEED",java_multiple_files:!1,java_package:"com.wacom.ink.protobuf",java_outer_classname:"UIM_3_1_0",csharp_namespace:"Protobuf.WILL_3_1_0",swift_prefix:"WILL3_1_0"},nested:{BlendMode:{values:{SOURCE_OVER:0,DESTINATION_OVER:1,DESTINATION_OUT:2,LIGHTER:3,COPY:4,MIN:5,MAX:6}},Rectangle:{fields:{x:{type:"float",id:1},y:{type:"float",id:2},width:{type:"float",id:3},height:{type:"float",id:4}}},Matrix:{fields:{m00:{type:"float",id:1},m01:{type:"float",id:2},m02:{type:"float",id:3},m03:{type:"float",id:4},m10:{type:"float",id:5},m11:{type:"float",id:6},m12:{type:"float",id:7},m13:{type:"float",id:8},m20:{type:"float",id:9},m21:{type:"float",id:10},m22:{type:"float",id:11},m23:{type:"float",id:12},m30:{type:"float",id:13},m31:{type:"float",id:14},m32:{type:"float",id:15},m33:{type:"float",id:16}}},Interval:{fields:{fromIndex:{type:"uint32",id:1},toIndex:{type:"uint32",id:2},fromTValue:{type:"float",id:3},toTValue:{type:"float",id:4},id:{type:"bytes",id:100}}},PathPointProperties:{fields:{color:{type:"sint32",id:1},size:{type:"float",id:2},rotation:{type:"float",id:3},scaleX:{type:"float",id:4},scaleY:{type:"float",id:5},scaleZ:{type:"float",id:6},offsetX:{type:"float",id:7},offsetY:{type:"float",id:8},offsetZ:{type:"float",id:9}}},Property:{fields:{name:{type:"string",id:1},value:{type:"string",id:2}}},InkState:{values:{PLANE:0,HOVERING:1,IN_VOLUME:2,VOLUME_HOVERING:3}},InkSensorMetricType:{values:{LENGTH:0,TIME:1,FORCE:2,ANGLE:3,NORMALIZED:4,LOGICAL:5,DIMENSIONLESS:6}},InkInputProviderType:{values:{PEN:0,TOUCH:1,MOUSE:2,CONTROLLER:3}},InputContext:{fields:{id:{type:"bytes",id:1},environmentID:{type:"bytes",id:2},sensorContextID:{type:"bytes",id:3}}},Environment:{fields:{id:{type:"bytes",id:1},properties:{rule:"repeated",type:"Property",id:2}}},InkInputProvider:{fields:{id:{type:"bytes",id:1},type:{type:"InkInputProviderType",id:2},properties:{rule:"repeated",type:"Property",id:3}}},InputDevice:{fields:{id:{type:"bytes",id:1},properties:{rule:"repeated",type:"Property",id:2}}},SensorContext:{fields:{id:{type:"bytes",id:1},sensorChannelsContext:{rule:"repeated",type:"SensorChannelsContext",id:2}}},SensorChannelsContext:{fields:{id:{type:"bytes",id:1},channels:{rule:"repeated",type:"SensorChannel",id:2},samplingRateHint:{type:"uint32",id:3},latency:{type:"uint32",id:4},inkInputProviderID:{type:"bytes",id:5},inputDeviceID:{type:"bytes",id:6}}},SensorChannel:{fields:{id:{type:"bytes",id:1},type:{type:"string",id:2},metric:{type:"InkSensorMetricType",id:3},resolution:{type:"double",id:4},min:{type:"float",id:5},max:{type:"float",id:6},precision:{type:"uint32",id:7}}},InputContextData:{fields:{inputContexts:{rule:"repeated",type:"InputContext",id:1},inkInputProviders:{rule:"repeated",type:"InkInputProvider",id:2},inputDevices:{rule:"repeated",type:"InputDevice",id:3},environments:{rule:"repeated",type:"Environment",id:4},sensorContexts:{rule:"repeated",type:"SensorContext",id:5}}},ChannelData:{fields:{sensorChannelID:{type:"bytes",id:1},values:{rule:"repeated",type:"sint32",id:2}}},SensorData:{fields:{id:{type:"bytes",id:1},inputContextID:{type:"bytes",id:2},state:{type:"InkState",id:3},timestamp:{type:"uint64",id:4},dataChannels:{rule:"repeated",type:"ChannelData",id:5}}},InputData:{fields:{inputContextData:{type:"InputContextData",id:1},sensorData:{rule:"repeated",type:"SensorData",id:2}}},RotationMode:{values:{NONE:0,RANDOM:1,TRAJECTORY:2}},BrushPrototype:{fields:{coordX:{rule:"repeated",type:"float",id:1},coordY:{rule:"repeated",type:"float",id:2},coordZ:{rule:"repeated",type:"float",id:3},indices:{rule:"repeated",type:"uint32",id:4},shapeURI:{type:"string",id:5},size:{type:"float",id:6}}},VectorBrush:{fields:{name:{type:"string",id:1},prototype:{rule:"repeated",type:"BrushPrototype",id:2},spacing:{type:"float",id:3}}},RasterBrush:{fields:{name:{type:"string",id:1},spacing:{type:"float",id:2},scattering:{type:"float",id:3},rotationMode:{type:"RotationMode",id:4},shapeTexture:{rule:"repeated",type:"bytes",id:5},shapeTextureURI:{rule:"repeated",type:"string",id:6},fillTexture:{type:"bytes",id:7},fillTextureURI:{type:"string",id:8},fillWidth:{type:"float",id:9},fillHeight:{type:"float",id:10},randomizeFill:{type:"bool",id:11},blendMode:{type:"BlendMode",id:12}}},Brushes:{fields:{vectorBrushes:{rule:"repeated",type:"VectorBrush",id:1},rasterBrushes:{rule:"repeated",type:"RasterBrush",id:2}}},Stroke:{oneofs:{data:{oneof:["splineData","splineCompressed"]},properties:{oneof:["propertiesIndex","propertiesValue"]},brushURI:{oneof:["brushURIIndex","brushURIValue"]},renderModeURI:{oneof:["renderModeURIIndex","renderModeURIValue"]}},fields:{id:{type:"bytes",id:1},precisions:{type:"sint32",id:2},startParameter:{type:"float",id:3},endParameter:{type:"float",id:4},splineData:{type:"SplineData",id:5},splineCompressed:{type:"SplineCompressed",id:6},propertiesIndex:{type:"uint32",id:7},propertiesValue:{type:"PathPointProperties",id:8},brushURIIndex:{type:"uint32",id:9},brushURIValue:{type:"string",id:10},renderModeURIIndex:{type:"uint32",id:11},renderModeURIValue:{type:"string",id:12},randomSeed:{type:"uint32",id:13},sensorDataOffset:{type:"uint32",id:14},sensorDataID:{type:"bytes",id:15},sensorDataMapping:{rule:"repeated",type:"uint32",id:16}},nested:{SplineData:{fields:{splineX:{rule:"repeated",type:"float",id:1},splineY:{rule:"repeated",type:"float",id:2},splineZ:{rule:"repeated",type:"float",id:3},red:{rule:"repeated",type:"uint32",id:4},green:{rule:"repeated",type:"uint32",id:5},blue:{rule:"repeated",type:"uint32",id:6},alpha:{rule:"repeated",type:"uint32",id:7},size:{rule:"repeated",type:"float",id:8},rotation:{rule:"repeated",type:"float",id:9},scaleX:{rule:"repeated",type:"float",id:10},scaleY:{rule:"repeated",type:"float",id:11},scaleZ:{rule:"repeated",type:"float",id:12},offsetX:{rule:"repeated",type:"float",id:13},offsetY:{rule:"repeated",type:"float",id:14},offsetZ:{rule:"repeated",type:"float",id:15}}},SplineCompressed:{fields:{splineX:{rule:"repeated",type:"sint32",id:1},splineY:{rule:"repeated",type:"sint32",id:2},splineZ:{rule:"repeated",type:"sint32",id:3},red:{rule:"repeated",type:"uint32",id:4},green:{rule:"repeated",type:"uint32",id:5},blue:{rule:"repeated",type:"uint32",id:6},alpha:{rule:"repeated",type:"uint32",id:7},size:{rule:"repeated",type:"sint32",id:8},rotation:{rule:"repeated",type:"sint32",id:9},scaleX:{rule:"repeated",type:"sint32",id:10},scaleY:{rule:"repeated",type:"sint32",id:11},scaleZ:{rule:"repeated",type:"sint32",id:12},offsetX:{rule:"repeated",type:"sint32",id:13},offsetY:{rule:"repeated",type:"sint32",id:14},offsetZ:{rule:"repeated",type:"sint32",id:15}}}}},InkData:{fields:{strokes:{rule:"repeated",type:"Stroke",id:1},unitScaleFactor:{type:"float",id:2},transform:{type:"Matrix",id:3},brushURIs:{rule:"repeated",type:"string",id:4},renderModeURIs:{rule:"repeated",type:"string",id:5},properties:{rule:"repeated",type:"PathPointProperties",id:6}}},TripleStore:{fields:{statements:{rule:"repeated",type:"SemanticTriple",id:1}},nested:{SemanticTriple:{fields:{subject:{type:"string",id:1},predicate:{type:"string",id:2},object:{type:"string",id:3}}}}},Properties:{fields:{properties:{rule:"repeated",type:"Property",id:1}}},StructureType:{values:{STROKE:0,SENSOR_DATA:1}},Node:{oneofs:{id:{oneof:["groupID","index"]}},fields:{depth:{type:"uint32",id:1},groupID:{type:"bytes",id:2},index:{type:"uint32",id:3},interval:{type:"Interval",id:4},bounds:{type:"Rectangle",id:5}}},InkTree:{fields:{name:{type:"string",id:1},tree:{rule:"repeated",type:"Node",id:2}}},InkStructure:{fields:{type:{type:"StructureType",id:1},inkTree:{type:"InkTree",id:2},views:{rule:"repeated",type:"InkTree",id:3}}},PathFragment:{fields:{id:{type:"bytes",id:1},pointIndexStart:{type:"uint32",id:2},pointIndexEnd:{type:"uint32",id:3},ts:{type:"float",id:4},tf:{type:"float",id:5}}},Path:{fields:{layout:{type:"uint32",id:1},pointProps:{type:"PathPointProperties",id:2},data:{rule:"repeated",type:"float",id:3}}},Polygon:{fields:{shape:{type:"Path",id:1},holes:{rule:"repeated",type:"Path",id:2}}},PolygonArray:{fields:{data:{rule:"repeated",type:"Polygon",id:1}}},InkPath:{oneofs:{data:{oneof:["path","polygons"]}},fields:{path:{type:"Path",id:1},polygons:{type:"PolygonArray",id:2},spline:{type:"Path",id:3}}},InkStroke:{fields:{path:{type:"Stroke",id:1},ink:{type:"InkPath",id:2}}},Style:{fields:{color:{type:"sint32",id:1},brushURI:{type:"string",id:2},randomSeed:{type:"uint32",id:3},renderModeURI:{type:"string",id:4}}},InkSegment:{fields:{ink:{type:"InkPath",id:1},complete:{type:"bool",id:2}}},StrokesContext:{fields:{context:{rule:"repeated",type:"bytes",id:1}}},Intersection:{fields:{strokeID:{type:"bytes",id:1},fragments:{rule:"repeated",type:"PathFragment",id:2}}},Replacement:{fields:{strokeID:{type:"bytes",id:1},strokes:{rule:"repeated",type:"InkStroke",id:2}}},InkOperation:{oneofs:{operation:{oneof:["compose","add","remove","update","replace","split","select","updateSelection","transform"]}},fields:{compose:{type:"Compose",id:1},add:{type:"Add",id:2},remove:{type:"Remove",id:3},update:{type:"Update",id:4},replace:{type:"Replace",id:5},split:{type:"Split",id:6},select:{type:"Select",id:7},updateSelection:{type:"UpdateSelection",id:8},transform:{type:"Transform",id:9}},nested:{Compose:{oneofs:{stage:{oneof:["style","segment","abort"]}},fields:{style:{type:"Style",id:1},segment:{type:"InkSegment",id:2},abort:{type:"bool",id:3},pointerID:{type:"uint32",id:4},strokeID:{type:"bytes",id:5}}},Add:{fields:{strokes:{rule:"repeated",type:"InkStroke",id:1}}},Remove:{fields:{context:{type:"StrokesContext",id:1}}},Update:{fields:{context:{type:"StrokesContext",id:1},style:{type:"Style",id:2}}},Replace:{fields:{replacements:{rule:"repeated",type:"Replacement",id:1}}},Split:{fields:{intersections:{rule:"repeated",type:"Intersection",id:1},affectedArea:{type:"Rectangle",id:2}}},Select:{oneofs:{stage:{oneof:["selector","selection","abort"]}},fields:{selector:{type:"InkStroke",id:1},selection:{type:"StrokesContext",id:2},abort:{type:"bool",id:3}}},UpdateSelection:{oneofs:{stage:{oneof:["transform","complete"]}},fields:{transform:{type:"Matrix",id:1},complete:{type:"bool",id:2}}},Transform:{fields:{context:{type:"StrokesContext",id:1},matrix:{type:"Matrix",id:2}}}}},Range:{fields:{min:{type:"float",id:1},max:{type:"float",id:2},remapURI:{type:"string",id:3}}},PathPointContext:{fields:{statics:{type:"PathPointProperties",id:1},dynamics:{type:"PathPointSettings",id:2},colorMask:{type:"uint32",id:3}}},PathPointSettings:{fields:{size:{type:"PropertySettings",id:1},red:{type:"PropertySettings",id:2},green:{type:"PropertySettings",id:3},blue:{type:"PropertySettings",id:4},alpha:{type:"PropertySettings",id:5},rotation:{type:"PropertySettings",id:6},scaleX:{type:"PropertySettings",id:7},scaleY:{type:"PropertySettings",id:8},scaleZ:{type:"PropertySettings",id:9},offsetX:{type:"PropertySettings",id:11},offsetY:{type:"PropertySettings",id:12},offsetZ:{type:"PropertySettings",id:13}}},PropertySettings:{fields:{value:{type:"Range",id:1},velocity:{type:"Range",id:2},pressure:{type:"Range",id:3},altitude:{type:"Range",id:4},radiusX:{type:"Range",id:5},radiusY:{type:"Range",id:6},resolveURI:{type:"string",id:7},dependencies:{type:"uint32",id:8}}},InkTool:{oneofs:{brush:{oneof:["vectorBrush","rasterBrush"]}},fields:{vectorBrush:{type:"VectorBrush",id:1},rasterBrush:{type:"RasterBrush",id:2},blendMode:{type:"BlendMode",id:3},context:{type:"PathPointContext",id:4}}}}}}},root:"UIM_3_1_0",messages:{InputContext:function(e){e.id=U.toBytes(U.format(e.id)),e.environmentID=U.toBytes(U.format(e.environmentID)),e.sensorContextID=U.toBytes(U.format(e.sensorContextID))},Environment:function(e){e.id=U.toBytes(U.format(e.id))},InkInputProvider:function(e){e.id=U.toBytes(U.format(e.id))},InputDevice:function(e){e.id=U.toBytes(U.format(e.id))},SensorContext:function(e){e.id=U.toBytes(U.format(e.id))},SensorChannelsContext:function(e){e.id=U.toBytes(U.format(e.id)),e.inputDeviceID=U.toBytes(U.format(e.inputDeviceID)),e.inkInputProviderID&&(e.inkInputProviderID=U.toBytes(U.format(e.inkInputProviderID))),e.samplingRateHint&&(e.samplingRateHint=e.samplingRateHint.value),e.latency&&(e.latency=e.latency.value)},SensorChannel:function(e){e.id=U.toBytes(U.format(e.id))},ChannelData:function(e){e.sensorChannelID=U.toBytes(U.format(e.sensorChannelID))},SensorData:function(e){e.id=U.toBytes(e.id),e.inputContextID=U.toBytes(U.format(e.inputContextID))},InputData:function(e){var t=[],n={};e.sensorData.forEach((function(e,r){n[e.id]?t.push(r):0==e.dataChannels.length?(console.warn("Not found corresponding data channels for sensor data with id: ".concat(e.id,". Invalid sensor data is discarded.")),t.push(r)):n[e.id]=e})),t.reverse().forEach((function(t){return e.sensorData.removeAt(t)}))},View:function(e){e.name.startsWith("will://")&&(e.name=e.name.toLowerCase()),e.name=e.name.substring(e.name.lastIndexOf("/")+1)},Node:function(e,t){var n=qo["3.0.0"].format;e.chunkToIndex>e.chunkFromIndex&&(e.interval={fromIndex:e.chunkFromIndex,toIndex:e.chunkToIndex,fromTValue:0,toTValue:1}),e.update={nodeID:e.id},e.type==n.NodeType.STROKE_GROUP||e.type==n.NodeType.SENSOR_DATA_GROUP?(U.validate(e.id)||(e.id=U.generate(),e.update.id=e.id),e.groupID=U.toBytes(e.id),e.id="groupID"):(e.type==n.NodeType.STROKE?e.update.id=t.context.inkData.strokes[e.index].id:e.update.id=t.context.inputData.sensorData[e.index].id,e.id="index"),Object.defineProperty(e,"bounds",{get:function(){return e.groupBoundingBox},configurable:!0})},InkData:function(e,t){e.brushURIs=[],e.renderModeURIs=[],e.properties=[],e.unitScaleFactor=1,e.transform=t.context.transform},Stroke:function(e,t){var n=qo["3.1.0"].format,r=qo["3.1.0"].messages.PathPointProperties;e.data="splineData",e.splineData={splineX:e.splineX,splineY:e.splineY,splineZ:e.splineZ,red:e.red.map((function(e){return Math.round(255*e)})),green:e.green.map((function(e){return Math.round(255*e)})),blue:e.blue.map((function(e){return Math.round(255*e)})),alpha:e.red.map((function(e){return Math.round(255*e)})),size:e.size,rotation:e.rotation,scaleX:e.scaleX,scaleY:e.scaleY,scaleZ:e.scaleZ,offsetX:e.offsetX,offsetY:e.offsetY,offsetZ:e.offsetZ},e.id=U.toBytes(e.id),e.sensorDataID&&(e.sensorDataID=U.toBytes(e.sensorDataID)),e.randomSeed=e.style.particlesRandomSeed,e.brushURI="brushURIValue",e.renderModeURI="renderModeURIValue",e.properties="propertiesValue",e.brushURIValue=e.style.brushURI,e.style.renderModeURI&&(e.renderModeURIValue=e.style.renderModeURI),r(e.style.properties,t,!1),e.propertiesValue=Zo.encodePathPointProperties(e.style.properties,n.PathPointProperties.create())},PathPointProperties:function(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!n){var r=qo["3.0.0"].format;for(var i in r.PathPointProperties.fields)if(e[i]){var o=e[i].value;"red"!=i&&"green"!=i&&"blue"!=i||(o=Math.round(255*o)),e[i]=o}else e[i]=void 0}},TripleStore:function(e,t){var n=qo["3.0.0"].format,r=[],i=[{tree:t.context.inkTree}].concat(ue(t.context.views)),o=t.context.inkTree.first.type,a=o==n.NodeType.STROKE_GROUP?Wo.Type.STROKE:Wo.Type.SENSOR_DATA;i.forEach((function(e){if(e.tree.first.type!=o){var t=e.tree.first.type==n.NodeType.STROKE_GROUP?Wo.Type.STROKE:Wo.Type.SENSOR_DATA;throw new Error("Inconsistent view found: ".concat(t.name," Expected: ").concat(a.name))}var i=new Wo(a,e.name);i.tree=e.tree,r.push(i)})),r.forEach((function(t){t.tree.forEach((function(n){return function(t,n,r,i){var o="uim:node/".concat(n),a=r||n;e.statements.forEach((function(e){e.subject==o&&(e.subject=je.createNodeURI(t,a,i)),e.object==o&&(e.object=je.createNodeURI(t,a,i))}))}(t,n.update.nodeID,n.update.id,n.interval)}))}))}}},"3.0.0":{schema:{nested:{WacomInkFormat3:{options:{optimize_for:"SPEED",java_multiple_files:!1,java_package:"com.wacom.ink.protobuf",java_outer_classname:"Will3_0_0",csharp_namespace:"Protobuf.WILL_3_0_0"},nested:{Rectangle:{fields:{x:{type:"float",id:1},y:{type:"float",id:2},width:{type:"float",id:3},height:{type:"float",id:4}}},Matrix4:{fields:{m00:{type:"float",id:1},m01:{type:"float",id:2},m02:{type:"float",id:3},m03:{type:"float",id:4},m10:{type:"float",id:5},m11:{type:"float",id:6},m12:{type:"float",id:7},m13:{type:"float",id:8},m20:{type:"float",id:9},m21:{type:"float",id:10},m22:{type:"float",id:11},m23:{type:"float",id:12},m30:{type:"float",id:13},m31:{type:"float",id:14},m32:{type:"float",id:15},m33:{type:"float",id:16}}},Range:{fields:{min:{type:"float",id:1},max:{type:"float",id:2},remapURI:{type:"string",id:3}}},Float32:{fields:{value:{type:"float",id:1}}},Uint32:{fields:{value:{type:"uint32",id:1}}},Property:{fields:{name:{type:"string",id:1},value:{type:"string",id:2}}},SemanticTriple:{fields:{subject:{type:"string",id:1},predicate:{type:"string",id:2},object:{type:"string",id:3}}},InkState:{values:{PLANE:0,HOVERING:1,IN_VOLUME:2,VOLUME_HOVERING:3}},InkSensorMetricType:{values:{LENGTH:0,TIME:1,FORCE:2,ANGLE:3,NORMALIZED:4,LOGICAL:5,DIMENSIONLESS:6}},InkInputProviderType:{values:{PEN:0,TOUCH:1,MOUSE:2,CONTROLLER:3}},InputContext:{fields:{id:{type:"string",id:1},environmentID:{type:"string",id:2},sensorContextID:{type:"string",id:3}}},Environment:{fields:{id:{type:"string",id:1},properties:{rule:"repeated",type:"Property",id:2}}},InkInputProvider:{fields:{id:{type:"string",id:1},type:{type:"InkInputProviderType",id:2},properties:{rule:"repeated",type:"Property",id:3}}},InputDevice:{fields:{id:{type:"string",id:1},properties:{rule:"repeated",type:"Property",id:2}}},SensorContext:{fields:{id:{type:"string",id:1},sensorChannelsContext:{rule:"repeated",type:"SensorChannelsContext",id:2}}},SensorChannelsContext:{fields:{id:{type:"string",id:1},channels:{rule:"repeated",type:"SensorChannel",id:2},samplingRateHint:{type:"Uint32",id:3},latency:{type:"Uint32",id:4},inkInputProviderID:{type:"string",id:5},inputDeviceID:{type:"string",id:6}}},SensorChannel:{fields:{id:{type:"string",id:1},type:{type:"string",id:2},metric:{type:"InkSensorMetricType",id:3},resolution:{type:"double",id:4},min:{type:"float",id:5},max:{type:"float",id:6},precision:{type:"uint32",id:7}}},InputContextData:{fields:{inputContexts:{rule:"repeated",type:"InputContext",id:1},inkInputProviders:{rule:"repeated",type:"InkInputProvider",id:2},inputDevices:{rule:"repeated",type:"InputDevice",id:3},environments:{rule:"repeated",type:"Environment",id:4},sensorContexts:{rule:"repeated",type:"SensorContext",id:5}}},ChannelData:{fields:{sensorChannelID:{type:"string",id:1},values:{rule:"repeated",type:"sint32",id:2}}},SensorData:{fields:{id:{type:"string",id:1},inputContextID:{type:"string",id:2},state:{type:"InkState",id:3},timestamp:{type:"uint64",id:4},dataChannels:{rule:"repeated",type:"ChannelData",id:5}}},Stroke:{fields:{id:{type:"string",id:1},startParameter:{type:"float",id:2},endParameter:{type:"float",id:3},splineX:{rule:"repeated",type:"float",id:4},splineY:{rule:"repeated",type:"float",id:5},splineZ:{rule:"repeated",type:"float",id:6},red:{rule:"repeated",type:"float",id:7},green:{rule:"repeated",type:"float",id:8},blue:{rule:"repeated",type:"float",id:9},alpha:{rule:"repeated",type:"float",id:10},size:{rule:"repeated",type:"float",id:11},rotation:{rule:"repeated",type:"float",id:12},scaleX:{rule:"repeated",type:"float",id:13},scaleY:{rule:"repeated",type:"float",id:14},scaleZ:{rule:"repeated",type:"float",id:15},offsetX:{rule:"repeated",type:"float",id:16},offsetY:{rule:"repeated",type:"float",id:17},offsetZ:{rule:"repeated",type:"float",id:18},sensorDataOffset:{type:"uint32",id:19},sensorDataID:{type:"string",id:20},sensorDataMapping:{rule:"repeated",type:"uint32",id:21},style:{type:"Style",id:22}}},RotationMode:{values:{NONE:0,RANDOM:1,TRAJECTORY:2}},BlendMode:{values:{SOURCE_OVER:0,DESTINATION_OVER:1,DESTINATION_OUT:2,LIGHTER:3,COPY:4,MIN:5,MAX:6}},BrushPrototype:{fields:{coordX:{rule:"repeated",type:"float",id:1},coordY:{rule:"repeated",type:"float",id:2},coordZ:{rule:"repeated",type:"float",id:3},indices:{rule:"repeated",type:"uint32",id:4},shapeURI:{type:"string",id:5},size:{type:"float",id:6}}},VectorBrush:{fields:{name:{type:"string",id:1},prototype:{rule:"repeated",type:"BrushPrototype",id:2},spacing:{type:"float",id:3}}},RasterBrush:{fields:{name:{type:"string",id:1},spacing:{type:"float",id:2},scattering:{type:"float",id:3},rotationMode:{type:"RotationMode",id:4},shapeTexture:{rule:"repeated",type:"bytes",id:5},shapeTextureURI:{rule:"repeated",type:"string",id:6},fillTexture:{type:"bytes",id:7},fillTextureURI:{type:"string",id:8},fillWidth:{type:"float",id:9},fillHeight:{type:"float",id:10},randomizeFill:{type:"bool",id:11},blendMode:{type:"BlendMode",id:12}}},NodeType:{values:{STROKE_GROUP:0,SENSOR_DATA_GROUP:1,STROKE:2,SENSOR_DATA:3}},Node:{fields:{id:{type:"string",id:1},depth:{type:"uint32",id:2},index:{type:"uint32",id:3},type:{type:"NodeType",id:4},groupBoundingBox:{type:"Rectangle",id:5},chunkFromIndex:{type:"uint32",id:6},chunkToIndex:{type:"uint32",id:7}}},View:{fields:{name:{type:"string",id:1},tree:{rule:"repeated",type:"Node",id:2}}},PathPointProperties:{fields:{size:{type:"Float32",id:1},red:{type:"Float32",id:2},green:{type:"Float32",id:3},blue:{type:"Float32",id:4},alpha:{type:"Float32",id:5},rotation:{type:"Float32",id:6},scaleX:{type:"Float32",id:7},scaleY:{type:"Float32",id:8},scaleZ:{type:"Float32",id:9},offsetX:{type:"Float32",id:11},offsetY:{type:"Float32",id:12},offsetZ:{type:"Float32",id:13}}},Style:{fields:{properties:{type:"PathPointProperties",id:1},brushURI:{type:"string",id:2},particlesRandomSeed:{type:"uint32",id:3},renderModeURI:{type:"string",id:4}}},InputData:{fields:{inputContextData:{type:"InputContextData",id:1},sensorData:{rule:"repeated",type:"SensorData",id:2}}},InkData:{fields:{strokes:{rule:"repeated",type:"Stroke",id:1}}},Brushes:{fields:{vectorBrushes:{rule:"repeated",type:"VectorBrush",id:1},rasterBrushes:{rule:"repeated",type:"RasterBrush",id:2}}},TripleStore:{fields:{statements:{rule:"repeated",type:"SemanticTriple",id:1}}},Tool:{oneofs:{brush:{oneof:["vectorBrush","rasterBrush"]}},fields:{vectorBrush:{type:"VectorBrush",id:1},rasterBrush:{type:"RasterBrush",id:2},blendMode:{type:"BlendMode",id:3},context:{type:"PathPointContext",id:4}}},PathPointContext:{fields:{statics:{type:"PathPointProperties",id:1},dynamics:{type:"PathPointSettings",id:2}}},PathPointSettings:{fields:{size:{type:"PropertySettings",id:1},red:{type:"PropertySettings",id:2},green:{type:"PropertySettings",id:3},blue:{type:"PropertySettings",id:4},alpha:{type:"PropertySettings",id:5},rotation:{type:"PropertySettings",id:6},scaleX:{type:"PropertySettings",id:7},scaleY:{type:"PropertySettings",id:8},scaleZ:{type:"PropertySettings",id:9},offsetX:{type:"PropertySettings",id:11},offsetY:{type:"PropertySettings",id:12},offsetZ:{type:"PropertySettings",id:13}}},PropertySettings:{fields:{value:{type:"Range",id:1},velocity:{type:"Range",id:2},pressure:{type:"Range",id:3},altitude:{type:"Range",id:4},radiusX:{type:"Range",id:5},radiusY:{type:"Range",id:6},resolveURI:{type:"string",id:7},dependencies:{rule:"repeated",type:"InputPropertyType",id:8}}},InputPropertyType:{values:{PRESSURE:0,RADIUS_X:1,RADIUS_Y:2,AZIMUTH:3,ALTITUDE:4,ROTATION:5}},InkObject:{fields:{inputData:{type:"InputData",id:1},inkData:{type:"InkData",id:2},brushes:{type:"Brushes",id:3},inkTree:{rule:"repeated",type:"Node",id:4},views:{rule:"repeated",type:"View",id:5},knowledgeGraph:{type:"TripleStore",id:6},transform:{type:"Matrix4",id:7},properties:{rule:"repeated",type:"Property",id:8}}}}}}},root:"WacomInkFormat3",messages:{}}};qo.evolution.forEach((function(e){var t=qo[e].root;Object.defineProperty(qo[e],"format",{get:function(){return this.value||(this.value=Ho.Root.fromJSON(this.schema.nested[t])),this.value}})})),Object.defineProperty(qo,"latest",{value:qo[qo.evolution.last].format,enumerable:!0});var Ko=Ho.Namespace,Jo=Ho.Message,$o=function(){function e(){k(this,e),this.format=qo.latest}return S(e,null,[{key:"encode",value:function(e){return e.constructor.encode(e).finish()}},{key:"decode",value:function(e,t){if(!(t instanceof Ko))throw new Error("Expected 'Type' to be instance of 'Namespace'");return e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e instanceof Uint8Array?t.decode(e):e instanceof Jo?e:t.fromObject(e)}}]),e}();Ho.Namespace;var Qo=Ho.Message,ea=function(){function e(){k(this,e),Object.defineProperty(this,"format",{value:qo.latest})}return S(e,[{key:"decodeInkObject",value:function(e,t){if("3.0.0"==t){var n=this.decode("InkObject",e.data,t);return{Properties:new Qo({properties:n.properties}),KnowledgeGraph:n.knowledgeGraph,InputData:n.inputData,Brushes:n.brushes,InkData:n.inkData,InkStructure:new Qo({type:qo.latest.StructureType.STROKE,inkTree:{tree:n.inkTree},views:n.views})}}return{Properties:this.decode("Properties",e.prps,t),KnowledgeGraph:this.decode("TripleStore",e.knwg,t),InputData:this.decode("InputData",e.inpt,t),Brushes:this.decode("Brushes",e.brsh,t),InkData:this.decode("InkData",e.inkd,t),InkStructure:this.decode("InkStructure",e.inks,t)}}},{key:"decode",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"latest";if(t){for(var r=qo[n].format,i=r[e].decode(t),o=qo.evolution.slice();o.first!=n;)o=o.slice(1);return this.evolution=o.slice(1),this.evolution.length>0&&(this.state={context:i},this.resolve(i),delete this.state),i}}},{key:"resolve",value:function(e){var t=this,n=e.constructor.name;this.evolution.forEach((function(r){var i=qo[r].messages[n];i&&i(e,t.state)})),Object.values(e).forEach((function(e){e instanceof Qo?t.resolve(e):Array.isArray(e)&&e.length>0&&e.first instanceof Qo&&e.forEach((function(e){e instanceof Qo&&t.resolve(e)}))}))}}]),e}();function ta(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var na=function(e){T(n,e);var t=ta(n);function n(){var e;return k(this,n),(e=t.call(this)).precisionDetector=new yo,Object.defineProperty(A(e),"precisionCalculator",{get:function(){return e.precisionDetector.calculator},set:function(t){return e.precisionDetector.calculator=t},enumerable:!0}),e}return S(n,[{key:"encode",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2?arguments[2]:void 0;if(0!=e.length){var i=this.buildStrokesIndex(e);this.state={index:i.index},this.precisionDetector.calculator&&this.precisionDetector.reset();var o=this.format.InkData.create({strokes:e.map((function(e){return t.encodeStroke(e)})),unitScaleFactor:n,transform:this.encodeMatrix(r),brushURIs:i.brushURIs,renderModeURIs:i.renderModeURIs,properties:i.properties.map((function(e){return Zo.encodePathPointProperties(e,t.format.PathPointProperties.create())}))});return this.reset(),this.precisionDetector.calculator&&this.precisionDetector.printStats(),$o.encode(o)}}},{key:"buildStrokesIndex",value:function(e){var t=this,n=[],r=[],i=[],o={},a={};return e.forEach((function(e){var s=[];for(var u in t.format.PathPointProperties.fields)"color"==u?(s.push("red",e.pointProps.red||0),s.push("green",e.pointProps.green||0),s.push("blue",e.pointProps.blue||0),s.push("alpha",(e.pointProps.alpha||0).toFixed(2))):s.push(u,(e.pointProps[u]||0).toFixed(2));s=s.join("_");var c=n.indexOf(e.brush.name),l=e.renderMode!=ho.RenderMode.SOURCE_OVER?r.indexOf(e.renderMode):void 0,h=i.indexOf(s);-1==c&&(c=n.push(e.brush.name)-1),-1==l&&(l=r.push(e.renderMode)-1),-1==h&&(h=i.push(s)-1,o[s]=e.pointProps),a[e.id]={brushURI:c,renderModeURI:l,pathPointProperties:h}})),{properties:i.map((function(e){return o[e]})),brushURIs:n,renderModeURIs:r,index:a}}},{key:"decode",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];if(e){var i=$o.decode(e,this.format.InkData),o={brushes:n,brushURIs:i.brushURIs,sensorPaths:Object.assign.apply(Object,[{}].concat(ue(r.map((function(e){return x({},e.id,e)}))))),renderModeURIs:i.renderModeURIs,properties:i.properties},a={strokes:i.strokes.map((function(e){return t.decodeStroke(e,o)})),unitScaleFactor:i.unitScaleFactor||1,transform:this.decodeMatrix(i.transform)};return this.reset(),a}}},{key:"encodeStroke",value:function(e){var t=this.state?this.state.index[e.id]:void 0,n=this.format.Stroke.create({id:U.toBytes(e.id),startParameter:e.ts,endParameter:e.tf,randomSeed:e.randomSeed});if(t?(n.brushURIIndex=t.brushURI+1,n.propertiesIndex=t.pathPointProperties+1,isFinite(t.renderModeURI)&&(n.renderModeURIIndex=t.renderModeURI+1)):(n.brushURIValue=e.brush.name,n.propertiesValue=Zo.encodePathPointProperties(e.pointProps,this.format.PathPointProperties.create()),e.renderMode!=ho.RenderMode.SOURCE_OVER&&(n.renderModeURIValue=e.renderMode)),this.precisionDetector.calculator&&e.compressionType==ho.CompressionType.AUTO&&this.precisionDetector.updatePrecisionSchema(e),e.precisionSchema?(n.precisions=e.precisionSchema.precisions,n.splineCompressed=this.encodeSplineCompressed(e.spline,e.precisionSchema)):n.splineData=this.encodeSpline(e.spline),e.sensorData&&!this.ignoreSensorData){if(!t)throw new Error("Model-less strokes sensor data persistance is not supported");n.sensorDataID=U.toBytes(e.sensorData.id),n.sensorDataOffset=e.sensorDataOffset,n.sensorDataMapping=e.sensorDataMapping}return n}},{key:"decodeStroke",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n="splineCompressed"==e.data,r=e[e.data];if(0==r.splineX.length||0==r.splineY.length)throw new Error("Incomplete path definition");var i=[me.Property.X,me.Property.Y];r.splineZ.length>0&&i.push(me.Property.Z),r.red&&r.red.length>0&&i.push(me.Property.RED),r.green.length>0&&i.push(me.Property.GREEN),r.blue.length>0&&i.push(me.Property.BLUE),r.alpha.length>0&&i.push(me.Property.ALPHA),r.size.length>0&&i.push(me.Property.SIZE),r.rotation.length>0&&i.push(me.Property.ROTATION),r.scaleX.length>0&&i.push(me.Property.SCALE_X),r.scaleY.length>0&&i.push(me.Property.SCALE_Y),r.scaleZ.length>0&&i.push(me.Property.SCALE_Z),r.offsetX.length>0&&i.push(me.Property.OFFSET_X),r.offsetY.length>0&&i.push(me.Property.OFFSET_Y),r.offsetZ.length>0&&i.push(me.Property.OFFSET_Z);var o,a,s=r.splineX.length,u=i.length,c=Float32Array.createSharedInstance(s*u);n?(o=new vr(e.precisions),this.decodeSplineCompressed(r,i,c,o)):this.decodeSpline(r,i,c),e.renderModeURI&&(a="renderModeURIValue"==e.renderModeURI?e.renderModeURIValue:t.renderModeURIs[e.renderModeURIIndex-1]);var l,h="propertiesValue"==e.properties?Zo.decodePathPointProperties(e.propertiesValue,i):Zo.decodePathPointProperties(t.properties[e.propertiesIndex-1],i),f="brushURIValue"==e.brushURI?e.brushURIValue:t.brushURIs[e.brushURIIndex-1],d=(t.brushes?t.brushes[f]:void 0)||f;t.sensorPaths&&e.sensorDataID&&(l=t.sensorPaths[U.fromBytes(e.sensorDataID)]);var p=e.startParameter,y=e.endParameter;1==p&&(console.warn("Invalid ts = 1 found on spline deserializtion, ts is set to 0.99999. Stroke ".concat(U.fromBytes(e.id)," is affected.")),p=.99999),0==y&&(console.warn("Invalid tf = 1 found on spline deserializtion, tf is set to 0.00001. Stroke ".concat(U.fromBytes(e.id)," is affected.")),y=1e-5);var v=Mr.createSharedInstance(i,c,h,p,y);v.id=this.cloneStroke?U.generate():U.fromBytes(e.id);var m=new ho(d,v,t.inkPath,l);return m.randomSeed=e.randomSeed,m.precisionSchema=o,a&&(m.renderMode=a),l&&(m.sensorDataOffset=e.sensorDataOffset,m.sensorDataMapping=e.sensorDataMapping),m}},{key:"encodeSpline",value:function(e){for(var t=this.format.Stroke.SplineData.create(),n=function(n){var r=e.getPointRef(n);e.layout.forEach((function(e){switch(e){case me.Property.X:t.splineX.push(r.x);break;case me.Property.Y:t.splineY.push(r.y);break;case me.Property.Z:t.splineZ.push(r.z);break;case me.Property.RED:t.red.push(r.red);break;case me.Property.GREEN:t.green.push(r.green);break;case me.Property.BLUE:t.blue.push(r.blue);break;case me.Property.ALPHA:t.alpha.push(Math.round(255*r.alpha));break;case me.Property.SIZE:t.size.push(r.size);break;case me.Property.ROTATION:t.rotation.push(r.rotation);break;case me.Property.SCALE_X:t.scaleX.push(r.scaleX);break;case me.Property.SCALE_Y:t.scaleY.push(r.scaleY);break;case me.Property.SCALE_Z:t.scaleZ.push(r.scaleZ);break;case me.Property.OFFSET_X:t.offsetX.push(r.offsetX);break;case me.Property.OFFSET_Y:t.offsetY.push(r.offsetY);break;case me.Property.OFFSET_Z:t.offsetZ.push(r.offsetZ);break;default:throw new Error("Invalid layout property provided: ".concat(e))}}))},r=0;r<e.length;r++)n(r);return t}},{key:"decodeSpline",value:function(e,t,n){for(var r=t.length,i=function(i){t.forEach((function(t,o){var a;switch(t){case me.Property.X:a=e.splineX[i];break;case me.Property.Y:a=e.splineY[i];break;case me.Property.Z:a=e.splineZ[i];break;case me.Property.RED:a=e.red[i];break;case me.Property.GREEN:a=e.green[i];break;case me.Property.BLUE:a=e.blue[i];break;case me.Property.ALPHA:a=e.alpha[i]/255;break;case me.Property.SIZE:a=e.size[i];break;case me.Property.ROTATION:a=e.rotation[i];break;case me.Property.SCALE_X:a=e.scaleX[i];break;case me.Property.SCALE_Y:a=e.scaleY[i];break;case me.Property.SCALE_Z:a=e.scaleZ[i];break;case me.Property.OFFSET_X:a=e.offsetX[i];break;case me.Property.OFFSET_Y:a=e.offsetY[i];break;case me.Property.OFFSET_Z:a=e.offsetZ[i];break;default:throw new Error("Invalid layout property provided: ".concat(t.name))}n[i*r+o]=a}))},o=0;o<n.length;o++)i(o)}},{key:"encodeSplineCompressed",value:function(e,t){for(var n=this.format.Stroke.SplineCompressed.create(),r=t.factors,i={},o=function(e,n,o){var a;if((o=Math.round(o*r[e]))>Number.MAX_INT32||o<-Number.MAX_INT32)throw new Error("Int32 precision exceeded ".concat(o," for precision with type ").concat(e," and value ").concat(t[e],". This value is not applicable."));return a=n in i?o-i[n]:o,i[n]=o,a},a=function(t){var r=e.getPointRef(t);e.layout.forEach((function(e,t){switch(e){case me.Property.X:n.splineX.push(o("position","x",r.x));break;case me.Property.Y:n.splineY.push(o("position","y",r.y));break;case me.Property.Z:n.splineZ.push(o("position","z",r.z));break;case me.Property.RED:n.red.push(r.red);break;case me.Property.GREEN:n.green.push(r.green);break;case me.Property.BLUE:n.blue.push(r.blue);break;case me.Property.ALPHA:n.alpha.push(Math.round(255*r.alpha));break;case me.Property.SIZE:n.size.push(o("size","size",r.size));break;case me.Property.ROTATION:n.rotation.push(o("rotation","rotation",r.rotation));break;case me.Property.SCALE_X:n.scaleX.push(o("scale","scaleX",r.scaleX));break;case me.Property.SCALE_Y:n.scaleY.push(o("scale","scaleY",r.scaleY));break;case me.Property.SCALE_Z:n.scaleZ.push(o("scale","scaleZ",r.scaleZ));break;case me.Property.OFFSET_X:n.offsetX.push(o("offset","offsetX",r.offsetX));break;case me.Property.OFFSET_Y:n.offsetY.push(o("offset","offsetY",r.offsetY));break;case me.Property.OFFSET_Z:n.offsetZ.push(o("offset","offsetZ",r.offsetZ));break;default:throw new Error("Invalid layout property provided: ".concat(e))}}))},s=0;s<e.length;s++)a(s);return n}},{key:"decodeSplineCompressed",value:function(e,t,n,r){for(var i=t.length,o=r.factors,a={},s=function(e,t,n){return t in a&&(n+=a[t]),a[t]=n,n/o[e]},u=function(r){t.forEach((function(t,o){var a;switch(t){case me.Property.X:a=s("position","x",e.splineX[r]);break;case me.Property.Y:a=s("position","y",e.splineY[r]);break;case me.Property.Z:a=s("position","z",e.splineZ[r]);break;case me.Property.RED:a=e.red[r];break;case me.Property.GREEN:a=e.green[r];break;case me.Property.BLUE:a=e.blue[r];break;case me.Property.ALPHA:a=e.alpha[r]/255;break;case me.Property.SIZE:a=s("size","size",e.size[r]);break;case me.Property.ROTATION:a=s("rotation","rotation",e.rotation[r]);break;case me.Property.SCALE_X:a=s("scale","scaleX",e.scaleX[r]);break;case me.Property.SCALE_Y:a=s("scale","scaleY",e.scaleY[r]);break;case me.Property.SCALE_Z:a=s("scale","scaleZ",e.scaleZ[r]);break;case me.Property.OFFSET_X:a=s("offset","offsetX",e.offsetX[r]);break;case me.Property.OFFSET_Y:a=s("offset","offsetY",e.offsetY[r]);break;case me.Property.OFFSET_Z:a=s("offset","offsetZ",e.offsetZ[r]);break;default:throw new Error("Invalid layout property provided: ".concat(t.name))}n[r*i+o]=a}))},c=0;c<n.length;c++)u(c)}},{key:"encodeMatrix",value:function(e){if(e&&!e.isIdentity)return this.format.Matrix.create({m00:e.m11,m01:e.m12,m02:e.m13,m03:e.m14,m10:e.m21,m11:e.m22,m12:e.m23,m13:e.m24,m20:e.m31,m21:e.m32,m22:e.m33,m23:e.m34,m30:e.m41,m31:e.m42,m32:e.m43,m33:e.m44})}},{key:"decodeMatrix",value:function(e){if(e)return ae.fromMatrix([e.m00,e.m01,e.m02,e.m03,e.m10,e.m11,e.m12,e.m13,e.m20,e.m21,e.m22,e.m23,e.m30,e.m31,e.m32,e.m33])}},{key:"reset",value:function(){this.state=void 0,this.ignoreSensorData=!1,this.cloneStroke=!1}}]),n}($o);function ra(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var ia=function(e){T(n,e);var t=ra(n);function n(){return k(this,n),t.call(this)}return S(n,[{key:"encode",value:function(e){var t=this;if(0!=e.length){var n={},r={},i={},o={},a={};e.forEach((function(e){var t=e.context;n[t.id]||(n[t.id]=t,a[t.environment.id]||(a[t.environment.id]=t.environment),r[t.sensorContext.id]||(r[t.sensorContext.id]=t.sensorContext,t.sensorContext.channelsContexts.forEach((function(e){i[e.device.id]||(i[e.device.id]=e.device),e.inkProvider&&(o[e.inkProvider.id]||(o[e.inkProvider.id]=e.inkProvider))}))))}));var s=this.format.InputData.create({sensorData:e.map((function(e){return t.encodeSensorData(e)})),inputContextData:this.format.InputContextData.create({inputContexts:Object.values(n).map((function(e){return t.format.InputContext.create({id:U.toBytes(U.format(e.id)),environmentID:U.toBytes(U.format(e.environment.id)),sensorContextID:U.toBytes(U.format(e.sensorContext.id))})})),sensorContexts:Object.values(r).map((function(e){return t.format.SensorContext.create({id:U.toBytes(U.format(e.id)),sensorChannelsContext:e.channelsContexts.map((function(e){return t.format.SensorChannelsContext.create({id:U.toBytes(U.format(e.id)),channels:e.channels.map((function(e){return t.format.SensorChannel.create({id:U.toBytes(U.format(e.id)),type:e.type,metric:t.format.InkSensorMetricType[e.metric.name],resolution:e.resolution,min:e.min,max:e.max,precision:e.precision})})),samplingRateHint:e.samplingRate,latency:e.latency,inkInputProviderID:e.inkProvider?U.toBytes(U.format(e.inkProvider.id)):void 0,inputDeviceID:U.toBytes(U.format(e.device.id))})}))})})),inkInputProviders:Object.values(o).map((function(e){return t.format.InkInputProvider.create({id:U.toBytes(U.format(e.id)),type:t.format.InkInputProviderType[e.type.name],properties:t.encodeProperties(e.props)})})),inputDevices:Object.values(i).map((function(e){return t.format.InputDevice.create({id:U.toBytes(U.format(e.id)),properties:t.encodeProperties(e.props)})})),environments:Object.values(a).map((function(e){return t.format.Environment.create({id:U.toBytes(U.format(e.id)),properties:t.encodeProperties(e.props)})}))})});return $o.encode(s)}}},{key:"decode",value:function(e){var t=this;if(!e)return[];var n=$o.decode(e,this.format.InputData),r={},i={},o={},a={},s={};return n.inputContextData.environments.forEach((function(e){var n=new xe;t.decodeProperties(e.properties,n.props),Object.freeze(n),n.id!=U.fromBytes(e.id).replace(/-/g,"")&&console.warn("Environment decode id missmatch - found ".concat(U.fromBytes(e.id).replace(/-/g,""),", expected ").concat(n.id)),s[n.id]=n})),n.inputContextData.inkInputProviders.forEach((function(e){var n=t.decodeProperties(e.properties),r=new H(H.Type[t.format.InkInputProviderType[e.type]],n);Object.freeze(r),r.id!=U.fromBytes(e.id).replace(/-/g,"")&&console.warn("InkInputProvider decode id missmatch - found ".concat(U.fromBytes(e.id).replace(/-/g,""),", expected ").concat(r.id)),a[r.id]=r})),n.inputContextData.inputDevices.forEach((function(e){var n=new We;t.decodeProperties(e.properties,n.props),Object.freeze(n),n.id!=U.fromBytes(e.id).replace(/-/g,"")&&console.warn("InputDevice decode id missmatch - found ".concat(U.fromBytes(e.id).replace(/-/g,""),", expected ").concat(n.id)),o[n.id]=n})),n.inputContextData.sensorContexts.forEach((function(e){var n=new _e;e.sensorChannelsContext.forEach((function(e){var r=new Le(o[U.fromBytes(e.inputDeviceID).replace(/-/g,"")],a[U.fromBytes(e.inkInputProviderID).replace(/-/g,"")]);e.samplingRateHint&&(r.samplingRate=e.samplingRateHint),e.latency&&(r.latency=e.latency),e.channels.forEach((function(e){var n=Me.Metric[t.format.InkSensorMetricType[e.metric]],i=new Me(e.type,n,e.resolution,e.min,e.max);i.precision=e.precision,r.add(i),i.id!=U.fromBytes(e.id).replace(/-/g,"")&&console.warn("SensorChannel decode id missmatch - found ".concat(U.fromBytes(e.id).replace(/-/g,""),", expected ").concat(i.id))})),n.addContext(r),r.id!=U.fromBytes(e.id).replace(/-/g,"")&&console.warn("SensorChannelsContext decode id missmatch - found ".concat(U.fromBytes(e.id).replace(/-/g,""),", expected ").concat(r.id))})),Object.freeze(n),i[n.id]=n,n.id!=U.fromBytes(e.id).replace(/-/g,"")&&console.warn("SensorContext decode id missmatch - found ".concat(U.fromBytes(e.id).replace(/-/g,""),", expected ").concat(n.id))})),n.inputContextData.inputContexts.forEach((function(e){var t=new Ue(s[U.fromBytes(e.environmentID).replace(/-/g,"")],i[U.fromBytes(e.sensorContextID).replace(/-/g,"")]);Object.freeze(t),t.id!=U.fromBytes(e.id).replace(/-/g,"")&&console.warn("InputContext decode id missmatch - found ".concat(U.fromBytes(e.id).replace(/-/g,""),", expected ").concat(t.id)),r[t.id]=t})),n.sensorData.map((function(e){return t.decodeSensorData(e,r[U.fromBytes(e.inputContextID).replace(/-/g,"")])}))}},{key:"encodeSensorData",value:function(e){var t=this,n=this.format.SensorData.create({id:U.toBytes(e.id),inputContextID:U.toBytes(U.format(e.context.id)),state:this.format.InkState[e.inkState.name],timestamp:e.created});return e.streams.forEach((function(e){var r={};e.channels.forEach((function(e){return r[e.id]=[]}));for(var i=function(t){var n=e.get(t);e.channels.forEach((function(e){r[e.id].push(n[ce.getPropName(e.name)])}))},o=0;o<e.length;o++)i(o);e.channels.forEach((function(e){if(!("precision"in e))throw new Error("Precision not found for channel with id: ".concat(e.id));var i=r[e.id];if(0!=i.length){var o=Math.pow(10,e.precision),a=Math.round(i[0]*o),s=t.format.ChannelData.create({sensorChannelID:U.toBytes(U.format(e.id))});s.values.push(a);for(var u=1;u<i.length;u++){var c=Math.round(i[u]*o);s.values.push(c-a),a=c}n.dataChannels.push(s)}}))})),n}},{key:"decodeSensorData",value:function(e,t){if(0==e.dataChannels.length)throw new Error("Not found corresponding data channels for sensor data with id: ".concat(U.fromBytes(e.id)));var n=new ze(t,U.fromBytes(e.id));n.created=parseInt(p.default.fromValue(e.timestamp).toString()),n.inkState=ze.InkState[this.format.InkState[e.state]];var r={};return e.dataChannels.forEach((function(e){var n=U.fromBytes(e.sensorChannelID).replace(/-/g,"");if(0!=e.values.length){var i=t.sensorContext.getContextByChannelID(n);if(!i)throw new Error("Not found corresponding channel context for data with channelID ".concat(n));var o=i.get(n),a=Math.pow(10,o.precision),s=e.values[0],u=[];u.push(s/a);for(var c=1;c<e.values.length;c++){var l=s+e.values[c];u.push(l/a),s=l}r[n]=u}else r[n]=[]})),t.sensorContext.channelsContexts.forEach((function(e){var t=new Ve(e.channels),i=r[e.channels.first.id];if(i)for(var o=i.length,a=function(n){e.channels.forEach((function(e){var i=r[e.id];if(!i)throw new Error("Not found corresponding data for channel ".concat(e.type," with id: ").concat(e.id));t.data.push(i[n])}))},s=0;s<o;s++)a(s);else{if(t.ink)throw new Error("Empty ink stream is not allowed");var u=0;if(e.channels.forEach((function(e){r[e.id]&&(u+=r[e.id].length)})),u>0)throw new Error("Un-alligned channels context is not valid, found with id: ".concat(e.id))}n.add(t)})),n}},{key:"encodeProperties",value:function(e){var t=this;return Object.keys(e).map((function(n){return t.format.Property.create({name:n,value:e[n]})}))}},{key:"decodeProperties",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Object.assign.apply(Object,[t].concat(ue(e.map((function(e){return x({},e.name,e.value)})))))}}]),n}($o);function oa(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return aa(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return aa(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function aa(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function sa(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var ua=function(e){T(i,e);var t,n,r=sa(i);function i(){return k(this,i),r.call(this)}return S(i,[{key:"encode",value:(n=be(Pe.mark((function e(t){var n,r,i,o,a,s;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!=t.length){e.next=2;break}return e.abrupt("return");case 2:n=this.format.Brushes.create(),r=oa(t),e.prev=4,r.s();case 6:if((i=r.n()).done){e.next=19;break}if(!((o=i.value)instanceof tn)){e.next=13;break}a=this.encodeBrush2D(o),n.vectorBrushes.push(a),e.next=17;break;case 13:return e.next=15,this.encodeBrushGL(o);case 15:s=e.sent,n.rasterBrushes.push(s);case 17:e.next=6;break;case 19:e.next=24;break;case 21:e.prev=21,e.t0=e.catch(4),r.e(e.t0);case 24:return e.prev=24,r.f(),e.finish(24);case 27:return e.abrupt("return",$o.encode(n));case 28:case"end":return e.stop()}}),e,this,[[4,21,24,27]])}))),function(e){return n.apply(this,arguments)})},{key:"decode",value:function(e){var t=this;if(!e)return{};var n=$o.decode(e,this.format.Brushes),r={};return n.vectorBrushes.forEach((function(e){return r[e.name]=t.decodeBrush2D(e)})),n.rasterBrushes.forEach((function(e){return r[e.name]=t.decodeBrushGL(e)})),r}},{key:"encodeBrush2D",value:function(e){if(!e.name)throw new Error("Encode Brush2D failed. name property is required.");var t,n=this.format.VectorBrush.create({name:e.name,spacing:e.spacing}),r=oa(Array.isArray(e.shape)?e.shape:[e.shape]);try{for(r.s();!(t=r.n()).done;){var i=t.value,o=this.format.BrushPrototype.create({size:i.size});if(i.descriptor.shape.name)o.shapeURI=i.descriptor.shape.name;else for(var a=i.shape,s=0;s<a.shape.length;s++)o.coordX.push(a.shape.getPointX(s)),o.coordY.push(a.shape.getPointY(s));n.prototype.push(o)}}catch(e){r.e(e)}finally{r.f()}return n}},{key:"decodeBrush2D",value:function(e){var t=ct.instance?ct.instance.get(e.name):null;if(t){if(t instanceof tn)return t;throw new Error("URI conflict detected, ".concat(e.name," expected Brush2D, found ").concat(t.constructor.name))}var n=[];return e.prototype.forEach((function(e){var t;if(e.shapeURI)t=e.shapeURI;else{t=Float32Array.createSharedInstance(2*e.coordX.length);for(var r=0;r<t.length/2;r++)t[2*r]=e.coordX[r],t[2*r+1]=e.coordY[r]}n.push(new Xt(t,e.size))})),new tn(e.name,n,e.spacing)}},{key:"encodeBrushGL",value:(t=be(Pe.mark((function e(t){var n,r,i,o;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.name){e.next=2;break}throw new Error("Encode BrushGL failed. name property is required.");case 2:if(n={shape:[],shapeURI:[],fill:void 0,filleURI:void 0},!Array.isArray(t.shape)){e.next=11;break}if(!((r=t.descriptor.shape.map((function(e){return e.name})).filter((function(e){return e}))).length>0)){e.next=9;break}if(r.length==t.shape.length){e.next=8;break}throw new Error("Brush ".concat(t.name," shape names length do not match with brush mipmap"));case 8:n.shapeURI=r;case 9:e.next=12;break;case 11:t.descriptor.shape.name&&n.shapeURI.push(t.descriptor.shape.name);case 12:if(n.fillURI=t.descriptor.fill.name,0!=n.shapeURI.length&&n.fillURI){e.next=23;break}return e.next=16,t.getShapeBinary();case 16:return e.t0=e.sent,e.next=19,t.getFillBinary();case 19:e.t1=e.sent,i={shape:e.t0,fill:e.t1},0==n.shapeURI.length&&(Array.isArray(t.shape)?n.shape=i.shape:n.shape.push(i.shape)),n.fillURI||(n.fill=i.fill);case 23:return o=t.blendMode.replace(/-/g,"_").toUpperCase(),e.abrupt("return",this.format.RasterBrush.create({name:t.name,spacing:t.spacing,scattering:t.scattering,blendMode:this.format.BlendMode[o],rotationMode:this.format.RotationMode[t.rotationMode.name],shapeTexture:n.shape,shapeTextureURI:n.shapeURI,fillTexture:n.fill,fillTextureURI:n.fillURI,fillWidth:t.fillTextureSize.width,fillHeight:t.fillTextureSize.height,randomizeFill:t.randomizeFill}));case 25:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"decodeBrushGL",value:function(e){var t,n,r=ct.instance?ct.instance.get(e.name):null;if(r){if(r instanceof Tn)return r;throw new Error("URI conflict detected, ".concat(e.name," expected BrushGL, found ").concat(r.constructor.name))}if(e.shapeTextureURI.length>0)t=1==e.shapeTextureURI.length?{name:e.shapeTextureURI.first}:e.shapeTextureURI.map((function(e){return{name:e}}));else{if(!(e.shapeTexture.length>0))throw new Error("Decode BrushGL shape - insufficent data found");t=1==e.shapeTexture.length?{value:e.shapeTexture.first}:e.shapeTexture.map((function(e){return{value:e}}))}if(e.fillTextureURI)n={name:e.fillTextureURI};else{if(!e.fillTexture)throw new Error("Decode BrushGL fill - insufficent data found");n={value:e.fillTexture}}return new Tn(e.name,t,n,{spacing:e.spacing,scattering:e.scattering,blendMode:un[this.format.BlendMode[e.blendMode]],rotationMode:Tn.RotationMode[e.rotationMode]},{randomize:e.randomizeFill,size:{width:e.fillWidth,height:e.fillHeight}})}}]),i}($o);function ca(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var la=function(e){T(n,e);var t=ca(n);function n(){return k(this,n),t.call(this)}return S(n,[{key:"encode",value:function(e){var t=this;if(e&&0!=e.length){var n=this.format.TripleStore.create({statements:e.map((function(e){return t.format.TripleStore.SemanticTriple.create({subject:e.subject,predicate:e.predicate,object:e.object})}))});return $o.encode(n)}}},{key:"decode",value:function(e){if(e){var t=$o.decode(e,this.format.TripleStore);return M(Vo,ue(t.statements))}}}]),n}($o);function ha(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var fa=function(e){T(n,e);var t=ha(n);function n(e){return k(this,n),t.call(this,e)}return S(n)}(z);function da(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var pa=function(e){T(n,e);var t=da(n);function n(e){var r,i;return k(this,n),r=t.call(this,e),Object.defineProperty(A(r),"bounds",{get:function(){return i||(i=r.getBounds()),i},set:function(e){return i=e},enumerable:!0}),Object.defineProperty(A(r),"boundsValue",{get:function(){return i}}),Object.defineProperty(A(r),"root",{get:function(){for(var e=this.parent,t=this;e;)t=e,e=e.parent;return t},enumerable:!0}),r}return S(n,[{key:"getBounds",value:function(){throw new Error("getBounds is abstract, should be implemented")}},{key:"invalidateBounds",value:function(){this.preventInvalidateBounds||(this.bounds=void 0,this.parent&&this.parent.invalidateBounds())}},{key:"remove",value:function(){this.parent.removeChild(this)}}]),n}(fa);function ya(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var va=function(e){T(n,e);var t=ya(n);function n(e){var r,i;return k(this,n),(r=t.call(this,e)).children=[],Object.defineProperty(A(r),"uri",{get:function(){return i||(i=je.createNodeURI(r.root.model,r.id)),i},enumerable:!0}),Object.defineProperty(A(r),"type",{value:"GROUP",enumerable:!0}),Object.defineProperty(A(r),"content",{value:r.children,enumerable:!0}),r}return S(n,[{key:"getBounds",value:function(){var e;return this.children.forEach((function(t){e=e?e.union(t.bounds):t.bounds})),e}},{key:"contains",value:function(e){if(!(e instanceof pa))throw new Error("Incompatible node found - ".concat(e.constructor.name,". InkElement expected."));for(var t=!1,n=e.parent;n;){if(n==this){t=!0;break}n=n.parent}return t}},{key:"indexOf",value:function(e){if(!(e instanceof pa))throw new Error("Incompatible node found - ".concat(e.constructor.name,". InkElement expected."));return this.children.indexOf(e)}},{key:"appendChild",value:function(e,t){if(!(e instanceof pa))throw new Error("Incompatible node found - ".concat(e.constructor.name,". InkElement expected."));if(t<0)throw new Error("Index should be a positive number");var n=e.parent;if(n){if(this.root.id!=e.root.id)throw new Error("Moving nodes from one hierarchy to another is not allowed. Make sure root is common for this operation.");e.parent.children.remove(e),e.parent.invalidateBounds()}if(e.parent=this,isFinite(t)&&t<this.children.length?this.children.insert(e,t):this.children.push(e),!n){var r=this.root.model;r&&r.register(e,t)}return this.invalidateBounds(),e}},{key:"removeChild",value:function(e){if(!this.children.includes(e))throw new Error("Node was not found");var t=this.root.model;t&&t.unregister(e),this.invalidateBounds(),this.children.remove(e)}},{key:"remove",value:function(){this.parent?this.parent.removeChild(this):(this.model&&this.model.unregister(this),this.invalidateBounds())}}]),n}(pa);function ma(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var ga=function(e){T(n,e);var t=ma(n);function n(e,r){var i;return k(this,n),i=t.call(this),Object.defineProperty(A(i),"uri",{get:function(){return i.id},enumerable:!0}),Object.defineProperty(A(i),"type",{value:"PATH",enumerable:!0}),Object.defineProperty(A(i),"index",{get:function(){return i.parent?i.parent.indexOf(A(i)):0},enumerable:!0}),Object.defineProperty(A(i),"content",{value:e,enumerable:!0}),Object.defineProperty(A(i),"fragment",{value:r,enumerable:!0}),i}return S(n,[{key:"buildURI",value:function(){return je.createNodeURI(this.root.model,this.content.id,this.fragment)}},{key:"getBounds",value:function(){if(this.fragment){var e=this.content.slice(this.fragment);return e.brush instanceof tn&&e.buildPath({lastPipelineStage:Li.BRUSH_APPLIER}),e.bounds}return this.content.bounds}},{key:"split",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;if(this.content instanceof ze)throw new Error("SensorData is immutable. Split is not applicable.");if(!this.parent)throw new Error("Dettached InkPath node split is not allowed. Attach to hierarchy first.");if(e<0||e>this.content.length-1)throw new Error("Index out of range {0, ".concat(this.content.length-1,"}"));var r=this.fragment?this.fragment.pointIndexStart:0,i=this.fragment?this.fragment.pointIndexEnd:this.content.length-1,o=this.fragment?this.fragment.ts:this.content.ts,a=this.fragment?this.fragment.tf:this.content.tf,s=this.content.spline.getFragment(r,o,e+1,t-.05,!0),u=this.content.spline.getFragment(e-1,1==t?.05:t,i,a,!0);if(s.pointIndexEnd+1-s.pointIndexStart<4)throw new Error("Invalid split index ".concat(e," found - range {").concat(s.pointIndexStart,", ").concat(s.pointIndexEnd,"} is invalid. At least 4 points are needed to define path fragment."));if(u.pointIndexEnd+1-u.pointIndexStart<4)throw new Error("Invalid split index ".concat(e," found - range {").concat(u.pointIndexStart,", ").concat(u.pointIndexEnd,"} is invalid. At least 4 points are needed to define path fragment."));var c=new n(this.content,s),l=new n(this.content,u),h=this.index;return this.remove(),this.parent.appendChild(c,h),this.parent.appendChild(l,h+1),[c,l]}}]),n}(pa);function ba(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Ea=function(e){T(n,e);var t=ba(n);function n(e,r,i){var o;return k(this,n),(o=t.call(this,r.type,e)).model=r,o.root=o.createGroup(i),o.root.model=A(o),Object.defineProperty(A(o),"tree",{get:function(){return console.warn("View 'tree' property is deprecated. Use root instead."),o.root}}),o.register(o.root),o}return S(n,[{key:"addPath",value:function(e,t){return t||(t=this.root),t.appendChild(this.createElement(e))}},{key:"createElement",value:function(e,t){if(t&&!(t instanceof Cr))throw new Error("fragment expected type is SplineFragment");return this.model.createElement(e,this,t)}},{key:"createGroup",value:function(e){return this.model.createGroup(e,this)}},{key:"register",value:function(e){var t=this;if(e instanceof va)e.children.forEach((function(e){return t.register(e)}));else{if(!(e instanceof ga))throw new Error("Register unknown node found: ".concat(e.id));if(e.content instanceof ho){if(this.type!=n.Type.STROKE)throw new Error("Incompatible element content found - ".concat(e.content.constructor.name,". Content should be instance of Stroke."))}else{if(!(e.content instanceof ze))throw new Error("Incompatible element content found - ".concat(e.content.constructor.name,". Content should be ").concat(this.type.name,"."));if(this.type!=n.Type.SENSOR_DATA)throw new Error("Incompatible element content found - ".concat(e.content.constructor.name,". Content should be instance of SensorData."))}if(!this.model.content.includes(e.content))throw new Error("Node content not found in underling ink model - ".concat(e.content.id))}this.model.index(e),this.updateKnowledgeGraph(e)}},{key:"unregister",value:function(e){var t=this;e instanceof va&&e.children.forEach((function(e){return t.unregister(e)})),delete this.model.registry.nodes[e.uri],this.updateKnowledgeGraph(e)}},{key:"updateKnowledgeGraph",value:function(e){var t=this;if(this.model.registry.nodes[e.uri]){if(!this.knowledgeGraph)return;this.knowledgeGraph.filter((function(t){return t.subject==e.uri||t.object==e.uri})).forEach((function(n){var r;n.subject==e.uri&&n.object.startsWith("uim:ne/")&&(r=t.model.knowledgeGraph).add.apply(r,ue(t.knowledgeGraph.filter((function(e){return e.subject==n.object})))),t.model.knowledgeGraph.add(n)}))}else this.model.knowledgeGraph.filter((function(t){return t.subject==e.uri||t.object==e.uri})).forEach((function(n){var r;n.subject==e.uri&&n.object.startsWith("uim:ne/")&&(r=t.model.knowledgeGraph).remove.apply(r,ue(t.model.knowledgeGraph.filter((function(e){return e.subject==n.object})))),t.model.knowledgeGraph.remove(n)}))}},{key:"extractKnowledge",value:function(){for(var e=this,t=this.model.knowledgeGraph.filter((function(t){return t.subject.startsWith(je.createNodeURISchema(e))})),n=new Set(t.map((function(e){return e.object})));n.size>0;){var r=this.model.knowledgeGraph.filter((function(e){return n.has(e.subject)&&!t.includes(e)}));t.push.apply(t,ue(r)),n=new Set(r.map((function(e){return e.object})))}return t}}]),n}(Wo);function Pa(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return ka(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ka(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function ka(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function wa(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}function Sa(e,t){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.add(e)}function Ia(e,t,n){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return n}var xa=new WeakSet,Ra=function(e){T(n,e);var t=wa(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n.Type.STROKE,i=arguments.length>1?arguments[1]:void 0;return k(this,n),Sa(A(e=t.call(this,r)),xa),e.root=e.createGroup(i),e.root.model=A(e),Object.defineProperty(A(e),"tree",{get:function(){return console.warn("InkModel 'tree' property is deprecated. Use root instead."),e.root}}),e.props={},e.views={},e.unitScaleFactor=1,e.transform=void 0,e.knowledgeGraph=new Vo,e.knowledgeGraphAnalyzer=new go(A(e)),Object.defineProperty(A(e),"registry",{value:{nodes:{},content:{},brushes:{}},enumerable:!0}),e.type==n.Type.STROKE?e.registry.sensorData={}:Object.defineProperty(e.registry,"sensorData",{get:function(){return e.content},enumerable:!0}),Object.defineProperty(A(e),"content",{value:[],enumerable:!0}),Object.defineProperty(A(e),"strokes",{get:function(){return e.type==n.Type.STROKE?e.content.slice():[]},enumerable:!0}),Object.defineProperty(A(e),"sensorData",{get:function(){var t;if(e.type==n.Type.STROKE){var r={};if(e.disableSensorDataIntegrity)r=Object.assign.apply(Object,[r].concat(ue(Object.values(e.registry.sensorData).map((function(e){return x({},e.id,e)})))));else{var i,o=Pa(e.content);try{for(o.s();!(i=o.n()).done;){var a=i.value;a.sensorData&&(r[a.sensorData.id]=a.sensorData)}}catch(e){o.e(e)}finally{o.f()}}t=Object.values(r)}else t=e.content;return t},set:function(t){if(!(t instanceof ze))throw new Error("Data type mismatch, expected SensorData");if(e.type==n.Type.SENSOR_DATA)throw new Error("For SensorData models use 'addPath' method instead");e.disableSensorDataIntegrity=!0,e.index(t)},enumerable:!0}),Object.defineProperty(A(e),"brushes",{get:function(){if(e.type==n.Type.SENSOR_DATA)return[];var t,r={},i=Pa(e.content);try{for(i.s();!(t=i.n()).done;){var o=t.value;r[o.brush.name]=e.registry.brushes[o.brush.name]||o.brush}}catch(e){i.e(e)}finally{i.f()}return Object.values(r)},set:function(t){var n,r=Pa(t);try{for(r.s();!(n=r.n()).done;){var i=n.value;e.index(i)}}catch(e){r.e(e)}finally{r.f()}},enumerable:!0}),e.register(e.root),e.disableSensorDataIntegrity=!0,e}return S(n,[{key:"addPath",value:function(e,t){t||(t=this.root);var n=this.createElement(e);return e.sensorData&&this.index(e.sensorData),e.brush&&!this.registry.brushes[e.brush.id]&&this.index(e.brush),t.appendChild(n)}},{key:"removePath",value:function(e){e.nodeID&&this.registry.nodes[e.nodeID].remove()}},{key:"replacePath",value:function(e,t){var n=this,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(e.nodeID){var i=this.getNode(e.nodeID),o=i.parent||this.root,a=t.map((function(e){return n.createElement(e)})),s=o.children.indexOf(i);if(r){var u=o.appendChild(this.createGroup(),s);a.forEach((function(e){return u.appendChild(e)}))}else a.forEach((function(e,t){return o.appendChild(e,s+t)}));this.removePath(e)}}},{key:"createElement",value:function(e,t,r){if(!e)throw new Error("Element content not found");var i=t?t.type:this.type;switch(i){case n.Type.STROKE:if(!(e instanceof ho))throw new Error("Incompatible element content found - ".concat(e.constructor.name,". Content should be instance of Stroke."));if(r&&!t)throw new Error("Main tree is not fragmentable. Fragments are applicable only in Views.");break;case n.Type.SENSOR_DATA:if(!(e instanceof ze))throw new Error("Incompatible element content found - ".concat(e.constructor.name,". Content should be instance of SensorData."));if(r)throw new Error("SensorData fragments are not supported");break;default:throw console.warn(i),new Error("Invalid ink model type found")}return new ga(e,r)}},{key:"createGroup",value:function(e,t){return new va(e)}},{key:"getItem",value:function(e){return console.warn("InkModel getItem method is deprecated. Use oneof(getNode(id), getStroke(id), getSensorData(id), getBrush(id))."),this.registry.nodes[e]||this.registry.content[e]||this.registry.sensorData[e]||this.registry.brushes[e]}},{key:"getNode",value:function(e){var t;if(ce.isValidURL(e))t=this.registry.nodes[e];else{var n,r=Pa([this].concat(ue(Object.values(this.views))));try{for(r.s();!(n=r.n()).done;){var i=n.value,o=je.createNodeURI(i,e);if(t=this.registry.nodes[o])break}}catch(e){r.e(e)}finally{r.f()}}return t}},{key:"getPath",value:function(e){return this.registry.content[e]}},{key:"getStroke",value:function(e){return this.type==n.Type.STROKE?this.registry.content[e]:void 0}},{key:"getSensorData",value:function(e){return this.registry.sensorData[e]}},{key:"getBrush",value:function(e){return this.registry.brushes[e]}},{key:"register",value:function(e,t){var r=this;if(e instanceof va)e.children.forEach((function(e){return r.register(e)}));else{if(!(e instanceof ga))throw new Error("Register unknown node found: ".concat(e.id));if(e.content instanceof ho){if(this.type!=n.Type.STROKE)throw new Error("Incompatible element content found - ".concat(e.content.constructor.name,". Content should be instance of Stroke."))}else{if(!(e.content instanceof ze))throw new Error("Incompatible element content found - ".concat(e.content.constructor.name,". Content should be ").concat(this.type.name,"."));if(this.type!=n.Type.SENSOR_DATA)throw new Error("Incompatible element content found - ".concat(e.content.constructor.name,". Content should be instance of SensorData."))}Object.defineProperty(e.content,"nodeID",{value:e.id,enumerable:!0,configurable:!0}),isFinite(t)&&t<this.content.length?this.content.insert(e.content,t):this.content.push(e.content),this.index(e.content)}this.index(e),this.resetViews()}},{key:"index",value:function(e){var t,r=e.id;if(e instanceof fa){r=e.uri,t="nodes";var i=this.type.name;e instanceof va&&(i="ol<".concat(i,">")),Object.defineProperty(e,"dataType",{value:i,enumerable:!0})}else e instanceof ho?t="content":e instanceof ze?t=this.type==n.Type.SENSOR_DATA?"content":"sensorData":e instanceof ft&&(t="brushes");if(!t)throw new Error("Expected item type is oneof(InkNode, Stroke, SensorData, Brush)");var o=this.registry[t];if(o[r]){if(o[r]!=e)throw new Error("Cannot register ".concat(t," item with id ").concat(r,". Already is available item with such identifier."))}else o[r]=e}},{key:"unregister",value:function(e){var t=this;this.resetViews(),e instanceof va?e.children.forEach((function(e){return t.unregister(e)})):e instanceof ga&&(delete e.content.nodeID,delete this.registry.content[e.content.id],this.content.remove(e.content)),e==this.root?e.children.clear():delete this.registry.nodes[e.uri]}},{key:"createView",value:function(e,t,n){var r=new Ea(e,this,t);return this.views[e]=r,n&&(r.knowledgeGraph=n,r.updateKnowledgeGraph(r.root)),r}},{key:"resetViews",value:function(){if(!this.keepViews){for(var e in this.views)this.views[e].root.remove(),delete this.views[e];this.knowledgeGraph.length>0&&this.knowledgeGraphAnalyzer.clean()}}},{key:"clear",value:function(){delete this.version,delete this.transform,this.unitScaleFactor=1,this.knowledgeGraphAnalyzer.clean(),this.knowledgeGraph.clear(),this.content.clear(),this.views={},this.registry.nodes={},this.registry.content={},this.registry.brushes={},this.registry.sensorData&&(this.registry.sensorData={}),this.root=this.createGroup(this.root.id),this.root.model=this,this.register(this.root)}},{key:"clone",value:function(){var e=new n(this.type,this.root.id);if(e.id=this.id,e.props=this.props,e.transform=this.transform,e.unitScaleFactor=this.unitScaleFactor,e.knowledgeGraph=this.knowledgeGraph.clone(),this.type!=n.Type.STROKE)throw new Error("SENSOR_DATA model copy is not supported yet");Object.values(this.registry.sensorData).forEach((function(t){return e.index(t)})),Object.values(this.registry.brushes).forEach((function(t){return e.index(t)})),e.keepViews=!0,Ia(this,xa,Ta).call(this,this.root,e.root),delete e.keepViews;for(var t=0,r=Object.values(this.views);t<r.length;t++){var i=r[t],o=e.createView(i.name,i.root.id);Ia(this,xa,Ta).call(this,i.root,o.root)}return e}}]),n}(Wo);function Ta(e,t){var n,r=t.root.model,i=Pa(e.children);try{for(i.s();!(n=i.n()).done;){var o=n.value;if("PATH"==o.type){var a=r.model?r.model.getPath(o.content.id):o.content.clone(!0,!0),s=r.createElement(a,o.fragment);t.appendChild(s)}else{if("GROUP"!=o.type)throw new Error("Unknown node type found: ".concat(o.type));var u=r.createGroup(o.id);t.appendChild(u),Ia(this,xa,Ta).call(this,o,u)}}}catch(e){i.e(e)}finally{i.f()}}function Aa(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return Oa(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Oa(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function Oa(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ca(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Da=function(e){T(n,e);var t=Ca(n);function n(){return k(this,n),t.call(this)}return S(n,[{key:"encode",value:function(e){var t=this,n=this.format.InkStructure.create({type:this.format.StructureType[e.type.name],inkTree:this.format.InkTree.create({tree:this.encodeNodeTree(e)}),views:Object.values(e.views).map((function(e){return t.encodeView(e)}))});return $o.encode(n)}},{key:"decode",value:function(e,t,n){var r=this;if(e){var i=$o.decode(e,this.format.InkStructure),o=i.type==this.format.StructureType.STROKE?Ra.Type.STROKE:Ra.Type.SENSOR_DATA,a=o==Ra.Type.STROKE?t:n,s=new Ra(o,U.fromBytes(i.inkTree.tree.first.groupID)),u=i.inkTree.tree.first.bounds;if(u&&(s.root.bounds=new $(u.x,u.y,u.width,u.height)),this.decodeNodeTree(s,i.inkTree.tree.slice(1),a),i.views.forEach((function(e){return r.decodeView(e,s,a)})),o==Ra.Type.STROKE){var c,l=Aa(n);try{for(l.s();!(c=l.n()).done;){var h=c.value;s.index(h)}}catch(e){l.e(e)}finally{l.f()}}return s}}},{key:"encodeView",value:function(e){return this.format.InkTree.create({name:e.name,tree:this.encodeNodeTree(e)})}},{key:"decodeView",value:function(e,t,n,r){var i=e.tree.first,o=U.fromBytes(i.groupID);if(!t.views[e.name]||t.views[e.name].root.id!=o){var a=t.createView(e.name,o,r),s=e.tree.first.bounds;s&&(a.root.bounds=new $(s.x,s.y,s.width,s.height)),this.decodeNodeTree(a,e.tree.slice(1),n),a.invalid&&(console.warn("Drop view ".concat(a.uri," due to invalid structure")),delete t.views[a.name],t.knowledgeGraphAnalyzer.clean())}}},{key:"encodeNodeTree",value:function(e){var t=[];return t.push(this.encodeNode(e.root,0)),this.addChildren(t,e,e.root.children,1),t}},{key:"addChildren",value:function(e,t,n,r){var i=this;n.forEach((function(n){var o=n instanceof ga?(t.model||t).content.indexOf(n.content):void 0;e.push(i.encodeNode(n,r,o)),n instanceof va&&i.addChildren(e,t,n.children,r+1)}))}},{key:"encodeNode",value:function(e,t,n){var r=this.format.Node.create({depth:t,bounds:e.boundsValue});return e instanceof va?r.groupID=U.toBytes(e.id):r.index=n,e.fragment&&(r.interval=this.format.Interval.create({fromIndex:e.fragment.pointIndexStart,toIndex:e.fragment.pointIndexEnd,fromTValue:e.fragment.ts,toTValue:e.fragment.tf})),r}},{key:"decodeNodeTree",value:function(e,t,n){if(!e.invalid){var r,i=e.root,o=0,a={},s=Aa(t);try{for(s.s();!(r=s.n()).done;){for(var u=r.value;o>=u.depth;)i=i.parent,o--;if(i.preventInvalidateBounds=!0,"groupID"==u.id){if(!(i instanceof va))throw new Error("Incorrect tree structure, a tree node is not a InkGroup instance and cannot contain child nodes.");i=i.appendChild(e.createGroup(U.fromBytes(u.groupID))),u.bounds&&(i.bounds=new $(u.bounds.x,u.bounds.y,u.bounds.width,u.bounds.height))}else{var c=function(){var t=void 0;if(u.interval){if(e instanceof Ra)throw new Error("Intervals are not applicable for InkModel, only View allow intervals");if(e.type==Ra.Type.STROKE){var r=n[u.index];try{t=r.spline.getFragment(u.interval.fromIndex,u.interval.fromTValue,u.interval.toIndex,u.interval.toTValue,!0),a[r.id]?a[r.id].forEach((function(e){if(t.overlaps(e))throw new Error("Overlapped fragments are not allowed. ".concat(t," overlaps ").concat(e))})):a[r.id]=[],a[r.id].push(t)}catch(t){if(Ra.reservedViewNames.includes(e.name))return console.warn("invalid path fragment content id: ".concat(r.id," - ").concat(t.message)),e.invalid=!0,{v:void 0};throw t}}else console.warn("Intervals are not applicable for SensorData models, only Stroke models allow intervals. Model ".concat(e.name," discards interval."))}if(e.type==Ra.Type.STROKE&&Ra.reservedViewNames.includes(e.name)){var o=n[u.index],s=je.createNodeURI(e,o.id,t);if((e.model||e).getNode(s))return console.warn("duplicate path node ".concat(s)),e.invalid=!0,{v:void 0}}var c=e.createElement(n[u.index],t);i=i.appendChild(c)}();if("object"===P(c))return c.v}delete i.parent.preventInvalidateBounds,o=u.depth}}catch(e){s.e(e)}finally{s.f()}}}}]),n}($o);function Ma(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Na=function(e){T(n,e);var t=Ma(n);function n(){return k(this,n),t.call(this)}return S(n,[{key:"encode",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=this.format.Properties.create({properties:Object.keys(t).map((function(n){return e.format.Property.create({name:n,value:t[n]})}))});return 0==n.properties.length?void 0:$o.encode(n)}},{key:"decode",value:function(e){if(e){var t=$o.decode(e,this.format.Properties);return Object.assign.apply(Object,[{}].concat(ue(t.properties.map((function(e){return x({},e.name,e.value)})))))}}}]),n}($o);function La(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Ba=function(e){T(r,e);var t,n=La(r);function r(){var e;return k(this,r),(e=n.call(this)).brushesCodec=new ua,e}return S(r,[{key:"encode",value:(t=be(Pe.mark((function e(t){var n,i,o,a,s,u,c,l=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=l.length>1&&void 0!==l[1]?l[1]:{},i=l.length>2&&void 0!==l[2]?l[2]:{},o=l.length>3&&void 0!==l[3]?l[3]:un.SOURCE_OVER,a=[],isFinite(i.red)&&a.push(r.ColorIndex.indexOf("RED")),isFinite(i.green)&&a.push(r.ColorIndex.indexOf("GREEN")),isFinite(i.blue)&&a.push(r.ColorIndex.indexOf("BLUE")),isFinite(i.alpha)&&a.push(r.ColorIndex.indexOf("ALPHA")),s=ce.encodeBitMask(a),e.t0=this.format.InkTool,e.t1=t instanceof tn?this.brushesCodec.encodeBrush2D(t):void 0,!(t instanceof Tn)){e.next=17;break}return e.next=14,this.brushesCodec.encodeBrushGL(t);case 14:e.t2=e.sent,e.next=18;break;case 17:e.t2=void 0;case 18:return e.t3=e.t2,e.t4=this.format.PathPointContext.create({statics:Zo.encodePathPointProperties(i,this.format.PathPointProperties.create()),dynamics:this.encodePathPointSettings(n),colorMask:s}),e.t5={vectorBrush:e.t1,rasterBrush:e.t3,context:e.t4},u=e.t0.create.call(e.t0,e.t5),o!=un.SOURCE_OVER&&(c=o.replace(/-/g,"_").toUpperCase(),u.blendMode=this.format.BlendMode[c]),e.abrupt("return",$o.encode(u));case 24:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"decode",value:function(e){if(e){var t=$o.decode(e,this.format.InkTool),n=ce.decodeBitMask(t.context.colorMask).map((function(e){return me.Property[r.ColorIndex[e]]})),i=[me.Property.RED,me.Property.GREEN,me.Property.BLUE,me.Property.ALPHA].filter((function(e){return!n.includes(e)}));return{brush:"vectorBrush"==t.brush?this.brushesCodec.decodeBrush2D(t.vectorBrush):this.brushesCodec.decodeBrushGL(t.rasterBrush),blendMode:un[this.format.BlendMode[t.blendMode]],statics:Zo.decodePathPointProperties(t.context.statics,i),dynamics:this.decodePathPointSettings(t.context.dynamics)}}}},{key:"encodePathPointSettings",value:function(e){var t=this.format.PathPointSettings.create();for(var n in this.format.PathPointSettings.fields)e[n]&&!e[n].disabled&&(t[n]=this.encodePropertySettings(n,e[n]));return t}},{key:"decodePathPointSettings",value:function(e){if(e){var t={};for(var n in this.format.PathPointSettings.fields)e[n]&&(t[n]=this.decodePropertySettings(e[n]));return t}}},{key:"encodePropertySettings",value:function(e,t){var n=this,i=function(t){if(t)return n.format.Range.create({min:t.min,max:t.max,remapURI:n.getActionDescriptorName(e,"Remap",t.remap)})};return this.format.PropertySettings.create({value:i(t.value),velocity:i(t.velocity),pressure:i(t.pressure),altitude:i(t.altitude),radiusX:i(t.radiusX),radiusY:i(t.radiusY),resolveURI:this.getActionDescriptorName(e,"Resolve",t.resolve),dependencies:ce.encodeBitMask((t.dependencies||[]).map((function(e){return r.InputPropertyTypeIndex.indexOf(Me.getTypeName(e))})))||void 0})}},{key:"getActionDescriptorName",value:function(e,t,n){if(n){if("function"==typeof n)throw new Error("Encode '".concat(e,"' property failed. ").concat(t," action name property is required. Please provide ActionDescriptor for it's definition."));if("string"==typeof n)return n;if(n.name)return n.name;throw new Error("Encode '".concat(e,"' property failed. ").concat(t," action name property is required. Please provide ActionDescriptor for it's definition."))}}},{key:"decodePropertySettings",value:function(e){var t={},n=function(e,n){n&&(t[e]={min:n.min,max:n.max,remap:n.remapURI})};n("value",e.value),n("velocity",e.velocity),n("pressure",e.pressure),n("altitude",e.altitude),n("radiusX",e.radiusX),n("radiusY",e.radiusY),t.resolve=e.resolveURI;var i=ce.decodeBitMask(e.dependencies).map((function(e){return Me.Type[r.InputPropertyTypeIndex[e]]}));return i.length>0&&(t.dependencies=i),t}}]),r}($o);Ba.InputPropertyTypeIndex=Object.freeze([void 0,"PRESSURE","RADIUS_X","RADIUS_Y","AZIMUTH","ALTITUDE","ROTATION"]),Ba.ColorIndex=Object.freeze([void 0,"RED","GREEN","BLUE","ALPHA"]);var _a=new TextEncoder,Fa=3,Ua=1,ja=0,Ga={extension:"uim",contentType:"application/vnd.wacom-ink.model",description:"Universal Ink Model",fourCC:{format:_a.encode("UINK"),InkObject:_a.encode("DATA"),Properties:_a.encode("PRPS"),InputData:_a.encode("INPT"),Brushes:_a.encode("BRSH"),InkData:_a.encode("INKD"),InkStructure:_a.encode("INKS"),TripleStore:_a.encode("KNWG")}};Object.defineProperty(Ga,"version",{value:"".concat(Fa,".").concat(Ua,".").concat(ja)}),Object.defineProperty(Ga,"serial",{value:new Uint8Array([Fa,Ua,ja])}),Ho.BufferReader,Ho.BufferWriter;var Ya=function(){function e(){var t=this;k(this,e),this.dataCodec=new na,this.inputCodec=new ia,this.brushesCodec=new ua,this.structureCodec=new Da,this.tripleStoreCodec=new la,this.propsCodec=new Na,this.inkToolCodec=new Ba,this.compatibilityProvider=new ea,this.riffEncoder=new ir(Ga.fourCC.format,Ga.serial),this.riffDecoder=new lr,Object.defineProperty(this,"precisionCalculator",{get:function(){return t.dataCodec.precisionCalculator},set:function(e){return t.dataCodec.precisionCalculator=e},enumerable:!0})}var t,n,r,i;return S(e,[{key:"encodeInkModel",value:(i=be(Pe.mark((function e(t){var n,r=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=r.length>1&&void 0!==r[1]?r[1]:tr.CompressionType.NONE,this.riffEncoder.add(this.propsCodec.encode(t.props),{version:Ga.serial,fourCC:Ga.fourCC.Properties,contentType:tr.ContentType.PROTO,compressionType:n}),this.riffEncoder.add(this.inputCodec.encode(t.sensorData),{version:Ga.serial,fourCC:Ga.fourCC.InputData,contentType:tr.ContentType.PROTO,compressionType:n}),e.t0=this.riffEncoder,e.next=6,this.brushesCodec.encode(t.brushes);case 6:return e.t1=e.sent,e.t2={version:Ga.serial,fourCC:Ga.fourCC.Brushes,contentType:tr.ContentType.PROTO,compressionType:n},e.t0.add.call(e.t0,e.t1,e.t2),this.riffEncoder.add(this.dataCodec.encode(t.strokes,t.unitScaleFactor,t.transform),{version:Ga.serial,fourCC:Ga.fourCC.InkData,contentType:tr.ContentType.PROTO,compressionType:n}),this.riffEncoder.add(this.tripleStoreCodec.encode(t.knowledgeGraph),{version:Ga.serial,fourCC:Ga.fourCC.TripleStore,contentType:tr.ContentType.PROTO,compressionType:n}),this.riffEncoder.add(this.structureCodec.encode(t),{version:Ga.serial,fourCC:Ga.fourCC.InkStructure,contentType:tr.ContentType.PROTO,compressionType:n}),e.next=14,this.riffEncoder.encode();case 14:return e.abrupt("return",e.sent);case 15:case"end":return e.stop()}}),e,this)}))),function(e){return i.apply(this,arguments)})},{key:"decodeInkModel",value:(r=be(Pe.mark((function e(t,n){var r,i;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t instanceof ArrayBuffer&&(t=new Uint8Array(t)),e.next=3,this.riffDecoder.decode(t);case 3:return r=e.sent,i=r.asProps(),e.abrupt("return",this.buildInkModel(i,r.version,n));case 6:case"end":return e.stop()}}),e,this)}))),function(e,t){return r.apply(this,arguments)})},{key:"buildInkModel",value:function(e,t,n){var r=this,i=this.compatibilityProvider.decodeInkObject(e,t),o=this.tripleStoreCodec.decode(i.KnowledgeGraph);if(n){var a=(n.type==Ra.Type.STROKE?i.InkData.strokes:i.InputData.sensorData).map((function(e){return n.getPath(e.id)}));i.InkStructure.views.forEach((function(e){return r.decodeView(e,n,a,o)}))}else{var s=this.inputCodec.decode(i.InputData),u=this.brushesCodec.decode(i.Brushes),c=this.dataCodec.decode(i.InkData,u,s);(n=this.structureCodec.decode(i.InkStructure,c?c.strokes:[],s)).version=t,c&&(n.unitScaleFactor=c.unitScaleFactor,n.transform=c.transform,n.brushes=Object.values(u)),o&&(n.knowledgeGraph=o),i.Properties&&(n.props=this.propsCodec.decode(i.Properties))}return n}},{key:"encodeInputData",value:function(e){return this.inputCodec.encode(e)}},{key:"decodeInputData",value:function(e){return this.inputCodec.decode(e)}},{key:"encodeInkData",value:function(e){return this.dataCodec.ignoreSensorData=!0,this.dataCodec.encode(e)}},{key:"decodeInkData",value:function(e){this.dataCodec.cloneStroke=!0;var t=this.dataCodec.decode(e);return t?t.strokes:[]}},{key:"encodeBrushes",value:(n=be(Pe.mark((function e(t){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.brushesCodec.encode(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"decodeBrushes",value:function(e){return Object.values(this.brushesCodec.decode(e))}},{key:"encodeProperties",value:function(e){return this.propsCodec.encode(e)}},{key:"decodeProperties",value:function(e){return this.propsCodec.decode(e)}},{key:"encodeTripleStore",value:function(e){return this.tripleStoreCodec.encode(e)}},{key:"decodeTripleStore",value:function(e){return this.tripleStoreCodec.decode(e)}},{key:"decodeJSON",value:function(e){var t,n=e.head,r=n.type,i=n.version,o=e.body;if("InkModel"==r){var a={},s=new TextDecoder,u=qo[i].format;for(var c in o){var l=$o.decode(o[c],u[c]);a[s.decode(Ga.fourCC[c]).toLowerCase()]=$o.encode(l)}t=this.buildInkModel(a,i)}else{var h=qo.latest,f=$o.decode(o,h[r]);switch(r){case"InputData":t=this.decodeInputData(f);break;case"InkData":t=this.decodeInkData(f);break;case"Brushes":t=this.decodeBrushes(f);break;case"Properties":t=this.decodeProperties(f);break;case"TripleStore":t=this.decodeTripleStore(f);break;case"InkTool":t=this.inkToolCodec.decode(f);break;case"InkOperation":if(!this.inkOperationCodec)throw new Error("InkCodec.inkOperationCodec property is not found");this.inkOperationCodec.decode(void 0,f);break;default:throw new Error("input type missmatch - ".concat(r,", expected oneof(InkModel, InputData, InkData, Brushes, Properties, TripleStore, InkTool, InkOperation)"))}}return t}}],[{key:"buildJSON",value:(t=be(Pe.mark((function e(t){var n,r,i,o,a,s,u,c,l,h,f,d,p=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=p.length>1&&void 0!==p[1]?p[1]:"InkModel",t instanceof ArrayBuffer&&(t=new Uint8Array(t)),r={type:n},i={},"InkModel"!=n){e.next=18;break}return o=new lr,e.next=8,o.decode(t);case 8:for(l in a=e.sent,s=a.asProps(),u=qo[a.version].format,c=new TextDecoder,r.version=a.version,r.format=c.decode(Ga.fourCC.format),s.data?i.InkObject=s.data:(s.inks&&(i.InkStructure=s.inks),s.inpt&&(i.InputData=s.inpt),s.inkd&&(i.InkData=s.inkd),s.brsh&&(i.Brushes=s.brsh),s.prps&&(i.Properties=s.prps),s.knwg&&(i.TripleStore=s.knwg)),i)h=$o.decode(i[l],u[l]),i[l]=h.toJSON();e.next=24;break;case 18:if(f=qo.latest,d=$o.decode(t,f[n])){e.next=22;break}throw new Error("Build type missmatch - ".concat(n,", expected oneof(InkModel, InputData, InkData, Brushes, Properties, TripleStore, InkTool, InkOperation)"));case 22:r.version=qo.evolution.last,i=d.toJSON();case 24:return e.abrupt("return",{head:r,body:i});case 25:case"end":return e.stop()}}),e)}))),function(e){return t.apply(this,arguments)})}]),e}();function Xa(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var za=function(e){T(n,e);var t=Xa(n);function n(){var e;return k(this,n),(e=t.call(this)).dataCodec=new na,e}return S(n,[{key:"composeStyle",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2?arguments[2]:void 0,r=this.format.InkOperation.create({compose:this.format.InkOperation.Compose.create({style:this.encodeStyle(e),pointerID:t,strokeID:n?U.toBytes(n):void 0})});return $o.encode(r)}},{key:"composeSegment",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3?arguments[3]:void 0;if(e&&0!=e.length){var i=this.format.InkOperation.create({compose:this.format.InkOperation.Compose.create({segment:this.format.InkSegment.create({ink:this.encodeInkPath(e),complete:t}),pointerID:n,strokeID:r?U.toBytes(r):void 0})});return $o.encode(i)}}},{key:"composeAbort",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1?arguments[1]:void 0,n=this.format.InkOperation.create({compose:this.format.InkOperation.Compose.create({abort:!0,pointerID:e,strokeID:t?U.toBytes(t):void 0})});return $o.encode(n)}},{key:"add",value:function(e){var t=this;e instanceof ho&&(e=[e]);var n=this.format.InkOperation.create({add:this.format.InkOperation.Add.create({strokes:e.map((function(e){return t.encodeInkStroke(e)}))})});return $o.encode(n)}},{key:"remove",value:function(e){var t=this.buildStrokesContext(e);if(t){var n=this.format.InkOperation.create({remove:this.format.InkOperation.Remove.create({context:t})});return $o.encode(n)}}},{key:"update",value:function(e,t){var n=this.buildStrokesContext(e);if(n){var r=this.format.InkOperation.create({update:this.format.InkOperation.Update.create({context:n,style:this.encodeStyle(t,!0)})});return $o.encode(r)}}},{key:"replace",value:function(e){var t=this,n=Object.keys(e);if(0!=n.length){var r=this.format.InkOperation.create({replace:this.format.InkOperation.Replace.create({replacements:n.map((function(n){return t.format.Replacement.create({strokeID:U.toBytes(n),strokes:e[n].map((function(e){return t.encodeInkStroke(e)}))})}))})});return $o.encode(r)}}},{key:"split",value:function(e,t){var n=this,r=Object.keys(e);if(0!=r.length){var i=this.format.InkOperation.create({split:this.format.InkOperation.Split.create({intersections:r.map((function(t){return n.encodeIntersection(t,e[t])})),affectedArea:t})});return $o.encode(i)}}},{key:"selectSelector",value:function(e){var t=this.format.InkOperation.create({select:this.format.InkOperation.Select.create({selector:this.encodeInkStroke(e)})});return $o.encode(t)}},{key:"selectSelection",value:function(e){var t=this.buildStrokesContext(e);if(!t)return this.selectAbort();var n=this.format.InkOperation.create({select:this.format.InkOperation.Select.create({selection:t})});return $o.encode(n)}},{key:"selectAbort",value:function(){var e=this.format.InkOperation.create({select:this.format.InkOperation.Select.create({abort:!0})});return $o.encode(e)}},{key:"updateSelectionTransform",value:function(e){if(!e.isIdentity){var t=this.format.InkOperation.create({updateSelection:this.format.InkOperation.UpdateSelection.create({transform:this.dataCodec.encodeMatrix(e)})});return $o.encode(t)}}},{key:"updateSelectionComplete",value:function(){var e=this.format.InkOperation.create({updateSelection:this.format.InkOperation.UpdateSelection.create({complete:!0})});return $o.encode(e)}},{key:"transform",value:function(e,t){var n=this.buildStrokesContext(e);if(n&&!t.isIdentity){var r=this.format.InkOperation.create({transform:this.format.InkOperation.Transform.create({context:n,matrix:this.dataCodec.encodeMatrix(t)})});return $o.encode(r)}}},{key:"decode",value:function(e,t){var n=this,r=$o.decode(t,this.format.InkOperation),i=r.operation;switch(r=r[i],i){case"compose":var o,a=r.pointerID;switch(r.strokeID.length>0&&(o=U.fromBytes(r.strokeID)),r.stage){case"style":return this.onComposeStyle(e,this.decodeStyle(r.style,!1),a,o);case"segment":return this.onComposeSegment(e,this.decodeInkPath(r.segment.ink),r.segment.complete,a,o);case"abort":return this.onComposeAbort(e,a,o);default:throw new Error("Unknown compose stage found: ".concat(r.stage))}case"add":return this.onAdd(e,r.strokes.map((function(e){return n.decodeInkStroke(e)})));case"remove":return this.onRemove(e,this.getContextStrokes(r.context));case"update":return this.onUpdate(e,this.getContextStrokes(r.context),this.decodeStyle(r.style,!0));case"replace":var s={};return r.replacements.forEach((function(e){var t=U.fromBytes(e.strokeID);s[t]=e.strokes.map((function(e){return n.decodeInkStroke(e)}))})),this.onReplace(e,s);case"split":var u={},c=new $(r.affectedArea.x,r.affectedArea.y,r.affectedArea.width,r.affectedArea.height);return r.intersections.forEach((function(e){var t=n.decodeIntersection(e);u[t.id]=t.fragments})),this.onSplit(e,u,c);case"select":switch(r.stage){case"selector":return this.onSelectSelector(e,this.decodeInkStroke(r.selector));case"selection":return this.onSelectSelection(e,this.getContextStrokes(r.selection));case"abort":return this.onSelectAbort(e);default:throw new Error("Unknown select stage found: ".concat(r.stage))}case"updateSelection":switch(r.stage){case"transform":return this.onUpdateSelectionTransform(e,this.dataCodec.decodeMatrix(r.transform));case"complete":return this.onUpdateSelectionComplete(e);default:throw new Error("Unknown selection update found: ".concat(r.stage))}case"transform":return this.onTransform(e,this.getContextStrokes(r.context),this.dataCodec.decodeMatrix(r.matrix));default:throw new Error("Unknown ink operation found: ".concat(i))}}},{key:"buildStrokesContext",value:function(e){if(e&&(Array.isArray(e)||(e=[e]),0!=e.length))return this.format.StrokesContext.create({context:e.map((function(e){return"string"==typeof e?U.toBytes(e):U.toBytes(e.id)}))})}},{key:"getContextStrokes",value:function(e){var t=this;return e.context.map((function(e){var n=U.fromBytes(e),r=t.getStroke(n);return r||console.warn("Stroke with id ".concat(n," is not provided from getStroke implementation")),r}))}},{key:"traceDecode",value:function(){this.debug||(this.debug=!0,this.onComposeStyle=console.log.bind(console,"onComposeStyle"),this.onComposeSegment=console.log.bind(console,"onComposeSegment"),this.onComposeAbort=console.log.bind(console,"onComposeAbort"),this.onAdd=console.log.bind(console,"onAdd"),this.onRemove=console.log.bind(console,"onRemove"),this.onUpdate=console.log.bind(console,"onUpdate"),this.onReplace=console.log.bind(console,"onReplace"),this.onSplit=console.log.bind(console,"onSplit"),this.onSelectSelector=console.log.bind(console,"onSelectSelector"),this.onSelectSelection=console.log.bind(console,"onSelectSelection"),this.onSelectAbort=console.log.bind(console,"onSelectAbort"),this.onUpdateSelectionTransform=console.log.bind(console,"onUpdateSelectionTransform"),this.onUpdateSelectionComplete=console.log.bind(console,"onUpdateSelectionComplete"),this.onTransform=console.log.bind(console,"onTransform"))}},{key:"getStroke",value:function(e){throw new Error("InkOperation.getStroke(id) is abstract and should be implemented")}},{key:"onComposeStyle",value:function(e,t,n,r){throw new Error("InkOperation.onComposeStyle(user, style, pointerID, strokeID) should be implemented")}},{key:"onComposeSegment",value:function(e,t,n,r,i){throw new Error("InkOperation.onComposeSegment(user, segment, endStroke, pointerID, strokeID) should be implemented")}},{key:"onComposeAbort",value:function(e,t,n){throw new Error("InkOperation.onComposeAbort(user, pointerID, strokeID) should be implemented")}},{key:"onAdd",value:function(e,t){throw new Error("InkOperation.onAdd(user, strokes) should be implemented")}},{key:"onRemove",value:function(e,t){throw new Error("InkOperation.onRemove(user, strokes) should be implemented")}},{key:"onUpdate",value:function(e,t,n,r){throw new Error("InkOperation.onUpdate(user, strokes, style, edit) should be implemented")}},{key:"onReplace",value:function(e,t){throw new Error("InkOperation.onReplace(user, replacements) should be implemented")}},{key:"onSplit",value:function(e,t,n){throw new Error("InkOperation.onSplit(user, intersections, affectedArea) should be implemented")}},{key:"onSelectSelector",value:function(e,t){throw new Error("InkOperation.onSelectSelector(user, selector) should be implemented")}},{key:"onSelectSelection",value:function(e,t){throw new Error("InkOperation.onSelectSelection(user, strokes) should be implemented")}},{key:"onSelectAbort",value:function(e){throw new Error("InkOperation.onSelectAbort(user) should be implemented")}},{key:"onUpdateSelectionTransform",value:function(e,t){throw new Error("InkOperation.onUpdateSelectionTransform(user, transform) should be implemented")}},{key:"onUpdateSelectionComplete",value:function(e){throw new Error("InkOperation.onUpdateSelectionComplete(user) should be implemented")}},{key:"onTransform",value:function(e,t,n){throw new Error("InkOperation.onTransform(user, strokes, transform) should be implemented")}},{key:"encodeStyle",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.format.Style.create();if(e.color)n.color=Zo.rgba(e.color.red,e.color.green,e.color.blue,e.color.alpha);else if(!t)throw new Error("Style property color is required");if(e.brush){if(n.brushURI=e.brush.name,e.brush instanceof Tn)if(e.randomSeed){if(t)throw new Error("Style property randomSeed is not applicable for update, it is immutable");n.randomSeed=e.randomSeed}else if(!t)throw new Error("Style property randomSeed is required")}else if(!t)throw new Error("Style property brush is required");if(e.renderMode)e.renderMode!=ho.RenderMode.SOURCE_OVER&&(n.renderModeURI=e.renderMode);else if(e.blendMode)e.blendMode!=un.SOURCE_OVER&&(n.renderModeURI=ho.RenderMode.get(e.blendMode));else if(!t)throw new Error("Style property oneof(renderMode, blendMode) is required");return n}},{key:"decodeStyle",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n={};if(e.color&&(n.color=wt.fromColor(Zo.fromRGBA(e.color))),e.brushURI){var r=new ht(e.brushURI);if(n.brush=r.value,n.brush instanceof Tn)if(e.randomSeed)t?console.warn("Style property randomSeed is not applicable for update, it is immutable"):n.randomSeed=e.randomSeed;else if(!t)throw new Error("Style property randomSeed is required")}return e.renderModeURI&&(n.renderMode=e.renderModeURI),n}},{key:"encodeInkStroke",value:function(e){return this.format.InkStroke.create({path:this.dataCodec.encodeStroke(e),ink:this.encodeInkPath(e.path)})}},{key:"decodeInkStroke",value:function(e){return this.dataCodec.decodeStroke(e.path,{inkPath:e.ink?this.decodeInkPath(e.ink):void 0})}},{key:"encodeInkPath",value:function(e,t){var n=this,r=this.format.InkPath.create();if(e instanceof ii)r.polygons=this.format.PolygonArray.create({data:e.map((function(e){return n.format.Polygon.create({shape:n.format.Path.create({data:e.shape.points}),holes:e.holes.map((function(e){return n.format.Path.create({data:e.points})}))})}))});else if(e instanceof jr)r.path=this.format.Path.create({layout:ce.encodeBitMask(e.layout.map((function(e){return e.value+1}))),pointProps:Zo.encodePathPointProperties(e.pointProps,this.format.PathPointProperties.create()),data:e.points});else{if(!(e instanceof Mr))throw new Error("Expected path type - oneof(InkPath2D, InterpolatedSpline, Spline), not found");if(t)throw new Error("spline already provided through path property");t=e}return t&&(r.spline=this.format.Path.create({layout:ce.encodeBitMask(t.layout.map((function(e){return e.value+1}))),pointProps:Zo.encodePathPointProperties(t.pointProps,this.format.PathPointProperties.create()),data:t.points})),r}},{key:"decodeInkPath",value:function(e){var t;if("polygons"==e.data)t=M(ii,ue(e.polygons.data.map((function(e){return Yt.createInstance(e.shape.data,e.holes.map((function(e){return e.data})))}))));else if("path"==e.data){var n=ce.decodeBitMask(e.path.layout).map((function(e){return me.Property[e-1]})),r=Zo.decodePathPointProperties(e.path.pointProps,n);t=jr.createSharedInstance(n,e.path.data,r)}if(e.spline){var i=ce.decodeBitMask(e.spline.layout).map((function(e){return me.Property[e-1]})),o=Zo.decodePathPointProperties(e.spline.pointProps,i),a=Mr.createSharedInstance(i,e.spline.data,o);t?t.spline=a:t=a}return t}},{key:"encodeIntersection",value:function(e,t){var n=this;return this.format.Intersection.create({strokeID:U.toBytes(e),fragments:t.map((function(e){return n.format.PathFragment.create({id:U.toBytes(e.id),pointIndexStart:e.pointIndexStart,pointIndexEnd:e.pointIndexEnd,ts:e.ts,tf:e.tf})}))})}},{key:"decodeIntersection",value:function(e){var t=U.fromBytes(e.strokeID),n=this.getStroke(t);if(!n)throw new Error("Stroke with id ".concat(t," is not provided from getStroke implementation"));return{id:U.fromBytes(e.strokeID),fragments:e.fragments.map((function(e){var t=n.spline.getFragment(e.pointIndexStart,e.ts,e.pointIndexEnd,e.tf,!0);return t.id=U.fromBytes(e.id),t}))}}}]),n}($o);function Va(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Ha=function(e){T(n,e);var t=Va(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:2,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return k(this,n),(e=t.call(this)).precision=r,e.autoAdjustOnIntegerOverflow=i,e}return S(n,[{key:"calculatePrecision",value:function(e,t){if(!this.autoAdjustOnIntegerOverflow)return this.precision;for(var n=this.precision,r=NaN;isNaN(r);){if(r=br.calculateError(e,n),0==n){if(isNaN(r))throw new Error("Can't calculate appropriate precision value for the provided float sequence.");break}n--}return n}}]),n}(hr);function Wa(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Za=function(e){T(n,e);var t=Wa(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.5;return k(this,n),(e=t.call(this)).qualityFactor=r,e}return S(n,[{key:"calculatePrecision",value:function(e,t){var n=new Float32Array(9),r=0,i=NaN,o=br.l2Norm(e);if(0==o)return 2;for(var a=0;a<9;a++){var s=br.calculateError(e,a)/o;if(isNaN(s))break;if(!isNaN(i)&&i<=s)break;if(r=a,n[a]=s,br.isZero(s))break;i=s}return Math.round(this.qualityFactor*r)}}]),n}(hr);function qa(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var Ka=function(e){T(n,e);var t=qa(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.5;return k(this,n),(e=t.call(this)).qualityFactor=r,e}return S(n,[{key:"calculatePrecision",value:function(e,t){var n=new Float32Array(e.length);n[0]=0;for(var r=1;r<e.length;r++)n[r]=(e[r]-e[r-1])%1;var i=br.variance(n),o=i%1==0?0:-Math.floor(Math.log10(i));return o=Math.min(o,9),Math.round(this.qualityFactor*o)}}]),n}(hr);function Ja(e,t,n){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,n)}var $a=new WeakMap,Qa=function(){function e(t,n,r,i,o,a,s,u){var c=this;k(this,e),Ja(this,$a,{writable:!0,value:void 0}),this.strokeID=t,n<i&&1==r&&(n++,r=0),i>n&&0==o&&(i--,o=1),this.segmentIndexStart=n,this.segmentIndexEnd=i,this.ts=r,this.tf=o,Object.defineProperty(this,"id",{get:function(){return B(c,$a)||_(c,$a,"".concat(t,"::").concat(n,"-").concat(i,"::").concat(r.toFixed(5),"-").concat(o.toFixed(5))),B(c,$a)}}),this.bounds=a,this.shapesPath=u,this.splineParameters=s}return S(e,[{key:"toString",value:function(){return"node[".concat(this.strokeID,"](").concat(this.segmentIndexStart,", ").concat(this.ts,", ").concat(this.segmentIndexEnd,", ").concat(this.tf,") - ").concat(this.bounds.toString())}},{key:"toJSON",value:function(){return{strokeID:this.strokeID,segmentIndexStart:this.segmentIndexStart,segmentIndexEnd:this.segmentIndexEnd,ts:this.ts,tf:this.tf}}}],[{key:"fromJSON",value:function(t,n,r,i){return new e(t.strokeID,t.segmentIndexStart,t.ts,t.segmentIndexEnd,t.tf,n,r,i)}}]),e}(),es=function(){function e(t){k(this,e),this.tree=t,this.brushAppliers={},this.splineInterpolator=new ei(!1,!0)}return S(e,[{key:"getBrushApplier",value:function(e){return this.brushAppliers[e.name]||(this.brushAppliers[e.name]=new hi(e)),this.brushAppliers[e.name]}},{key:"buildStrokeNodes",value:function(e){var t,n,r=[],i=0,o=0,a=0,s=0,u=0,c=e.pipeline||{},l=c.interpolatedSpline,h=c.shapesPath;if(delete e.pipeline,!l||!h){var f=this.getBrushApplier(e.brush);l=this.splineInterpolator.process(e.spline),h=f.process(l)}if(0==h.length)return r;for(var d=[],p=[],y=0;y<h.length;y++){var v=l.splineParameters[y],m=h[y].bounds;0==i?(o=v.segmentIndex,s=v.t,n=m):this.mustEndCurrentGroup(i,m,n)?(r.push(new Qa(e.id,o,s,a,u,n,d,p)),d=[d.last],p=[p.last],o=a,s=u,n=m.union(t),i=1):n=this.fragmentBounds,d.push(v),p.push(h[y]),i++,t=m,a=v.segmentIndex,u=v.t}return r.push(new Qa(e.id,o,s,a,u,n,d,p)),this.tree.load(r),r}},{key:"mustEndCurrentGroup",value:function(t,n,r){if(delete this.fragmentBounds,t>50)return!0;var i=n.union(r);return t>1&&i.area>1e4||(!!(t>20&&e.isTooSquare(i))||(this.fragmentBounds=i,!1))}}],[{key:"isTooSquare",value:function(e){var t=e.width/e.height;return t>.2&&t<5}}]),e}(),ts=function(){function e(){k(this,e),this.tree=new $n,this.nodeProducer=new es(this.tree),this.strokes={},this.nodes={}}return S(e,[{key:"getNodes",value:function(e){return this.nodes[e]}},{key:"getStroke",value:function(e){return this.strokes[e]}},{key:"getBrushApplier",value:function(e){return this.nodeProducer.getBrushApplier(this.strokes[e].brush)}},{key:"add",value:function(e){if(!(e.brush instanceof Tn)){if(this.strokes[e.id])throw new Error("SpatialContext stroke with id ".concat(e.id," is already available"));this.strokes[e.id]=e,this.nodes[e.id]=this.nodeProducer.buildStrokeNodes(e)}}},{key:"reload",value:function(e){this.tree.unload(this.nodes[e.id]),this.nodes[e.id]=this.nodeProducer.buildStrokeNodes(e)}},{key:"remove",value:function(e){var t="string"==typeof e?e:e.id;this.tree.unload(this.nodes[t]),delete this.nodes[t],delete this.strokes[t]}},{key:"replace",value:function(e,t){var n=this;t.forEach((function(e){return n.add(e)})),this.remove(e)}},{key:"clone",value:function(t){var n=new e;n.nodeProducer.brushAppliers=this.nodeProducer.brushAppliers;var r=[];for(var i in this.strokes){var o=t.getStroke(i);if(!o)throw new Error("Provided ink model do not provides stroke with id ".concat(i));n.strokes[i]=o,n.nodes[i]=this.nodes[i].slice(),r=r.concat(n.nodes[i])}return n.tree.load(r),n}},{key:"reset",value:function(){this.strokes={},this.nodes={},this.tree.clear()}}]),e}(),ns=function(){function e(t,n,r){k(this,e),this.splineParameter=t,this.segmentIndex=n,this.on=r}return S(e,[{key:"toString",value:function(){return"SplineSplitPoint: ".concat(this.splineParameter.segmentIndex," T:").concat(this.splineParameter.t," -> ").concat(this.on?"ON":"OFF",", split by ").concat(this.segmentIndex)}},{key:"toJSON",value:function(){return{splineParameter:this.splineParameter.toJSON(),segmentIndex:this.segmentIndex,on:this.on}}}],[{key:"fromJSON",value:function(t){return new e(Tr.fromJSON(t.splineParameter),t.segmentIndex,t.on)}},{key:"compare",value:function(e,t){return e.splineParameter.segmentIndex<t.splineParameter.segmentIndex?-1:e.splineParameter.segmentIndex>t.splineParameter.segmentIndex?1:e.splineParameter.t<t.splineParameter.t?-1:e.splineParameter.t>t.splineParameter.t?1:0}}]),e}(),rs=function(){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]&&arguments[0];k(this,e),this.splitStrokes=n,Object.defineProperty(this,"context",{get:function(){if(!t)throw new Error("Spatial context not found. Set value first.");return t},set:function(e){if(!e)throw new Error("Spatial context value is required");t=e},enumerable:!0}),this.splineInterpolator=new ei}return S(e,[{key:"processStrokePart",value:function(e,t,n,r){var i=e.splineParameters,o=e.shapesPath;1==o.length&&o.push(o[0]);for(var a=i[0],s=o[0],u=s.bounds,c=1;c<o.length;c++){var l=o[c].bounds;if(l.union(u).intersects(r))o[c].union(s).intersects(t)&&(this.splitStrokes?this.split(e,t,n,s,o[c],a,i[c]):this.selected.add(e.strokeID));a=i[c],s=o[c],u=l}}},{key:"split",value:function(e,t,n,r,i,o,a){var s,u,c=this.context.getStroke(e.strokeID),l=c.spline,h=l.segmentsCount-1,f=r.intersects(t),d=i.intersects(t);if(f&&d)0==o.segmentIndex&&o.t==l.ts&&(s=o),a.segmentIndex==h&&a.t==l.tf&&(u=a);else if(f)0==o.segmentIndex&&o.t==l.ts&&(s=o),u=this.seekNonIntersectingPartFromEnd(c,t,i,o,a);else if(d)s=this.seekNonIntersectingPartFromStart(c,t,r,o,a),a.segmentIndex==h&&a.t==l.tf&&(u=a);else{var p=this.seekNonIntersectingPartFromStart(c,t,r,o,a),y=this.seekNonIntersectingPartFromEnd(c,t,i,p,a);(p.segmentIndex==y.segmentIndex&&p.t<y.t||p.segmentIndex<y.segmentIndex)&&(s=p,u=y)}if(s||u){var v=this.splitPoints[c.id];v||(v=[],this.splitPoints[c.id]=v),s&&v.push(new ns(s,n,!1)),u&&v.push(new ns(u,n,!0))}}},{key:"seekNonIntersectingPartFromStart",value:function(e,t,n,r,i){for(var o=r,a=i,s=r,u=this.context.getBrushApplier(e.id);Tr.areDistantEnough(o,a,this.splineParameterDistanceThreshold);){var c=Tr.calcMiddleOfSegment(o,a),l=this.splineInterpolator.calculateInterpolatedPoint(e.spline,c.segmentIndex,c.t);u.applyBrush(l).union(n).intersects(t)?a=c:(o=c,s=c)}return s}},{key:"seekNonIntersectingPartFromEnd",value:function(e,t,n,r,i){for(var o=r,a=i,s=i,u=this.context.getBrushApplier(e.id);Tr.areDistantEnough(o,a,this.splineParameterDistanceThreshold);){var c=Tr.calcMiddleOfSegment(o,a),l=this.splineInterpolator.calculateInterpolatedPoint(e.spline,c.segmentIndex,c.t);u.applyBrush(l).union(n).intersects(t)?o=c:(a=c,s=c)}return s}},{key:"reset",value:function(e){e&&(this.context=e),this.fragments={},this.selected=new Set,this.splitStrokes&&(this.splitPoints={}),this.context.tree.canvas&&this.context.tree.canvas.refresh()}}]),e}();function is(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return os(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return os(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function os(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function as(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var ss=function(e){T(i,e);var t,n,r=as(i);function i(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i.Mode.WHOLE_STROKE;return k(this,i),x(A(e=r.call(this,t!=i.Mode.WHOLE_STROKE)),"splineParameterDistanceThreshold",.01),e.mode=t,e}return S(i,[{key:"intersect",value:(n=be(Pe.mark((function e(t,n){var r,o,a,s;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t instanceof ho)){e.next=5;break}(s=t.pipeline||{}).shapesPath?a=s.shapesPath:(r=s.interpolatedSpline||this.splineInterpolator.process(t.spline),o=this.context.nodeProducer.getBrushApplier(t.brush)),e.next=20;break;case 5:if(n){e.next=7;break}throw new Error("brush expected");case 7:if(n instanceof tn){e.next=9;break}throw new Error("brush should be Brush2D instance");case 9:if(!(t instanceof jr)){e.next=14;break}o=this.context.nodeProducer.getBrushApplier(n),r=t,e.next=20;break;case 14:if(!(t instanceof Mr)){e.next=19;break}r=this.splineInterpolator.process(t),o=this.context.nodeProducer.getBrushApplier(n),e.next=20;break;case 19:throw new Error("Expected input should be instance of Stroke, InterpolatedSpline, Spline");case 20:return a||(a=o.process(r)),this.reset(),this.context.tree.canvas&&this.context.tree.canvas.fillShape(a),e.next=25,this.processNodes(a);case 25:return this.mode==i.Mode.PARTIAL_STROKE&&this.buildFragments(),e.abrupt("return",{type:"INTERSECTION",intersected:this.fragments,selected:Array.from(this.selected),length:Object.keys(this.fragments).length+this.selected.size});case 27:case"end":return e.stop()}}),e,this)}))),function(e,t){return n.apply(this,arguments)})},{key:"processNodes",value:(t=be(Pe.mark((function e(t){var n,r,o,a,s,u,c,l,h,f,d,p,y,v,m,g,b,E=this;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!=t.length){e.next=5;break}return e.abrupt("return");case 5:1==t.length&&t.push(t[0]);case 6:for(n=t[0],r=n.bounds,o={},a=this.mode==i.Mode.PARTIAL_STROKE&&this.splitPointsProducer,s=1;s<t.length;s++){if(u=t[s].bounds,c=u.union(r),(l=this.context.tree.search(c)).length>0)if(h=t[s].union(n),f=s-1,a){d=is(l);try{for(d.s();!(p=d.n()).done;)y=p.value,o[y.id]||(o[y.id]=[]),o[y.id].push({node:y,eraserHull:h,eraserSegmentIndex:f,eraserSegmentBounds:c})}catch(e){d.e(e)}finally{d.f()}}else{v=is(l);try{for(v.s();!(m=v.n()).done;)g=m.value,this.processStrokePart(g,h,f,c)}catch(e){v.e(e)}finally{v.f()}}n=t[s],r=u}if(!a){e.next=18;break}if(b=[],Object.values(o).forEach((function(e){var t=E.encodeNodeProcessingInfo(e);b.push(t)})),!(b.length>0)){e.next=18;break}return e.next=17,this.splitPointsProducer.build(b);case 17:this.splitPoints=e.sent;case 18:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"buildFragments",value:function(){for(var e in this.splitPoints){var t=this.context.getStroke(e),n=this.convertSplitPointsToFragments(t);0==n.length?this.selected.add(t.id):this.fragments[t.id]=n}}},{key:"convertSplitPointsToFragments",value:function(e){var t=[],n=this.splitPoints[e.id];if(0==n.length)return t;n.sort(ns.compare);var r,i,o,a=new Set,s=e.spline,u=!0;n[0].on?(o=1,r=n[0].splineParameter):(o=0,r=new Tr(0,s.ts));for(var c=o;c<n.length;c++){var l=n[c];if(l.on)a.delete(l.segmentIndex),0==a.size&&(r=l.splineParameter,u=!0);else if(a.add(l.segmentIndex),u&&(i=l.splineParameter,u=!1,r.segmentIndex!=i.segmentIndex||r.t!=i.t)){var h=Cr.getInstance(s,r,i);t.push(h)}}if(u&&(i=new Tr(s.segmentsCount-1,s.tf),r.segmentIndex!=i.segmentIndex||r.t!=i.t)){var f=Cr.getInstance(s,r,i);t.push(f)}return t}},{key:"encodeNodeProcessingInfo",value:function(e){var t,n={splineParameterDistanceThreshold:this.splineParameterDistanceThreshold,input:[]},r=is(e);try{for(r.s();!(t=r.n()).done;){var i=t.value;if(!n.target){var o=this.context.getStroke(i.node.strokeID);o.spline.id=o.id,n.target={stroke:{brush:o.brush.toJSON(),spline:o.spline.toJSON()},bounds:i.node.bounds.toJSON(),shapesPath:i.node.shapesPath.map((function(e){return e.toJSON()})),splineParameters:i.node.splineParameters.map((function(e){return e.toJSON()}))}}n.input.push({node:i.node.toJSON(),eraserHull:i.eraserHull.toJSON(),eraserSegmentIndex:i.eraserSegmentIndex,eraserSegmentBounds:i.eraserSegmentBounds.toJSON()})}}catch(e){r.e(e)}finally{r.f()}return n}}],[{key:"decodeNodeProcessingInfo",value:function(e){var t=e.target,n=e.input,r=e.splineParameterDistanceThreshold,i=tn.fromJSON(t.stroke.brush),o=Mr.fromJSON(t.stroke.spline),a=new ho(i,o),s={bounds:$.fromRect(t.bounds),shapesPath:t.shapesPath.map((function(e){return Yt.fromJSON(e)})),splineParameters:t.splineParameters.map((function(e){return Tr.fromJSON(e)}))};return n=n.map((function(e){return{node:Qa.fromJSON(e.node,s.bounds,s.splineParameters,s.shapesPath),eraserHull:Yt.fromJSON(e.eraserHull),eraserSegmentIndex:e.eraserSegmentIndex,eraserSegmentBounds:$.fromRect(e.eraserSegmentBounds)}})),{stroke:a,input:n,splineParameterDistanceThreshold:r}}}]),i}(rs);Object.defineEnum(ss,"Mode",["WHOLE_STROKE","PARTIAL_STROKE"]);var us=function(){function e(){var t=this;k(this,e),this.contour,this.lastPointIndex,this.segmentIndex,this.p0,this.p1,Object.defineProperty(this,"bounds",{get:function(){return $.ofEdges(t.p0.x,t.p0.y,t.p1.x,t.p1.y)}})}return S(e,[{key:"reset",value:function(e){this.contour=e,this.segmentIndex=-1,this.lastPointIndex=e.length-1,this.p0=me.createInstance(e.layout),this.p1=me.createInstance(e.layout)}},{key:"moveNext",value:function(){return this.segmentIndex++,this.segmentIndex<this.lastPointIndex?(this.p0.fill(this.segmentIndex,this.contour.points,this.contour.layout,this.contour.pointProps),this.p1.fill(this.segmentIndex+1,this.contour.points,this.contour.layout,this.contour.pointProps),!0):this.segmentIndex==this.lastPointIndex&&(this.p0.fill(this.segmentIndex,this.contour.points,this.contour.layout,this.contour.pointProps),this.p1.fill(0,this.contour.points,this.contour.layout,this.contour.pointProps),!0)}}]),e}();function cs(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return ls(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ls(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function ls(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function hs(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}var fs=function(e){T(n,e);var t=hs(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n.Mode.WHOLE_STROKE;return k(this,n),(e=t.call(this,r!=n.Mode.WHOLE_STROKE)).mode=r,e}return S(n,[{key:"select",value:function(e){var t;if(1==e.spline.segmentsCount)t=e.path;else{var r=new Yt(e.spline);0==(t=r.simplify()).length&&(t=new Ln(r))}return this.selection=t,this.selectionBounds=t.bounds,this.reset(),this.context.tree.canvas&&this.context.tree.canvas.fillShape(t),this.splitStrokesWithSelectionContours(),this.mode!=n.Mode.WHOLE_STROKE&&this.buildFragments(),this.fillStrokesEnclosedBySelection(),{type:"SELECTION",contours:this.selection,bounds:this.selectionBounds,intersected:this.fragments,selected:Array.from(this.selected),length:Object.keys(this.fragments).length+this.selected.size}}},{key:"splitStrokesWithSelectionContours",value:function(){var e,t=new us,n=Yt.createInstance([0,0,0,0]),r=cs(this.selection);try{for(r.s();!(e=r.n()).done;){var i=e.value;for(t.reset(i.shape);t.moveNext();){n.shape.points[0]=t.p0.x,n.shape.points[1]=t.p0.y,n.shape.points[2]=t.p1.x,n.shape.points[3]=t.p1.y;var o,a=t.bounds,s=cs(this.context.tree.search(a));try{for(s.s();!(o=s.n()).done;){var u=o.value;this.processStrokePart(u,n,t.segmentIndex,a)}}catch(e){s.e(e)}finally{s.f()}}}}catch(e){r.e(e)}finally{r.f()}}},{key:"buildFragments",value:function(){var e=this;for(var t in this.splitPoints){var r=this.context.getStroke(t);this.fragments[r.id]=this.convertSplitPointsToFragments(r)}this.markStrokeFragmentsInsideSelection(),this.mode!=n.Mode.WHOLE_STROKE&&Object.keys(this.fragments).forEach((function(t){return e.updateFragments(t)}))}},{key:"convertSplitPointsToFragments",value:function(e){var t=[],n=this.splitPoints[e.id];if(0==n.length)return t;n.sort(ns.compare);var r,i=new Set,o=e.spline,a=new Tr(0,o.ts),s=cs(n);try{for(s.s();!(r=s.n()).done;){var u=r.value;if(u.on&&i.delete(u.segmentIndex),0==i.size&&!u.splineParameter.equals(a)){var c=Cr.getInstance(o,a,u.splineParameter);u.on&&(c.overlapped=!0),t.push(c),a=u.splineParameter}u.on||i.add(u.segmentIndex)}}catch(e){s.e(e)}finally{s.f()}var l=new Tr(o.segmentsCount-1,o.tf);if(!l.equals(a)){var h=Cr.getInstance(o,a,l);t.push(h)}return t}},{key:"markStrokeFragmentsInsideSelection",value:function(){for(var e in this.fragments){var t,n=this.context.getStroke(e),r=cs(this.fragments[e]);try{for(r.s();!(t=r.n()).done;){var i=t.value;i.overlapped||this.isStrokeWithinSelectionContours(n,i)&&(i.inside=!0,this.selected.add(i.id))}}catch(e){r.e(e)}finally{r.f()}}}},{key:"updateFragments",value:function(e){var t,r,i=[],o=cs(this.fragments[e]);try{for(o.s();!(r=o.n()).done;){var a=r.value;switch(this.mode){case n.Mode.PARTIAL_STROKE:a.overlapped||i.push(a);break;case n.Mode.PARTIAL_INCLUSIVE:t?a.inside?t=a.union(t):a.overlapped?(a=t.union(a),t=null,i.push(a)):(this.selected.add(t.id),i.push(t),t=null,i.push(a)):a.inside||a.overlapped?t=a:i.push(a);break;case n.Mode.PARTIAL_EXCLUSIVE:t?a.inside?(i.push(t),i.push(a),t=null):t=t.union(a):a.inside?i.push(a):t=a;break;default:throw console.warn(this.strategy),new Error("Invalid strategy found")}}}catch(e){o.e(e)}finally{o.f()}if(t&&i.push(t),1==i.length&&this.mode!=n.Mode.PARTIAL_STROKE)if(this.mode==n.Mode.PARTIAL_EXCLUSIVE)delete this.fragments[e];else{var s=i.first;(s.overlapped||this.selected.has(s.id))&&(this.selected.delete(s.id),delete this.fragments[e],this.selected.add(e))}e in this.fragments&&(this.fragments[e]=i)}},{key:"fillStrokesEnclosedBySelection",value:function(){var e,t=cs(this.context.tree.search(this.selectionBounds));try{for(t.s();!(e=t.n()).done;){var n=e.value;if(!(n.strokeID in this.fragments)&&!this.selected.has(n.strokeID)){var r=this.context.getStroke(n.strokeID);this.isStrokeWithinSelectionContours(r)&&this.selected.add(n.strokeID)}}}catch(e){t.e(e)}finally{t.f()}}},{key:"isStrokeWithinSelectionContours",value:function(e,t){t||(t=e.spline.getFragment());var r=new Tr(t.segmentIndexStart,t.ts),i=new Tr(t.segmentIndexEnd,t.tf),o=Tr.calcMiddleOfSegment(r,i),a=this.splineInterpolator.calculateInterpolatedPoint(e.spline,o.segmentIndex,o.t),s=this.context.getBrushApplier(e.id).applyBrush(a),u=n.getConvexPolyInternalPoint(s);return this.isPointWithinSelectionContours(u)}},{key:"isPointWithinSelectionContours",value:function(e){var t,n=cs(this.selection);try{for(n.s();!(t=n.n()).done;){if(t.value.containsPoint(e))return!0}}catch(e){n.e(e)}finally{n.f()}return!1}}],[{key:"getConvexPolyInternalPoint",value:function(e){var t=e.shape.getPoint(0),n=e.shape.getPoint(Math.floor(e.shape.length/2));return{x:.5*(t.x+n.x),y:.5*(t.y+n.y)}}}]),n}(rs);function ds(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=C(e);if(t){var i=C(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return O(this,n)}}Object.defineEnum(fs,"Mode",["WHOLE_STROKE","PARTIAL_STROKE","PARTIAL_INCLUSIVE","PARTIAL_EXCLUSIVE"]);var ps=function(e){T(r,e);var t,n=ds(r);function r(){var e;return k(this,r),(e=n.call(this,r.WORKER_NAME,r.buildWorkerURL(),Ii.WorkerType.CLASSIC)).actions={},e}return S(r,[{key:"build",value:(t=be(Pe.mark((function e(t){var n,r;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!=t.length){e.next=2;break}return e.abrupt("return");case 2:return n=this.nextID,r={},this.actions[n]={splitPoints:r,queue:t.slice(),expected:t.length,total:t.length},this.updateProgress(0,t.length),e.next=8,this.broadcast("BUILD",n);case 8:return e.abrupt("return",r);case 9:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"buildRequestMessage",value:function(e,t){var n={action:e,actionID:t},r=this.actions[t];if("BUILD"!=e)throw new Error("Unknow data action found: ".concat(e));var i=r.queue.shift();if(i)return n.data=i,n}},{key:"recieve",value:function(e){var t=this.actions[e.actionID];if(t.expected--,"BUILD"!==e.action)throw new Error("Unknow data action found: ".concat(e.action));if(this.update(e.actionID,e.strokeID,e.splitPoints),t.expected>0){var n=this.buildRequestMessage(e.action,e.actionID);n&&this.send(e.worker,n)}0==t.expected&&(delete this.actions[e.actionID],this.resolve(e.actionID))}},{key:"update",value:function(e,t,n){var r;if(n){n=n.map((function(e){return ns.fromJSON(e)}));var i=this.actions[e];i.splitPoints[t]?(r=i.splitPoints[t]).push.apply(r,ue(n)):i.splitPoints[t]=n;var o=100*(i.total-i.expected)/i.total;this.updateProgress(o,i.expected)}}},{key:"updateProgress",value:function(e,t){}}],[{key:"buildWorkerURL",value:function(){if(("undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("digital-ink-min.js",document.baseURI).href).contains("/wacom-src/"))return"/node_modules/digital-ink/workers/".concat(r.WORKER_NAME,".js");if("function"!=typeof DedicatedWorkerGlobalScope){var e=void 0===l?"undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("digital-ink-min.js",document.baseURI).href:l;return(e=e.substring(0,e.lastIndexOf("/"))).endsWith("workers")||(e+="/workers"),e+="/".concat(r.WORKER_NAME),"undefined"==typeof navigator?e+=".mjs":e+=".js",e}}}]),r}(Ii);x(ps,"WORKER_NAME","SplitPointsProvider");var ys=Pr,vs=Rr,ms=Fr,gs=qr,bs=ei,Es=hi,Ps=Pi,ks=Ai,ws=Ci,Ss=Mi,Is=Li;e.BlendMode=un,e.Brush2D=tn,e.BrushApplier=Es,e.BrushGL=Tn,e.BrushPrototype=Xt,e.Color=wt,e.ColorsBox=Bn,e.ConvexHullChainProducer=Ps,e.ConvexHullChainProducerAsync=ks,e.CurvatureBasedInterpolator=bs,e.DistanceBasedInterpolator=gs,e.Environment=xe,e.FixedValuePrecisionCalculator=Ha,e.InkBuilder=qi,e.InkBuilderAsync=eo,e.InkCanvas2D=So,e.InkCanvasGL=Fo,e.InkCodec=Ya,e.InkController=I,e.InkInputProvider=H,e.InkModel=Ra,e.InkObjectFormat=Ga,e.InkOperation=za,e.InkPath2D=ii,e.InkPathProducer=Eo,e.InkToolCodec=Ba,e.InputContext=Ue,e.InputDevice=We,e.InputListener=ye,e.InterpolatedSpline=jr,e.Intersector=ss,e.Matrix=ae,e.OffscreenCanvasGL=Mo,e.Path=At,e.PathPoint=me,e.PathPointContext=nn,e.PathProducer=ys,e.PathSegment=ji,e.Pipeline=Fi,e.PipelineStage=Is,e.Point=Z,e.PointerData=Ui,e.Polygon=Yt,e.PolygonArray=Ln,e.PolygonMerger=ws,e.PolygonSimplifier=Ss,e.PrecisionCalculator=hr,e.PrecisionSchema=vr,e.RIFFDecoder=lr,e.RIFFEncoder=ir,e.RIFFFormat=tr,e.RMSEBasedPrecisionCalculator=Za,e.Rect=$,e.RenderingContext2D=Zn,e.Scalar=Oe,e.Selector=fs,e.SemanticTriple=Xo,e.SensorChannel=Me,e.SensorChannelsContext=Le,e.SensorContext=_e,e.SensorData=ze,e.SensorStream=Ve,e.ShapeFactory=dt,e.Smoother=vs,e.SpatialContext=ts,e.Spline=Mr,e.SplineProducer=ms,e.SplitPointsProducer=ps,e.Stroke=ho,e.StrokeRenderer2D=Ao,e.StrokeRendererGL=Yo,e.TextTable=le,e.TripleStore=Vo,e.TypedArrayCodec=lt,e.URIResolver=ct,e.VarianceBasedPrecisionCalculator=Ka,e.fsx=Qt,e.math=Ft,e.utils=ce,e.uuid=U,e.version=E,Object.defineProperty(e,"__esModule",{value:!0})}));
