<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

include_once __DIR__.'/../../../core.php';

// Ottieni i parametri quest e chat dall'URL
$id_quest = get('id_quest');
$id_chat = get('id_chat');

// Recupera i dettagli della quest se necessario
$quest = null;
if ($id_quest) {
    $quest = $dbo->table('gdr_quest')->where('id', $id_quest)->first();
}

// Controlla se l'utente ha i permessi per modificare la griglia
// Solo gli amministratori o l'utente che ha creato la quest possono modificare la griglia
$can_edit_grid = $user->gruppo == 'Amministratori' || ($quest && $quest->idutente == $user->id);

// Funzione per ottenere le icone dei personaggi
function getCharacterIcons() {
    global $dbo;
    $icons = [];

    $query = "SELECT id, nome, cognome, alias, immagine_chat, immagine_chat_alias FROM gdr_schede WHERE deleted_at IS NULL ORDER BY nome";
    $results = $dbo->fetchArray($query);

    if ($results) {
        foreach ($results as $row) {
            $icons[] = [
                'id' => $row['id'],
                'nome' => $row['nome'].' '.$row['cognome'],
                'immagine' => $row['immagine_chat'] ? $row['immagine_chat'] : 'assets/img/default-avatar.jpg'
            ];

            if( !empty($row['alias']) ){
                $icons[] = [
                    'id' => $row['id'],
                    'nome' => $row['alias'],
                    'immagine' => $row['immagine_chat_alias'] ? $row['immagine_chat_alias'] : 'assets/img/default-avatar.jpg'
                ];
            }
        }
    }

    return $icons;
}

// Ottieni l'elenco dei personaggi per il selettore
$characterIcons = getCharacterIcons();

// Sfondi predefiniti per la griglia con le nuove location
$backgroundImages = [
    base_path().'/modules/gdr_chat/modals/img/bar.jpg' => 'Bar',
    base_path().'/modules/gdr_chat/modals/img/discoteca.jpg' => 'Discoteca',
    base_path().'/modules/gdr_chat/modals/img/magazzino.jpg' => 'Magazzino',
    base_path().'/modules/gdr_chat/modals/img/parcheggio.jpg' => 'Parcheggio',
    base_path().'/modules/gdr_chat/modals/img/strade.jpg' => 'Strade cittadine',
];


// Mostra le opzioni di gestione solo agli amministratori o al creatore della quest
if ($can_edit_grid) {
    echo '
<div class="row mt-3">
    <!-- Sezione per la selezione dello sfondo (prima) -->
    <div class="col-md-12 token-selector">
        <h4>'.tr('Gestione Sfondo').'</h4>
        <div class="alert alert-info">
            '.tr('Per mappe puoi consultare').':
                <ul>
                    <li> <a href="https://inkarnate.com/explore" target="_blank">'.tr('inkarnate.com').'</a> </li>
                    <li> <a href="https://www.dungeonfog.com" target="_blank">'.tr('dungeonfog.com').'</a> </li>
                </ul>
            </div>
        </div>
        <div class="row">
            <div class="col-md-8">
                <label>'.tr('Seleziona sfondo').'</label>
                <select id="background-select" class="form-control">';

foreach ($backgroundImages as $url => $name) {
    echo '<option value="'.$url.'">'.$name.'</option>';
}

echo '
                </select>
            </div>
            <div class="col-md-4">
                <label>'.tr('Sfondo personalizzato').'</label>
                <div class="input-group">
                    <input type="text" id="custom-background" class="form-control" placeholder="'.tr('URL sfondo personalizzato').'">
                    <div class="input-group-append">
                        <button id="apply-background" class="btn btn-success">'.tr('Applica').'</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <!-- Sezione per la creazione dei token (dopo) -->
        <div class="col-md-12 token-selector">
            <h4>'.tr('Gestione Token').'</h4>
            <div class="row">
                <div class="col-md-4">
                    <label>'.tr('Seleziona un personaggio').'</label>
                    <select id="character-select" class="form-control">
                        <option value="">'.tr('Seleziona un personaggio').'</option>';

    foreach ($characterIcons as $character) {
        echo '<option value="'.$character['id'].'"
                    data-image="'.$character['immagine'].'"
                    data-name="'.$character['nome'].'"
                    data-alias="'.$character['alias'].'">
                    '.$character['nome'].'
                </option>';
    }

    echo '
                    </select>
                </div>
                <div class="col-md-2">
                    <label>'.tr('Anteprima').'</label>
                    <div class="token-preview" id="token-preview"></div>
                </div>
                <div class="col-md-2">
                    <label>'.tr('Dimensione').'</label>
                    <select id="token-size" class="form-control">
                        <option value="1">1x1</option>
                        <option value="2">2x2</option>
                        <option value="3">3x3</option>
                        <option value="4">4x4</option>
                        <option value="5">5x5</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label>&nbsp;</label>
                    <button id="create-token" class="btn btn-primary btn-block">'.tr('Crea Token').'</button>
                </div>
                <div class="col-md-4">
                    <label>'.tr('Creazione manuale').'</label>
                    <div class="row">
                        <div class="col-md-5">
                            <input type="text" id="manual-name" class="form-control" placeholder="'.tr('Nome personaggio').'">
                        </div>
                        <div class="col-md-4">
                            <input type="text" id="manual-icon" class="form-control" placeholder="'.tr('URL icona').'">
                        </div>
                        <div class="col-md-2">
                            <select id="manual-size" class="form-control">
                                <option value="1">1x1</option>
                                <option value="2">2x2</option>
                                <option value="3">3x3</option>
                                <option value="4">4x4</option>
                                <option value="5">5x5</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <button id="add-manual-token" class="btn btn-info"><i class="fa fa-plus"></i></button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-md-12">
                    <label>'.tr('Token disponibili').'</label>
                    <div class="token-container" id="token-container">
                        <!-- I token creati appariranno qui -->
                    </div>
                </div>
            </div>
        </div>
    </div>';

echo '
    <div class="row">
        <div class="col-md-12">
            '.($can_edit_grid ? '<button type="button" class="btn btn-block btn-lg btn-success" id="save-grid-btn"><i class="fa fa-save"></i> '.tr('Salva posizioni').'</button>' : '').'
        </div>
    </div>';
}

echo '
    <div class="row mt-3">
        <div class="col-md-12 grid-container">
            <div id="div_griglia"></div>
        </div>
    </div>
</div>';

?>

<style>
    .token-selector {
        margin-bottom: 20px;
        padding: 10px;
        border: 1px solid #ccc;
        border-radius: 5px;
        background-color: #f9f9f9;
    }

    .token-preview {
        display: inline-block;
        width: 50px;
        height: 50px;
        border: 1px solid #ccc;
        vertical-align: middle;
        background-size: cover;
        background-position: center;
    }

    .token-container {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 10px;
        min-height: 60px;
        border: 1px dashed #ccc;
        padding: 5px;
        border-radius: 5px;
    }

    /* Stile per l'input manuale */
    #manual-name, #manual-icon {
        margin-bottom: 5px;
    }

    /* Stile per il contenitore della griglia */
    .grid-container {
        height: 600px;
        width: 100%;
        overflow: auto;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 5px;
        position: relative;
    }

    /* Stile per la griglia */
    .grid {
        display: grid !important;
        grid-template-columns: repeat(40, 50px) !important;
        grid-template-rows: repeat(20, 50px) !important;
        grid-gap: 2px !important;
        position: relative !important;
        background-color: transparent !important;
        min-height: 1000px !important;
    }

    /* Stile per l'immagine di sfondo */
    .grid-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        pointer-events: none; /* Permette di cliccare attraverso l'immagine */
    }

    /* Stile per l'immagine di sfondo */
    .grid-background img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: fill; /* Adatta l'immagine all'intera area senza mantenere le proporzioni */
    }

    /* Stile per le celle della griglia */
    .cell_grid {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(0, 0, 0, 0.2);
        min-width: 50px;
        min-height: 50px;
        position: relative;
    }

    /* Stile per i token */
    .token {
        width: 100%;
        height: 100%;
        background-size: cover !important;
        background-position: center !important;
        cursor: move;
        position: relative;
    }

    /* Stili per token di dimensioni multiple */
    .token.size-2 {
        width: calc(200% + 2px);
        height: calc(200% + 2px);
        z-index: 10;
    }

    .token.size-3 {
        width: calc(300% + 4px);
        height: calc(300% + 4px);
        z-index: 10;
    }

    .token.size-4 {
        width: calc(400% + 6px);
        height: calc(400% + 6px);
        z-index: 10;
    }

    .token.size-5 {
        width: calc(500% + 8px);
        height: calc(500% + 8px);
        z-index: 10;
    }

    /* Stile per il pulsante di rimozione del token */
    .token .remove-token {
        position: absolute;
        top: -10px;
        right: -10px;
        width: 20px;
        height: 20px;
        background-color: red;
        color: white;
        border-radius: 50%;
        text-align: center;
        line-height: 20px;
        font-size: 12px;
        cursor: pointer;
        display: none;
    }

    /* Mostra il pulsante di rimozione al passaggio del mouse */
    .token:hover .remove-token {
        display: block;
    }

    /* Stile per i token draggable */
    .draggable-token {
        width: 50px;
        height: 50px;
        background-size: cover !important;
        background-position: center !important;
        cursor: move;
        border: 1px solid #ccc;
    }

    /* Stile per le celle quando un token viene trascinato sopra */
    .cell_grid.droppable-hover {
        background-color: rgba(0, 255, 0, 0.2) !important;
        border: 2px dashed #00ff00 !important;
    }

    /* Stile per i placeholder delle celle occupate da token grandi */
    .token-placeholder {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        pointer-events: none;
        z-index: 5;
    }
</style>

<script>
    // Variabile per controllare se l'utente può modificare la griglia
    // Verifichiamo se la variabile è già stata dichiarata
    if (typeof canEditGrid === 'undefined') {
        // Se non esiste, la dichiariamo
        window.canEditGrid = <?php echo isset($can_edit_grid) && $can_edit_grid ? 'true' : 'false'; ?>;
    } else {
        // Se esiste già, aggiorniamo il suo valore
        canEditGrid = <?php echo isset($can_edit_grid) && $can_edit_grid ? 'true' : 'false'; ?>;
    }

    // Variabile per memorizzare i dati della griglia salvati
    // Verifichiamo se la variabile è già stata dichiarata
    if (typeof savedGridData === 'undefined') {
        // Se non esiste, la dichiariamo
        window.savedGridData = <?php
            // Verifica se ci sono dati salvati nella quest
            if (isset($quest) && isset($quest->posizioni) && !empty($quest->posizioni)) {
                echo $quest->posizioni;
            } else {
                echo 'null';
            }
        ?>;
    } else {
        // Se esiste già, aggiorniamo il suo valore
        savedGridData = <?php
            // Verifica se ci sono dati salvati nella quest
            if (isset($quest) && isset($quest->posizioni) && !empty($quest->posizioni)) {
                echo $quest->posizioni;
            } else {
                echo 'null';
            }
        ?>;
    }

    // Variabili globali per l'AJAX
    window.globals = window.globals || {};
    globals.id_module = '<?php echo isset($id_module) ? $id_module : 0; ?>';
    globals.id_record = '<?php echo isset($id_record) ? $id_record : 0; ?>';
    globals.id_quest = '<?php echo isset($id_quest) ? $id_quest : 0; ?>';
    globals.id_chat = '<?php echo isset($id_chat) ? $id_chat : 0; ?>';
    globals.rootdir = '<?php echo $rootdir; ?>';

    // Define changeGridBackground in the global scope
    function changeGridBackground(url) {
        // Verifica che l'URL sia valido
        if (!url || typeof url !== 'string' || url.trim() === '') {
            console.error('URL non valido per lo sfondo');
            return;
        }

        // Assicurati che l'URL sia una stringa
        url = String(url);

        // Set a fixed size for the grid based on the background image
        const img = new Image();
        img.onload = function() {
            // Get the original image dimensions
            const imgWidth = this.width;
            const imgHeight = this.height;

            console.log("Original image dimensions:", imgWidth, "x", imgHeight);

            // Calculate the grid size based on the image dimensions
            const cellSize = 50; // Each cell is 50x50px

            // Calculate how many cells we need to cover the image
            const columns = Math.ceil(imgWidth / cellSize);
            const rows = Math.ceil(imgHeight / cellSize);

            console.log("Grid dimensions:", columns, "columns x", rows, "rows");

            // Update the grid CSS to match the image dimensions
            $('.grid').css({
                'width': imgWidth + 'px',
                'height': imgHeight + 'px',
                'min-height': imgHeight + 'px',
                'grid-template-columns': `repeat(${columns}, ${cellSize}px)`,
                'grid-template-rows': `repeat(${rows}, ${cellSize}px)`
            });

            // Aggiorna o crea l'immagine di sfondo
            const bgContainer = $('.grid-background');
            bgContainer.empty(); // Rimuovi eventuali immagini precedenti

            // Crea una nuova immagine
            const bgImg = $(`<img src="${url}" alt="Background" />`);
            bgContainer.append(bgImg);

            // Imposta le dimensioni del container dell'immagine
            bgContainer.css({
                'width': imgWidth + 'px',
                'height': imgHeight + 'px'
            });

            // Adjust the container height if needed
            if (imgHeight > 600) {
                // If image is taller than default container height, increase container height
                $('.grid-container').css('height', Math.min(imgHeight, window.innerHeight * 0.8) + 'px');
            } else {
                // Reset to default height
                $('.grid-container').css('height', '600px');
            }

            // Crea celle aggiuntive se necessario
            const totalCells = rows * columns;
            const currentCells = $('.cell_grid').length;

            if (totalCells > currentCells) {
                console.log("Creating additional cells:", totalCells - currentCells);

                // Aggiungi celle mancanti
                for (let i = currentCells; i < totalCells; i++) {
                    const cell = $(`<div class="cell_grid" id="cell-${i}"></div>`);
                    $('.grid').append(cell);

                    // Rendi le celle droppable se l'utente può modificare la griglia
                    if (window.canEditGrid) {
                        cell.droppable({
                            accept: '.draggable-token, .token',
                            hoverClass: 'droppable-hover',
                            drop: function(event, ui) {
                                // ... (codice per il drop)
                            }
                        });
                    }
                }
            }
        };

        // Gestione degli errori di caricamento dell'immagine
        img.onerror = function() {
            console.error('Errore nel caricamento dell\'immagine:', url);
            alert('Impossibile caricare l\'immagine. Verifica che l\'URL sia corretto.');
        };

        // Imposta l'URL dell'immagine
        img.src = url;
    }

    $(document).ready(function () {
        caricaGriglia($("#div_griglia"));

       // Gestione del pulsante di salvataggio
       $("#save-grid-btn").click(function() {
           salvaPosizioniGriglia();
       });

       // Anteprima del token quando si seleziona un personaggio
       $('#character-select').change(function() {
            const selectedOption = $(this).find('option:selected');
            const imageUrl = selectedOption.data('image');

            if (imageUrl) {
                $('#token-preview').css('background-image', 'url("' + imageUrl + '")');
            } else {
                $('#token-preview').css('background-image', 'none');
            }
        });

        // Crea un nuovo token draggable
        $('#create-token').click(function() {
            const selectedOption = $('#character-select').find('option:selected');
            const imageUrl = selectedOption.data('image');
            const name = selectedOption.data('name');
            const alias = selectedOption.data('alias');
            const displayName = alias ? `${name} (${alias})` : name;
            const size = parseInt($('#token-size').val()) || 1;

            if (!imageUrl) {
                const alertMessage = globals.translations && globals.translations.select_character_first ? globals.translations.select_character_first : 'Seleziona prima un personaggio';
                alert(alertMessage);
                return;
            }

            createDraggableToken(imageUrl, displayName, size);
        });

        // Aggiunta manuale di token
        $('#add-manual-token').click(function() {
            const name = $('#manual-name').val().trim();
            const iconUrl = $('#manual-icon').val().trim();
            const size = parseInt($('#manual-size').val()) || 1;

            if (!name || !iconUrl) {
                alert('Inserisci sia il nome che l\'URL dell\'icona');
                return;
            }

            createDraggableToken(iconUrl, name, size);

            // Pulisci i campi
            $('#manual-name').val('');
            $('#manual-icon').val('');
        });

        // Cambio sfondo predefinito
        $('#background-select').change(function() {
            const backgroundUrl = $(this).val();
            if (backgroundUrl) {
                changeGridBackground(backgroundUrl);
            }
        });

        // Applica sfondo personalizzato
        $('#apply-background').click(function() {
            const customUrl = $('#custom-background').val().trim();
            if (customUrl) {
                changeGridBackground(customUrl);
                // Pulisci il campo
                $('#custom-background').val('');
            } else {
                alert('Inserisci un URL valido per lo sfondo personalizzato');
            }
        });

        // Remove the changeGridBackground function from here and move it to global scope

        // Funzione per creare token draggable
        function createDraggableToken(imageUrl, displayName, size = 1) {
            const tokenId = 'token-' + Date.now();
            const sizeClass = size > 1 ? ` size-${size}` : '';
            const newToken = $(`
                <div class="draggable-token${sizeClass}" id="${tokenId}"
                     title="${displayName}"
                     data-image-url="${imageUrl}"
                     data-token-size="${size}">
                </div>
            `);

            // Imposta l'immagine di sfondo
            newToken.css('background-image', 'url("' + imageUrl + '")');

            // Adatta la dimensione del token nel container
            if (size > 1) {
                const baseSize = 50;
                newToken.css({
                    'width': (baseSize * size) + 'px',
                    'height': (baseSize * size) + 'px'
                });
            }

            $('#token-container').append(newToken);

            // Rendi il token draggable
            newToken.draggable({
                helper: 'clone',
                cursor: 'move',
                revert: 'invalid',
                start: function(event, ui) {
                    $(ui.helper).css('z-index', 1000);
                    // Assicurati che l'helper abbia lo stesso sfondo
                    $(ui.helper).css('background-image', 'url("' + imageUrl + '")');
                    // Mantieni la dimensione nell'helper
                    if (size > 1) {
                        const baseSize = 50;
                        $(ui.helper).css({
                            'width': (baseSize * size) + 'px',
                            'height': (baseSize * size) + 'px'
                        });
                    }
                }
            });
        }

        // Permetti di rimuovere i token dal container
        $(document).on('click', '.draggable-token', function() {
            const confirmMessage = globals.translations && globals.translations.remove_token ? globals.translations.remove_token : 'Vuoi rimuovere questo token dal selettore?';
            if (confirm(confirmMessage)) {
                $(this).remove();
            }
        });

        // Funzione helper per controllare se le celle sono disponibili per un token di dimensione specifica
        function checkCellsAvailable(startCell, size) {
            const startIndex = parseInt(startCell.attr('id').replace('cell-', ''));
            const gridColumns = 40; // Numero di colonne nella griglia
            const startRow = Math.floor(startIndex / gridColumns);
            const startCol = startIndex % gridColumns;

            // Controlla se il token esce dai bordi della griglia
            if (startCol + size > gridColumns) {
                return false; // Esce dal bordo destro
            }

            // Controlla se tutte le celle necessarie sono libere
            for (let row = 0; row < size; row++) {
                for (let col = 0; col < size; col++) {
                    const cellIndex = (startRow + row) * gridColumns + (startCol + col);
                    const cell = $('#cell-' + cellIndex);

                    if (!cell.length || cell.children('.token').length > 0) {
                        return false; // Cella non esiste o è occupata
                    }
                }
            }
            return true;
        }

        // Funzione helper per occupare le celle per un token di dimensione specifica
        function occupyCells(startCell, size, tokenElement) {
            const startIndex = parseInt(startCell.attr('id').replace('cell-', ''));
            const gridColumns = 40;
            const startRow = Math.floor(startIndex / gridColumns);
            const startCol = startIndex % gridColumns;

            // Occupa tutte le celle necessarie
            for (let row = 0; row < size; row++) {
                for (let col = 0; col < size; col++) {
                    const cellIndex = (startRow + row) * gridColumns + (startCol + col);
                    const cell = $('#cell-' + cellIndex);

                    if (row === 0 && col === 0) {
                        // Prima cella: contiene il token vero e proprio
                        cell.append(tokenElement);
                    } else {
                        // Altre celle: aggiungi un marcatore invisibile per indicare che sono occupate
                        cell.append('<div class="token-placeholder" data-main-token="' + tokenElement.attr('id') + '"></div>');
                    }
                }
            }
        }

        // Funzione helper per liberare le celle quando un token viene rimosso
        function freeCells(tokenElement) {
            const tokenId = tokenElement.attr('id');
            // Rimuovi tutti i placeholder che fanno riferimento a questo token
            $('.token-placeholder[data-main-token="' + tokenId + '"]').remove();
        }
    });

    // Funzione per caricare i dati salvati della griglia
    function caricaDatiSalvati() {
        // Verifica se ci sono dati salvati
        if (!window.savedGridData) {
            console.log('Nessun dato salvato trovato');
            return;
        }

        console.log('Caricamento dati salvati:', window.savedGridData);

        try {
            // Se i dati sono una stringa JSON, li convertiamo in oggetto
            if (typeof window.savedGridData === 'string') {
                window.savedGridData = JSON.parse(window.savedGridData);
            }

            // Carica lo sfondo se presente
            if (window.savedGridData.background && window.savedGridData.background.backgroundUrl) {
                console.log('Caricamento sfondo:', window.savedGridData.background.backgroundUrl);
                changeGridBackground(window.savedGridData.background.backgroundUrl);
            }

            // Carica i token se presenti
            if (window.savedGridData.tokens && window.savedGridData.tokens.length > 0) {
                console.log('Caricamento token:', window.savedGridData.tokens.length);

                // Attendiamo un po' per assicurarci che la griglia sia pronta
                setTimeout(function() {
                    // Itera su tutti i token salvati
                    window.savedGridData.tokens.forEach(function(tokenData) {
                        // Trova la cella corrispondente
                        const cell = $('#' + tokenData.cellId);

                        if (cell.length > 0) {
                            // Ottieni la dimensione del token (default 1 se non specificata)
                            const tokenSize = tokenData.tokenSize || 1;
                            const tokenId = 'loaded-token-' + Date.now() + '-' + Math.random();
                            const sizeClass = tokenSize > 1 ? ` size-${tokenSize}` : '';

                            // Controlla se le celle necessarie sono disponibili
                            if (!checkCellsAvailable(cell, tokenSize)) {
                                console.warn('Impossibile caricare il token: celle non disponibili');
                                return;
                            }

                            // Crea un nuovo token nella cella
                            const newToken = $(`
                                <div class="token${sizeClass}" id="${tokenId}"
                                     title="${tokenData.tokenTitle}"
                                     data-image-url="${tokenData.imageUrl}"
                                     data-token-size="${tokenSize}">
                                    <div class="remove-token">×</div>
                                </div>
                            `);

                            // Imposta l'immagine di sfondo
                            newToken.css('background-image', 'url("' + tokenData.imageUrl + '")');

                            // Occupa le celle necessarie
                            occupyCells(cell, tokenSize, newToken);

                            // Se l'utente può modificare la griglia, rendi il token draggable
                            if (window.canEditGrid) {
                                newToken.draggable({
                                    cursor: 'move',
                                    revert: 'invalid',
                                    start: function(event, ui) {
                                        $(ui.helper).css('z-index', 1000);
                                        // Assicurati che l'helper abbia lo stesso sfondo
                                        $(ui.helper).css('background-image', 'url("' + tokenData.imageUrl + '")');
                                        // Mantieni la dimensione nell'helper se è maggiore di 1
                                        if (tokenSize > 1) {
                                            const baseSize = 50;
                                            $(ui.helper).css({
                                                'width': (baseSize * tokenSize) + 'px',
                                                'height': (baseSize * tokenSize) + 'px'
                                            });
                                        }
                                    }
                                });

                                // Aggiungi funzionalità per rimuovere il token
                                newToken.find('.remove-token').click(function(e) {
                                    e.stopPropagation();
                                    freeCells(newToken);
                                    newToken.remove();
                                });
                            } else {
                                // Se l'utente non può modificare la griglia, nascondi il pulsante di rimozione
                                newToken.find('.remove-token').hide();
                            }
                        }
                    });
                }, 1000); // Attendiamo 1 secondo per assicurarci che la griglia sia pronta
            }
        } catch (error) {
            console.error('Errore nel caricamento dei dati salvati:', error);
        }
    }

    function caricaGriglia(container) {
        localLoading(container, true);

        // Crea la griglia fissa 20x20
        createFixedGrid(container);

        setTimeout(function() {
            // Carica i dati salvati dopo aver creato la griglia
            caricaDatiSalvati();
            localLoading(container, false);
        },300);

        // Dopo aver creato la griglia, rendi le celle droppable solo se l'utente ha i permessi
        setTimeout(function() {
            // Rendi le celle droppable solo se l'utente può modificare la griglia
            if (window.canEditGrid) {
                $('.cell_grid').droppable({
                    accept: '.draggable-token, .token',
                    hoverClass: 'droppable-hover',
                    drop: function(event, ui) {
                        const cell = $(this);
                        const token = ui.draggable;
                        const isFromContainer = token.hasClass('draggable-token');

                        // Ottieni la dimensione del token
                        let tokenSize = 1;
                        if (token.data('token-size')) {
                            tokenSize = parseInt(token.data('token-size'));
                        }

                        // Controlla se le celle necessarie sono disponibili
                        if (!checkCellsAvailable(cell, tokenSize)) {
                            return; // Non è possibile posizionare il token qui
                        }

                        // Ottieni l'URL dell'immagine direttamente dall'attributo data o dallo stile
                        let imageUrl;
                        if (token.data('image-url')) {
                            imageUrl = token.data('image-url');
                        } else {
                            // Estrai l'URL dall'attributo style background-image
                            const bgImage = token.css('background-image');
                            imageUrl = bgImage.replace(/^url\(['"](.+)['"]\)$/, '$1');
                        }

                        const tokenTitle = token.attr('title');
                        const tokenId = 'grid-token-' + Date.now();
                        const sizeClass = tokenSize > 1 ? ` size-${tokenSize}` : '';

                        // Crea un nuovo token nella cella
                        const newToken = $(`
                            <div class="token${sizeClass}" id="${tokenId}"
                                 title="${tokenTitle}"
                                 data-image-url="${imageUrl}"
                                 data-token-size="${tokenSize}">
                                <div class="remove-token">×</div>
                            </div>
                        `);

                        // Imposta l'immagine di sfondo con virgolette doppie per gestire correttamente gli URL
                        newToken.css('background-image', 'url("' + imageUrl + '")');

                        // Se il token proviene da un'altra cella, libera le celle precedenti
                        if (!isFromContainer && token.hasClass('token')) {
                            freeCells(token);
                        }

                        // Occupa le celle necessarie
                        occupyCells(cell, tokenSize, newToken);

                        // Se il token proviene da un'altra cella, rimuovilo
                        if (!isFromContainer) {
                            token.remove();
                        }

                        // Rendi il nuovo token draggable
                        newToken.draggable({
                            cursor: 'move',
                            revert: 'invalid',
                            start: function(event, ui) {
                                $(ui.helper).css('z-index', 1000);
                                // Assicurati che l'helper abbia lo stesso sfondo
                                $(ui.helper).css('background-image', 'url("' + imageUrl + '")');
                                // Mantieni la dimensione nell'helper se è maggiore di 1
                                if (tokenSize > 1) {
                                    const baseSize = 50;
                                    $(ui.helper).css({
                                        'width': (baseSize * tokenSize) + 'px',
                                        'height': (baseSize * tokenSize) + 'px'
                                    });
                                }
                            }
                        });

                        // Aggiungi funzionalità per rimuovere il token
                        newToken.find('.remove-token').click(function(e) {
                            e.stopPropagation();
                            freeCells(newToken);
                            newToken.remove();
                        });
                    }
                });

                // Rendi i token esistenti draggable solo se l'utente può modificare la griglia
                $('.token').draggable({
                    cursor: 'move',
                    revert: 'invalid',
                    start: function(event, ui) {
                        $(ui.helper).css('z-index', 1000);

                        // Ottieni l'URL dell'immagine
                        let imageUrl;
                        if ($(this).data('image-url')) {
                            imageUrl = $(this).data('image-url');
                        } else {
                            const bgImage = $(this).css('background-image');
                            imageUrl = bgImage.replace(/^url\(['"](.+)['"]\)$/, '$1');
                        }

                        // Ottieni la dimensione del token
                        const tokenSize = $(this).data('token-size') || 1;

                        // Assicurati che l'helper abbia lo stesso sfondo con virgolette doppie
                        $(ui.helper).css('background-image', 'url("' + imageUrl + '")');

                        // Mantieni la dimensione nell'helper se è maggiore di 1
                        if (tokenSize > 1) {
                            const baseSize = 50;
                            $(ui.helper).css({
                                'width': (baseSize * tokenSize) + 'px',
                                'height': (baseSize * tokenSize) + 'px'
                            });
                        }
                    }
                });
            }
        }, 700); // Piccolo ritardo per assicurarsi che la griglia sia completamente caricata
    }

    // Funzione per salvare le posizioni della griglia
    function salvaPosizioniGriglia() {
        // Raccoglie i dati delle posizioni dei token nella griglia
        const gridData = [];

        // Itera su tutte le celle della griglia
        $('.cell_grid').each(function(index) {
            const cell = $(this);
            const cellId = cell.attr('id');

            // Se la cella contiene un token, salva le informazioni
            if (cell.children('.token').length > 0) {
                const token = cell.children('.token');
                const tokenData = {
                    cellId: cellId,
                    tokenTitle: token.attr('title'),
                    imageUrl: token.data('image-url'),
                    tokenSize: token.data('token-size') || 1
                };

                gridData.push(tokenData);
            }
        });

        // Ottieni anche le informazioni sullo sfondo corrente
        let backgroundUrl = "";

        // Controlla se c'è un'immagine di sfondo
        if ($(".grid-background img").length > 0) {
            backgroundUrl = $(".grid-background img").attr("src");
        } else if ($("#background-select").val()) {
            // Usa il valore del selettore di sfondo se disponibile
            backgroundUrl = $("#background-select").val();
        }

        const backgroundData = {
            backgroundUrl: backgroundUrl
        };

        // Prepara i dati da inviare
        const saveData = {
            tokens: gridData,
            background: backgroundData
        };

        // Mostra un indicatore di caricamento
        const btn = $('#save-grid-btn');
        const originalText = btn.html();
        const savingText = globals.translations && globals.translations.saving ? globals.translations.saving : 'Salvataggio...';
        btn.html('<i class="fa fa-spinner fa-spin"></i> ' + savingText);
        btn.prop('disabled', true);

        // Invia i dati al server tramite AJAX
        $.ajax({
            url: globals.rootdir + '/modules/gdr_chat/actions.php',
            type: 'POST',
            dataType: 'json',
            data: {
                id_module: '<?php echo isset($id_module) ? $id_module : 0; ?>',
                id_record: '<?php echo isset($id_record) ? $id_record : 0; ?>',
                id_quest: '<?php echo isset($id_quest) ? $id_quest : 0; ?>',
                id_chat: '<?php echo isset($id_chat) ? $id_chat : 0; ?>',
                op: 'save_grid_positions',
                grid_data: JSON.stringify(saveData)
            },
            success: function(response) {
                // Ripristina il pulsante
                btn.html(originalText);
                btn.prop('disabled', false);

                // Mostra un messaggio di successo
                if (response.result) {
                    // Usa SweetAlert se disponibile, altrimenti un alert normale
                    if (typeof swal !== 'undefined') {
                        const successText = globals.translations && globals.translations.success ? globals.translations.success : 'Successo';
                        swal(successText, response.message, 'success');
                    } else {
                        alert(response.message);
                    }
                } else {
                    // Mostra un messaggio di errore
                    if (typeof swal !== 'undefined') {
                        const errorText = globals.translations && globals.translations.error ? globals.translations.error : 'Errore';
                        swal(errorText, response.message, 'error');
                    } else {
                        alert(response.message);
                    }
                }
            },
            error: function() {
                // Ripristina il pulsante
                btn.html(originalText);
                btn.prop('disabled', false);

                // Mostra un messaggio di errore
                if (typeof swal !== 'undefined') {
                    const errorText2 = globals.translations && globals.translations.error ? globals.translations.error : 'Errore';
                    const connectionErrorText1 = globals.translations && globals.translations.connection_error ? globals.translations.connection_error : 'Errore di connessione';
                    swal(errorText2, connectionErrorText1, 'error');
                } else {
                    const connectionErrorText2 = globals.translations && globals.translations.connection_error ? globals.translations.connection_error : 'Errore di connessione';
                    alert(connectionErrorText2);
                }
            }
        });
    }

    // Funzione per creare una griglia fissa 40x20
    function createFixedGrid(container) {
        // Svuota il container
        container.empty();

        // Aggiungi un div per l'immagine di sfondo
        const bgContainer = $('<div class="grid-background"></div>');
        container.append(bgContainer);

        // Crea un nuovo div per la griglia
        const grid = $('<div class="grid"></div>');

        // Default dimensions (will be updated when background is set)
        const rows = 20;
        const columns = 40;

        console.log("Creating initial grid:", columns, "columns x", rows, "rows");

        // Create the grid cells
        for (let i = 0; i < rows * columns; i++) {
            const cell = $(`<div class="cell_grid" id="cell-${i}"></div>`);
            grid.append(cell);
        }

        // Aggiungi la griglia al container
        container.append(grid);

        // Set the background of the grid
        const backgroundUrl = $('#background-select').val();
        if (backgroundUrl) {
            // Use the improved background setting function
            changeGridBackground(backgroundUrl);
        }
    }
    // Fine dello script
</script>
