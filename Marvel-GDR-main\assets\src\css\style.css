/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

*:focus {
    outline: none;
}

@font-face {
    font-family: 'Source Sans Pro';
    src: local('Source Sans Pro'),
        url('../fonts/sourcesanspro-regular-webfont.eot') format('embedded-opentype'),
        url('../fonts/sourcesanspro-regular-webfont.woff') format('woff'),
        url('../fonts/sourcesanspro-regular-webfont.woff2') format('woff2'),
        url('../fonts/sourcesanspro-regular-webfont.ttf') format('truetype'),
        url('../fonts/sourcesanspro-regular-webfont.svg') format('svg');
}

.li-widget > a {
    color: #333;
}

.li-widget > a:hover {
    color: #000;
}

.preloader{
    background-color: #f4f6f9de ;
}

html {
    font-size: 13px;
    height: 100%;
}

body {
    font-family: sans-serif, "Source Sans Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: left;
}

.main-header .navbar{
    background: #333;
}

textarea {
    resize: vertical;
}

a.disabled {
    pointer-events: none;
    cursor: default;
    opacity: 0.6;
}

#progress {
    margin: auto;
    position: relative;
    display: none;
}

#progress .progress-bar {
    background-image: url(../img/progress.gif);
}

#progress .progress-bar span {
    position: absolute;
    display: inline-block;
    width: 100%;
    text-align: center;
}

input[type=file] {
    height: initial;
    margin-bottom: 5px;
}

#datetime {
    font-size: 11px;
    font-weight: normal;
    color: #aaa;
    margin: 8px 0 0 0;
}

.ui-menu {
    position: fixed;
}

.autocomplete,
.ui-autocomplete {
    background: white;
    min-width: 160px;
    padding: 10px;
    margin: 2px;
    list-style: none;
    border-style: solid;
    border-width: 1px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    z-index: 9990;
}

.autocomplete .group,
.ui-autocomplete-category {
    font-size: 1.5em;
    background: inherit;
}

.autocomplete>div {
    padding: 5px;
}

.highlight {
    background: #FFFF66;
}

.autocomplete>div:hover:not(.group),
.autocomplete>div.selected {
    background: #F5F5F5;
    cursor: pointer;
}

.ui-autocomplete-scrollable {
    max-height: 80vh;
    width: 600px;
    overflow-y: auto;
    overflow-x: hidden;
}

.ui-autocomplete a {
    color: inherit;
}

.square {
    width: 18px;
    height: 18px;
    margin: auto;
}

.content-wrapper {
    padding-top: 10px;
}

.content {
    padding: 0 20px !important;
}

.navbar a:hover,
.brand-link:hover,
.alert a,
.alert a:hover,
a:hover,
a:focus {
    text-decoration: none;
}

.no-padding {
    padding: 0;
}

.no-padding-left {
    padding-left: 0px;
}

.box-center {
    width: 600px;
    margin: 7% auto
}

.box-center-large {
    width: 850px;
    margin: 7% auto
}

.card-center-large {
    width: 1000px;
    margin: 7% auto
}

.card-center-medium {
    margin: 7% auto
}

.box-center .box-body,
.box-center-large .box-body {
    padding: 20px;
    border-top: 0
}

.li-widget a:hover {
    text-decoration: none;
}

.clickable {
    cursor: pointer;
}


#mini-loader {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9000;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.15);
}

#mini-loader>div {
    position: fixed;
    bottom: 20px;
    left: auto;
    right: 20px;
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -50px;
    background: transparent url(../img/ajax-loader.gif) top left no-repeat;
}

#tiny-loader {
    position: fixed;
    z-index: 9000;
    width: 100%;
    height: 100%;
    top: 50%;
    left: 50%;
    background: transparent url(../img/ajax-loader.gif) top left no-repeat;
}

body .header .brand-link {
    font-family: inherit;
}

.btn-github {
    color: #ffffff;
    background-color: #444444;
    border-color: rgba(0, 0, 0, 0.2);
}

.navbar-custom-menu>ul>li>a:hover,
.navbar-custom-menu>ul>li>a:focus,
.navbar-custom-menu>ul>li>a:active {
    filter: brightness(75%);
}

.btn-github:active,
.btn-github.active,
.open .dropdown-toggle.btn-github {
    background-image: none;
}

.btn-github.disabled,
.btn-github[disabled],
fieldset[disabled] .btn-github,
.btn-github.disabled:hover,
.btn-github[disabled]:hover,
fieldset[disabled] .btn-github:hover,
.btn-github.disabled:focus,
.btn-github[disabled]:focus,
fieldset[disabled] .btn-github:focus,
.btn-github.disabled:active,
.btn-github[disabled]:active,
fieldset[disabled] .btn-github:active,
.btn-github.disabled.active,
.btn-github[disabled].active,
fieldset[disabled] .btn-github.active {
    background-color: #444444;
    border-color: rgba(0, 0, 0, 0.2);
}

.btn-github .badge {
    color: #444444;
    background-color: #ffffff;
}

span.form-control {
    background: transparent;
    border: none;
}

.form-inline {
    display: inline ;
}

.panel-heading.mini {
    padding: 5px 8px;
}

.dropdown.col-md-3>.dropdown-menu {
    width: 91.5%;
    left: 15px;
    padding: 5px;
}

.input-group-addon {
    padding: 3px 12px;
}

.colorpicker {
    margin-top: 0;
}

#pulsanti {
    position: sticky;
    top: 0;
    padding: 6px;
    z-index: 999;
    background: #f6f6f6;
}



/* Tooltip dark */

.ui-tooltip {
    background: #222;
    color: white;
    border: 0;
    font-size: 11px;
}

.nav-tabs-custom>.nav-tabs.pull-right>li>a.back-btn {
    font-size: 12px;
    color: #FFFFFF;
}

.navbar-nav>.notifications-menu>.dropdown-menu>li .menu>li>a,
.navbar-nav>.messages-menu>.dropdown-menu>li .menu>li>a,
.navbar-nav>.tasks-menu>.dropdown-menu>li .menu>li>a {
    white-space: normal;
}

.nav-tabs-custom>.nav-tabs.pull-right>li>a.back-btn:hover {
    cursor: pointer;
    color: #72AFD2;
}

.input-searching {
    background: #FFBF91;
}

.deleteicon:hover {
    color: black;
}

.deleteicon {
    color: gray;
    width: 15px;
    height: 15px;
    position: absolute;
    bottom: 19px;
    right: 29px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.input-group-addon {
    min-width: 40px;
}

.info-box-icon i {
    color: #fff;
}
.progress-description {
    white-space: normal;
    overflow: auto;
    font-size: 1rem;
}

.colorpicker {
    z-index: 1 ;
}

.signature-pad {
    width: 100%;
    height: 100%;
    font-size: 10px;
    border: 1px solid #e8e8e8;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.27), 0 0 40px rgba(0, 0, 0, 0.08) inset;
    border-radius: 4px;
}

.signature-pad:before,
.signature-pad:after {
    content: "";
    width: 40%;
    height: 10px;
    left: 20px;
    bottom: 10px;
    background: transparent;
    -webkit-transform: skew(-3deg) rotate(-3deg);
    -moz-transform: skew(-3deg) rotate(-3deg);
    -ms-transform: skew(-3deg) rotate(-3deg);
    -o-transform: skew(-3deg) rotate(-3deg);
    transform: skew(-3deg) rotate(-3deg);
    box-shadow: 0 8px 12px rgba(0, 0, 0, 0.4);
}

.signature-pad:after {
    left: auto;
    right: 20px;
    -webkit-transform: skew(3deg) rotate(3deg);
    -moz-transform: skew(3deg) rotate(3deg);
    -ms-transform: skew(3deg) rotate(3deg);
    -o-transform: skew(3deg) rotate(3deg);
    transform: skew(3deg) rotate(3deg);
}

#canvas {
    width: 100%;
    height: 100%;
    min-height: 202px;
    border-radius: 4px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.02) inset;
}

.sidebar-form {
    border-radius: 3px;
    margin: 10px 10px;
}

.sidebar-form input[type="text"],
.sidebar-form .btn {
    border: 1px;
    height: 35px;
    -webkit-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

.sidebar-form input[type="text"] {
    border-top-left-radius: 2px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 2px;
}

.sidebar-form .btn {
    border-top-left-radius: 0;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 0;
}

.swal2-buttonswrapper .btn {
    margin: 5px;
}


/* Personalizzazione del plugin Select2 */

.select2-search,
.select2-search__field {
    width: 100%;
}

.select2-results__option[aria-selected=true] {
    display: none
}

.input-group-addon.no-padding {
    border: 0px;
}

.input-group-addon.no-padding>* {
    border-radius: 0px;
}

div.DTS tbody td,
div.DTS tbody th {
    max-width: 500px;
}

input.min-width {
    min-width: 200px;
}

input.small-width {
    min-width: 100px;
}

.pac-container {
    z-index: 1100;
}

.form-control#blob {
    height: auto;
}

.callout a {
    text-decoration: none;
}

.dropdown-menu {
    min-width: 130px;
}

.img-circle.square {
    box-shadow: 0px 0px 1px 1px lightgray;
}

.select2-dropdown {
    z-index: 2000;
}

.input-group-text > .btn{
    line-height: 1px;
}

.hide-it-off-screen {
    position: absolute;
    left: -10000px;
    top: -10000px;
}

.box-info ul>li> :last-child {
    margin-bottom: 5px;
}

.row {
    overflow-wrap: break-word;
    word-wrap: break-word;
    -ms-word-break: break-all;
    word-break: unset;
    -ms-hyphens: auto;
    -moz-hyphens: auto;
    -webkit-hyphens: auto;
    hyphens: auto;
}

.request .morelink {
    color: #fff;
    font-weight: bold;
}

.component-loader {
    position: absolute;
    z-index: 20;
    opacity: 0.5;
    width: 100%;
    height: 100%;
    background: #fff;
}

.component-loader>div {
    position: relative;
    top: 50%;
    color: #333;
}

.btn-group-flex {
    display: flex;
}

.btn-group-flex .btn {
    flex: 1
}

/* Interventi da pianificare in Dashboard */

#external-events .fc-event {
    margin-top: 10px;
    padding: 10px;
    cursor: pointer;
}

.fc-event.fc-h-event,
.fc-event.fc-v-event {
    border-top-width: 10px;
    border-style: solid;
}

.fc-daygrid-dot-event {
    padding: 0;
}

.fc-event-primary {
    background-color: #3a87ad;
    border: 1px solid #3a87ad;
    color:white;
}

.fc-event-danger {
    background-color: #dd4b46;
    border: 1px solid #dd4b46;
    color: white;
}

.fc-event .fc-event-main {
    overflow: hidden;
}

/* Fix per tabs editor */

.nav-tabs-custom>.nav-tabs>li {
    border-top: 3px solid #ddd;
}

.nav-tabs-custom>.nav-tabs.pull-right>li:first-of-type.active>a {
    border-right-color: #f4f4f4;
    border-right-width: 1px;
    border-left-width: 0px;
}

.nav-tabs-custom>.nav-tabs>li.header {
    padding: 0px;
}

.nav-tabs-custom>.nav-tabs>li {
    margin-bottom: -1px;
    margin-right: 0px;
}

.ui-autocomplete {
    background: #f6f6f6;
    border-color: #ccc;
    border-color: rgba(0, 0, 0, 0.2);
    -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
}

.ui-menu .ui-menu-item-wrapper {
    padding: 3px 1em 3px .4em;
    cursor: pointer;
}

.ui-menu .ui-menu-item-wrapper:hover {
    border: 1px solid #003eff;
    background: #007fff;
    font-weight: 400;
    color: #fff;
}

.parsley-errors-list {
    color: red;
}


/** TIMELINE */

.timeline {
    list-style: none;
    padding: 20px 0 20px;
    position: relative;
}

.timeline:before {
    top: 0;
    bottom: 0;
    position: absolute;
    content: " ";
    width: 3px;
    background-color: #eeeeee;
    left: 50%;
    margin-left: -4.5px;
}

.timeline>li {
    margin-bottom: 20px;
    position: relative;
}

.timeline>li:before,
.timeline>li:after {
    content: " ";
    display: table;
}

.timeline>li:after {
    clear: both;
}

.timeline>li:before,
.timeline>li:after {
    content: " ";
    display: table;
}

.timeline>li:after {
    clear: both;
}

.timeline>li>.timeline-panel {
    width: 50%;
    float: left;
    border: 1px solid #d4d4d4;
    border-radius: 2px;
    padding: 20px;
    position: relative;
    -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, 0.175);
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.175);
}

.timeline>li.timeline-inverted+li:not(.timeline-inverted),
.timeline>li:not(.timeline-inverted)+li.timeline-inverted {
    margin-top: -60px;
}

.timeline>li:not(.timeline-inverted) {
    padding-right: 90px;
}

.timeline>li.timeline-inverted {
    padding-left: 90px;
}

.timeline>li>.timeline-panel:before {
    position: absolute;
    top: 26px;
    right: -15px;
    display: inline-block;
    border-top: 15px solid transparent;
    border-left: 15px solid #ccc;
    border-right: 0 solid #ccc;
    border-bottom: 15px solid transparent;
    content: " ";
}

.timeline>li>.timeline-panel:after {
    position: absolute;
    top: 27px;
    right: -14px;
    display: inline-block;
    border-top: 14px solid transparent;
    border-left: 14px solid #fff;
    border-right: 0 solid #fff;
    border-bottom: 14px solid transparent;
    content: " ";
}

.timeline>li>.timeline-badge {
    color: #fff;
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 1.4em;
    text-align: center;
    position: absolute;
    top: 16px;
    left: 50%;
    margin-left: -25px;
    background-color: #999999;
    z-index: 100;
    border-top-right-radius: 50%;
    border-top-left-radius: 50%;
    border-bottom-right-radius: 50%;
    border-bottom-left-radius: 50%;
}

.timeline>li.timeline-inverted>.timeline-panel {
    float: right;
}

.timeline>li.timeline-inverted>.timeline-panel:before {
    border-left-width: 0;
    border-right-width: 15px;
    left: -15px;
    right: auto;
}

.timeline>li.timeline-inverted>.timeline-panel:after {
    border-left-width: 0;
    border-right-width: 14px;
    left: -14px;
    right: auto;
}

.timeline-title {
    margin-top: 0;
    color: inherit;
}

.timeline-body>p,
.timeline-body>ul {
    margin-bottom: 0;
}

.timeline-body>p+p {
    margin-top: 5px;
}


/* Checklist */

.todo-list ul {
    list-style: none;
    margin-left: 11px;
}

.progress .progress-bar span {
    color: #000;
}

.attachment-img {
    max-width: 20px;
    max-height: 20px;
}

.has-feedback .form-control {
    padding-right: 6px;
}

.direct-chat-text img {
    max-width: 100%;
}


/* Dropzone */

.dropzone {
    border: 2px dashed #337ab7;
    border-radius: 5px;
    background: white;
    margin-top: 10px;
}

.dropzone .dz-message {
    font-weight: 400;
}

.dropzone .dz-message .note {
    font-size: 0.8em;
    font-weight: 200;
    display: block;
    margin-top: 1.4rem;
}


/** Fix tooltip per elementi disabilitati. */

div.tip {
    display: inline-block;
}

.disabled {
    pointer-events: none
}


/* Fancy checkbox: https://bootsnipp.com/snippets/M2bda */

.checkbox-group input[type="checkbox"] {
    display: none;
}

.checkbox-group input[type="checkbox"]+.btn-group {
    width: 100%;
    display: flex;
    overflow: auto;
}

.checkbox-group input[type="checkbox"]+.btn-group label:last-child {
    flex-grow: 100;
}

.checkbox-group input[type="checkbox"]+.btn-group>label span:first-child {
    display: none;
}

.checkbox-group input[type="checkbox"]+.btn-group>label span:last-child {
    display: inline-block;
}

.checkbox-group input[type="checkbox"]:checked+.btn-group>label span:first-child {
    display: inline-block;
}

.checkbox-group input[type="checkbox"]:checked+.btn-group>label span:last-child {
    display: none;
}


/* Sidebar destra per i plugin */

.control-sidebar{
    top: 0;
    padding-top: 50px;
}

.control-sidebar > h4 {
    font-size: 12px;
    padding-left: 14px;
    text-transform: uppercase;
    color: #888;
}


.control-sidebar > ul {
    display: block;
}

.number-input {
    text-align: right;
}

/* Loading specificato per un div */
.local-loader {
    position: relative;
    width: 100%;
    height: 0
}

.local-loader>div {
    position: absolute;
    top: 0px;
    z-index: 2;
    opacity: 0.5;
    width: 100%
}

.div-loading {
    opacity: 0.5;
}

.login-box .img-responsive {
    padding: 18px 0px 4px;
}

.no-selection {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.table-extra-condensed>thead>tr>th,
.table-extra-condensed>tbody>tr>th,
.table-extra-condensed>tfoot>tr>th,
.table-extra-condensed>thead>tr>td,
.table-extra-condensed>tbody>tr>td,
.table-extra-condensed>tfoot>tr>td {
    padding: 2px;
}

.table {
    padding: .5 rem !important;
}

table.dataTable {
    border-collapse: collapse !important;
}

.alert-warning {
    background-color: #EC971F;
}

.row-list .form-group {
    margin-bottom: 0px;
}

/*
* Colore help icon
*/
.tip.tooltipstered>.fa-question-circle-o {
    color: dodgerblue;
}

.tooltipster-box ul{
    list-style: none;
}

/*
* Colore testo btn-box-tool
*/
.btn-box-tool {
    color: #2e2e2e;
    padding-left: 100px;
}

.btn-box-tool:hover {
    color: #000000;
}

.select2-container--bootstrap4{
    display: block;
    flex: 1 1 auto;
    width: 85% !important;
}

.card-lg{
    width: 100%;
}

.select2-container--bootstrap .select2-selection--multiple .select2-search--inline .select2-search__field {
    height: 23px;
    padding: 3px 12px;
    margin-top: 4px;
}

.select2-container--bootstrap4 .select2-selection__clear {
    padding-left: 0.21em;
    padding-top: 0.05em;
}

.btn-group>.btn-xs>input[type="checkbox"], .label>input[type="checkbox"] {
    margin: 2px 0px;
}

/*
* Colore eventi fullcalendar
*/
.fc .fc-bg-event {
    background: rgba(255, 49, 21, .5);
}

kbd{
    background: #ccc;
    color: #333;
    font-size: 1.3em;
    padding: 6px 8px;
    display: inline-block;
    margin: 1px;
}

.read-more-target {
    display: none;
}
  
.read-more-trigger {
    cursor: pointer;
}
    
.read-more-target:target {
    display: block;
}

.fc-day-today {
    background:#fafad2;
}

.notifica-prezzi {
    position: absolute;
    color: #ff8300;
    z-index: 10;
}

.login-box .form-control-feedback{
    pointer-events: all;
}

.login-page{
    background: #ccc;
    background: radial-gradient(circle, rgba(255,255,255,1) 0%, rgba(229,229,229,1) 34%, rgba(204,204,204,1) 100%);
    display: block;
}

.icon{
    width: 15px;
    height: 15px;
    line-height: 30px;
    border-radius: 15px;
    margin-top: 3px;
    box-shadow: 0px 0px 1px black;
}

/* Sidebar green */
.main-sidebar.bg-green .nav-link {
    color: #fff;
}

.main-sidebar.bg-green .nav-link.active {
    color: #555;
    background-color: rgba(255,255,255,.9);
}

.main-sidebar.bg-green .nav-link:not(.active):hover {
    color: #f6f6f6;
    background-color: rgba(255,255,255,.1);
}

.main-sidebar.bg-green .nav-item.menu-open > .nav-link.active {
    background-color: rgba(255,255,255,.7);
}

.main-sidebar.bg-green .brand-link {
    background-color: #fff;
}

.main-sidebar.bg-green .user-panel .info > a {
    color: #fff;
}

.main-sidebar.bg-green .sidebar .btn-sidebar {
    background: #d7d7d7;
}

/* Sidebar green-light */
.main-sidebar.bg-green-light .nav-link {
    color: #555;
}

.main-sidebar.bg-green-light .nav-link.active {
    color: #fff;
    background-color: #28a745;
}

.main-sidebar.bg-green-light .nav-link:not(.active):hover {
    color: #555;
    background-color: rgba(40, 167, 69, 0.2);
}

.main-sidebar.bg-green-light .nav-item.menu-open > .nav-link.active {
    background-color: rgba(40, 167, 69, 0.7);
}

.main-sidebar.bg-green-light .brand-link {
    background-color: #fff;
}

.main-sidebar.bg-green-light .user-panel .info > a {
    color: #555;
}

.main-sidebar.bg-green-light .user-panel .image > i {
    color: #555;
}

.main-sidebar.bg-green-light .sidebar .btn-sidebar {
    background: #d7d7d7;
}

/* Sidebar blue */
.main-sidebar.bg-blue .nav-link {
    color: #fff;
}

.main-sidebar.bg-blue .nav-link.active {
    color: #555;
    background-color: rgba(255,255,255,.9);
}

.main-sidebar.bg-blue .nav-link:not(.active):hover {
    color: #f6f6f6;
    background-color: rgba(255,255,255,.1);
}

.main-sidebar.bg-blue .nav-item.menu-open > .nav-link.active {
    background-color: rgba(255,255,255,.7);
}

.main-sidebar.bg-blue .brand-link {
    background-color: #fff;
}

.main-sidebar.bg-blue .user-panel .info > a {
    color: #fff;
}

.main-sidebar.bg-blue .sidebar .btn-sidebar {
    background: #d7d7d7;
}

/* Sidebar blue-light */
.main-sidebar.bg-blue-light .nav-link {
    color: #555;
}

.main-sidebar.bg-blue-light .nav-link.active {
    color: #fff;
    background-color: #007bff;
}

.main-sidebar.bg-blue-light .nav-link:not(.active):hover {
    color: #555;
    background-color: rgba(0, 123, 255, 0.2);
}

.main-sidebar.bg-blue-light .nav-item.menu-open > .nav-link.active {
    background-color: rgba(0, 123, 255, 0.7);
}

.main-sidebar.bg-blue-light .brand-link {
    background-color: #fff;
}

.main-sidebar.bg-blue-light .user-panel .info > a {
    color: #555;
}

.main-sidebar.bg-blue-light .user-panel .image > i {
    color: #555;
}

.main-sidebar.bg-blue-light .sidebar .btn-sidebar {
    background: #d7d7d7;
}

/* Sidebar black */
.main-sidebar.bg-black .nav-link {
    color: #fff;
}

.main-sidebar.bg-black .nav-link.active {
    color: #555;
    background-color: rgba(255,255,255,.9);
}

.main-sidebar.bg-black .nav-link:not(.active):hover {
    color: #f6f6f6;
    background-color: rgba(255,255,255,.1);
}

.main-sidebar.bg-black .nav-item.menu-open > .nav-link.active {
    background-color: rgba(255,255,255,.7);
}

.main-sidebar.bg-black .brand-link {
    background-color: #fff;
}

.main-sidebar.bg-black .user-panel .info > a {
    color: #fff;
}

.main-sidebar.bg-black .sidebar .btn-sidebar {
    background: #d7d7d7;
}

/* Sidebar white */
.main-sidebar.bg-black-light .nav-link {
    color: #555;
}

.main-sidebar.bg-black-light .nav-link.active {
    color: #333;
    background-color: rgba(28,28,28,.2);
}

.main-sidebar.bg-black-light .nav-link:not(.active):hover {
    color: #f6f6f6;
    background-color: rgba(28, 28, 28, 0.9);
}

.main-sidebar.bg-black-light .nav-item.menu-open > .nav-link.active {
    color: #f6f6f6;
    background-color: rgba(28, 28, 28, .7);
}

.main-sidebar.bg-black-light .brand-link {

    background-color: rgba(255, 255, 255, .9);
}

.main-sidebar.bg-black-light .user-panel .info > a {
    color:  #333;
}

.main-sidebar.bg-black-light .sidebar .btn-sidebar {
    background: #d7d7d7;
}

/* Sidebar red */
.main-sidebar.bg-red .nav-link {
    color: #fff;
}

.main-sidebar.bg-red .nav-link.active {
    color: #555;
    background-color: rgba(255,255,255,.9);
}

.main-sidebar.bg-red .nav-link:not(.active):hover {
    color: #f6f6f6;
    background-color: rgba(255,255,255,.1);
}

.main-sidebar.bg-red .nav-item.menu-open > .nav-link.active {
    background-color: rgba(255,255,255,.7);
}

.main-sidebar.bg-red .brand-link {
    background-color: #fff;
}

.main-sidebar.bg-red .user-panel .info > a {
    color: #fff;
}

.main-sidebar.bg-red .sidebar .btn-sidebar {
    background: #d7d7d7;
}

/* Sidebar red-light */
.main-sidebar.bg-red-light .nav-link {
    color: #555;
}

.main-sidebar.bg-red-light .nav-link.active {
    color: #fff;
    background-color: #dc3545;
}

.main-sidebar.bg-red-light .nav-link:not(.active):hover {
    color: #555;
    background-color: rgba(220, 53, 69, 0.2);
}

.main-sidebar.bg-red-light .nav-item.menu-open > .nav-link.active {
    background-color: rgba(220, 53, 69, 0.7);
}

.main-sidebar.bg-red-light .brand-link {
    background-color: #fff;
}

.main-sidebar.bg-red-light .user-panel .info > a {
    color: #555;
}

.main-sidebar.bg-red-light .user-panel .image > i {
    color: #555;
}

.main-sidebar.bg-red-light .sidebar .btn-sidebar {
    background: #d7d7d7;
}

/* Sidebar yellow */
.main-sidebar.bg-yellow .nav-link {
    color: #333;
}

.main-sidebar.bg-yellow .nav-link.active {
    color: #555;
    background-color: rgba(255,255,255,.9);
}

.main-sidebar.bg-yellow .nav-link:not(.active):hover {
    color:  rgba(255,255,255,.9);
    background-color: rgba(255,255,255,.1);
}

.main-sidebar.bg-yellow .nav-item.menu-open > .nav-link.active {
    background-color: rgba(255,255,255,.7);
}

.main-sidebar.bg-yellow .brand-link {
    background-color: #333;
}

.main-sidebar.bg-yellow .user-panel .info > a {
    color: #333;
}

.main-sidebar.bg-yellow .sidebar .btn-sidebar {
    background: #d7d7d7;
}

/* Sidebar yellow-light */
.main-sidebar.bg-yellow-light .nav-link {
    color: #555;
}

.main-sidebar.bg-yellow-light .nav-link.active {
    color: #fff;
    background-color: #ffc107;
}

.main-sidebar.bg-yellow-light .nav-link:not(.active):hover {
    color: #555;
    background-color: rgba(255, 193, 7, 0.2);
}

.main-sidebar.bg-yellow-light .nav-item.menu-open > .nav-link.active {
    background-color: rgba(255, 193, 7, 0.7);
}

.main-sidebar.bg-yellow-light .brand-link {
    background-color: #fff;
}

.main-sidebar.bg-yellow-light .user-panel .info > a {
    color: #555;
}

.main-sidebar.bg-yellow-light .user-panel .image > i {
    color: #555;
}

.main-sidebar.bg-yellow-light .sidebar .btn-sidebar {
    background: #d7d7d7;
}

/* Sidebar purple */
.main-sidebar.bg-purple .nav-link {
    color: #fff;
}

.main-sidebar.bg-purple .nav-link.active {
    color: #555;
    background-color: rgba(255,255,255,.9);
}

.main-sidebar.bg-purple .nav-link:not(.active):hover {
    color: #f6f6f6;
    background-color: rgba(255,255,255,.1);
}

.main-sidebar.bg-purple .nav-item.menu-open > .nav-link.active {
    background-color: rgba(255,255,255,.7);
}

.main-sidebar.bg-purple .brand-link {
    background-color: #fff;
}

.main-sidebar.bg-purple .user-panel .info > a {
    color: #fff;
}

.main-sidebar.bg-purple .sidebar .btn-sidebar {
    background: #d7d7d7;
}

/* Sidebar purple-light */
.main-sidebar.bg-purple-light .nav-link {
    color: #555;
}

.main-sidebar.bg-purple-light .nav-link.active {
    color: #fff;
    background-color: #6f42c1;
}

.main-sidebar.bg-purple-light .nav-link:not(.active):hover {
    color: #555;
    background-color: rgba(111, 66, 193, 0.2);
}

.main-sidebar.bg-purple-light .nav-item.menu-open > .nav-link.active {
    background-color: rgba(111, 66, 193, 0.7);
}

.main-sidebar.bg-purple-light .brand-link {
    background-color: #fff;
}

.main-sidebar.bg-purple-light .user-panel .info > a {
    color: #555;
}

.main-sidebar.bg-purple-light .user-panel .image > i {
    color: #555;
}

.main-sidebar.bg-purple-light .sidebar .btn-sidebar {
    background: #d7d7d7;
}

/* Sidebar info */
.main-sidebar.bg-info .nav-link {
    color: #fff;
}

.main-sidebar.bg-info .nav-link.active {
    color: #555;
    background-color: rgba(255,255,255,.9);
}

.main-sidebar.bg-info .nav-link:not(.active):hover {
    color: #f6f6f6;
    background-color: rgba(255,255,255,.1);
}

.main-sidebar.bg-info .nav-item.menu-open > .nav-link.active {
    background-color: rgba(255,255,255,.7);
}

.main-sidebar.bg-info .brand-link {
    background-color: #fff;
}

.main-sidebar.bg-info .user-panel .info > a {
    color: #fff;
}

.main-sidebar.bg-info .sidebar .btn-sidebar {
    background: #d7d7d7;
}

/* Sidebar info-light */
.main-sidebar.bg-info-light .nav-link {
    color: #555;
}

.main-sidebar.bg-info-light .nav-link.active {
    color: #fff;
    background-color: #17a2b8;
}

.main-sidebar.bg-info-light .nav-link:not(.active):hover {
    color: #555;
    background-color: rgba(23, 162, 184, 0.2);
}

.main-sidebar.bg-info-light .nav-item.menu-open > .nav-link.active {
    background-color: rgba(23, 162, 184, 0.7);
}

.main-sidebar.bg-info-light .brand-link {
    background-color: #fff;
}

.main-sidebar.bg-info-light .user-panel .info > a {
    color: #555;
}

.main-sidebar.bg-info-light .user-panel .image > i {
    color: #555;
}

.main-sidebar.bg-info-light .sidebar .btn-sidebar {
    background: #d7d7d7;
}

.container {
    padding: 20px;
}

.conto3 {
    font-size: 1rem;
}

h5 {
    font-size: 1rem;
}

body:not(.sidebar-collapse) .sidebar .nav-treeview .nav-link {
    margin-left: 15px;
    width: auto;
}

body:not(.sidebar-collapse) .sidebar .nav-treeview .nav-treeview .nav-link {
    margin-left: 30px;
    width: auto;
}

.nav-sidebar {
    margin-bottom: 60px;
    text-align: left;
}

.hide, .hidden{
    display: none;
}

.with-control-sidebar {
    margin-right: 200px;
}

.control-sidebar-open {
    right: 0;
    display: block;
    width: 200px;
}

.control-sidebar.control-sidebar-open,
.control-sidebar.control-sidebar-open+.control-sidebar-bg {
    right: 0;
}

.control-sidebar-button{
    position: fixed;
    right: 0px;
    padding: 10px;
    top: 62px;
    z-index: 50;
    cursor: pointer;
    background: #fff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    box-shadow: 0px 0px 3px rgba(0,0,0,0.1);
}

.dropdown-menu {
    z-index: 99999;
}

.module-header p{
    margin: 3px 0;
}

.module-header h3,
.module-header h4,
.module-header h5{
    margin: 4px 0;
}

.round-16{
    border-radius: 16px;
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
}

.module-header .separator{
    color: #ccc;
    padding: 0 5px;
}

.fullscreen-icon {
	background-image: url('../img/leaflet/icon-fullscreen.svg');
}

.leaflet-control-layers-toggle {
    background-image: url('../img/leaflet/layers.png');
}

/** Rimozione avviso CKEditor temporanea **/
.cke_notification_warning{
    display:none !important;
}

/** Fix per stile tema bootstrap-datetimepicker **/
.bootstrap-datetimepicker-widget table td,
.bootstrap-datetimepicker-widget table th{
    padding: 0 !important;
    border: none !important;
}

@media screen and (max-width: 767px) {
    .navbar-left {
        display: inline-block;
    }

    .navbar-left ul>li:last-child {
        display: none;
    }

    .box-center,
    .box-center-large {
        width: 90%;
        margin-top: 20px
    }

    .main-header .brand-link {
        display: none;
    }
}

@media screen and (min-width: 768px) and (max-width: 1024px) {
    .signature-pad {
        margin: 10%;
    }
}

@media screen and (max-width: 992px) {

    .table-bordered>thead>tr>th,
    .table-bordered>tbody>tr>th,
    .table-bordered>tfoot>tr>th,
    .table-bordered>thead>tr>td,
    .table-bordered>tbody>tr>td,
    .table-bordered>tfoot>tr>td {
        word-break: break-all;
    }
}

@media screen and (max-width: 1024px) {
    .push {
        position: static;
    }

    .signature-pad {
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: auto;
        height: auto;
        min-width: 250px;
        min-height: 140px;
        margin: 5%;
    }

    .login-page{
        background: #ddd;
    }    
}



@media (min-width: 1140px) {
    .modal-lg {
        max-width: 60vw;
    }
}



/*
* Component: Sidebar Mini
*/

@media (min-width: 768px) {

    .sidebar-mini:not(.sidebar-mini-expand-feature).sidebar-collapse .sidebar-menu>li:hover>a>span:not(.pull-right),
    .sidebar-mini:not(.sidebar-mini-expand-feature).sidebar-collapse .sidebar-menu>li:hover>.treeview-menu {
        width: 220px;
    }

    .sidebar-mini:not(.sidebar-mini-expand-feature).sidebar-collapse .sidebar-menu>li:hover>a>.pull-right-container {
        left: 220px;
    }

    html body.sidebar-mini.sidebar-mini.sidebar-collapse i.fa.fa-user-circle-o.fa-3x.pull-left {
        font-size: 2.4em;
    }
}
