{"version": "3.4.3", "name": "gl-matrix", "description": "Javascript Matrix and Vector library for High Performance WebGL apps", "sideEffects": false, "main": "cjs/index.js", "module": "esm/index.js", "homepage": "http://glmatrix.net", "license": "MIT", "bugs": {"url": "https://github.com/toji/gl-matrix/issues"}, "repository": {"type": "git", "url": "https://github.com/toji/gl-matrix.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}]}