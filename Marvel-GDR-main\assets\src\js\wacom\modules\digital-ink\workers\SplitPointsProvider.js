(function(){"use strict";const __location="undefined"==typeof document?void 0:document.currentScript.src;function _mergeNamespaces(t,e){return e.forEach((function(e){e&&"string"!=typeof e&&!Array.isArray(e)&&Object.keys(e).forEach((function(r){if("default"!==r&&!(r in t)){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}}))})),Object.freeze(t)}function _classCallCheck$1(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties$1(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function _createClass$1(t,e,r){return e&&_defineProperties$1(t.prototype,e),r&&_defineProperties$1(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function _setPrototypeOf$1(t,e){return _setPrototypeOf$1=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},_setPrototypeOf$1(t,e)}function _inherits$1(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_setPrototypeOf$1(t,e)}function _typeof$1(t){return _typeof$1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof$1(t)}function _assertThisInitialized$1(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function _possibleConstructorReturn$1(t,e){if(e&&("object"===_typeof$1(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized$1(t)}function _getPrototypeOf$1(t){return _getPrototypeOf$1=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},_getPrototypeOf$1(t)}function _arrayLikeToArray$g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function _arrayWithoutHoles$1(t){if(Array.isArray(t))return _arrayLikeToArray$g(t)}function _iterableToArray$1(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function _unsupportedIterableToArray$g(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray$g(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$g(t,e):void 0}}function _nonIterableSpread$1(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _toConsumableArray$1(t){return _arrayWithoutHoles$1(t)||_iterableToArray$1(t)||_unsupportedIterableToArray$g(t)||_nonIterableSpread$1()}function _typeof(t){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof(t)}function _setPrototypeOf(t,e){return _setPrototypeOf=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},_setPrototypeOf(t,e)}function _inherits(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_setPrototypeOf(t,e)}function _assertThisInitialized(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function _possibleConstructorReturn(t,e){if(e&&("object"===_typeof(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(t)}function _getPrototypeOf(t){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},_getPrototypeOf(t)}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function _createClass(t,e,r){return e&&_defineProperties(t.prototype,e),r&&_defineProperties(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function _defineProperty$1(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var _class,_class2,_class3,_class4;function _createForOfIteratorHelper$e(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=_unsupportedIterableToArray$f(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function _unsupportedIterableToArray$f(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray$f(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$f(t,e):void 0}}function _arrayLikeToArray$f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function _createSuper$q(t){var e=_isNativeReflectConstruct$r();return function(){var r,n=_getPrototypeOf(t);if(e){var i=_getPrototypeOf(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn(this,r)}}function _isNativeReflectConstruct$r(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}var e=function(){function t(){_classCallCheck(this,t)}return _createClass(t,null,[{key:"extend",value:function(t){var e,r=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this;return"string"==typeof t?(e=t,t=globalThis[e]):e=t.name,t?(this.debug&&console.log("============================================================ extend",t.name,n.name),Object.getOwnPropertyNames(n.prototype).filter((function(t){return"constructor"!=t})).forEach((function(e){!(e in t.prototype)||n.overrides.includes(e)?(r.debug&&(n.overrides.includes(e)?console.log("%coverride ".concat(e),"color: chartreuse"):console.log("%cdefine ".concat(e),"color: green")),t.prototype[e]=n.prototype[e]):r.debug&&console.log("%cexclude ".concat(e),"color: red")})),Object.getOwnPropertyNames(n).forEach((function(e){"function"==typeof n[e]&&"extend"!=e&&(!(e in t)||n.overrides.includes(e)?(r.debug&&(n.overrides.includes(e)?console.log("%coverride static ".concat(e),"color: chartreuse"):console.log("%cdefine static ".concat(e),"color: orange")),t[e]=n[e]):r.debug&&console.log("%cexclude static ".concat(e),"color: red"))})),n.properties&&Object.keys(n.properties).forEach((function(e){e in t.prototype?r.debug&&console.log("%cexclude prop ".concat(e),"color: red"):n.properties[e]&&(r.debug&&console.log("%cdefine prop ".concat(e),"color: darkseagreen"),Object.defineProperty(t.prototype,e,n.properties[e]))})),n.classProperties&&Object.keys(n.classProperties).forEach((function(e){e in t?r.debug&&console.log("%cexclude static prop ".concat(e),"color: red"):n.classProperties[e]&&(r.debug&&console.log("%cdefine static prop ".concat(e),"color: chocolate"),Object.defineProperty(t,e,n.classProperties[e]))})),!0):(this.debug&&console.warn("============================================================ Class ".concat(e," not found")),!1)}}]),t}();_defineProperty$1(e,"overrides",["toString"]);var r=function(){function t(e,r,n){_classCallCheck(this,t),Object.defineProperty(this,"type",{value:e,enumerable:!0}),Object.defineProperty(this,"name",{value:r,enumerable:!0}),Object.defineProperty(this,"value",{value:n,enumerable:!0})}return _createClass(t,[{key:"toString",value:function(){return this.name}}]),t}(),i$1=function(t){_inherits(r,t);var e=_createSuper$q(r);function r(){return _classCallCheck(this,r),e.apply(this,arguments)}return _createClass(r,[{key:"format",value:function(t){return this.toString().length<t.length?t.substring(0,t.length-this.toString().length)+this:this.toString()}},{key:"pad",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"0";return String(this).length<t?new Array(t-String(this).length+1).join(e)+String(this):this.toString()}},{key:"toFloat32",value:function(){var t=new Float32Array(1);return t[0]=this,t[0]}}]),r}(e);_defineProperty$1(i$1,"classProperties",{MAX_INT32:{value:2147483647,enumerable:!0},MAX_UINT32:{value:4294967295,enumerable:!0},MAX_INT64:"undefined"==typeof BigInt?void 0:{value:0x7FFFFFFFFFFFFFFFn,enumerable:!0},MAX_UINT64:"undefined"==typeof BigInt?void 0:{value:0xFFFFFFFFFFFFFFFFn,enumerable:!0}}),_defineProperty$1(i$1,"properties",{length:{get:function(){return 1+(0|Math.log10((this^this>>31)-(this>>31)))},configurable:!0}});var n=function(t){_inherits(r,t);var e=_createSuper$q(r);function r(){return _classCallCheck(this,r),e.apply(this,arguments)}return _createClass(r,null,[{key:"fromArrayBuffer",value:function(t){if(!(t instanceof ArrayBuffer))throw new Error("ArrayBuffer is expected");var e=new Uint8Array(t),r=new SharedArrayBuffer(t.byteLength);return new Uint8Array(r).set(e),r}}]),r}(e),s=["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64","BigInt64","BigUint64"],o=function(){function t(e,r){_classCallCheck(this,t),this.width=e,this.height=r}return _createClass(t,[{key:"toJSON",value:function(){return{width:this.width,height:this.height}}},{key:"toString",value:function(){return"size(".concat(this.width,", ").concat(this.height,")")}}],[{key:"fromSize",value:function(e){if(e instanceof t)return e;if("string"==typeof e){if(!e.startsWith("size("))throw new Error("Invalid value found. Expected template - size(width, height).");var r=e.substring(e.indexOf("(")+1,e.indexOf(")")).split(/,\s*/g);e={width:parseFloat(r[0]),height:parseFloat(r[1])}}if(isNaN(e.width))throw new Error("Invalid width found, expected number");if(isNaN(e.height))throw new Error("Invalid height found, expected number");return new t(e.width,e.height)}}]),t}(),a=function(t){_inherits(n,t);var r=_createSuper$q(n);function n(){return _classCallCheck(this,n),r.apply(this,arguments)}return _createClass(n,[{key:"preMultiply",value:function(t){var e=t.postMultiply(this);return e.multiplicationType=this.multiplicationType,e}},{key:"multiply",value:function(t){if(t instanceof DOMMatrix||(t=DOMMatrix.fromMatrix(t)),this.multiplicationType==DOMMatrix.MultiplicationType.POST)return this.postMultiply(t);var e=this.preMultiply(t);return e.multiplicationType=DOMMatrix.MultiplicationType.PRE,e}},{key:"multiplySelf",value:function(t){t instanceof DOMMatrix||(t=DOMMatrix.fromMatrix(t)),this.multiplicationType==DOMMatrix.MultiplicationType.POST?this.postMultiplySelf(t):this.preMultiplySelf(t)}},{key:"transformPoint",value:function(t){return DOMPoint.fromPoint(t).matrixTransform(this)}},{key:"invert",value:function(){return this.inverse()}},{key:"decompose",value:function(){return{translate:{x:this.tx,y:this.ty},rotate:{angle:Math.atan2(this.b,this.a)},skew:{angleX:Math.tan(this.c),angleY:Math.tan(this.b)},scale:{x:Math.sqrt(this.a*this.a+this.c*this.c),y:Math.sqrt(this.d*this.d+this.b*this.b)},matrix:this}}},{key:"disassemble",value:function(){return console.warn("disassemble is deprecated, use decompose instead"),this.decompose()}},{key:"toString",value:function(t){if(!t)return this.nativeToString();var e=function(t){return((t<0?"":" ")+t.toPrecision(6)).substring(0,8)};return" Matrix 4x4\n"+"-".repeat(39)+"\n".concat(e(this.m11),", ").concat(e(this.m21),", ").concat(e(this.m31),", ").concat(e(this.m41))+"\n".concat(e(this.m12),", ").concat(e(this.m22),", ").concat(e(this.m32),", ").concat(e(this.m42))+"\n".concat(e(this.m13),", ").concat(e(this.m23),", ").concat(e(this.m33),", ").concat(e(this.m43))+"\n".concat(e(this.m14),", ").concat(e(this.m24),", ").concat(e(this.m34),", ").concat(e(this.m44))}}],[{key:"fromMatrix",value:function(t,e){var r;return"string"==typeof t?r=new DOMMatrix(t):("e"in t||(t.e=t.tx||t.dx),"f"in t||(t.f=t.ty||t.dy),r=DOMMatrix.nativeFromMatrix(t)),r.multiplicationType=e||t.multiplicationType||DOMMatrix.MultiplicationType.POST,r}},{key:"fromTranslate",value:function(t){var e=isFinite(t)?{tx:t,ty:t}:{tx:t.x,ty:t.y};return DOMMatrix.fromMatrix(e)}},{key:"fromRotate",value:function(t,e){var r=Math.sin(t),n=Math.cos(t),i={a:n,b:r,c:-r,d:n};return e&&(i.tx=e.x-e.x*n+e.y*r,i.ty=e.y-e.x*r-e.y*n),DOMMatrix.fromMatrix(i)}},{key:"fromScale",value:function(t,e){isFinite(t)&&(t={x:t,y:t});var r={a:t.x,d:t.y};return e&&(r.tx=e.x-e.x*t.x,r.ty=e.y-e.y*t.y),DOMMatrix.fromMatrix(r)}},{key:"fromPoints",value:function(t,e){var r=DOMMatrix.fromMatrix({m11:t[0].x,m21:t[1].x,m31:t[2].x,m12:t[0].y,m22:t[1].y,m32:t[2].y,m13:1,m23:1,m33:1}),n=DOMMatrix.fromMatrix({m11:e[0].x,m21:e[1].x,m31:e[2].x,m12:e[0].y,m22:e[1].y,m32:e[2].y,m13:1,m23:1,m33:1}),i=r.invert().preMultiply(n);return DOMMatrix.fromMatrix({a:i.m11,b:i.m12,c:i.m21,d:i.m22,tx:i.m31,ty:i.m32})}},{key:"extend",value:function(){if("undefined"==typeof DOMMatrix||DOMMatrix.nativeFromMatrix)return!1;DOMMatrix.nativeFromMatrix=DOMMatrix.fromMatrix,DOMMatrix.prototype.nativeToString=DOMMatrix.prototype.toString,DOMMatrix.prototype.postMultiply=DOMMatrix.prototype.multiply,DOMMatrix.prototype.postMultiplySelf=DOMMatrix.prototype.multiplySelf,e.extend("DOMMatrix",this)}}]),n}(e);_defineProperty$1(a,"overrides",e.overrides.concat(["fromMatrix","multiply","multiplySelf","transformPoint"])),_defineProperty$1(a,"properties",{tx:{get:function(){return this.e},set:function(t){this.e=t},enumerable:!0},ty:{get:function(){return this.f},set:function(t){this.f=t},enumerable:!0},dx:{get:function(){return this.e},set:function(t){this.e=t},enumerable:!0},dy:{get:function(){return this.f},set:function(t){this.f=t},enumerable:!0},multiplicationType:{value:"POST",enumerable:!0,writable:!0}}),_defineProperty$1(a,"classProperties",{MultiplicationType:{value:{PRE:"PRE",POST:"POST"},enumerable:!0}});var h=Object.freeze({__proto__:null,ObjectExt:function(t){_inherits(n,t);var e=_createSuper$q(n);function n(){return _classCallCheck(this,n),e.apply(this,arguments)}return _createClass(n,null,[{key:"equals",value:function(t,e){if(t===e)return!0;if(!(t instanceof Object&&e instanceof Object))return!1;if(t.constructor!==e.constructor)return!1;if(t instanceof Array||ArrayBuffer.isTypedArray(t))return t.length==e.length&&t.every((function(t,r){return Object.equals(t,e[r])}));for(var r in t)if(t.hasOwnProperty(r)){if(!e.hasOwnProperty(r))return!1;if(t[r]!==e[r]){if("object"!=_typeof(t[r]))return!1;if(!Object.equals(t[r],e[r]))return!1}}for(var n in e)if(e.hasOwnProperty(n)&&!t.hasOwnProperty(n))return!1;return!0}},{key:"clone",value:function(t,e){var r=new Array;return function t(n){if(null===n)return null;if("object"!=_typeof(n)||n.immutable)return n;if("function"==typeof n.clone)return n.clone();for(var i=0;i<r.length;i++)if(r[i][0]===n)return r[i][1];var o=Object.create(Object.getPrototypeOf(n));for(var s in r.push([n,o]),n)e&&"function"==typeof n[s]||n.hasOwnProperty(s)&&(o[s]=t(n[s]));return o}(t)}},{key:"toSource",value:function(t,e){if("object"==_typeof(t)){var r=[];for(var n in t)t.hasOwnProperty(n)&&r.push("".concat(n,": ").concat(Object.toSource(t[n])));return(e?"".concat(e," = "):"")+"{"+r.join(", ")+"}"}if("function"==typeof t){var i=t.toString();return t.name&&i.startsWith(t.name)&&(i="function "+i),i}return t}},{key:"defineEnum",value:function(t,e,n){if(t[e])throw new Error("Already exist property: ".concat(e));var i={values:n.map((function(t,n){return new r(e,t,n)}))};return i.values.forEach((function(t){Object.defineProperty(i,t.name,{value:t,enumerable:!0}),Object.defineProperty(i,t.value,{value:t,enumerable:!0})})),Object.defineProperty(t,e,{value:i,enumerable:!0}),i}}]),n}(e),StringExt:function(t){_inherits(r,t);var e=_createSuper$q(r);function r(){return _classCallCheck(this,r),e.apply(this,arguments)}return _createClass(r,[{key:"startsWith",value:function(t){return this.length>=t.length&&this.substring(0,t.length)==t}},{key:"endsWith",value:function(t){return this.length>=t.length&&this.substring(this.length-t.length,this.length)==t}},{key:"includes",value:function(t){return-1!=this.indexOf(t)}},{key:"contains",value:function(t){return this.includes(t)}},{key:"containsIgnoreCase",value:function(t){return-1!=this.toUpperCase().indexOf(t.toUpperCase())}},{key:"pad",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"-";return this.length<t?new Array(t-this.length+1).join(e)+this:this.toString()}},{key:"toCharArray",value:function(t){for(var e=[],r=!0,n=0;n<this.length;n++){var i=this.charCodeAt(n);i>255&&(r=!1),e[n]=i}if(t){if(!r)throw new Error("Current value is not byte string");e=new Uint8Array(e)}return e}}],[{key:"fromCharArray",value:function(t){var e="";try{e=String.fromCharCode.apply(null,t)}catch(n){for(var r=0;r<t.length;r++)e+=String.fromCharCode(t[r])}return e}}]),r}(e),NumberExt:i$1,DateExt:function(t){_inherits(r,t);var e=_createSuper$q(r);function r(){return _classCallCheck(this,r),e.apply(this,arguments)}return _createClass(r,[{key:"format",value:function(t){var e=t;function r(t,r){var n=RegExp("".concat(t,"+")).exec(e);if(n){var i=n[0],o=i.replace(new RegExp(i.substring(0,1),"g"),"0");"Y"==t&&"YY"==i&&(r=parseInt(r.toString().substring(2))),e=e.replace(i,r.format(o))}}if(r("Y",this.getFullYear()),r("M",this.getMonth()+1),r("D",this.getDate()),r("h",this.getHours()),r("m",this.getMinutes()),r("s",this.getSeconds()),r("S",this.getMilliseconds()),e.match(/[a-zA-Z]/))throw new Error("Invalid pattern found in ".concat(t,": ").concat(e.match(/[a-zA-Z]+/)[0]));return e}}],[{key:"format",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();return new Date(e).format(t)}}]),r}(e),FunctionExt:(_class=function(t){_inherits(r,t);var e=_createSuper$q(r);function r(){return _classCallCheck(this,r),e.apply(this,arguments)}return _createClass(r,null,[{key:"createClass",value:function(t,e){return new Function(e?e.name:void 0,"return class ".concat(t).concat(e?" extends ".concat(e.name):""," {\n\t\t\tconstructor() {\n\t\t\t\t").concat(e?"super(...arguments);":"","\n\t\t\t}\n\t\t}"))(e)}}]),r}(e),_defineProperty$1(_class,"properties",{body:{get:function(){var t=this.toString();return t=t.substring(t.indexOf("{")+1,t.lastIndexOf("}"))},configurable:!0}}),_class),ArrayExt:(_class2=function(t){_inherits(r,t);var e=_createSuper$q(r);function r(){return _classCallCheck(this,r),e.apply(this,arguments)}return _createClass(r,[{key:"clear",value:function(){this.length=0}},{key:"includes",value:function(t){return this.indexOf(t)>-1}},{key:"clone",value:function(){if(!Object.clone)throw new Error("Object extension is required");return this.map((function(t){return Object.clone(t)}))}},{key:"unique",value:function(){var t=this;return this.filter((function(e,r){return t.indexOf(e)==r}))}},{key:"add",value:function(t){return!this.includes(t)&&(this.push(t),!0)}},{key:"insert",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.splice(e,0,t)}},{key:"indicesOf",value:function(t){var e=[],r=this.indexOf(t);if(-1!=r){e.push(r);for(var n=this.lastIndexOf(t);n>r;)r=this.indexOf(t,r+1),e.push(r)}return e}},{key:"remove",value:function(){for(var t=this,e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return function(t){var e=[],r=-1;return t.forEach((function(t){r-t!=1&&e.push([]),e.last.push(t),r=t})),e}(r.map((function(e){return t.indicesOf(e)})).flat().sort((function(t,e){return e-t}))).forEach((function(e){return t.removeAt(e.last,e.length)})),this}},{key:"removeAt",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return t>-1&&this.splice(t,e),this}},{key:"replace",value:function(t,e,r){var n=this.indexOf(t);return n>-1&&(!r&&e instanceof Array?this.splice.apply(this,[n,1].concat(_toConsumableArray$1(e))):this.splice(n,1,e)),this}}],[{key:"from",value:function(t){return Array.prototype.slice.call(t)}}]),r}(e),_defineProperty$1(_class2,"properties",{first:{get:function(){return this[0]},configurable:!0},last:{get:function(){return this[this.length-1]},configurable:!0}}),_class2),ArrayBufferExt:function(t){_inherits(r,t);var e=_createSuper$q(r);function r(){return _classCallCheck(this,r),e.apply(this,arguments)}return _createClass(r,[{key:"toBase64",value:function(){if(!String.fromCharArray)throw new Error("String extension is required");var t=new Uint8Array(this);return btoa(String.fromCharArray(t))}}],[{key:"fromBase64",value:function(t){if(!String.prototype.toCharArray)throw new Error("String extension is required");return atob(t).toCharArray(!0).buffer}},{key:"isTypedArray",value:function(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}}]),r}(e),SharedArrayBufferExt:n,TypedArrayExt:function(t){_inherits(i,t);var r=_createSuper$q(i);function i(){return _classCallCheck(this,i),r.apply(this,arguments)}return _createClass(i,[{key:"clone",value:function(){if("undefined"!=typeof SharedArrayBuffer&&this.buffer instanceof SharedArrayBuffer){var t=new SharedArrayBuffer(this.byteLength),e=new this.constructor(t);return e.set(this,this.byteOffset),e}return new this.constructor(this,this.byteOffset,this.length)}},{key:"concat",value:function(){for(var t,e=this,r=this.length,n=this.length,i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return o.forEach((function(t){if(e.constructor!=t.constructor)throw new Error("Concat array from wrong type detected - expected ".concat(e.constructor.name,", found ").concat(t.constructor.name));r+=t.length})),(t="undefined"!=typeof SharedArrayBuffer&&this.buffer instanceof SharedArrayBuffer?this.constructor.createSharedInstance(r):new this.constructor(r)).set(this),o.forEach((function(e){t.set(e,n),n+=e.length})),t}},{key:"toArray",value:function(){return Array.from(this)}}],[{key:"createSharedInstance",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(t instanceof this)return"undefined"==typeof SharedArrayBuffer||t.buffer instanceof SharedArrayBuffer?t:new this(n.fromArrayBuffer(t.buffer));if("number"!=typeof t&&!Array.isArray(t))throw new Error("Expected data type is Array");var e="number"==typeof t?t:t.length;if("undefined"==typeof SharedArrayBuffer)return new this("number"==typeof t?e:t);var r=new SharedArrayBuffer(e*this.BYTES_PER_ELEMENT);if("number"==typeof t)return new this(r);var i=new this(r);return i.set(t),i}},{key:"from",value:function(t){return new this(t)}},{key:"extend",value:function(){var t=this;s.forEach((function(r){if(e.extend(r+"Array",t)){var n=globalThis[r+"Array"];Array.prototype["to"+r+"Array"]=function(){return n.from(this)}}}))}}]),i}(e),SetExt:function(t){_inherits(r,t);var e=_createSuper$q(r);function r(){return _classCallCheck(this,r),e.apply(this,arguments)}return _createClass(r,[{key:"map",value:function(t){var e,r=new Set,n=_createForOfIteratorHelper$e(this);try{for(n.s();!(e=n.n()).done;){var i=e.value;(i=t(i))&&r.add(i)}}catch(t){n.e(t)}finally{n.f()}return r}},{key:"filter",value:function(t){var e,r=new Set,n=_createForOfIteratorHelper$e(this);try{for(n.s();!(e=n.n()).done;){var i=e.value;t(i)&&r.add(i)}}catch(t){n.e(t)}finally{n.f()}return r}}]),r}(e),ScreenExt:(_class3=function(t){_inherits(r,t);var e=_createSuper$q(r);function r(){return _classCallCheck(this,r),e.apply(this,arguments)}return _createClass(r)}(e),_defineProperty$1(_class3,"properties",{size:{get:function(){return{width:Math.floor(screen.width),height:Math.floor(screen.height)}},configurable:!0},resolution:{get:function(){return{width:Math.floor(screen.width*devicePixelRatio),height:Math.floor(screen.height*devicePixelRatio)}},configurable:!0}}),_class3),LocationExt:function(t){_inherits(r,t);var e=_createSuper$q(r);function r(){return _classCallCheck(this,r),e.apply(this,arguments)}return _createClass(r,null,[{key:"properties",get:function(){return{query:{get:function(){if(!this._query){var t=Object.assign.apply(Object,[{}].concat(_toConsumableArray$1(this.search.substring(1).split("&").filter((function(t){return t})).map((function(t){return t.split("=")})).map((function(t){return _defineProperty$1({},t[0],decodeURIComponent(t[1]))})))));Object.defineProperty(this,"_query",{value:t})}return this._query},configurable:!0}}}}]),r}(e),HTMLElementExt:function(t){_inherits(r,t);var e=_createSuper$q(r);function r(){return _classCallCheck(this,r),e.apply(this,arguments)}return _createClass(r,[{key:"getInlineStyle",value:function(t){return this.style[t]||this.style["-webkit-"+t]||this.style["-khtml-"+t]||this.style["-moz-"+t]||this.style["-ms-"+t]||this.style["-o-"+t]||""}},{key:"setStyle",value:function(t,e){var r=this;["-webkit","-khtml","-moz","-ms","-o"].forEach((function(n){return r.style[n+"-"+t]=e})),this.style[t]=e}},{key:"getStyle",value:function(t){var e=t,r=t.startsWith("-");r&&(e=t.substring(1));for(var n=e.split("-"),i=n.length-1;i>0;i--)n[i]=n[i].substring(0,1).toUpperCase()+n[i].substring(1);e=n.join("");var o=this.style.display,s=this.style.visibility;"none"==o&&(this.style.visibility="hidden",this.style.display="");var a=window.getComputedStyle(this)[e];if(!r&&void 0===a)for(var l=["-webkit","-khtml","-moz","-ms","-o"],u=0;u<l.length&&"undefined"==(a=this.getStyle(l[u]+"-"+t));u++);return"none"==o&&(this.style.visibility=s,this.style.display="none"),a}},{key:"getMathStyle",value:function(t,e){var r=e?this.getInlineStyle(t):this.getStyle(t);return"auto"==r&&(r=0),parseFloat(r)}},{key:"getTransformStyle",value:function(){var t={translate:{x:0,y:0},scale:{x:1,y:1},rotate:{angle:0},skew:{angleX:0,angleY:0},matrix:{a:1,b:0,c:0,d:1,tx:0,ty:0}},e=this.getStyle("transform");if("none"!=e){var r=e.substring(e.indexOf("(")+1,e.indexOf(")")).split(/,\s*/g),n=parseFloat(r[0]),i=parseFloat(r[1]),o=parseFloat(r[2]),s=parseFloat(r[3]),a=parseFloat(r[4]),l=parseFloat(r[5]);t.scale={x:Math.sqrt(n*n+o*o),y:Math.sqrt(s*s+i*i)},t.skew={angleX:Math.tan(o),angleY:Math.tan(i)},t.rotate={angle:Math.atan2(i,n)},t.translate={x:a,y:l},t.matrix={a:n,b:i,c:o,d:s,tx:a,ty:l}}return t}},{key:"toRect",value:function(){var t=this.style.display,e=this.style.visibility;"none"==t&&(this.style.visibility="hidden",this.style.display="");var r=this.offsetWidth+this.getMathStyle("margin-left")+this.getMathStyle("margin-right"),n=this.offsetHeight+this.getMathStyle("margin-top")+this.getMathStyle("margin-bottom"),i=new DOMRect(this.offsetLeft,this.offsetTop,this.offsetWidth,this.offsetHeight);return i.outerSize=new DOMSize(r,n),"none"==t&&(this.style.visibility=e,this.style.display="none"),i}}]),r}(e),HTMLImageElementExt:function(t){_inherits(r,t);var e=_createSuper$q(r);function r(){return _classCallCheck(this,r),e.apply(this,arguments)}return _createClass(r,[{key:"toDataURL",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"png",e=document.createElement("canvas");return e.width=this.width,e.height=this.height,e.getContext("2d").drawImage(this,0,0),e.toDataURL("image/".concat(t))}},{key:"toBlob",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"png";return new Blob([this.toArrayBuffer(t)],{type:"image/".concat(t)})}},{key:"toArrayBuffer",value:function(t){if(!ArrayBuffer.fromBase64)throw new Error("ArrayBuffer extension is required");var e=this.toDataURL(t).split(",")[1];return ArrayBuffer.fromBase64(e)}}]),r}(e),ImageExt:function(t){_inherits(r,t);var e=_createSuper$q(r);function r(){return _classCallCheck(this,r),e.apply(this,arguments)}return _createClass(r,null,[{key:"fromBytes",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"png",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:new Image;return new Promise((function(n,i){r.onload=function(){URL.revokeObjectURL(r.src),n(r)},r.onerror=i,r.src=URL.createObjectURL(new Blob([t.buffer||t],{type:"image/".concat(e)}))}))}}]),r}(e),DOMPointExt:function(t){_inherits(r,t);var e=_createSuper$q(r);function r(){return _classCallCheck(this,r),e.apply(this,arguments)}return _createClass(r,[{key:"transform",value:function(t){return t instanceof DOMMatrix||(t=DOMMatrix.fromMatrix(t)),this.matrixTransform(t)}},{key:"toString",value:function(){return"point(".concat(this.x,", ").concat(this.y,", ").concat(this.z,")")}}]),r}(e),DOMRectExt:(_class4=function(t){_inherits(n,t);var r=_createSuper$q(n);function n(){return _classCallCheck(this,n),r.apply(this,arguments)}return _createClass(n,[{key:"union",value:function(t){return t?DOMRect.ofEdges(Math.min(this.left,t.left),Math.min(this.top,t.top),Math.max(this.right,t.right),Math.max(this.bottom,t.bottom)):this}},{key:"intersect",value:function(t){if(t){var e=DOMRect.ofEdges(Math.max(this.left,t.left),Math.max(this.top,t.top),Math.min(this.right,t.right),Math.min(this.bottom,t.bottom));return e.width>0&&e.height>0?e:void 0}}},{key:"ceil",value:function(t){var e=Math.floor(this.left),r=Math.floor(this.top),n=Math.ceil(this.right),i=Math.ceil(this.bottom);if(t){var o=this.width,s=this.height;n=e+(o+=o%2),i=r+(s+=s%2)}return DOMRect.ofEdges(e,r,n,i)}},{key:"floor",value:function(t){var e=Math.ceil(this.left),r=Math.ceil(this.top),n=Math.floor(this.right),i=Math.floor(this.bottom);if(t){var o=this.width,s=this.height;n=e+(o-=o%2),i=r+(s-=s%2)}return DOMRect.ofEdges(e,r,n,i)}},{key:"contains",value:function(t){return this.left<=t.x&&this.right>=t.x&&this.top<=t.y&&this.bottom>=t.y}},{key:"transform",value:function(t){t instanceof DOMMatrix||(t=DOMMatrix.fromMatrix(t));var e=DOMPoint.fromPoint({x:this.left,y:this.top}).transform(t),r=DOMPoint.fromPoint({x:this.right,y:this.top}).transform(t),n=DOMPoint.fromPoint({x:this.left,y:this.bottom}).transform(t),i=DOMPoint.fromPoint({x:this.right,y:this.bottom}).transform(t),o=Math.min(e.x,r.x,n.x,i.x),s=Math.min(e.y,r.y,n.y,i.y),a=Math.max(e.x,r.x,n.x,i.x),l=Math.max(e.y,r.y,n.y,i.y);return DOMRect.ofEdges(o,s,a,l)}},{key:"matrixTransform",value:function(t){return this.transform(t)}},{key:"toPath",value:function(){var t=[];return t.push(this.left,this.top),t.push(this.right,this.top),t.push(this.right,this.bottom),t.push(this.left,this.bottom),t.push(this.left,this.top),t.toFloat32Array()}},{key:"toString",value:function(){return"rect(".concat(this.x,", ").concat(this.y,", ").concat(this.width,", ").concat(this.height,")")}}],[{key:"ofEdges",value:function(t,e,r,n){return new DOMRect(t,e,r-t,n-e)}},{key:"extend",value:function(){e.extend("DOMRect",this)&&(globalThis.DOMSize=o)}}]),n}(e),_defineProperty$1(_class4,"properties",{size:{get:function(){return new o(this.width,this.height)},configurable:!0},center:{get:function(){return new DOMPoint((this.left+this.right)/2,(this.top+this.bottom)/2)},configurable:!0}}),_class4),DOMMatrixExt:a});function _isNativeReflectConstruct$q(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function _construct(t,e,r){return _construct=_isNativeReflectConstruct$q()?Reflect.construct:function(t,e,r){var n=[null];n.push.apply(n,e);var i=new(Function.bind.apply(t,n));return r&&_setPrototypeOf$1(i,r.prototype),i},_construct.apply(null,arguments)}function asyncGeneratorStep(t,e,r,n,i,o,s){try{var a=t[o](s),l=a.value}catch(t){return void r(t)}a.done?e(l):Promise.resolve(l).then(n,i)}function _asyncToGenerator(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var o=t.apply(e,r);function s(t){asyncGeneratorStep(o,n,i,s,a,"next",t)}function a(t){asyncGeneratorStep(o,n,i,s,a,"throw",t)}s(void 0)}))}}"undefined"==typeof globalThis&&("undefined"!=typeof window?window.globalThis=window:"undefined"!=typeof self?self.globalThis=self:"undefined"!=typeof global&&(global.globalThis=global)),globalThis.JS_EXT_SCOPE||Object.defineProperty(globalThis,"JS_EXT_SCOPE",{value:["Object","String","Number","Date","Function","Set","Array","ArrayBuffer","SharedArrayBuffer","TypedArray","Screen","Location","HTMLElement","HTMLImageElement","Image","DOMPoint","DOMRect","DOMMatrix"],enumerable:!0,configurable:!0}),globalThis.JS_EXT_SCOPE.forEach((function(t){var e=h["".concat(t,"Ext")];if(!e)throw new Error("Extension ".concat(t," not found"));e.extend(t)})),Math.toDegrees=function(t){return t*(180/this.PI)},Math.toRadians=function(t){return t*(this.PI/180)},Math.randomInt=function(t,e){return Math.floor(this.random()*(e-t+1))+t},"function"==typeof Worker&&(Worker.prototype.on=function(t,e){this["on".concat(t)]=function(r){var n="message"==t?r.data:r;e(n)}}),"function"==typeof DedicatedWorkerGlobalScope&&(DedicatedWorkerGlobalScope.prototype.on=function(t,e){this["on".concat(t)]=function(r){var n="message"==t?r.data:r;e(n)}});var commonjsGlobal="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},runtime={exports:{}};!function(t){var e=function(t){var e,r=Object.prototype,n=r.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",a=i.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,r){return t[e]=r}}function u(t,e,r,n){var i=e&&e.prototype instanceof v?e:v,o=Object.create(i.prototype),s=new A(n||[]);return o._invoke=function(t,e,r){var n=c;return function(i,o){if(n===f)throw new Error("Generator is already running");if(n===d){if("throw"===i)throw o;return T()}for(r.method=i,r.arg=o;;){var s=r.delegate;if(s){var a=S(s,r);if(a){if(a===y)continue;return a}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===c)throw n=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=f;var l=h(t,e,r);if("normal"===l.type){if(n=r.done?d:p,l.arg===y)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n=d,r.method="throw",r.arg=l.arg)}}}(t,r,s),o}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var c="suspendedStart",p="suspendedYield",f="executing",d="completed",y={};function v(){}function m(){}function g(){}var P={};l(P,o,(function(){return this}));var _=Object.getPrototypeOf,b=_&&_(_(w([])));b&&b!==r&&n.call(b,o)&&(P=b);var x=g.prototype=v.prototype=Object.create(P);function E(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function I(t,e){function r(i,o,s,a){var l=h(t[i],t,o);if("throw"!==l.type){var u=l.arg,c=u.value;return c&&"object"==typeof c&&n.call(c,"__await")?e.resolve(c.__await).then((function(t){r("next",t,s,a)}),(function(t){r("throw",t,s,a)})):e.resolve(c).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,a)}))}a(l.arg)}var i;this._invoke=function(t,n){function o(){return new e((function(e,i){r(t,n,e,i)}))}return i=i?i.then(o,o):o()}}function S(t,r){var n=t.iterator[r.method];if(n===e){if(r.delegate=null,"throw"===r.method){if(t.iterator.return&&(r.method="return",r.arg=e,S(t,r),"throw"===r.method))return y;r.method="throw",r.arg=new TypeError("The iterator does not provide a 'throw' method")}return y}var i=h(n,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var o=i.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function w(t){if(t){var r=t[o];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,s=function r(){for(;++i<t.length;)if(n.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return s.next=s}}return{next:T}}function T(){return{value:e,done:!0}}return m.prototype=g,l(x,"constructor",g),l(g,"constructor",m),m.displayName=l(g,a,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,l(t,a,"GeneratorFunction")),t.prototype=Object.create(x),t},t.awrap=function(t){return{__await:t}},E(I.prototype),l(I.prototype,s,(function(){return this})),t.AsyncIterator=I,t.async=function(e,r,n,i,o){void 0===o&&(o=Promise);var s=new I(u(e,r,n,i),o);return t.isGeneratorFunction(r)?s:s.next().then((function(t){return t.done?t.value:s.next()}))},E(x),l(x,a,"Generator"),l(x,o,(function(){return this})),l(x,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=w,A.prototype={constructor:A,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function i(n,i){return a.type="throw",a.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var s=this.tryEntries[o],a=s.completion;if("root"===s.tryLoc)return i("end");if(s.tryLoc<=this.prev){var l=n.call(s,"catchLoc"),u=n.call(s,"finallyLoc");if(l&&u){if(this.prev<s.catchLoc)return i(s.catchLoc,!0);if(this.prev<s.finallyLoc)return i(s.finallyLoc)}else if(l){if(this.prev<s.catchLoc)return i(s.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return i(s.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var s=o?o.completion:{};return s.type=t,s.arg=e,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),O(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var i=n.arg;O(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:w(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}(t.exports);try{regeneratorRuntime=e}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=e:Function("r","regeneratorRuntime = r")(e)}}(runtime);var regenerator=runtime.exports;"function"==typeof Worker&&(Worker.prototype.on=function(t,e){this["on".concat(t)]=function(r){var n="message"==t?r.data:r;e(n)}}),"function"==typeof DedicatedWorkerGlobalScope&&(DedicatedWorkerGlobalScope.prototype.on=function(t,e){this["on".concat(t)]=function(r){var n="message"==t?r.data:r;e(n)}});var ThreadProcessor=function(){function t(){_classCallCheck$1(this,t),this.worker,this.transferables=[]}var e,r,n,i;return _createClass$1(t,[{key:"recieve",value:(i=_asyncToGenerator(regenerator.mark((function t(e){var r;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("INIT"!=e.action){t.next=7;break}return this.worker=e.worker,t.next=4,this.init(e);case 4:r=t.sent,t.next=10;break;case 7:return t.next=9,this.process(e);case 9:r=t.sent;case 10:r.worker=this.worker,this.send(r);case 12:case"end":return t.stop()}}),t,this)}))),function(t){return i.apply(this,arguments)})},{key:"send",value:function(t){self.postMessage(t,this.transferables),this.transferables.length=0}},{key:"init",value:(n=_asyncToGenerator(regenerator.mark((function t(e){return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",e);case 1:case"end":return t.stop()}}),t)}))),function(t){return n.apply(this,arguments)})},{key:"process",value:(r=_asyncToGenerator(regenerator.mark((function t(e){return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:throw new Error("ThreadProcessor.process(message) is abstract and should be implemented");case 1:case"end":return t.stop()}}),t)}))),function(t){return r.apply(this,arguments)})}],[{key:"connect",value:(e=_asyncToGenerator(regenerator.mark((function t(){var e,r,n,i,o,s,a,l=arguments;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:for(e=l.length,r=new Array(e),n=0;n<e;n++)r[n]=l[n];if(i=_construct(this,r),"undefined"!=typeof self){t.next=10;break}return t.next=5,import("worker_threads");case 5:o=t.sent,s=o.parentPort,a=o.workerData,global.self=s,self.name=a.name,self.data=a;case 10:self.on("message",function(){var t=_asyncToGenerator(regenerator.mark((function t(e){return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,i.recieve(e);case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}());case 11:case"end":return t.stop()}}),t,this)}))),function(){return e.apply(this,arguments)})}]),t}();function _defineProperty(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var SplineParameter=function(){function t(e,r){var n=this;_classCallCheck$1(this,t),e>0&&0==r?(this.segmentIndex=e-1,this.t=1):(this.segmentIndex=e,this.t=r),Object.defineProperty(this,"index",{get:function(){return console.warn("SplineParameter index => segmentIndex"),n.segmentIndex}})}return _createClass$1(t,[{key:"equals",value:function(t){return this.segmentIndex==t.segmentIndex&&this.t==t.t}},{key:"toString",value:function(){return"spline-parameter(".concat(this.segmentIndex,", ").concat(this.t,")")}},{key:"toJSON",value:function(){return{segmentIndex:this.segmentIndex,t:this.t}}}],[{key:"fromJSON",value:function(e){return new t(e.segmentIndex,e.t)}},{key:"calcMiddleOfSegment",value:function(e,r){var n=.5*(e.t+r.t+(r.segmentIndex-e.segmentIndex)),i=Math.trunc(n),o=n-i;return new t(e.segmentIndex+i,o)}},{key:"areDistantEnough",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.01,n=e.t+(e.segmentIndex-t.segmentIndex);return n-t.t>r}}]),t}(),SplineSplitPoint=function(){function t(e,r,n){_classCallCheck$1(this,t),this.splineParameter=e,this.segmentIndex=r,this.on=n}return _createClass$1(t,[{key:"toString",value:function(){return"SplineSplitPoint: ".concat(this.splineParameter.segmentIndex," T:").concat(this.splineParameter.t," -> ").concat(this.on?"ON":"OFF",", split by ").concat(this.segmentIndex)}},{key:"toJSON",value:function(){return{splineParameter:this.splineParameter.toJSON(),segmentIndex:this.segmentIndex,on:this.on}}}],[{key:"fromJSON",value:function(e){return new t(SplineParameter.fromJSON(e.splineParameter),e.segmentIndex,e.on)}},{key:"compare",value:function(t,e){return t.splineParameter.segmentIndex<e.splineParameter.segmentIndex?-1:t.splineParameter.segmentIndex>e.splineParameter.segmentIndex?1:t.splineParameter.t<e.splineParameter.t?-1:t.splineParameter.t>e.splineParameter.t?1:0}}]),t}();function _arrayLikeToArray$e(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function _arrayWithoutHoles(t){if(Array.isArray(t))return _arrayLikeToArray$e(t)}function _iterableToArray(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function _unsupportedIterableToArray$e(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray$e(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$e(t,e):void 0}}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _toConsumableArray(t){return _arrayWithoutHoles(t)||_iterableToArray(t)||_unsupportedIterableToArray$e(t)||_nonIterableSpread()}function _superPropBase(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=_getPrototypeOf$1(t)););return t}function _get(){return _get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,r){var n=_superPropBase(t,e);if(n){var i=Object.getOwnPropertyDescriptor(n,e);return i.get?i.get.call(arguments.length<3?t:r):i.value}},_get.apply(this,arguments)}var EPSILON$2=1e-6,ARRAY_TYPE="undefined"!=typeof Float32Array?Float32Array:Array,RANDOM=Math.random;function create$4(){var t=new ARRAY_TYPE(6);return ARRAY_TYPE!=Float32Array&&(t[1]=0,t[2]=0,t[4]=0,t[5]=0),t[0]=1,t[3]=1,t}function rotate$1(t,e,r){var n=e[0],i=e[1],o=e[2],s=e[3],a=e[4],l=e[5],u=Math.sin(r),h=Math.cos(r);return t[0]=n*h+o*u,t[1]=i*h+s*u,t[2]=n*-u+o*h,t[3]=i*-u+s*h,t[4]=a,t[5]=l,t}function scale$4(t,e,r){var n=e[0],i=e[1],o=e[2],s=e[3],a=e[4],l=e[5],u=r[0],h=r[1];return t[0]=n*u,t[1]=i*u,t[2]=o*h,t[3]=s*h,t[4]=a,t[5]=l,t}function translate(t,e,r){var n=e[0],i=e[1],o=e[2],s=e[3],a=e[4],l=e[5],u=r[0],h=r[1];return t[0]=n,t[1]=i,t[2]=o,t[3]=s,t[4]=n*u+o*h+a,t[5]=i*u+s*h+l,t}function create$3(){var t=new ARRAY_TYPE(16);return ARRAY_TYPE!=Float32Array&&(t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=0,t[12]=0,t[13]=0,t[14]=0),t[0]=1,t[5]=1,t[10]=1,t[15]=1,t}function fromValues$4(t,e,r,n,i,o,s,a,l,u,h,c,p,f,d,y){var v=new ARRAY_TYPE(16);return v[0]=t,v[1]=e,v[2]=r,v[3]=n,v[4]=i,v[5]=o,v[6]=s,v[7]=a,v[8]=l,v[9]=u,v[10]=h,v[11]=c,v[12]=p,v[13]=f,v[14]=d,v[15]=y,v}function invert(t,e){var r=e[0],n=e[1],i=e[2],o=e[3],s=e[4],a=e[5],l=e[6],u=e[7],h=e[8],c=e[9],p=e[10],f=e[11],d=e[12],y=e[13],v=e[14],m=e[15],g=r*a-n*s,P=r*l-i*s,_=r*u-o*s,b=n*l-i*a,x=n*u-o*a,E=i*u-o*l,I=h*y-c*d,S=h*v-p*d,C=h*m-f*d,O=c*v-p*y,A=c*m-f*y,w=p*m-f*v,T=g*w-P*A+_*O+b*C-x*S+E*I;return T?(T=1/T,t[0]=(a*w-l*A+u*O)*T,t[1]=(i*A-n*w-o*O)*T,t[2]=(y*E-v*x+m*b)*T,t[3]=(p*x-c*E-f*b)*T,t[4]=(l*C-s*w-u*S)*T,t[5]=(r*w-i*C+o*S)*T,t[6]=(v*_-d*E-m*P)*T,t[7]=(h*E-p*_+f*P)*T,t[8]=(s*A-a*C+u*I)*T,t[9]=(n*C-r*A-o*I)*T,t[10]=(d*x-y*_+m*g)*T,t[11]=(c*_-h*x-f*g)*T,t[12]=(a*S-s*O-l*I)*T,t[13]=(r*O-n*S+i*I)*T,t[14]=(y*P-d*b-v*g)*T,t[15]=(h*b-c*P+p*g)*T,t):null}function multiply$3(t,e,r){var n=e[0],i=e[1],o=e[2],s=e[3],a=e[4],l=e[5],u=e[6],h=e[7],c=e[8],p=e[9],f=e[10],d=e[11],y=e[12],v=e[13],m=e[14],g=e[15],P=r[0],_=r[1],b=r[2],x=r[3];return t[0]=P*n+_*a+b*c+x*y,t[1]=P*i+_*l+b*p+x*v,t[2]=P*o+_*u+b*f+x*m,t[3]=P*s+_*h+b*d+x*g,P=r[4],_=r[5],b=r[6],x=r[7],t[4]=P*n+_*a+b*c+x*y,t[5]=P*i+_*l+b*p+x*v,t[6]=P*o+_*u+b*f+x*m,t[7]=P*s+_*h+b*d+x*g,P=r[8],_=r[9],b=r[10],x=r[11],t[8]=P*n+_*a+b*c+x*y,t[9]=P*i+_*l+b*p+x*v,t[10]=P*o+_*u+b*f+x*m,t[11]=P*s+_*h+b*d+x*g,P=r[12],_=r[13],b=r[14],x=r[15],t[12]=P*n+_*a+b*c+x*y,t[13]=P*i+_*l+b*p+x*v,t[14]=P*o+_*u+b*f+x*m,t[15]=P*s+_*h+b*d+x*g,t}function create$2(){var t=new ARRAY_TYPE(3);return ARRAY_TYPE!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0),t}function clone$2(t){var e=new ARRAY_TYPE(3);return e[0]=t[0],e[1]=t[1],e[2]=t[2],e}function length$2(t){var e=t[0],r=t[1],n=t[2];return Math.hypot(e,r,n)}function fromValues$3(t,e,r){var n=new ARRAY_TYPE(3);return n[0]=t,n[1]=e,n[2]=r,n}function copy$2(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t}function set$2(t,e,r,n){return t[0]=e,t[1]=r,t[2]=n,t}function add$2(t,e,r){return t[0]=e[0]+r[0],t[1]=e[1]+r[1],t[2]=e[2]+r[2],t}function subtract$2(t,e,r){return t[0]=e[0]-r[0],t[1]=e[1]-r[1],t[2]=e[2]-r[2],t}function multiply$2(t,e,r){return t[0]=e[0]*r[0],t[1]=e[1]*r[1],t[2]=e[2]*r[2],t}function divide$2(t,e,r){return t[0]=e[0]/r[0],t[1]=e[1]/r[1],t[2]=e[2]/r[2],t}function ceil$2(t,e){return t[0]=Math.ceil(e[0]),t[1]=Math.ceil(e[1]),t[2]=Math.ceil(e[2]),t}function floor$2(t,e){return t[0]=Math.floor(e[0]),t[1]=Math.floor(e[1]),t[2]=Math.floor(e[2]),t}function min$2(t,e,r){return t[0]=Math.min(e[0],r[0]),t[1]=Math.min(e[1],r[1]),t[2]=Math.min(e[2],r[2]),t}function max$2(t,e,r){return t[0]=Math.max(e[0],r[0]),t[1]=Math.max(e[1],r[1]),t[2]=Math.max(e[2],r[2]),t}function round$2(t,e){return t[0]=Math.round(e[0]),t[1]=Math.round(e[1]),t[2]=Math.round(e[2]),t}function scale$3(t,e,r){return t[0]=e[0]*r,t[1]=e[1]*r,t[2]=e[2]*r,t}function scaleAndAdd$2(t,e,r,n){return t[0]=e[0]+r[0]*n,t[1]=e[1]+r[1]*n,t[2]=e[2]+r[2]*n,t}function distance$2(t,e){var r=e[0]-t[0],n=e[1]-t[1],i=e[2]-t[2];return Math.hypot(r,n,i)}function squaredDistance$2(t,e){var r=e[0]-t[0],n=e[1]-t[1],i=e[2]-t[2];return r*r+n*n+i*i}function squaredLength$2(t){var e=t[0],r=t[1],n=t[2];return e*e+r*r+n*n}function negate$2(t,e){return t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t}function inverse$2(t,e){return t[0]=1/e[0],t[1]=1/e[1],t[2]=1/e[2],t}function normalize$2(t,e){var r=e[0],n=e[1],i=e[2],o=r*r+n*n+i*i;return o>0&&(o=1/Math.sqrt(o)),t[0]=e[0]*o,t[1]=e[1]*o,t[2]=e[2]*o,t}function dot$2(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]}function cross$3(t,e,r){var n=e[0],i=e[1],o=e[2],s=r[0],a=r[1],l=r[2];return t[0]=i*l-o*a,t[1]=o*s-n*l,t[2]=n*a-i*s,t}function lerp$2(t,e,r,n){var i=e[0],o=e[1],s=e[2];return t[0]=i+n*(r[0]-i),t[1]=o+n*(r[1]-o),t[2]=s+n*(r[2]-s),t}function hermite(t,e,r,n,i,o){var s=o*o,a=s*(2*o-3)+1,l=s*(o-2)+o,u=s*(o-1),h=s*(3-2*o);return t[0]=e[0]*a+r[0]*l+n[0]*u+i[0]*h,t[1]=e[1]*a+r[1]*l+n[1]*u+i[1]*h,t[2]=e[2]*a+r[2]*l+n[2]*u+i[2]*h,t}function bezier(t,e,r,n,i,o){var s=1-o,a=s*s,l=o*o,u=a*s,h=3*o*a,c=3*l*s,p=l*o;return t[0]=e[0]*u+r[0]*h+n[0]*c+i[0]*p,t[1]=e[1]*u+r[1]*h+n[1]*c+i[1]*p,t[2]=e[2]*u+r[2]*h+n[2]*c+i[2]*p,t}function random$2(t,e){e=e||1;var r=2*RANDOM()*Math.PI,n=2*RANDOM()-1,i=Math.sqrt(1-n*n)*e;return t[0]=Math.cos(r)*i,t[1]=Math.sin(r)*i,t[2]=n*e,t}function transformMat4$2(t,e,r){var n=e[0],i=e[1],o=e[2],s=r[3]*n+r[7]*i+r[11]*o+r[15];return s=s||1,t[0]=(r[0]*n+r[4]*i+r[8]*o+r[12])/s,t[1]=(r[1]*n+r[5]*i+r[9]*o+r[13])/s,t[2]=(r[2]*n+r[6]*i+r[10]*o+r[14])/s,t}function transformMat3$1(t,e,r){var n=e[0],i=e[1],o=e[2];return t[0]=n*r[0]+i*r[3]+o*r[6],t[1]=n*r[1]+i*r[4]+o*r[7],t[2]=n*r[2]+i*r[5]+o*r[8],t}function transformQuat$1(t,e,r){var n=r[0],i=r[1],o=r[2],s=r[3],a=e[0],l=e[1],u=e[2],h=i*u-o*l,c=o*a-n*u,p=n*l-i*a,f=i*p-o*c,d=o*h-n*p,y=n*c-i*h,v=2*s;return h*=v,c*=v,p*=v,f*=2,d*=2,y*=2,t[0]=a+h+f,t[1]=l+c+d,t[2]=u+p+y,t}function rotateX(t,e,r,n){var i=[],o=[];return i[0]=e[0]-r[0],i[1]=e[1]-r[1],i[2]=e[2]-r[2],o[0]=i[0],o[1]=i[1]*Math.cos(n)-i[2]*Math.sin(n),o[2]=i[1]*Math.sin(n)+i[2]*Math.cos(n),t[0]=o[0]+r[0],t[1]=o[1]+r[1],t[2]=o[2]+r[2],t}function rotateY(t,e,r,n){var i=[],o=[];return i[0]=e[0]-r[0],i[1]=e[1]-r[1],i[2]=e[2]-r[2],o[0]=i[2]*Math.sin(n)+i[0]*Math.cos(n),o[1]=i[1],o[2]=i[2]*Math.cos(n)-i[0]*Math.sin(n),t[0]=o[0]+r[0],t[1]=o[1]+r[1],t[2]=o[2]+r[2],t}function rotateZ(t,e,r,n){var i=[],o=[];return i[0]=e[0]-r[0],i[1]=e[1]-r[1],i[2]=e[2]-r[2],o[0]=i[0]*Math.cos(n)-i[1]*Math.sin(n),o[1]=i[0]*Math.sin(n)+i[1]*Math.cos(n),o[2]=i[2],t[0]=o[0]+r[0],t[1]=o[1]+r[1],t[2]=o[2]+r[2],t}function angle$1(t,e){var r=t[0],n=t[1],i=t[2],o=e[0],s=e[1],a=e[2],l=Math.sqrt(r*r+n*n+i*i)*Math.sqrt(o*o+s*s+a*a),u=l&&dot$2(t,e)/l;return Math.acos(Math.min(Math.max(u,-1),1))}function zero$2(t){return t[0]=0,t[1]=0,t[2]=0,t}function str$2(t){return"vec3("+t[0]+", "+t[1]+", "+t[2]+")"}function exactEquals$2(t,e){return t[0]===e[0]&&t[1]===e[1]&&t[2]===e[2]}function equals$3(t,e){var r=t[0],n=t[1],i=t[2],o=e[0],s=e[1],a=e[2];return Math.abs(r-o)<=EPSILON$2*Math.max(1,Math.abs(r),Math.abs(o))&&Math.abs(n-s)<=EPSILON$2*Math.max(1,Math.abs(n),Math.abs(s))&&Math.abs(i-a)<=EPSILON$2*Math.max(1,Math.abs(i),Math.abs(a))}Math.hypot||(Math.hypot=function(){for(var t=0,e=arguments.length;e--;)t+=arguments[e]*arguments[e];return Math.sqrt(t)});var sub$2=subtract$2,mul$2=multiply$2,div$2=divide$2,dist$2=distance$2,sqrDist$2=squaredDistance$2,len$2=length$2,sqrLen$2=squaredLength$2,forEach$2=(vec=create$2(),function(t,e,r,n,i,o){var s,a;for(e||(e=3),r||(r=0),a=n?Math.min(n*e+r,t.length):t.length,s=r;s<a;s+=e)vec[0]=t[s],vec[1]=t[s+1],vec[2]=t[s+2],i(vec,vec,o),t[s]=vec[0],t[s+1]=vec[1],t[s+2]=vec[2];return t}),vec,vec3=Object.freeze({__proto__:null,create:create$2,clone:clone$2,length:length$2,fromValues:fromValues$3,copy:copy$2,set:set$2,add:add$2,subtract:subtract$2,multiply:multiply$2,divide:divide$2,ceil:ceil$2,floor:floor$2,min:min$2,max:max$2,round:round$2,scale:scale$3,scaleAndAdd:scaleAndAdd$2,distance:distance$2,squaredDistance:squaredDistance$2,squaredLength:squaredLength$2,negate:negate$2,inverse:inverse$2,normalize:normalize$2,dot:dot$2,cross:cross$3,lerp:lerp$2,hermite:hermite,bezier:bezier,random:random$2,transformMat4:transformMat4$2,transformMat3:transformMat3$1,transformQuat:transformQuat$1,rotateX:rotateX,rotateY:rotateY,rotateZ:rotateZ,angle:angle$1,zero:zero$2,str:str$2,exactEquals:exactEquals$2,equals:equals$3,sub:sub$2,mul:mul$2,div:div$2,dist:dist$2,sqrDist:sqrDist$2,len:len$2,sqrLen:sqrLen$2,forEach:forEach$2});function create$1(){var t=new ARRAY_TYPE(4);return ARRAY_TYPE!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0,t[3]=0),t}function clone$1(t){var e=new ARRAY_TYPE(4);return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e}function fromValues$2(t,e,r,n){var i=new ARRAY_TYPE(4);return i[0]=t,i[1]=e,i[2]=r,i[3]=n,i}function copy$1(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}function set$1(t,e,r,n,i){return t[0]=e,t[1]=r,t[2]=n,t[3]=i,t}function add$1(t,e,r){return t[0]=e[0]+r[0],t[1]=e[1]+r[1],t[2]=e[2]+r[2],t[3]=e[3]+r[3],t}function subtract$1(t,e,r){return t[0]=e[0]-r[0],t[1]=e[1]-r[1],t[2]=e[2]-r[2],t[3]=e[3]-r[3],t}function multiply$1(t,e,r){return t[0]=e[0]*r[0],t[1]=e[1]*r[1],t[2]=e[2]*r[2],t[3]=e[3]*r[3],t}function divide$1(t,e,r){return t[0]=e[0]/r[0],t[1]=e[1]/r[1],t[2]=e[2]/r[2],t[3]=e[3]/r[3],t}function ceil$1(t,e){return t[0]=Math.ceil(e[0]),t[1]=Math.ceil(e[1]),t[2]=Math.ceil(e[2]),t[3]=Math.ceil(e[3]),t}function floor$1(t,e){return t[0]=Math.floor(e[0]),t[1]=Math.floor(e[1]),t[2]=Math.floor(e[2]),t[3]=Math.floor(e[3]),t}function min$1(t,e,r){return t[0]=Math.min(e[0],r[0]),t[1]=Math.min(e[1],r[1]),t[2]=Math.min(e[2],r[2]),t[3]=Math.min(e[3],r[3]),t}function max$1(t,e,r){return t[0]=Math.max(e[0],r[0]),t[1]=Math.max(e[1],r[1]),t[2]=Math.max(e[2],r[2]),t[3]=Math.max(e[3],r[3]),t}function round$1(t,e){return t[0]=Math.round(e[0]),t[1]=Math.round(e[1]),t[2]=Math.round(e[2]),t[3]=Math.round(e[3]),t}function scale$2(t,e,r){return t[0]=e[0]*r,t[1]=e[1]*r,t[2]=e[2]*r,t[3]=e[3]*r,t}function scaleAndAdd$1(t,e,r,n){return t[0]=e[0]+r[0]*n,t[1]=e[1]+r[1]*n,t[2]=e[2]+r[2]*n,t[3]=e[3]+r[3]*n,t}function distance$1(t,e){var r=e[0]-t[0],n=e[1]-t[1],i=e[2]-t[2],o=e[3]-t[3];return Math.hypot(r,n,i,o)}function squaredDistance$1(t,e){var r=e[0]-t[0],n=e[1]-t[1],i=e[2]-t[2],o=e[3]-t[3];return r*r+n*n+i*i+o*o}function length$1(t){var e=t[0],r=t[1],n=t[2],i=t[3];return Math.hypot(e,r,n,i)}function squaredLength$1(t){var e=t[0],r=t[1],n=t[2],i=t[3];return e*e+r*r+n*n+i*i}function negate$1(t,e){return t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t[3]=-e[3],t}function inverse$1(t,e){return t[0]=1/e[0],t[1]=1/e[1],t[2]=1/e[2],t[3]=1/e[3],t}function normalize$1(t,e){var r=e[0],n=e[1],i=e[2],o=e[3],s=r*r+n*n+i*i+o*o;return s>0&&(s=1/Math.sqrt(s)),t[0]=r*s,t[1]=n*s,t[2]=i*s,t[3]=o*s,t}function dot$1(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]+t[3]*e[3]}function cross$2(t,e,r,n){var i=r[0]*n[1]-r[1]*n[0],o=r[0]*n[2]-r[2]*n[0],s=r[0]*n[3]-r[3]*n[0],a=r[1]*n[2]-r[2]*n[1],l=r[1]*n[3]-r[3]*n[1],u=r[2]*n[3]-r[3]*n[2],h=e[0],c=e[1],p=e[2],f=e[3];return t[0]=c*u-p*l+f*a,t[1]=-h*u+p*s-f*o,t[2]=h*l-c*s+f*i,t[3]=-h*a+c*o-p*i,t}function lerp$1(t,e,r,n){var i=e[0],o=e[1],s=e[2],a=e[3];return t[0]=i+n*(r[0]-i),t[1]=o+n*(r[1]-o),t[2]=s+n*(r[2]-s),t[3]=a+n*(r[3]-a),t}function random$1(t,e){var r,n,i,o,s,a;e=e||1;do{s=(r=2*RANDOM()-1)*r+(n=2*RANDOM()-1)*n}while(s>=1);do{a=(i=2*RANDOM()-1)*i+(o=2*RANDOM()-1)*o}while(a>=1);var l=Math.sqrt((1-s)/a);return t[0]=e*r,t[1]=e*n,t[2]=e*i*l,t[3]=e*o*l,t}function transformMat4$1(t,e,r){var n=e[0],i=e[1],o=e[2],s=e[3];return t[0]=r[0]*n+r[4]*i+r[8]*o+r[12]*s,t[1]=r[1]*n+r[5]*i+r[9]*o+r[13]*s,t[2]=r[2]*n+r[6]*i+r[10]*o+r[14]*s,t[3]=r[3]*n+r[7]*i+r[11]*o+r[15]*s,t}function transformQuat(t,e,r){var n=e[0],i=e[1],o=e[2],s=r[0],a=r[1],l=r[2],u=r[3],h=u*n+a*o-l*i,c=u*i+l*n-s*o,p=u*o+s*i-a*n,f=-s*n-a*i-l*o;return t[0]=h*u+f*-s+c*-l-p*-a,t[1]=c*u+f*-a+p*-s-h*-l,t[2]=p*u+f*-l+h*-a-c*-s,t[3]=e[3],t}function zero$1(t){return t[0]=0,t[1]=0,t[2]=0,t[3]=0,t}function str$1(t){return"vec4("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+")"}function exactEquals$1(t,e){return t[0]===e[0]&&t[1]===e[1]&&t[2]===e[2]&&t[3]===e[3]}function equals$2(t,e){var r=t[0],n=t[1],i=t[2],o=t[3],s=e[0],a=e[1],l=e[2],u=e[3];return Math.abs(r-s)<=EPSILON$2*Math.max(1,Math.abs(r),Math.abs(s))&&Math.abs(n-a)<=EPSILON$2*Math.max(1,Math.abs(n),Math.abs(a))&&Math.abs(i-l)<=EPSILON$2*Math.max(1,Math.abs(i),Math.abs(l))&&Math.abs(o-u)<=EPSILON$2*Math.max(1,Math.abs(o),Math.abs(u))}var sub$1=subtract$1,mul$1=multiply$1,div$1=divide$1,dist$1=distance$1,sqrDist$1=squaredDistance$1,len$1=length$1,sqrLen$1=squaredLength$1,forEach$1=function(){var t=create$1();return function(e,r,n,i,o,s){var a,l;for(r||(r=4),n||(n=0),l=i?Math.min(i*r+n,e.length):e.length,a=n;a<l;a+=r)t[0]=e[a],t[1]=e[a+1],t[2]=e[a+2],t[3]=e[a+3],o(t,t,s),e[a]=t[0],e[a+1]=t[1],e[a+2]=t[2],e[a+3]=t[3];return e}}(),vec4=Object.freeze({__proto__:null,create:create$1,clone:clone$1,fromValues:fromValues$2,copy:copy$1,set:set$1,add:add$1,subtract:subtract$1,multiply:multiply$1,divide:divide$1,ceil:ceil$1,floor:floor$1,min:min$1,max:max$1,round:round$1,scale:scale$2,scaleAndAdd:scaleAndAdd$1,distance:distance$1,squaredDistance:squaredDistance$1,length:length$1,squaredLength:squaredLength$1,negate:negate$1,inverse:inverse$1,normalize:normalize$1,dot:dot$1,cross:cross$2,lerp:lerp$1,random:random$1,transformMat4:transformMat4$1,transformQuat:transformQuat,zero:zero$1,str:str$1,exactEquals:exactEquals$1,equals:equals$2,sub:sub$1,mul:mul$1,div:div$1,dist:dist$1,sqrDist:sqrDist$1,len:len$1,sqrLen:sqrLen$1,forEach:forEach$1});function fromValues$1(t,e,r,n,i,o,s,a){var l=new ARRAY_TYPE(8);return l[0]=t,l[1]=e,l[2]=r,l[3]=n,l[4]=i,l[5]=o,l[6]=s,l[7]=a,l}function create(){var t=new ARRAY_TYPE(2);return ARRAY_TYPE!=Float32Array&&(t[0]=0,t[1]=0),t}function clone(t){var e=new ARRAY_TYPE(2);return e[0]=t[0],e[1]=t[1],e}function fromValues(t,e){var r=new ARRAY_TYPE(2);return r[0]=t,r[1]=e,r}function copy(t,e){return t[0]=e[0],t[1]=e[1],t}function set(t,e,r){return t[0]=e,t[1]=r,t}function add(t,e,r){return t[0]=e[0]+r[0],t[1]=e[1]+r[1],t}function subtract(t,e,r){return t[0]=e[0]-r[0],t[1]=e[1]-r[1],t}function multiply(t,e,r){return t[0]=e[0]*r[0],t[1]=e[1]*r[1],t}function divide(t,e,r){return t[0]=e[0]/r[0],t[1]=e[1]/r[1],t}function ceil(t,e){return t[0]=Math.ceil(e[0]),t[1]=Math.ceil(e[1]),t}function floor(t,e){return t[0]=Math.floor(e[0]),t[1]=Math.floor(e[1]),t}function min(t,e,r){return t[0]=Math.min(e[0],r[0]),t[1]=Math.min(e[1],r[1]),t}function max(t,e,r){return t[0]=Math.max(e[0],r[0]),t[1]=Math.max(e[1],r[1]),t}function round(t,e){return t[0]=Math.round(e[0]),t[1]=Math.round(e[1]),t}function scale$1(t,e,r){return t[0]=e[0]*r,t[1]=e[1]*r,t}function scaleAndAdd(t,e,r,n){return t[0]=e[0]+r[0]*n,t[1]=e[1]+r[1]*n,t}function distance(t,e){var r=e[0]-t[0],n=e[1]-t[1];return Math.hypot(r,n)}function squaredDistance(t,e){var r=e[0]-t[0],n=e[1]-t[1];return r*r+n*n}function length(t){var e=t[0],r=t[1];return Math.hypot(e,r)}function squaredLength(t){var e=t[0],r=t[1];return e*e+r*r}function negate(t,e){return t[0]=-e[0],t[1]=-e[1],t}function inverse(t,e){return t[0]=1/e[0],t[1]=1/e[1],t}function normalize(t,e){var r=e[0],n=e[1],i=r*r+n*n;return i>0&&(i=1/Math.sqrt(i)),t[0]=e[0]*i,t[1]=e[1]*i,t}function dot(t,e){return t[0]*e[0]+t[1]*e[1]}function cross$1(t,e,r){var n=e[0]*r[1]-e[1]*r[0];return t[0]=t[1]=0,t[2]=n,t}function lerp(t,e,r,n){var i=e[0],o=e[1];return t[0]=i+n*(r[0]-i),t[1]=o+n*(r[1]-o),t}function random(t,e){e=e||1;var r=2*RANDOM()*Math.PI;return t[0]=Math.cos(r)*e,t[1]=Math.sin(r)*e,t}function transformMat2(t,e,r){var n=e[0],i=e[1];return t[0]=r[0]*n+r[2]*i,t[1]=r[1]*n+r[3]*i,t}function transformMat2d(t,e,r){var n=e[0],i=e[1];return t[0]=r[0]*n+r[2]*i+r[4],t[1]=r[1]*n+r[3]*i+r[5],t}function transformMat3(t,e,r){var n=e[0],i=e[1];return t[0]=r[0]*n+r[3]*i+r[6],t[1]=r[1]*n+r[4]*i+r[7],t}function transformMat4(t,e,r){var n=e[0],i=e[1];return t[0]=r[0]*n+r[4]*i+r[12],t[1]=r[1]*n+r[5]*i+r[13],t}function rotate(t,e,r,n){var i=e[0]-r[0],o=e[1]-r[1],s=Math.sin(n),a=Math.cos(n);return t[0]=i*a-o*s+r[0],t[1]=i*s+o*a+r[1],t}function angle(t,e){var r=t[0],n=t[1],i=e[0],o=e[1],s=Math.sqrt(r*r+n*n)*Math.sqrt(i*i+o*o),a=s&&(r*i+n*o)/s;return Math.acos(Math.min(Math.max(a,-1),1))}function zero(t){return t[0]=0,t[1]=0,t}function str(t){return"vec2("+t[0]+", "+t[1]+")"}function exactEquals(t,e){return t[0]===e[0]&&t[1]===e[1]}function equals$1(t,e){var r=t[0],n=t[1],i=e[0],o=e[1];return Math.abs(r-i)<=EPSILON$2*Math.max(1,Math.abs(r),Math.abs(i))&&Math.abs(n-o)<=EPSILON$2*Math.max(1,Math.abs(n),Math.abs(o))}var len=length,sub=subtract,mul=multiply,div=divide,dist=distance,sqrDist=squaredDistance,sqrLen=squaredLength,forEach=function(){var t=create();return function(e,r,n,i,o,s){var a,l;for(r||(r=2),n||(n=0),l=i?Math.min(i*r+n,e.length):e.length,a=n;a<l;a+=r)t[0]=e[a],t[1]=e[a+1],o(t,t,s),e[a]=t[0],e[a+1]=t[1];return e}}(),vec2=Object.freeze({__proto__:null,create:create,clone:clone,fromValues:fromValues,copy:copy,set:set,add:add,subtract:subtract,multiply:multiply,divide:divide,ceil:ceil,floor:floor,min:min,max:max,round:round,scale:scale$1,scaleAndAdd:scaleAndAdd,distance:distance,squaredDistance:squaredDistance,length:length,squaredLength:squaredLength,negate:negate,inverse:inverse,normalize:normalize,dot:dot,cross:cross$1,lerp:lerp,random:random,transformMat2:transformMat2,transformMat2d:transformMat2d,transformMat3:transformMat3,transformMat4:transformMat4,rotate:rotate,angle:angle,zero:zero,str:str,exactEquals:exactEquals,equals:equals$1,len:len,sub:sub,mul:mul,div:div,dist:dist,sqrDist:sqrDist,sqrLen:sqrLen,forEach:forEach}),Point$3=function(){function t(e,r,n,i){var o=this;if(_classCallCheck$1(this,t),isNaN(e))throw new Error("Invalid x found: ".concat(e));if(isNaN(r))throw new Error("Invalid y found: ".concat(r));var s=[e,r];isFinite(n)&&(s.push(n),isFinite(i)&&s.push(i)),this.value=s.toFloat32Array(),Object.defineProperty(this,"x",{get:function(){return o.value[0]},set:function(t){o.value[0]=t},enumerable:!0}),Object.defineProperty(this,"y",{get:function(){return o.value[1]},set:function(t){o.value[1]=t},enumerable:!0}),2==s.length?this.vec=vec2:3==s.length?(this.vec=vec3,Object.defineProperty(this,"z",{get:function(){return o.value[2]},set:function(t){o.value[2]=t},enumerable:!0})):(this.vec=vec4,Object.defineProperty(this,"w",{get:function(){return o.value[3]},set:function(t){o.value[3]=t},enumerable:!0}))}return _createClass$1(t,[{key:"add",value:function(e){e instanceof t||(e=t.fromPoint(e));var r=this.vec.create();return this.vec.add(r,this.value,e.value),t.fromPoint(r)}},{key:"addSelf",value:function(e){return e instanceof t||(e=t.fromPoint(e)),this.vec.add(this.value,this.value,e.value),this}},{key:"subtract",value:function(e){e instanceof t||(e=t.fromPoint(e));var r=this.vec.create();return this.vec.subtract(r,this.value,e.value),t.fromPoint(r)}},{key:"subtractSelf",value:function(e){return e instanceof t||(e=t.fromPoint(e)),this.vec.subtract(this.value,this.value,e.value),this}},{key:"multiply",value:function(e){e instanceof t||(e=t.fromPoint(e));var r=this.vec.create();return this.vec.multiply(r,this.value,e.value),t.fromPoint(r)}},{key:"multiplySelf",value:function(e){return e instanceof t||(e=t.fromPoint(e)),this.vec.multiply(this.value,this.value,e.value),this}},{key:"divide",value:function(e){e instanceof t||(e=t.fromPoint(e));var r=this.vec.create();return this.vec.divide(r,this.value,e.value),t.fromPoint(r)}},{key:"divideSelf",value:function(e){return e instanceof t||(e=t.fromPoint(e)),this.vec.divide(this.value,this.value,e.value),this}},{key:"scale",value:function(e){var r=this.vec.create();return this.vec.scale(r,this.value,e),t.fromPoint(r)}},{key:"scaleSelf",value:function(t){return this.vec.scale(this.value,this.value,t),this}},{key:"abs",value:function(){return new t(Math.abs(this.x),Math.abs(this.y),isFinite(this.z)?Math.abs(this.z):void 0,isFinite(this.w)?Math.abs(this.w):void 0)}},{key:"absSelf",value:function(){return this.x=Math.abs(this.x),this.y=Math.abs(this.y),isFinite(this.z)&&(this.z=Math.abs(this.z)),isFinite(this.w)&&(this.w=Math.abs(this.w)),this}},{key:"transform",value:function(e){if(!e)return this;var r=this.vec.create();return this.vec.transformMat4(r,this.value,e.toFloat32Array()),t.fromPoint(r)}},{key:"transformSelf",value:function(t){return this.vec.transformMat4(this.value,this.value,t.toFloat32Array()),this}},{key:"toFloat32Array",value:function(){return this.value}},{key:"toJSON",value:function(){var t={x:this.x,y:this.y};return isFinite(this.z)&&(t.z=this.z,isFinite(this.w)&&(t.w=this.w)),t}},{key:"toString",value:function(){return"point(".concat(this.value.join(", "),")")}},{key:"clone",value:function(){return t.fromPoint(this)}}],[{key:"fromPoint",value:function(e){return Array.isArray(e)||ArrayBuffer.isTypedArray(e)?new t(e[0],e[1],e[2],e[3]):new t(e.x,e.y,e.z,e.w)}}]),t}(),INDEX={m11:0,m12:1,m13:2,m14:3,m21:4,m22:5,m23:6,m24:7,m31:8,m32:9,m33:10,m34:11,m41:12,m42:13,m43:14,m44:15},INDEX_2D={a:INDEX.m11,b:INDEX.m12,c:INDEX.m21,d:INDEX.m22,tx:INDEX.m41,ty:INDEX.m42},Matrix=function(){function t(){var e=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:create$3(),n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t.MultiplicationType.PRE;_classCallCheck$1(this,t),Object.defineProperty(this,"value",{value:r,enumerable:!0}),Object.defineProperty(this,"multiplicationType",{value:n,enumerable:!0});var i=function(t,e){var r=INDEX[t];this.value[r]=e};Object.defineProperty(this,"a",{get:function(){return e.value[INDEX_2D.a]},set:i.bind(this,"m11"),enumerable:!0}),Object.defineProperty(this,"b",{get:function(){return e.value[INDEX_2D.b]},set:i.bind(this,"m12"),enumerable:!0}),Object.defineProperty(this,"c",{get:function(){return e.value[INDEX_2D.c]},set:i.bind(this,"m21"),enumerable:!0}),Object.defineProperty(this,"d",{get:function(){return e.value[INDEX_2D.d]},set:i.bind(this,"m22"),enumerable:!0}),Object.defineProperty(this,"e",{get:function(){return e.value[INDEX_2D.tx]},set:i.bind(this,"m41"),enumerable:!0}),Object.defineProperty(this,"f",{get:function(){return e.value[INDEX_2D.ty]},set:i.bind(this,"m42"),enumerable:!0}),Object.defineProperty(this,"tx",{get:function(){return e.value[INDEX_2D.tx]},set:i.bind(this,"m41"),enumerable:!0}),Object.defineProperty(this,"ty",{get:function(){return e.value[INDEX_2D.ty]},set:i.bind(this,"m42"),enumerable:!0}),Object.defineProperty(this,"m11",{get:function(){return e.value[0]},set:i.bind(this,"m11"),enumerable:!0}),Object.defineProperty(this,"m12",{get:function(){return e.value[1]},set:i.bind(this,"m12"),enumerable:!0}),Object.defineProperty(this,"m13",{get:function(){return e.value[2]},set:i.bind(this,"m13"),enumerable:!0}),Object.defineProperty(this,"m14",{get:function(){return e.value[3]},set:i.bind(this,"m14"),enumerable:!0}),Object.defineProperty(this,"m21",{get:function(){return e.value[4]},set:i.bind(this,"m21"),enumerable:!0}),Object.defineProperty(this,"m22",{get:function(){return e.value[5]},set:i.bind(this,"m22"),enumerable:!0}),Object.defineProperty(this,"m23",{get:function(){return e.value[6]},set:i.bind(this,"m23"),enumerable:!0}),Object.defineProperty(this,"m24",{get:function(){return e.value[7]},set:i.bind(this,"m24"),enumerable:!0}),Object.defineProperty(this,"m31",{get:function(){return e.value[8]},set:i.bind(this,"m31"),enumerable:!0}),Object.defineProperty(this,"m32",{get:function(){return e.value[9]},set:i.bind(this,"m32"),enumerable:!0}),Object.defineProperty(this,"m33",{get:function(){return e.value[10]},set:i.bind(this,"m33"),enumerable:!0}),Object.defineProperty(this,"m34",{get:function(){return e.value[11]},set:i.bind(this,"m34"),enumerable:!0}),Object.defineProperty(this,"m41",{get:function(){return e.value[12]},set:i.bind(this,"m41"),enumerable:!0}),Object.defineProperty(this,"m42",{get:function(){return e.value[13]},set:i.bind(this,"m42"),enumerable:!0}),Object.defineProperty(this,"m43",{get:function(){return e.value[14]},set:i.bind(this,"m43"),enumerable:!0}),Object.defineProperty(this,"m44",{get:function(){return e.value[15]},set:i.bind(this,"m44"),enumerable:!0}),Object.defineProperty(this,"isIdentity",{get:function(){return 1==e.a&&0==e.b&&0==e.c&&1==e.d&&0==e.tx&&0==e.ty},enumerable:!0}),Object.defineProperty(this,"is2D",{get:function(){return!(0!=e.m31||0!=e.m32||0!=e.m13||0!=e.m23||1!=e.m33||0!=e.m43||0!=e.m14||0!=e.m24||0!=e.m34||1!=e.m44)},enumerable:!0}),Object.defineProperty(this,"translateX",{get:function(){return e.tx}}),Object.defineProperty(this,"translateY",{get:function(){return e.ty}}),Object.defineProperty(this,"skewX",{get:function(){return Math.tan(e.c)}}),Object.defineProperty(this,"skewY",{get:function(){return Math.tan(e.b)}}),Object.defineProperty(this,"scaleX",{get:function(){return Math.sqrt(e.a*e.a+e.c*e.c)}}),Object.defineProperty(this,"scaleY",{get:function(){return Math.sqrt(e.d*e.d+e.b*e.b)}}),Object.defineProperty(this,"rotation",{get:function(){return Math.atan2(e.b,e.a)}})}return _createClass$1(t,[{key:"clone",value:function(){return new t(this.value.clone(),this.multiplicationType)}},{key:"translate",value:function(e){return this.multiply(t.fromTranslate(e))}},{key:"translateSelf",value:function(e){this.multiplySelf(t.fromTranslate(e))}},{key:"rotate",value:function(e,r){return this.multiply(t.fromRotate(e,r))}},{key:"rotateSelf",value:function(e,r){this.multiplySelf(t.fromRotate(e,r))}},{key:"scale",value:function(e,r){return this.multiply(t.fromScale(e,r))}},{key:"scaleSelf",value:function(e,r){this.multiplySelf(t.fromScale(e,r))}},{key:"multiply",value:function(e){return this.multiplicationType==t.MultiplicationType.PRE?this.preMultiply(e):this.postMultiply(e)}},{key:"preMultiply",value:function(e){var r=create$3();return multiply$3(r,e.toFloat32Array(),this.value),new t(r,this.multiplicationType)}},{key:"postMultiply",value:function(e){var r=create$3();return multiply$3(r,this.value,e.toFloat32Array()),new t(r,this.multiplicationType)}},{key:"multiplySelf",value:function(e){this.multiplicationType==t.MultiplicationType.PRE?this.preMultiplySelf(e):this.postMultiplySelf(e)}},{key:"preMultiplySelf",value:function(t){multiply$3(this.value,t.toFloat32Array(),this.value)}},{key:"postMultiplySelf",value:function(t){multiply$3(this.value,this.value,t.toFloat32Array())}},{key:"invert",value:function(){var e=create$3();return invert(e,this.value),new t(e,this.multiplicationType)}},{key:"invertSelf",value:function(){invert(this.value,this.value)}},{key:"decompose",value:function(){return{translate:{x:this.tx,y:this.ty},rotate:{angle:Math.atan2(this.b,this.a)},skew:{angleX:Math.tan(this.c),angleY:Math.tan(this.b)},scale:{x:Math.sqrt(this.a*this.a+this.c*this.c),y:Math.sqrt(this.d*this.d+this.b*this.b)},matrix:this.toJSON()}}},{key:"transformPoint",value:function(t){return Point$3.fromPoint(t).transform(this)}},{key:"toFloat32Array",value:function(){return this.value}},{key:"toJSON",value:function(){return{a:this.a,b:this.b,c:this.c,d:this.d,tx:this.tx,ty:this.ty}}},{key:"toString",value:function(t){if(t){var e=function(t){return((t<0?"":" ")+t.toPrecision(6)).substring(0,8)};return" Matrix 4x4\n"+"-".repeat(39)+"\n".concat(e(this.m11),", ").concat(e(this.m21),", ").concat(e(this.m31),", ").concat(e(this.m41))+"\n".concat(e(this.m12),", ").concat(e(this.m22),", ").concat(e(this.m32),", ").concat(e(this.m42))+"\n".concat(e(this.m13),", ").concat(e(this.m23),", ").concat(e(this.m33),", ").concat(e(this.m43))+"\n".concat(e(this.m14),", ").concat(e(this.m24),", ").concat(e(this.m34),", ").concat(e(this.m44))}return this.is2D?"matrix(".concat(this.a,", ").concat(this.b,", ").concat(this.c,", ").concat(this.d,", ").concat(this.tx,", ").concat(this.ty,")"):"matrix3d(".concat(this.m11,", ").concat(this.m12,", ").concat(this.m13,", ").concat(this.m14,", ").concat(this.m21,", ").concat(this.m22,", ").concat(this.m23,", ").concat(this.m24,", ").concat(this.m31,", ").concat(this.m32,", ").concat(this.m33,", ").concat(this.m34,", ").concat(this.m41,", ").concat(this.m42,", ").concat(this.m43,", ").concat(this.m44,")")}}],[{key:"fromString",value:function(e,r){var n=create$3();if("none"!=e){var i=e.substring(0,e.indexOf("("));e=e.substring(e.indexOf("(")+1,e.indexOf(")")).split(/,\s*/g),"matrix3d"==i?(n[0]=parseFloat(e[0]),n[1]=parseFloat(e[1]),n[2]=parseFloat(e[2]),n[3]=parseFloat(e[3]),n[4]=parseFloat(e[4]),n[5]=parseFloat(e[5]),n[6]=parseFloat(e[6]),n[7]=parseFloat(e[7]),n[8]=parseFloat(e[8]),n[9]=parseFloat(e[9]),n[10]=parseFloat(e[10]),n[11]=parseFloat(e[11]),n[12]=parseFloat(e[12]),n[13]=parseFloat(e[13]),n[14]=parseFloat(e[14]),n[15]=parseFloat(e[15])):(n[INDEX_2D.a]=parseFloat(e[0]),n[INDEX_2D.b]=parseFloat(e[1]),n[INDEX_2D.c]=parseFloat(e[2]),n[INDEX_2D.d]=parseFloat(e[3]),n[INDEX_2D.tx]=parseFloat(e[4]),n[INDEX_2D.ty]=parseFloat(e[5]))}return new t(n,r)}},{key:"fromMatrix",value:function(e,r){if(!e)throw new Error("data not found, Matrix instance creation failed");if("function"==typeof e)throw new Error("data type function is not allowed");if(e instanceof t)return e;if(Array.isArray(e)&&(e=new Float32Array(e)),e instanceof Float32Array)return new t(e,r);if("string"==typeof e)return t.fromString(e,r);var n=create$3(),i=Object.assign({},e);return isFinite(e.a)&&(i.m11=e.a),isFinite(e.b)&&(i.m12=e.b),isFinite(e.c)&&(i.m21=e.c),isFinite(e.d)&&(i.m22=e.d),isFinite(e.tx)?i.m41=e.tx:isFinite(e.e)?i.m41=e.e:isFinite(e.dx)&&(i.m41=e.dx),isFinite(e.ty)?i.m42=e.ty:isFinite(e.f)?i.m42=e.f:isFinite(e.dy)&&(i.m42=e.dy),isFinite(i.m11)&&(n[0]=i.m11),isFinite(i.m12)&&(n[1]=i.m12),isFinite(i.m13)&&(n[2]=i.m13),isFinite(i.m14)&&(n[3]=i.m14),isFinite(i.m21)&&(n[4]=i.m21),isFinite(i.m22)&&(n[5]=i.m22),isFinite(i.m23)&&(n[6]=i.m23),isFinite(i.m24)&&(n[7]=i.m24),isFinite(i.m31)&&(n[8]=i.m31),isFinite(i.m32)&&(n[9]=i.m32),isFinite(i.m33)&&(n[10]=i.m33),isFinite(i.m34)&&(n[11]=i.m34),isFinite(i.m41)&&(n[12]=i.m41),isFinite(i.m42)&&(n[13]=i.m42),isFinite(i.m43)&&(n[14]=i.m43),isFinite(i.m44)&&(n[15]=i.m44),new t(n,r||e.multiplicationType)}},{key:"fromTranslate",value:function(e){var r=isFinite(e)?{tx:e,ty:e}:{tx:e.x,ty:e.y};return t.fromMatrix(r)}},{key:"fromRotate",value:function(e,r){var n=Math.sin(e),i=Math.cos(e),o={a:i,b:n,c:-n,d:i};return r&&(o.tx=r.x-r.x*i+r.y*n,o.ty=r.y-r.x*n-r.y*i),t.fromMatrix(o)}},{key:"fromScale",value:function(e,r){isFinite(e)&&(e={x:e,y:e});var n={a:e.x,d:e.y};return r&&(n.tx=r.x-r.x*e.x,n.ty=r.y-r.y*e.y),t.fromMatrix(n)}},{key:"fromPoints",value:function(e,r){if(!Array.isArray(e)||!Array.isArray(r))throw new Error("Expected input type Array requirement not satisfied");if(3!=e.length||3!=r.length)throw new Error("Expected input size 3 requirement not satisfied");var n=t.fromMatrix({m11:e[0].x,m21:e[1].x,m31:e[2].x,m12:e[0].y,m22:e[1].y,m32:e[2].y,m13:1,m23:1,m33:1}),i=t.fromMatrix({m11:r[0].x,m21:r[1].x,m31:r[2].x,m12:r[0].y,m22:r[1].y,m32:r[2].y,m13:1,m23:1,m33:1}),o=n.invert().preMultiply(i);return t.fromMatrix({a:o.m11,b:o.m12,c:o.m21,d:o.m22,tx:o.m31,ty:o.m32})}},{key:"multiply",value:function(e,r){var n=create$3();return multiply$3(n,e.value,r.value),new t(n)}}]),t}();function _createSuper$p(t){var e=_isNativeReflectConstruct$p();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$p(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}Matrix.MultiplicationType=Object.freeze({PRE:"PRE",POST:"POST"});var PathPoint=function(t){_inherits$1(r,t);var e=_createSuper$p(r);function r(t,n,i){var o,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return _classCallCheck$1(this,r),(o=e.call(this,t,n,i)).red=s.red,o.green=s.green,o.blue=s.blue,o.alpha=s.alpha,o.size=s.size||r.defaults.size,o.rotation=s.rotation||r.defaults.rotation,o.scaleX=s.scaleX||r.defaults.scaleX,o.scaleY=s.scaleY||r.defaults.scaleY,o.scaleZ=isFinite(i)?s.scaleZ||r.defaults.scaleZ:void 0,o.offsetX=s.offsetX||r.defaults.offsetX,o.offsetY=s.offsetY||r.defaults.offsetY,o.offsetZ=isFinite(i)?s.offsetZ||r.defaults.offsetZ:void 0,o.dX,o.dY,o}return _createClass$1(r,[{key:"fill",value:function(t,e,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o={},s=t*n.length;n.forEach((function(t,n){return r.setProperty(o,t,e[s+n])})),this.x=o.x,this.y=o.y,this.z=o.z,this.red=isFinite(o.red)?o.red:i.red,this.green=isFinite(o.green)?o.green:i.green,this.blue=isFinite(o.blue)?o.blue:i.blue,this.alpha=isFinite(o.alpha)?o.alpha:i.alpha,this.size=o.size||i.size||r.defaults.size,this.rotation=o.rotation||i.rotation||r.defaults.rotation,this.scaleX=o.scaleX||i.scaleX||r.defaults.scaleX,this.scaleY=o.scaleY||i.scaleY||r.defaults.scaleY,this.scaleZ=isFinite(o.z)?o.scaleZ||i.scaleZ||r.defaults.scaleZ:void 0,this.offsetX=o.offsetX||i.offsetX||r.defaults.offsetX,this.offsetY=o.offsetY||i.offsetY||r.defaults.offsetY,this.offsetZ=isFinite(o.z)?o.offsetZ||i.offsetZ||r.defaults.offsetZ:void 0,this.dX=o.dX,this.dY=o.dY}},{key:"getProperty",value:function(t){switch(t){case r.Property.X:return this.x;case r.Property.Y:return this.y;case r.Property.Z:return this.z;case r.Property.RED:return this.red;case r.Property.GREEN:return this.green;case r.Property.BLUE:return this.blue;case r.Property.ALPHA:return this.alpha;case r.Property.SIZE:return this.size;case r.Property.ROTATION:return this.rotation;case r.Property.SCALE_X:return this.scaleX;case r.Property.SCALE_Y:return this.scaleY;case r.Property.SCALE_Z:return this.scaleZ;case r.Property.OFFSET_X:return this.offsetX;case r.Property.OFFSET_Y:return this.offsetY;case r.Property.OFFSET_Z:return this.offsetZ;case r.Property.D_X:return this.dX;case r.Property.D_Y:return this.dY;default:throw console.warn(t),new Error("Invalid property found")}}},{key:"setProperty",value:function(t,e){r.setProperty(this,t,e)}},{key:"transform",value:function(t){if(!(t instanceof Matrix))throw new Error("matrix is instance of ".concat(t.constructor.name," - it should be instance of Matrix. Use Matrix.fromMatrix method to convert."));var e=t.scaleX,r=t.rotation;this.transformSelf(t),this.size*=e,this.rotation+=r}},{key:"toArray",value:function(t){var e=this;return t.map((function(t){var r=e.getProperty(t);if(null==r||isNaN(r))throw new Error("Property ".concat(t.name," has invalid value ").concat(r));return r}))}},{key:"toJSON",value:function(){var t=this,e={};return r.Property.values.forEach((function(r){var n=t.getProperty(r);null!=n&&isFinite(n)&&(e[r.name]=t.getProperty(r))})),e}}],[{key:"createInstance",value:function(t,e,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=new r(0,0,t.includes(r.Property.Z)?0:void 0);return n&&o.fill(i,n,t,e),o}},{key:"setProperty",value:function(t,e,n){switch(e){case r.Property.X:t.x=n;break;case r.Property.Y:t.y=n;break;case r.Property.Z:t.z=n;break;case r.Property.RED:t.red=n;break;case r.Property.GREEN:t.green=n;break;case r.Property.BLUE:t.blue=n;break;case r.Property.ALPHA:t.alpha=n;break;case r.Property.SIZE:t.size=n;break;case r.Property.ROTATION:t.rotation=n;break;case r.Property.SCALE_X:t.scaleX=n;break;case r.Property.SCALE_Y:t.scaleY=n;break;case r.Property.SCALE_Z:t.scaleZ=n;break;case r.Property.OFFSET_X:t.offsetX=n;break;case r.Property.OFFSET_Y:t.offsetY=n;break;case r.Property.OFFSET_Z:t.offsetZ=n;break;case r.Property.D_X:t.dX=n;break;case r.Property.D_Y:t.dY=n;break;default:throw console.warn(e),new Error("Invalid property found")}}}]),r}(Point$3);_defineProperty(PathPoint,"defaults",{size:1,rotation:0,scaleX:1,scaleY:1,scaleZ:1,offsetX:0,offsetY:0,offsetZ:0}),Object.defineEnum(PathPoint,"Property",["X","Y","Z","RED","GREEN","BLUE","ALPHA","SIZE","ROTATION","SCALE_X","SCALE_Y","SCALE_Z","OFFSET_X","OFFSET_Y","OFFSET_Z","D_X","D_Y"]);var AVLNode=function(){function t(e){_classCallCheck$1(this,t),this.key=e,this.height=1}return _createClass$1(t,[{key:"leftRotate",value:function(){var e=this.right,r=e.left;return e.left=this,this.right=r,this.height=Math.max(t.height(this.left),t.height(this.right))+1,e.height=Math.max(t.height(e.left),t.height(e.right))+1,e}},{key:"rightRotate",value:function(){var e=this.left,r=e.right;return e.right=this,this.left=r,this.height=Math.max(t.height(this.left),t.height(this.right))+1,e.height=Math.max(t.height(e.left),t.height(e.right))+1,e}},{key:"getBalanceFactor",value:function(){return t.height(this.left)-t.height(this.right)}}],[{key:"height",value:function(t){return t?t.height:0}},{key:"minValue",value:function(t){if(t){for(var e=t;e.left;)e=e.left;return e.key}}},{key:"maxValue",value:function(t){if(t){for(var e=t;e.right;)e=e.right;return e.key}}}]),t}(),AVLTree=function(){function t(){_classCallCheck$1(this,t),this.count=0,this.hasKey=!1,this.root}return _createClass$1(t,[{key:"min",value:function(){return AVLNode.minValue(this.root)}},{key:"max",value:function(){return AVLNode.maxValue(this.root)}},{key:"add",value:function(t){return this.hasKey=!1,this.root=this.insertNode(this.root,t),this.hasKey||this.count++,!this.hasKey}},{key:"insertNode",value:function(t,e){if(!t)return new AVLNode(e);if(e<t.key)t.left=this.insertNode(t.left,e);else{if(!(e>t.key))return this.hasKey=!0,t;t.right=this.insertNode(t.right,e)}if(!this.hasKey){t.height=1+Math.max(AVLNode.height(t.left),AVLNode.height(t.right));var r=t.getBalanceFactor();if(r>1){if(e<t.left.key)return t.rightRotate();if(e>t.left.key)return t.left=t.left.leftRotate(),t.rightRotate()}else if(r<-1){if(e>t.right.key)return t.leftRotate();if(e<t.right.key)return t.right=t.right.rightRotate(),t.leftRotate()}}return t}},{key:"contains",value:function(t){return this.containsNode(this.root,t)}},{key:"containsNode",value:function(t,e){return!!t&&(e<t.key?this.containsNode(t.left,e):!(e>t.key)||this.containsNode(t.right,e))}},{key:"printTree",value:function(){if(this.root)for(var t=[this.root],e=this.root.height;t.length>0;){var r=t.shift();e!=r.height&&console.log("-"),console.log("".concat(r.key," with height: ").concat(r.height,", balance: ").concat(r.getBalanceFactor())),e=r.height;var n=r.left,i=r.right;n&&t.push(n),i&&t.push(i)}}},{key:"toArray",value:function(){var e=[];return t.fillArray(e,this.root),e}}],[{key:"fillArray",value:function(t,e){e&&(this.fillArray(t,e.left),t.push(e.key),this.fillArray(t,e.right))}}]),t}(),SortedSet=function(){function t(){var e=this;_classCallCheck$1(this,t),this.tree=new AVLTree,Object.defineProperty(this,"length",{get:function(){return e.tree.count},enumerable:!0})}return _createClass$1(t,[{key:"clear",value:function(){this.tree=new AVLTree}},{key:"add",value:function(t){return this.tree.add(t)}},{key:"includes",value:function(t){return this.tree.contains(t)}},{key:"min",value:function(){return this.tree.min()}},{key:"max",value:function(){return this.tree.max()}},{key:"toArray",value:function(){return this.tree.toArray()}}]),t}();function _classApplyDescriptorSet(t,e,r){if(e.set)e.set.call(t,r);else{if(!e.writable)throw new TypeError("attempted to set read only private field");e.value=r}}function _classExtractFieldDescriptor(t,e,r){if(!e.has(t))throw new TypeError("attempted to "+r+" private field on non-instance");return e.get(t)}function _classPrivateFieldSet(t,e,r){return _classApplyDescriptorSet(t,_classExtractFieldDescriptor(t,e,"set"),r),r}function _classApplyDescriptorGet(t,e){return e.get?e.get.call(t):e.value}function _classPrivateFieldGet(t,e){return _classApplyDescriptorGet(t,_classExtractFieldDescriptor(t,e,"get"))}var poly2tri$1={},version$1="1.5.0",require$$0={version:version$1};function toStringBase(t){return"("+t.x+";"+t.y+")"}function toString(t){var e=t.toString();return"[object Object]"===e?toStringBase(t):e}function compare(t,e){return t.y===e.y?t.x-e.x:t.y-e.y}function equals(t,e){return t.x===e.x&&t.y===e.y}var xy$3={toString:toString,toStringBase:toStringBase,compare:compare,equals:equals},xy$2=xy$3,PointError$2=function(t,e){this.name="PointError",this.points=e=e||[],this.message=t||"Invalid Points!";for(var r=0;r<e.length;r++)this.message+=" "+xy$2.toString(e[r])};PointError$2.prototype=new Error,PointError$2.prototype.constructor=PointError$2;var pointerror=PointError$2,xy$1=xy$3,Point$2=function(t,e){this.x=+t||0,this.y=+e||0,this._p2t_edge_list=null};Point$2.prototype.toString=function(){return xy$1.toStringBase(this)},Point$2.prototype.toJSON=function(){return{x:this.x,y:this.y}},Point$2.prototype.clone=function(){return new Point$2(this.x,this.y)},Point$2.prototype.set_zero=function(){return this.x=0,this.y=0,this},Point$2.prototype.set=function(t,e){return this.x=+t||0,this.y=+e||0,this},Point$2.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},Point$2.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},Point$2.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},Point$2.prototype.mul=function(t){return this.x*=t,this.y*=t,this},Point$2.prototype.length=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},Point$2.prototype.normalize=function(){var t=this.length();return this.x/=t,this.y/=t,t},Point$2.prototype.equals=function(t){return this.x===t.x&&this.y===t.y},Point$2.negate=function(t){return new Point$2(-t.x,-t.y)},Point$2.add=function(t,e){return new Point$2(t.x+e.x,t.y+e.y)},Point$2.sub=function(t,e){return new Point$2(t.x-e.x,t.y-e.y)},Point$2.mul=function(t,e){return new Point$2(t*e.x,t*e.y)},Point$2.cross=function(t,e){return"number"==typeof t?"number"==typeof e?t*e:new Point$2(-t*e.y,t*e.x):"number"==typeof e?new Point$2(e*t.y,-e*t.x):t.x*e.y-t.y*e.x},Point$2.toString=xy$1.toString,Point$2.compare=xy$1.compare,Point$2.cmp=xy$1.compare,Point$2.equals=xy$1.equals,Point$2.dot=function(t,e){return t.x*e.x+t.y*e.y};var point=Point$2,xy=xy$3,Triangle$2=function(t,e,r){this.points_=[t,e,r],this.neighbors_=[null,null,null],this.interior_=!1,this.constrained_edge=[!1,!1,!1],this.delaunay_edge=[!1,!1,!1]},p2s=xy.toString;Triangle$2.prototype.toString=function(){return"["+p2s(this.points_[0])+p2s(this.points_[1])+p2s(this.points_[2])+"]"},Triangle$2.prototype.getPoint=function(t){return this.points_[t]},Triangle$2.prototype.GetPoint=Triangle$2.prototype.getPoint,Triangle$2.prototype.getPoints=function(){return this.points_},Triangle$2.prototype.getNeighbor=function(t){return this.neighbors_[t]},Triangle$2.prototype.containsPoint=function(t){var e=this.points_;return t===e[0]||t===e[1]||t===e[2]},Triangle$2.prototype.containsEdge=function(t){return this.containsPoint(t.p)&&this.containsPoint(t.q)},Triangle$2.prototype.containsPoints=function(t,e){return this.containsPoint(t)&&this.containsPoint(e)},Triangle$2.prototype.isInterior=function(){return this.interior_},Triangle$2.prototype.setInterior=function(t){return this.interior_=t,this},Triangle$2.prototype.markNeighborPointers=function(t,e,r){var n=this.points_;if(t===n[2]&&e===n[1]||t===n[1]&&e===n[2])this.neighbors_[0]=r;else if(t===n[0]&&e===n[2]||t===n[2]&&e===n[0])this.neighbors_[1]=r;else{if(!(t===n[0]&&e===n[1]||t===n[1]&&e===n[0]))throw new Error("poly2tri Invalid Triangle.markNeighborPointers() call");this.neighbors_[2]=r}},Triangle$2.prototype.markNeighbor=function(t){var e=this.points_;t.containsPoints(e[1],e[2])?(this.neighbors_[0]=t,t.markNeighborPointers(e[1],e[2],this)):t.containsPoints(e[0],e[2])?(this.neighbors_[1]=t,t.markNeighborPointers(e[0],e[2],this)):t.containsPoints(e[0],e[1])&&(this.neighbors_[2]=t,t.markNeighborPointers(e[0],e[1],this))},Triangle$2.prototype.clearNeighbors=function(){this.neighbors_[0]=null,this.neighbors_[1]=null,this.neighbors_[2]=null},Triangle$2.prototype.clearDelaunayEdges=function(){this.delaunay_edge[0]=!1,this.delaunay_edge[1]=!1,this.delaunay_edge[2]=!1},Triangle$2.prototype.pointCW=function(t){var e=this.points_;return t===e[0]?e[2]:t===e[1]?e[0]:t===e[2]?e[1]:null},Triangle$2.prototype.pointCCW=function(t){var e=this.points_;return t===e[0]?e[1]:t===e[1]?e[2]:t===e[2]?e[0]:null},Triangle$2.prototype.neighborCW=function(t){return t===this.points_[0]?this.neighbors_[1]:t===this.points_[1]?this.neighbors_[2]:this.neighbors_[0]},Triangle$2.prototype.neighborCCW=function(t){return t===this.points_[0]?this.neighbors_[2]:t===this.points_[1]?this.neighbors_[0]:this.neighbors_[1]},Triangle$2.prototype.getConstrainedEdgeCW=function(t){return t===this.points_[0]?this.constrained_edge[1]:t===this.points_[1]?this.constrained_edge[2]:this.constrained_edge[0]},Triangle$2.prototype.getConstrainedEdgeCCW=function(t){return t===this.points_[0]?this.constrained_edge[2]:t===this.points_[1]?this.constrained_edge[0]:this.constrained_edge[1]},Triangle$2.prototype.getConstrainedEdgeAcross=function(t){return t===this.points_[0]?this.constrained_edge[0]:t===this.points_[1]?this.constrained_edge[1]:this.constrained_edge[2]},Triangle$2.prototype.setConstrainedEdgeCW=function(t,e){t===this.points_[0]?this.constrained_edge[1]=e:t===this.points_[1]?this.constrained_edge[2]=e:this.constrained_edge[0]=e},Triangle$2.prototype.setConstrainedEdgeCCW=function(t,e){t===this.points_[0]?this.constrained_edge[2]=e:t===this.points_[1]?this.constrained_edge[0]=e:this.constrained_edge[1]=e},Triangle$2.prototype.getDelaunayEdgeCW=function(t){return t===this.points_[0]?this.delaunay_edge[1]:t===this.points_[1]?this.delaunay_edge[2]:this.delaunay_edge[0]},Triangle$2.prototype.getDelaunayEdgeCCW=function(t){return t===this.points_[0]?this.delaunay_edge[2]:t===this.points_[1]?this.delaunay_edge[0]:this.delaunay_edge[1]},Triangle$2.prototype.setDelaunayEdgeCW=function(t,e){t===this.points_[0]?this.delaunay_edge[1]=e:t===this.points_[1]?this.delaunay_edge[2]=e:this.delaunay_edge[0]=e},Triangle$2.prototype.setDelaunayEdgeCCW=function(t,e){t===this.points_[0]?this.delaunay_edge[2]=e:t===this.points_[1]?this.delaunay_edge[0]=e:this.delaunay_edge[1]=e},Triangle$2.prototype.neighborAcross=function(t){return t===this.points_[0]?this.neighbors_[0]:t===this.points_[1]?this.neighbors_[1]:this.neighbors_[2]},Triangle$2.prototype.oppositePoint=function(t,e){var r=t.pointCW(e);return this.pointCW(r)},Triangle$2.prototype.legalize=function(t,e){var r=this.points_;if(t===r[0])r[1]=r[0],r[0]=r[2],r[2]=e;else if(t===r[1])r[2]=r[1],r[1]=r[0],r[0]=e;else{if(t!==r[2])throw new Error("poly2tri Invalid Triangle.legalize() call");r[0]=r[2],r[2]=r[1],r[1]=e}},Triangle$2.prototype.index=function(t){var e=this.points_;if(t===e[0])return 0;if(t===e[1])return 1;if(t===e[2])return 2;throw new Error("poly2tri Invalid Triangle.index() call")},Triangle$2.prototype.edgeIndex=function(t,e){var r=this.points_;if(t===r[0]){if(e===r[1])return 2;if(e===r[2])return 1}else if(t===r[1]){if(e===r[2])return 0;if(e===r[0])return 2}else if(t===r[2]){if(e===r[0])return 1;if(e===r[1])return 0}return-1},Triangle$2.prototype.markConstrainedEdgeByIndex=function(t){this.constrained_edge[t]=!0},Triangle$2.prototype.markConstrainedEdgeByEdge=function(t){this.markConstrainedEdgeByPoints(t.p,t.q)},Triangle$2.prototype.markConstrainedEdgeByPoints=function(t,e){var r=this.points_;e===r[0]&&t===r[1]||e===r[1]&&t===r[0]?this.constrained_edge[2]=!0:e===r[0]&&t===r[2]||e===r[2]&&t===r[0]?this.constrained_edge[1]=!0:(e===r[1]&&t===r[2]||e===r[2]&&t===r[1])&&(this.constrained_edge[0]=!0)};var triangle=Triangle$2,sweep$1={};function assert$1(t,e){if(!t)throw new Error(e||"Assert Failed")}var assert_1=assert$1,advancingfront={exports:{}},Node$2=function(t,e){this.point=t,this.triangle=e||null,this.next=null,this.prev=null,this.value=t.x},AdvancingFront$1=function(t,e){this.head_=t,this.tail_=e,this.search_node_=t};AdvancingFront$1.prototype.head=function(){return this.head_},AdvancingFront$1.prototype.setHead=function(t){this.head_=t},AdvancingFront$1.prototype.tail=function(){return this.tail_},AdvancingFront$1.prototype.setTail=function(t){this.tail_=t},AdvancingFront$1.prototype.search=function(){return this.search_node_},AdvancingFront$1.prototype.setSearch=function(t){this.search_node_=t},AdvancingFront$1.prototype.findSearchNode=function(){return this.search_node_},AdvancingFront$1.prototype.locateNode=function(t){var e=this.search_node_;if(t<e.value){for(;e=e.prev;)if(t>=e.value)return this.search_node_=e,e}else for(;e=e.next;)if(t<e.value)return this.search_node_=e.prev,e.prev;return null},AdvancingFront$1.prototype.locatePoint=function(t){var e=t.x,r=this.findSearchNode(e),n=r.point.x;if(e===n){if(t!==r.point)if(t===r.prev.point)r=r.prev;else{if(t!==r.next.point)throw new Error("poly2tri Invalid AdvancingFront.locatePoint() call");r=r.next}}else if(e<n)for(;(r=r.prev)&&t!==r.point;);else for(;(r=r.next)&&t!==r.point;);return r&&(this.search_node_=r),r},advancingfront.exports=AdvancingFront$1,advancingfront.exports.Node=Node$2;var utils$2={},EPSILON$1=1e-12;utils$2.EPSILON=EPSILON$1;var Orientation$1={CW:1,CCW:-1,COLLINEAR:0};function orient2d$1(t,e,r){var n=(t.x-r.x)*(e.y-r.y)-(t.y-r.y)*(e.x-r.x);return n>-EPSILON$1&&n<EPSILON$1?Orientation$1.COLLINEAR:n>0?Orientation$1.CCW:Orientation$1.CW}function inScanArea$1(t,e,r,n){return!((t.x-e.x)*(n.y-e.y)-(n.x-e.x)*(t.y-e.y)>=-EPSILON$1)&&!((t.x-r.x)*(n.y-r.y)-(n.x-r.x)*(t.y-r.y)<=EPSILON$1)}function isAngleObtuse$1(t,e,r){var n=e.x-t.x,i=e.y-t.y;return n*(r.x-t.x)+i*(r.y-t.y)<0}utils$2.Orientation=Orientation$1,utils$2.orient2d=orient2d$1,utils$2.inScanArea=inScanArea$1,utils$2.isAngleObtuse=isAngleObtuse$1;var assert=assert_1,PointError$1=pointerror,Triangle$1=triangle,Node$1=advancingfront.exports.Node,utils$1=utils$2,EPSILON=utils$1.EPSILON,Orientation=utils$1.Orientation,orient2d=utils$1.orient2d,inScanArea=utils$1.inScanArea,isAngleObtuse=utils$1.isAngleObtuse;function triangulate(t){t.initTriangulation(),t.createAdvancingFront(),sweepPoints(t),finalizationPolygon(t)}function sweepPoints(t){var e,r=t.pointCount();for(e=1;e<r;++e)for(var n=t.getPoint(e),i=pointEvent(t,n),o=n._p2t_edge_list,s=0;o&&s<o.length;++s)edgeEventByEdge(t,o[s],i)}function finalizationPolygon(t){for(var e=t.front().head().next.triangle,r=t.front().head().next.point;!e.getConstrainedEdgeCW(r);)e=e.neighborCCW(r);t.meshClean(e)}function pointEvent(t,e){var r=t.locateNode(e),n=newFrontTriangle(t,e,r);return e.x<=r.point.x+EPSILON&&fill(t,r),fillAdvancingFront(t,n),n}function edgeEventByEdge(t,e,r){t.edge_event.constrained_edge=e,t.edge_event.right=e.p.x>e.q.x,isEdgeSideOfTriangle(r.triangle,e.p,e.q)||(fillEdgeEvent(t,e,r),edgeEventByPoints(t,e.p,e.q,r.triangle,e.q))}function edgeEventByPoints(t,e,r,n,i){if(!isEdgeSideOfTriangle(n,e,r)){var o=n.pointCCW(i),s=orient2d(r,o,e);if(s===Orientation.COLLINEAR)throw new PointError$1("poly2tri EdgeEvent: Collinear not supported!",[r,o,e]);var a=n.pointCW(i),l=orient2d(r,a,e);if(l===Orientation.COLLINEAR)throw new PointError$1("poly2tri EdgeEvent: Collinear not supported!",[r,a,e]);s===l?edgeEventByPoints(t,e,r,n=s===Orientation.CW?n.neighborCCW(i):n.neighborCW(i),i):flipEdgeEvent(t,e,r,n,i)}}function isEdgeSideOfTriangle(t,e,r){var n=t.edgeIndex(e,r);if(-1!==n){t.markConstrainedEdgeByIndex(n);var i=t.getNeighbor(n);return i&&i.markConstrainedEdgeByPoints(e,r),!0}return!1}function newFrontTriangle(t,e,r){var n=new Triangle$1(e,r.point,r.next.point);n.markNeighbor(r.triangle),t.addToMap(n);var i=new Node$1(e);return i.next=r.next,i.prev=r,r.next.prev=i,r.next=i,legalize(t,n)||t.mapTriangleToNodes(n),i}function fill(t,e){var r=new Triangle$1(e.prev.point,e.point,e.next.point);r.markNeighbor(e.prev.triangle),r.markNeighbor(e.triangle),t.addToMap(r),e.prev.next=e.next,e.next.prev=e.prev,legalize(t,r)||t.mapTriangleToNodes(r)}function fillAdvancingFront(t,e){for(var r=e.next;r.next&&!isAngleObtuse(r.point,r.next.point,r.prev.point);)fill(t,r),r=r.next;for(r=e.prev;r.prev&&!isAngleObtuse(r.point,r.next.point,r.prev.point);)fill(t,r),r=r.prev;e.next&&e.next.next&&isBasinAngleRight(e)&&fillBasin(t,e)}function isBasinAngleRight(t){var e=t.point.x-t.next.next.point.x,r=t.point.y-t.next.next.point.y;return assert(r>=0,"unordered y"),e>=0||Math.abs(e)<r}function legalize(t,e){for(var r=0;r<3;++r)if(!e.delaunay_edge[r]){var n=e.getNeighbor(r);if(n){var i=e.getPoint(r),o=n.oppositePoint(e,i),s=n.index(o);if(n.constrained_edge[s]||n.delaunay_edge[s]){e.constrained_edge[r]=n.constrained_edge[s];continue}if(inCircle(i,e.pointCCW(i),e.pointCW(i),o)){e.delaunay_edge[r]=!0,n.delaunay_edge[s]=!0,rotateTrianglePair(e,i,n,o);var a=!legalize(t,e);return a&&t.mapTriangleToNodes(e),(a=!legalize(t,n))&&t.mapTriangleToNodes(n),e.delaunay_edge[r]=!1,n.delaunay_edge[s]=!1,!0}}}return!1}function inCircle(t,e,r,n){var i=t.x-n.x,o=t.y-n.y,s=e.x-n.x,a=e.y-n.y,l=i*a-s*o;if(l<=0)return!1;var u=r.x-n.x,h=r.y-n.y,c=u*o-i*h;return!(c<=0)&&(i*i+o*o)*(s*h-u*a)+(s*s+a*a)*c+(u*u+h*h)*l>0}function rotateTrianglePair(t,e,r,n){var i,o,s,a,l,u,h,c,p,f,d,y;i=t.neighborCCW(e),o=t.neighborCW(e),s=r.neighborCCW(n),a=r.neighborCW(n),l=t.getConstrainedEdgeCCW(e),u=t.getConstrainedEdgeCW(e),h=r.getConstrainedEdgeCCW(n),c=r.getConstrainedEdgeCW(n),p=t.getDelaunayEdgeCCW(e),f=t.getDelaunayEdgeCW(e),d=r.getDelaunayEdgeCCW(n),y=r.getDelaunayEdgeCW(n),t.legalize(e,n),r.legalize(n,e),r.setDelaunayEdgeCCW(e,p),t.setDelaunayEdgeCW(e,f),t.setDelaunayEdgeCCW(n,d),r.setDelaunayEdgeCW(n,y),r.setConstrainedEdgeCCW(e,l),t.setConstrainedEdgeCW(e,u),t.setConstrainedEdgeCCW(n,h),r.setConstrainedEdgeCW(n,c),t.clearNeighbors(),r.clearNeighbors(),i&&r.markNeighbor(i),o&&t.markNeighbor(o),s&&t.markNeighbor(s),a&&r.markNeighbor(a),t.markNeighbor(r)}function fillBasin(t,e){for(orient2d(e.point,e.next.point,e.next.next.point)===Orientation.CCW?t.basin.left_node=e.next.next:t.basin.left_node=e.next,t.basin.bottom_node=t.basin.left_node;t.basin.bottom_node.next&&t.basin.bottom_node.point.y>=t.basin.bottom_node.next.point.y;)t.basin.bottom_node=t.basin.bottom_node.next;if(t.basin.bottom_node!==t.basin.left_node){for(t.basin.right_node=t.basin.bottom_node;t.basin.right_node.next&&t.basin.right_node.point.y<t.basin.right_node.next.point.y;)t.basin.right_node=t.basin.right_node.next;t.basin.right_node!==t.basin.bottom_node&&(t.basin.width=t.basin.right_node.point.x-t.basin.left_node.point.x,t.basin.left_highest=t.basin.left_node.point.y>t.basin.right_node.point.y,fillBasinReq(t,t.basin.bottom_node))}}function fillBasinReq(t,e){if(!isShallow(t,e)&&(fill(t,e),e.prev!==t.basin.left_node||e.next!==t.basin.right_node)){if(e.prev===t.basin.left_node){if(orient2d(e.point,e.next.point,e.next.next.point)===Orientation.CW)return;e=e.next}else if(e.next===t.basin.right_node){if(orient2d(e.point,e.prev.point,e.prev.prev.point)===Orientation.CCW)return;e=e.prev}else e=e.prev.point.y<e.next.point.y?e.prev:e.next;fillBasinReq(t,e)}}function isShallow(t,e){var r;return r=t.basin.left_highest?t.basin.left_node.point.y-e.point.y:t.basin.right_node.point.y-e.point.y,t.basin.width>r}function fillEdgeEvent(t,e,r){t.edge_event.right?fillRightAboveEdgeEvent(t,e,r):fillLeftAboveEdgeEvent(t,e,r)}function fillRightAboveEdgeEvent(t,e,r){for(;r.next.point.x<e.p.x;)orient2d(e.q,r.next.point,e.p)===Orientation.CCW?fillRightBelowEdgeEvent(t,e,r):r=r.next}function fillRightBelowEdgeEvent(t,e,r){r.point.x<e.p.x&&(orient2d(r.point,r.next.point,r.next.next.point)===Orientation.CCW?fillRightConcaveEdgeEvent(t,e,r):(fillRightConvexEdgeEvent(t,e,r),fillRightBelowEdgeEvent(t,e,r)))}function fillRightConcaveEdgeEvent(t,e,r){fill(t,r.next),r.next.point!==e.p&&orient2d(e.q,r.next.point,e.p)===Orientation.CCW&&orient2d(r.point,r.next.point,r.next.next.point)===Orientation.CCW&&fillRightConcaveEdgeEvent(t,e,r)}function fillRightConvexEdgeEvent(t,e,r){orient2d(r.next.point,r.next.next.point,r.next.next.next.point)===Orientation.CCW?fillRightConcaveEdgeEvent(t,e,r.next):orient2d(e.q,r.next.next.point,e.p)===Orientation.CCW&&fillRightConvexEdgeEvent(t,e,r.next)}function fillLeftAboveEdgeEvent(t,e,r){for(;r.prev.point.x>e.p.x;)orient2d(e.q,r.prev.point,e.p)===Orientation.CW?fillLeftBelowEdgeEvent(t,e,r):r=r.prev}function fillLeftBelowEdgeEvent(t,e,r){r.point.x>e.p.x&&(orient2d(r.point,r.prev.point,r.prev.prev.point)===Orientation.CW?fillLeftConcaveEdgeEvent(t,e,r):(fillLeftConvexEdgeEvent(t,e,r),fillLeftBelowEdgeEvent(t,e,r)))}function fillLeftConvexEdgeEvent(t,e,r){orient2d(r.prev.point,r.prev.prev.point,r.prev.prev.prev.point)===Orientation.CW?fillLeftConcaveEdgeEvent(t,e,r.prev):orient2d(e.q,r.prev.prev.point,e.p)===Orientation.CW&&fillLeftConvexEdgeEvent(t,e,r.prev)}function fillLeftConcaveEdgeEvent(t,e,r){fill(t,r.prev),r.prev.point!==e.p&&orient2d(e.q,r.prev.point,e.p)===Orientation.CW&&orient2d(r.point,r.prev.point,r.prev.prev.point)===Orientation.CW&&fillLeftConcaveEdgeEvent(t,e,r)}function flipEdgeEvent(t,e,r,n,i){var o=n.neighborAcross(i);assert(o,"FLIP failed due to missing triangle!");var s=o.oppositePoint(n,i);if(n.getConstrainedEdgeAcross(i)){var a=n.index(i);throw new PointError$1("poly2tri Intersecting Constraints",[i,s,n.getPoint((a+1)%3),n.getPoint((a+2)%3)])}inScanArea(i,n.pointCCW(i),n.pointCW(i),s)?(rotateTrianglePair(n,i,o,s),t.mapTriangleToNodes(n),t.mapTriangleToNodes(o),i===r&&s===e?r===t.edge_event.constrained_edge.q&&e===t.edge_event.constrained_edge.p&&(n.markConstrainedEdgeByPoints(e,r),o.markConstrainedEdgeByPoints(e,r),legalize(t,n),legalize(t,o)):flipEdgeEvent(t,e,r,n=nextFlipTriangle(t,orient2d(r,s,e),n,o,i,s),i)):(flipScanEdgeEvent(t,e,r,n,o,nextFlipPoint(e,r,o,s)),edgeEventByPoints(t,e,r,n,i))}function nextFlipTriangle(t,e,r,n,i,o){var s;return e===Orientation.CCW?(s=n.edgeIndex(i,o),n.delaunay_edge[s]=!0,legalize(t,n),n.clearDelaunayEdges(),r):(s=r.edgeIndex(i,o),r.delaunay_edge[s]=!0,legalize(t,r),r.clearDelaunayEdges(),n)}function nextFlipPoint(t,e,r,n){var i=orient2d(e,n,t);if(i===Orientation.CW)return r.pointCCW(n);if(i===Orientation.CCW)return r.pointCW(n);throw new PointError$1("poly2tri [Unsupported] nextFlipPoint: opposing point on constrained edge!",[e,n,t])}function flipScanEdgeEvent(t,e,r,n,i,o){var s=i.neighborAcross(o);assert(s,"FLIP failed due to missing triangle");var a=s.oppositePoint(i,o);inScanArea(r,n.pointCCW(r),n.pointCW(r),a)?flipEdgeEvent(t,r,a,s,a):flipScanEdgeEvent(t,e,r,n,s,nextFlipPoint(e,r,s,a))}sweep$1.triangulate=triangulate;var PointError=pointerror,Point$1=point,Triangle=triangle,sweep=sweep$1,AdvancingFront=advancingfront.exports,Node=AdvancingFront.Node,kAlpha=.3,Edge=function(t,e){if(this.p=t,this.q=e,t.y>e.y)this.q=t,this.p=e;else if(t.y===e.y)if(t.x>e.x)this.q=t,this.p=e;else if(t.x===e.x)throw new PointError("poly2tri Invalid Edge constructor: repeated points!",[t]);this.q._p2t_edge_list||(this.q._p2t_edge_list=[]),this.q._p2t_edge_list.push(this)},Basin=function(){this.left_node=null,this.bottom_node=null,this.right_node=null,this.width=0,this.left_highest=!1};Basin.prototype.clear=function(){this.left_node=null,this.bottom_node=null,this.right_node=null,this.width=0,this.left_highest=!1};var EdgeEvent=function(){this.constrained_edge=null,this.right=!1},SweepContext$1=function(t,e){e=e||{},this.triangles_=[],this.map_=[],this.points_=e.cloneArrays?t.slice(0):t,this.edge_list=[],this.pmin_=this.pmax_=null,this.front_=null,this.head_=null,this.tail_=null,this.af_head_=null,this.af_middle_=null,this.af_tail_=null,this.basin=new Basin,this.edge_event=new EdgeEvent,this.initEdges(this.points_)};SweepContext$1.prototype.addHole=function(t){this.initEdges(t);var e,r=t.length;for(e=0;e<r;e++)this.points_.push(t[e]);return this},SweepContext$1.prototype.AddHole=SweepContext$1.prototype.addHole,SweepContext$1.prototype.addHoles=function(t){var e,r=t.length;for(e=0;e<r;e++)this.initEdges(t[e]);return this.points_=this.points_.concat.apply(this.points_,t),this},SweepContext$1.prototype.addPoint=function(t){return this.points_.push(t),this},SweepContext$1.prototype.AddPoint=SweepContext$1.prototype.addPoint,SweepContext$1.prototype.addPoints=function(t){return this.points_=this.points_.concat(t),this},SweepContext$1.prototype.triangulate=function(){return sweep.triangulate(this),this},SweepContext$1.prototype.getBoundingBox=function(){return{min:this.pmin_,max:this.pmax_}},SweepContext$1.prototype.getTriangles=function(){return this.triangles_},SweepContext$1.prototype.GetTriangles=SweepContext$1.prototype.getTriangles,SweepContext$1.prototype.front=function(){return this.front_},SweepContext$1.prototype.pointCount=function(){return this.points_.length},SweepContext$1.prototype.head=function(){return this.head_},SweepContext$1.prototype.setHead=function(t){this.head_=t},SweepContext$1.prototype.tail=function(){return this.tail_},SweepContext$1.prototype.setTail=function(t){this.tail_=t},SweepContext$1.prototype.getMap=function(){return this.map_},SweepContext$1.prototype.initTriangulation=function(){var t,e=this.points_[0].x,r=this.points_[0].x,n=this.points_[0].y,i=this.points_[0].y,o=this.points_.length;for(t=1;t<o;t++){var s=this.points_[t];s.x>e&&(e=s.x),s.x<r&&(r=s.x),s.y>n&&(n=s.y),s.y<i&&(i=s.y)}this.pmin_=new Point$1(r,i),this.pmax_=new Point$1(e,n);var a=kAlpha*(e-r),l=kAlpha*(n-i);this.head_=new Point$1(e+a,i-l),this.tail_=new Point$1(r-a,i-l),this.points_.sort(Point$1.compare)},SweepContext$1.prototype.initEdges=function(t){var e,r=t.length;for(e=0;e<r;++e)this.edge_list.push(new Edge(t[e],t[(e+1)%r]))},SweepContext$1.prototype.getPoint=function(t){return this.points_[t]},SweepContext$1.prototype.addToMap=function(t){this.map_.push(t)},SweepContext$1.prototype.locateNode=function(t){return this.front_.locateNode(t.x)},SweepContext$1.prototype.createAdvancingFront=function(){var t,e,r,n=new Triangle(this.points_[0],this.tail_,this.head_);this.map_.push(n),t=new Node(n.getPoint(1),n),e=new Node(n.getPoint(0),n),r=new Node(n.getPoint(2)),this.front_=new AdvancingFront(t,r),t.next=e,e.next=r,e.prev=t,r.prev=e},SweepContext$1.prototype.removeNode=function(t){},SweepContext$1.prototype.mapTriangleToNodes=function(t){for(var e=0;e<3;++e)if(!t.getNeighbor(e)){var r=this.front_.locatePoint(t.pointCW(t.getPoint(e)));r&&(r.triangle=t)}},SweepContext$1.prototype.removeFromMap=function(t){var e,r=this.map_,n=r.length;for(e=0;e<n;e++)if(r[e]===t){r.splice(e,1);break}},SweepContext$1.prototype.meshClean=function(t){for(var e,r,n=[t];e=n.pop();)if(!e.isInterior())for(e.setInterior(!0),this.triangles_.push(e),r=0;r<3;r++)e.constrained_edge[r]||n.push(e.getNeighbor(r))};var sweepcontext=SweepContext$1;!function(t){var e=commonjsGlobal.poly2tri;t.noConflict=function(){return commonjsGlobal.poly2tri=e,t},t.VERSION=require$$0.version,t.PointError=pointerror,t.Point=point,t.Triangle=triangle,t.SweepContext=sweepcontext;var r=sweep$1;t.triangulate=r.triangulate,t.sweep={Triangulate:r.triangulate}}(poly2tri$1);var poly2tri=_mergeNamespaces({__proto__:null,default:poly2tri$1},[poly2tri$1]),shim$3=poly2tri?poly2tri$1||globalThis.poly2tri:{},SweepContext=shim$3.SweepContext,Point=shim$3.Point,clipper$1={exports:{}},module;module=clipper$1,function(){var t,e={version:"*******",use_lines:!0,use_xyz:!1},r=!1;if(module.exports?(module.exports=e,r=!0):"undefined"!=typeof document?window.ClipperLib=e:self.ClipperLib=e,r)n="chrome",t="Netscape";else{var n=navigator.userAgent.toString().toLowerCase();t=navigator.appName}var i,o={};function s(t,r,n){e.biginteger_used=1,null!=t&&("number"==typeof t&&void 0===r?this.fromInt(t):"number"==typeof t?this.fromNumber(t,r,n):null==r&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,r))}function a(){return new s(null,void 0,void 0)}-1!=n.indexOf("chrome")&&-1==n.indexOf("chromium")?o.chrome=1:o.chrome=0,-1!=n.indexOf("chromium")?o.chromium=1:o.chromium=0,-1!=n.indexOf("safari")&&-1==n.indexOf("chrome")&&-1==n.indexOf("chromium")?o.safari=1:o.safari=0,-1!=n.indexOf("firefox")?o.firefox=1:o.firefox=0,-1!=n.indexOf("firefox/17")?o.firefox17=1:o.firefox17=0,-1!=n.indexOf("firefox/15")?o.firefox15=1:o.firefox15=0,-1!=n.indexOf("firefox/3")?o.firefox3=1:o.firefox3=0,-1!=n.indexOf("opera")?o.opera=1:o.opera=0,-1!=n.indexOf("msie 10")?o.msie10=1:o.msie10=0,-1!=n.indexOf("msie 9")?o.msie9=1:o.msie9=0,-1!=n.indexOf("msie 8")?o.msie8=1:o.msie8=0,-1!=n.indexOf("msie 7")?o.msie7=1:o.msie7=0,-1!=n.indexOf("msie ")?o.msie=1:o.msie=0,e.biginteger_used=null,"Microsoft Internet Explorer"==t?(s.prototype.am=function(t,e,r,n,i,o){for(var s=32767&e,a=e>>15;--o>=0;){var l=32767&this[t],u=this[t++]>>15,h=a*l+u*s;i=((l=s*l+((32767&h)<<15)+r[n]+(1073741823&i))>>>30)+(h>>>15)+a*u+(i>>>30),r[n++]=1073741823&l}return i},i=30):"Netscape"!=t?(s.prototype.am=function(t,e,r,n,i,o){for(;--o>=0;){var s=e*this[t++]+r[n]+i;i=Math.floor(s/67108864),r[n++]=67108863&s}return i},i=26):(s.prototype.am=function(t,e,r,n,i,o){for(var s=16383&e,a=e>>14;--o>=0;){var l=16383&this[t],u=this[t++]>>14,h=a*l+u*s;i=((l=s*l+((16383&h)<<14)+r[n]+i)>>28)+(h>>14)+a*u,r[n++]=268435455&l}return i},i=28),s.prototype.DB=i,s.prototype.DM=(1<<i)-1,s.prototype.DV=1<<i,s.prototype.FV=Math.pow(2,52),s.prototype.F1=52-i,s.prototype.F2=2*i-52;var l,u,h=new Array;for(l="0".charCodeAt(0),u=0;u<=9;++u)h[l++]=u;for(l="a".charCodeAt(0),u=10;u<36;++u)h[l++]=u;for(l="A".charCodeAt(0),u=10;u<36;++u)h[l++]=u;function c(t){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(t)}function p(t,e){var r=h[t.charCodeAt(e)];return null==r?-1:r}function f(t){var e=a();return e.fromInt(t),e}function d(t){var e,r=1;return 0!=(e=t>>>16)&&(t=e,r+=16),0!=(e=t>>8)&&(t=e,r+=8),0!=(e=t>>4)&&(t=e,r+=4),0!=(e=t>>2)&&(t=e,r+=2),0!=(e=t>>1)&&(t=e,r+=1),r}function y(t){this.m=t}function v(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}function m(t,e){return t&e}function g(t,e){return t|e}function P(t,e){return t^e}function _(t,e){return t&~e}function b(t){if(0==t)return-1;var e=0;return 0==(65535&t)&&(t>>=16,e+=16),0==(255&t)&&(t>>=8,e+=8),0==(15&t)&&(t>>=4,e+=4),0==(3&t)&&(t>>=2,e+=2),0==(1&t)&&++e,e}function x(t){for(var e=0;0!=t;)t&=t-1,++e;return e}function E(){}function I(t){return t}function S(t){this.r2=a(),this.q3=a(),s.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t),this.m=t}y.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},y.prototype.revert=function(t){return t},y.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},y.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},y.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},v.prototype.convert=function(t){var e=a();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(s.ZERO)>0&&this.m.subTo(e,e),e},v.prototype.revert=function(t){var e=a();return t.copyTo(e),this.reduce(e),e},v.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],n=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[r=e+this.m.t]+=this.m.am(0,n,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},v.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},v.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},s.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},s.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},s.prototype.fromString=function(t,e){var r;if(16==e)r=4;else if(8==e)r=3;else if(256==e)r=8;else if(2==e)r=1;else if(32==e)r=5;else{if(4!=e)return void this.fromRadix(t,e);r=2}this.t=0,this.s=0;for(var n=t.length,i=!1,o=0;--n>=0;){var a=8==r?255&t[n]:p(t,n);a<0?"-"==t.charAt(n)&&(i=!0):(i=!1,0==o?this[this.t++]=a:o+r>this.DB?(this[this.t-1]|=(a&(1<<this.DB-o)-1)<<o,this[this.t++]=a>>this.DB-o):this[this.t-1]|=a<<o,(o+=r)>=this.DB&&(o-=this.DB))}8==r&&0!=(128&t[0])&&(this.s=-1,o>0&&(this[this.t-1]|=(1<<this.DB-o)-1<<o)),this.clamp(),i&&s.ZERO.subTo(this,this)},s.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},s.prototype.dlShiftTo=function(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s},s.prototype.drShiftTo=function(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s},s.prototype.lShiftTo=function(t,e){var r,n=t%this.DB,i=this.DB-n,o=(1<<i)-1,s=Math.floor(t/this.DB),a=this.s<<n&this.DM;for(r=this.t-1;r>=0;--r)e[r+s+1]=this[r]>>i|a,a=(this[r]&o)<<n;for(r=s-1;r>=0;--r)e[r]=0;e[s]=a,e.t=this.t+s+1,e.s=this.s,e.clamp()},s.prototype.rShiftTo=function(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var n=t%this.DB,i=this.DB-n,o=(1<<n)-1;e[0]=this[r]>>n;for(var s=r+1;s<this.t;++s)e[s-r-1]|=(this[s]&o)<<i,e[s-r]=this[s]>>n;n>0&&(e[this.t-r-1]|=(this.s&o)<<i),e.t=this.t-r,e.clamp()}},s.prototype.subTo=function(t,e){for(var r=0,n=0,i=Math.min(t.t,this.t);r<i;)n+=this[r]-t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n-=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n-=t[r],e[r++]=n&this.DM,n>>=this.DB;n-=t.s}e.s=n<0?-1:0,n<-1?e[r++]=this.DV+n:n>0&&(e[r++]=n),e.t=r,e.clamp()},s.prototype.multiplyTo=function(t,e){var r=this.abs(),n=t.abs(),i=r.t;for(e.t=i+n.t;--i>=0;)e[i]=0;for(i=0;i<n.t;++i)e[i+r.t]=r.am(0,n[i],e,i,0,r.t);e.s=0,e.clamp(),this.s!=t.s&&s.ZERO.subTo(e,e)},s.prototype.squareTo=function(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t[r]=0;for(r=0;r<e.t-1;++r){var n=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,n,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()},s.prototype.divRemTo=function(t,e,r){var n=t.abs();if(!(n.t<=0)){var i=this.abs();if(i.t<n.t)return null!=e&&e.fromInt(0),void(null!=r&&this.copyTo(r));null==r&&(r=a());var o=a(),l=this.s,u=t.s,h=this.DB-d(n[n.t-1]);h>0?(n.lShiftTo(h,o),i.lShiftTo(h,r)):(n.copyTo(o),i.copyTo(r));var c=o.t,p=o[c-1];if(0!=p){var f=p*(1<<this.F1)+(c>1?o[c-2]>>this.F2:0),y=this.FV/f,v=(1<<this.F1)/f,m=1<<this.F2,g=r.t,P=g-c,_=null==e?a():e;for(o.dlShiftTo(P,_),r.compareTo(_)>=0&&(r[r.t++]=1,r.subTo(_,r)),s.ONE.dlShiftTo(c,_),_.subTo(o,o);o.t<c;)o[o.t++]=0;for(;--P>=0;){var b=r[--g]==p?this.DM:Math.floor(r[g]*y+(r[g-1]+m)*v);if((r[g]+=o.am(0,b,r,P,0,c))<b)for(o.dlShiftTo(P,_),r.subTo(_,r);r[g]<--b;)r.subTo(_,r)}null!=e&&(r.drShiftTo(c,e),l!=u&&s.ZERO.subTo(e,e)),r.t=c,r.clamp(),h>0&&r.rShiftTo(h,r),l<0&&s.ZERO.subTo(r,r)}}},s.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(0==(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},s.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},s.prototype.exp=function(t,e){if(t>4294967295||t<1)return s.ONE;var r=a(),n=a(),i=e.convert(this),o=d(t)-1;for(i.copyTo(r);--o>=0;)if(e.sqrTo(r,n),(t&1<<o)>0)e.mulTo(n,i,r);else{var l=r;r=n,n=l}return e.revert(r)},s.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var r,n=(1<<e)-1,i=!1,o="",s=this.t,a=this.DB-s*this.DB%e;if(s-- >0)for(a<this.DB&&(r=this[s]>>a)>0&&(i=!0,o=c(r));s>=0;)a<e?(r=(this[s]&(1<<a)-1)<<e-a,r|=this[--s]>>(a+=this.DB-e)):(r=this[s]>>(a-=e)&n,a<=0&&(a+=this.DB,--s)),r>0&&(i=!0),i&&(o+=c(r));return i?o:"0"},s.prototype.negate=function(){var t=a();return s.ZERO.subTo(this,t),t},s.prototype.abs=function(){return this.s<0?this.negate():this},s.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var r=this.t;if(0!=(e=r-t.t))return this.s<0?-e:e;for(;--r>=0;)if(0!=(e=this[r]-t[r]))return e;return 0},s.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+d(this[this.t-1]^this.s&this.DM)},s.prototype.mod=function(t){var e=a();return this.abs().divRemTo(t,null,e),this.s<0&&e.compareTo(s.ZERO)>0&&t.subTo(e,e),e},s.prototype.modPowInt=function(t,e){var r;return r=t<256||e.isEven()?new y(e):new v(e),this.exp(t,r)},s.ZERO=f(0),s.ONE=f(1),E.prototype.convert=I,E.prototype.revert=I,E.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r)},E.prototype.sqrTo=function(t,e){t.squareTo(e)},S.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=a();return t.copyTo(e),this.reduce(e),e},S.prototype.revert=function(t){return t},S.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},S.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},S.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)};var C=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],O=(1<<26)/C[C.length-1];s.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},s.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),r=Math.pow(t,e),n=f(r),i=a(),o=a(),s="";for(this.divRemTo(n,i,o);i.signum()>0;)s=(r+o.intValue()).toString(t).substr(1)+s,i.divRemTo(n,i,o);return o.intValue().toString(t)+s},s.prototype.fromRadix=function(t,e){this.fromInt(0),null==e&&(e=10);for(var r=this.chunkSize(e),n=Math.pow(e,r),i=!1,o=0,a=0,l=0;l<t.length;++l){var u=p(t,l);u<0?"-"==t.charAt(l)&&0==this.signum()&&(i=!0):(a=e*a+u,++o>=r&&(this.dMultiply(n),this.dAddOffset(a,0),o=0,a=0))}o>0&&(this.dMultiply(Math.pow(e,o)),this.dAddOffset(a,0)),i&&s.ZERO.subTo(this,this)},s.prototype.fromNumber=function(t,e,r){if("number"==typeof e)if(t<2)this.fromInt(1);else for(this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(s.ONE.shiftLeft(t-1),g,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(e);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(s.ONE.shiftLeft(t-1),this);else{var n=new Array,i=7&t;n.length=1+(t>>3),e.nextBytes(n),i>0?n[0]&=(1<<i)-1:n[0]=0,this.fromString(n,256)}},s.prototype.bitwiseTo=function(t,e,r){var n,i,o=Math.min(t.t,this.t);for(n=0;n<o;++n)r[n]=e(this[n],t[n]);if(t.t<this.t){for(i=t.s&this.DM,n=o;n<this.t;++n)r[n]=e(this[n],i);r.t=this.t}else{for(i=this.s&this.DM,n=o;n<t.t;++n)r[n]=e(i,t[n]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()},s.prototype.changeBit=function(t,e){var r=s.ONE.shiftLeft(t);return this.bitwiseTo(r,e,r),r},s.prototype.addTo=function(t,e){for(var r=0,n=0,i=Math.min(t.t,this.t);r<i;)n+=this[r]+t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n+=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n+=t[r],e[r++]=n&this.DM,n>>=this.DB;n+=t.s}e.s=n<0?-1:0,n>0?e[r++]=n:n<-1&&(e[r++]=this.DV+n),e.t=r,e.clamp()},s.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},s.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},s.prototype.multiplyLowerTo=function(t,e,r){var n,i=Math.min(this.t+t.t,e);for(r.s=0,r.t=i;i>0;)r[--i]=0;for(n=r.t-this.t;i<n;++i)r[i+this.t]=this.am(0,t[i],r,i,0,this.t);for(n=Math.min(t.t,e);i<n;++i)this.am(0,t[i],r,i,0,e-i);r.clamp()},s.prototype.multiplyUpperTo=function(t,e,r){--e;var n=r.t=this.t+t.t-e;for(r.s=0;--n>=0;)r[n]=0;for(n=Math.max(e-this.t,0);n<t.t;++n)r[this.t+n-e]=this.am(e-n,t[n],r,0,0,this.t+n-e);r.clamp(),r.drShiftTo(1,r)},s.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(0==e)r=this[0]%t;else for(var n=this.t-1;n>=0;--n)r=(e*r+this[n])%t;return r},s.prototype.millerRabin=function(t){var e=this.subtract(s.ONE),r=e.getLowestSetBit();if(r<=0)return!1;var n=e.shiftRight(r);(t=t+1>>1)>C.length&&(t=C.length);for(var i=a(),o=0;o<t;++o){i.fromInt(C[Math.floor(Math.random()*C.length)]);var l=i.modPow(n,this);if(0!=l.compareTo(s.ONE)&&0!=l.compareTo(e)){for(var u=1;u++<r&&0!=l.compareTo(e);)if(0==(l=l.modPowInt(2,this)).compareTo(s.ONE))return!1;if(0!=l.compareTo(e))return!1}}return!0},s.prototype.clone=function(){var t=a();return this.copyTo(t),t},s.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},s.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},s.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},s.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},s.prototype.toByteArray=function(){var t=this.t,e=new Array;e[0]=this.s;var r,n=this.DB-t*this.DB%8,i=0;if(t-- >0)for(n<this.DB&&(r=this[t]>>n)!=(this.s&this.DM)>>n&&(e[i++]=r|this.s<<this.DB-n);t>=0;)n<8?(r=(this[t]&(1<<n)-1)<<8-n,r|=this[--t]>>(n+=this.DB-8)):(r=this[t]>>(n-=8)&255,n<=0&&(n+=this.DB,--t)),0!=(128&r)&&(r|=-256),0==i&&(128&this.s)!=(128&r)&&++i,(i>0||r!=this.s)&&(e[i++]=r);return e},s.prototype.equals=function(t){return 0==this.compareTo(t)},s.prototype.min=function(t){return this.compareTo(t)<0?this:t},s.prototype.max=function(t){return this.compareTo(t)>0?this:t},s.prototype.and=function(t){var e=a();return this.bitwiseTo(t,m,e),e},s.prototype.or=function(t){var e=a();return this.bitwiseTo(t,g,e),e},s.prototype.xor=function(t){var e=a();return this.bitwiseTo(t,P,e),e},s.prototype.andNot=function(t){var e=a();return this.bitwiseTo(t,_,e),e},s.prototype.not=function(){for(var t=a(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},s.prototype.shiftLeft=function(t){var e=a();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},s.prototype.shiftRight=function(t){var e=a();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},s.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+b(this[t]);return this.s<0?this.t*this.DB:-1},s.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=x(this[r]^e);return t},s.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:0!=(this[e]&1<<t%this.DB)},s.prototype.setBit=function(t){return this.changeBit(t,g)},s.prototype.clearBit=function(t){return this.changeBit(t,_)},s.prototype.flipBit=function(t){return this.changeBit(t,P)},s.prototype.add=function(t){var e=a();return this.addTo(t,e),e},s.prototype.subtract=function(t){var e=a();return this.subTo(t,e),e},s.prototype.multiply=function(t){var e=a();return this.multiplyTo(t,e),e},s.prototype.divide=function(t){var e=a();return this.divRemTo(t,e,null),e},s.prototype.remainder=function(t){var e=a();return this.divRemTo(t,null,e),e},s.prototype.divideAndRemainder=function(t){var e=a(),r=a();return this.divRemTo(t,e,r),new Array(e,r)},s.prototype.modPow=function(t,e){var r,n,i=t.bitLength(),o=f(1);if(i<=0)return o;r=i<18?1:i<48?3:i<144?4:i<768?5:6,n=i<8?new y(e):e.isEven()?new S(e):new v(e);var s=new Array,l=3,u=r-1,h=(1<<r)-1;if(s[1]=n.convert(this),r>1){var c=a();for(n.sqrTo(s[1],c);l<=h;)s[l]=a(),n.mulTo(c,s[l-2],s[l]),l+=2}var p,m,g=t.t-1,P=!0,_=a();for(i=d(t[g])-1;g>=0;){for(i>=u?p=t[g]>>i-u&h:(p=(t[g]&(1<<i+1)-1)<<u-i,g>0&&(p|=t[g-1]>>this.DB+i-u)),l=r;0==(1&p);)p>>=1,--l;if((i-=l)<0&&(i+=this.DB,--g),P)s[p].copyTo(o),P=!1;else{for(;l>1;)n.sqrTo(o,_),n.sqrTo(_,o),l-=2;l>0?n.sqrTo(o,_):(m=o,o=_,_=m),n.mulTo(_,s[p],o)}for(;g>=0&&0==(t[g]&1<<i);)n.sqrTo(o,_),m=o,o=_,_=m,--i<0&&(i=this.DB-1,--g)}return n.revert(o)},s.prototype.modInverse=function(t){var e=t.isEven();if(this.isEven()&&e||0==t.signum())return s.ZERO;for(var r=t.clone(),n=this.clone(),i=f(1),o=f(0),a=f(0),l=f(1);0!=r.signum();){for(;r.isEven();)r.rShiftTo(1,r),e?(i.isEven()&&o.isEven()||(i.addTo(this,i),o.subTo(t,o)),i.rShiftTo(1,i)):o.isEven()||o.subTo(t,o),o.rShiftTo(1,o);for(;n.isEven();)n.rShiftTo(1,n),e?(a.isEven()&&l.isEven()||(a.addTo(this,a),l.subTo(t,l)),a.rShiftTo(1,a)):l.isEven()||l.subTo(t,l),l.rShiftTo(1,l);r.compareTo(n)>=0?(r.subTo(n,r),e&&i.subTo(a,i),o.subTo(l,o)):(n.subTo(r,n),e&&a.subTo(i,a),l.subTo(o,l))}return 0!=n.compareTo(s.ONE)?s.ZERO:l.compareTo(t)>=0?l.subtract(t):l.signum()<0?(l.addTo(t,l),l.signum()<0?l.add(t):l):l},s.prototype.pow=function(t){return this.exp(t,new E)},s.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var n=e;e=r,r=n}var i=e.getLowestSetBit(),o=r.getLowestSetBit();if(o<0)return e;for(i<o&&(o=i),o>0&&(e.rShiftTo(o,e),r.rShiftTo(o,r));e.signum()>0;)(i=e.getLowestSetBit())>0&&e.rShiftTo(i,e),(i=r.getLowestSetBit())>0&&r.rShiftTo(i,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return o>0&&r.lShiftTo(o,r),r},s.prototype.isProbablePrime=function(t){var e,r=this.abs();if(1==r.t&&r[0]<=C[C.length-1]){for(e=0;e<C.length;++e)if(r[0]==C[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<C.length;){for(var n=C[e],i=e+1;i<C.length&&n<O;)n*=C[i++];for(n=r.modInt(n);e<i;)if(n%C[e++]==0)return!1}return r.millerRabin(t)},s.prototype.square=function(){var t=a();return this.squareTo(t),t};var A=s;A.prototype.IsNegative=function(){return-1==this.compareTo(A.ZERO)},A.op_Equality=function(t,e){return 0==t.compareTo(e)},A.op_Inequality=function(t,e){return 0!=t.compareTo(e)},A.op_GreaterThan=function(t,e){return t.compareTo(e)>0},A.op_LessThan=function(t,e){return t.compareTo(e)<0},A.op_Addition=function(t,e){return new A(t,void 0,void 0).add(new A(e,void 0,void 0))},A.op_Subtraction=function(t,e){return new A(t,void 0,void 0).subtract(new A(e,void 0,void 0))},A.Int128Mul=function(t,e){return new A(t,void 0,void 0).multiply(new A(e,void 0,void 0))},A.op_Division=function(t,e){return t.divide(e)},A.prototype.ToDouble=function(){return parseFloat(this.toString())};var w=function(t,e){var r;if(void 0===Object.getOwnPropertyNames){for(r in e.prototype)void 0!==t.prototype[r]&&t.prototype[r]!==Object.prototype[r]||(t.prototype[r]=e.prototype[r]);for(r in e)void 0===t[r]&&(t[r]=e[r]);t.$baseCtor=e}else{for(var n=Object.getOwnPropertyNames(e.prototype),i=0;i<n.length;i++)void 0===Object.getOwnPropertyDescriptor(t.prototype,n[i])&&Object.defineProperty(t.prototype,n[i],Object.getOwnPropertyDescriptor(e.prototype,n[i]));for(r in e)void 0===t[r]&&(t[r]=e[r]);t.$baseCtor=e}};e.Path=function(){return[]},e.Path.prototype.push=Array.prototype.push,e.Paths=function(){return[]},e.Paths.prototype.push=Array.prototype.push,e.DoublePoint=function(){var t=arguments;this.X=0,this.Y=0,1===t.length?(this.X=t[0].X,this.Y=t[0].Y):2===t.length&&(this.X=t[0],this.Y=t[1])},e.DoublePoint0=function(){this.X=0,this.Y=0},e.DoublePoint0.prototype=e.DoublePoint.prototype,e.DoublePoint1=function(t){this.X=t.X,this.Y=t.Y},e.DoublePoint1.prototype=e.DoublePoint.prototype,e.DoublePoint2=function(t,e){this.X=t,this.Y=e},e.DoublePoint2.prototype=e.DoublePoint.prototype,e.PolyNode=function(){this.m_Parent=null,this.m_polygon=new e.Path,this.m_Index=0,this.m_jointype=0,this.m_endtype=0,this.m_Childs=[],this.IsOpen=!1},e.PolyNode.prototype.IsHoleNode=function(){for(var t=!0,e=this.m_Parent;null!==e;)t=!t,e=e.m_Parent;return t},e.PolyNode.prototype.ChildCount=function(){return this.m_Childs.length},e.PolyNode.prototype.Contour=function(){return this.m_polygon},e.PolyNode.prototype.AddChild=function(t){var e=this.m_Childs.length;this.m_Childs.push(t),t.m_Parent=this,t.m_Index=e},e.PolyNode.prototype.GetNext=function(){return this.m_Childs.length>0?this.m_Childs[0]:this.GetNextSiblingUp()},e.PolyNode.prototype.GetNextSiblingUp=function(){return null===this.m_Parent?null:this.m_Index===this.m_Parent.m_Childs.length-1?this.m_Parent.GetNextSiblingUp():this.m_Parent.m_Childs[this.m_Index+1]},e.PolyNode.prototype.Childs=function(){return this.m_Childs},e.PolyNode.prototype.Parent=function(){return this.m_Parent},e.PolyNode.prototype.IsHole=function(){return this.IsHoleNode()},e.PolyTree=function(){this.m_AllPolys=[],e.PolyNode.call(this)},e.PolyTree.prototype.Clear=function(){for(var t=0,e=this.m_AllPolys.length;t<e;t++)this.m_AllPolys[t]=null;this.m_AllPolys.length=0,this.m_Childs.length=0},e.PolyTree.prototype.GetFirst=function(){return this.m_Childs.length>0?this.m_Childs[0]:null},e.PolyTree.prototype.Total=function(){var t=this.m_AllPolys.length;return t>0&&this.m_Childs[0]!==this.m_AllPolys[0]&&t--,t},w(e.PolyTree,e.PolyNode),e.Math_Abs_Int64=e.Math_Abs_Int32=e.Math_Abs_Double=function(t){return Math.abs(t)},e.Math_Max_Int32_Int32=function(t,e){return Math.max(t,e)},o.msie||o.opera||o.safari?e.Cast_Int32=function(t){return 0|t}:e.Cast_Int32=function(t){return~~t},void 0===Number.toInteger&&(Number.toInteger=null),o.chrome?e.Cast_Int64=function(t){return t<-2147483648||t>2147483647?t<0?Math.ceil(t):Math.floor(t):~~t}:o.firefox&&"function"==typeof Number.toInteger?e.Cast_Int64=function(t){return Number.toInteger(t)}:o.msie7||o.msie8?e.Cast_Int64=function(t){return parseInt(t,10)}:o.msie?e.Cast_Int64=function(t){return t<-2147483648||t>2147483647?t<0?Math.ceil(t):Math.floor(t):0|t}:e.Cast_Int64=function(t){return t<0?Math.ceil(t):Math.floor(t)},e.Clear=function(t){t.length=0},e.PI=3.141592653589793,e.PI2=6.283185307179586,e.IntPoint=function(){var t=arguments,r=t.length;if(this.X=0,this.Y=0,e.use_xyz)if(this.Z=0,3===r)this.X=t[0],this.Y=t[1],this.Z=t[2];else if(2===r)this.X=t[0],this.Y=t[1],this.Z=0;else if(1===r)if(t[0]instanceof e.DoublePoint){var n=t[0];this.X=e.Clipper.Round(n.X),this.Y=e.Clipper.Round(n.Y),this.Z=0}else void 0===(i=t[0]).Z&&(i.Z=0),this.X=i.X,this.Y=i.Y,this.Z=i.Z;else this.X=0,this.Y=0,this.Z=0;else if(2===r)this.X=t[0],this.Y=t[1];else if(1===r)if(t[0]instanceof e.DoublePoint)n=t[0],this.X=e.Clipper.Round(n.X),this.Y=e.Clipper.Round(n.Y);else{var i=t[0];this.X=i.X,this.Y=i.Y}else this.X=0,this.Y=0},e.IntPoint.op_Equality=function(t,e){return t.X===e.X&&t.Y===e.Y},e.IntPoint.op_Inequality=function(t,e){return t.X!==e.X||t.Y!==e.Y},e.IntPoint0=function(){this.X=0,this.Y=0,e.use_xyz&&(this.Z=0)},e.IntPoint0.prototype=e.IntPoint.prototype,e.IntPoint1=function(t){this.X=t.X,this.Y=t.Y,e.use_xyz&&(void 0===t.Z?this.Z=0:this.Z=t.Z)},e.IntPoint1.prototype=e.IntPoint.prototype,e.IntPoint1dp=function(t){this.X=e.Clipper.Round(t.X),this.Y=e.Clipper.Round(t.Y),e.use_xyz&&(this.Z=0)},e.IntPoint1dp.prototype=e.IntPoint.prototype,e.IntPoint2=function(t,r,n){this.X=t,this.Y=r,e.use_xyz&&(this.Z=void 0===n?0:n)},e.IntPoint2.prototype=e.IntPoint.prototype,e.IntRect=function(){var t=arguments,e=t.length;if(4===e)this.left=t[0],this.top=t[1],this.right=t[2],this.bottom=t[3];else if(1===e){var r=t[0];this.left=r.left,this.top=r.top,this.right=r.right,this.bottom=r.bottom}else this.left=0,this.top=0,this.right=0,this.bottom=0},e.IntRect0=function(){this.left=0,this.top=0,this.right=0,this.bottom=0},e.IntRect0.prototype=e.IntRect.prototype,e.IntRect1=function(t){this.left=t.left,this.top=t.top,this.right=t.right,this.bottom=t.bottom},e.IntRect1.prototype=e.IntRect.prototype,e.IntRect4=function(t,e,r,n){this.left=t,this.top=e,this.right=r,this.bottom=n},e.IntRect4.prototype=e.IntRect.prototype,e.ClipType={ctIntersection:0,ctUnion:1,ctDifference:2,ctXor:3},e.PolyType={ptSubject:0,ptClip:1},e.PolyFillType={pftEvenOdd:0,pftNonZero:1,pftPositive:2,pftNegative:3},e.JoinType={jtSquare:0,jtRound:1,jtMiter:2},e.EndType={etOpenSquare:0,etOpenRound:1,etOpenButt:2,etClosedLine:3,etClosedPolygon:4},e.EdgeSide={esLeft:0,esRight:1},e.Direction={dRightToLeft:0,dLeftToRight:1},e.TEdge=function(){this.Bot=new e.IntPoint0,this.Curr=new e.IntPoint0,this.Top=new e.IntPoint0,this.Delta=new e.IntPoint0,this.Dx=0,this.PolyTyp=e.PolyType.ptSubject,this.Side=e.EdgeSide.esLeft,this.WindDelta=0,this.WindCnt=0,this.WindCnt2=0,this.OutIdx=0,this.Next=null,this.Prev=null,this.NextInLML=null,this.NextInAEL=null,this.PrevInAEL=null,this.NextInSEL=null,this.PrevInSEL=null},e.IntersectNode=function(){this.Edge1=null,this.Edge2=null,this.Pt=new e.IntPoint0},e.MyIntersectNodeSort=function(){},e.MyIntersectNodeSort.Compare=function(t,e){var r=e.Pt.Y-t.Pt.Y;return r>0?1:r<0?-1:0},e.LocalMinima=function(){this.Y=0,this.LeftBound=null,this.RightBound=null,this.Next=null},e.Scanbeam=function(){this.Y=0,this.Next=null},e.Maxima=function(){this.X=0,this.Next=null,this.Prev=null},e.OutRec=function(){this.Idx=0,this.IsHole=!1,this.IsOpen=!1,this.FirstLeft=null,this.Pts=null,this.BottomPt=null,this.PolyNode=null},e.OutPt=function(){this.Idx=0,this.Pt=new e.IntPoint0,this.Next=null,this.Prev=null},e.Join=function(){this.OutPt1=null,this.OutPt2=null,this.OffPt=new e.IntPoint0},e.ClipperBase=function(){this.m_MinimaList=null,this.m_CurrentLM=null,this.m_edges=new Array,this.m_UseFullRange=!1,this.m_HasOpenPaths=!1,this.PreserveCollinear=!1,this.m_Scanbeam=null,this.m_PolyOuts=null,this.m_ActiveEdges=null},e.ClipperBase.horizontal=-9007199254740992,e.ClipperBase.Skip=-2,e.ClipperBase.Unassigned=-1,e.ClipperBase.tolerance=1e-20,e.ClipperBase.loRange=47453132,e.ClipperBase.hiRange=0xfffffffffffff,e.ClipperBase.near_zero=function(t){return t>-e.ClipperBase.tolerance&&t<e.ClipperBase.tolerance},e.ClipperBase.IsHorizontal=function(t){return 0===t.Delta.Y},e.ClipperBase.prototype.PointIsVertex=function(t,r){var n=r;do{if(e.IntPoint.op_Equality(n.Pt,t))return!0;n=n.Next}while(n!==r);return!1},e.ClipperBase.prototype.PointOnLineSegment=function(t,e,r,n){return n?t.X===e.X&&t.Y===e.Y||t.X===r.X&&t.Y===r.Y||t.X>e.X==t.X<r.X&&t.Y>e.Y==t.Y<r.Y&&A.op_Equality(A.Int128Mul(t.X-e.X,r.Y-e.Y),A.Int128Mul(r.X-e.X,t.Y-e.Y)):t.X===e.X&&t.Y===e.Y||t.X===r.X&&t.Y===r.Y||t.X>e.X==t.X<r.X&&t.Y>e.Y==t.Y<r.Y&&(t.X-e.X)*(r.Y-e.Y)==(r.X-e.X)*(t.Y-e.Y)},e.ClipperBase.prototype.PointOnPolygon=function(t,e,r){for(var n=e;;){if(this.PointOnLineSegment(t,n.Pt,n.Next.Pt,r))return!0;if((n=n.Next)===e)break}return!1},e.ClipperBase.prototype.SlopesEqual=e.ClipperBase.SlopesEqual=function(){var t,r,n,i,o,s,a=arguments,l=a.length;return 3===l?(t=a[0],r=a[1],a[2]?A.op_Equality(A.Int128Mul(t.Delta.Y,r.Delta.X),A.Int128Mul(t.Delta.X,r.Delta.Y)):e.Cast_Int64(t.Delta.Y*r.Delta.X)===e.Cast_Int64(t.Delta.X*r.Delta.Y)):4===l?(n=a[0],i=a[1],o=a[2],a[3]?A.op_Equality(A.Int128Mul(n.Y-i.Y,i.X-o.X),A.Int128Mul(n.X-i.X,i.Y-o.Y)):e.Cast_Int64((n.Y-i.Y)*(i.X-o.X))-e.Cast_Int64((n.X-i.X)*(i.Y-o.Y))==0):(n=a[0],i=a[1],o=a[2],s=a[3],a[4]?A.op_Equality(A.Int128Mul(n.Y-i.Y,o.X-s.X),A.Int128Mul(n.X-i.X,o.Y-s.Y)):e.Cast_Int64((n.Y-i.Y)*(o.X-s.X))-e.Cast_Int64((n.X-i.X)*(o.Y-s.Y))==0)},e.ClipperBase.SlopesEqual3=function(t,r,n){return n?A.op_Equality(A.Int128Mul(t.Delta.Y,r.Delta.X),A.Int128Mul(t.Delta.X,r.Delta.Y)):e.Cast_Int64(t.Delta.Y*r.Delta.X)===e.Cast_Int64(t.Delta.X*r.Delta.Y)},e.ClipperBase.SlopesEqual4=function(t,r,n,i){return i?A.op_Equality(A.Int128Mul(t.Y-r.Y,r.X-n.X),A.Int128Mul(t.X-r.X,r.Y-n.Y)):e.Cast_Int64((t.Y-r.Y)*(r.X-n.X))-e.Cast_Int64((t.X-r.X)*(r.Y-n.Y))==0},e.ClipperBase.SlopesEqual5=function(t,r,n,i,o){return o?A.op_Equality(A.Int128Mul(t.Y-r.Y,n.X-i.X),A.Int128Mul(t.X-r.X,n.Y-i.Y)):e.Cast_Int64((t.Y-r.Y)*(n.X-i.X))-e.Cast_Int64((t.X-r.X)*(n.Y-i.Y))==0},e.ClipperBase.prototype.Clear=function(){this.DisposeLocalMinimaList();for(var t=0,r=this.m_edges.length;t<r;++t){for(var n=0,i=this.m_edges[t].length;n<i;++n)this.m_edges[t][n]=null;e.Clear(this.m_edges[t])}e.Clear(this.m_edges),this.m_UseFullRange=!1,this.m_HasOpenPaths=!1},e.ClipperBase.prototype.DisposeLocalMinimaList=function(){for(;null!==this.m_MinimaList;){var t=this.m_MinimaList.Next;this.m_MinimaList=null,this.m_MinimaList=t}this.m_CurrentLM=null},e.ClipperBase.prototype.RangeTest=function(t,r){r.Value?(t.X>e.ClipperBase.hiRange||t.Y>e.ClipperBase.hiRange||-t.X>e.ClipperBase.hiRange||-t.Y>e.ClipperBase.hiRange)&&e.Error("Coordinate outside allowed range in RangeTest()."):(t.X>e.ClipperBase.loRange||t.Y>e.ClipperBase.loRange||-t.X>e.ClipperBase.loRange||-t.Y>e.ClipperBase.loRange)&&(r.Value=!0,this.RangeTest(t,r))},e.ClipperBase.prototype.InitEdge=function(t,r,n,i){t.Next=r,t.Prev=n,t.Curr.X=i.X,t.Curr.Y=i.Y,e.use_xyz&&(t.Curr.Z=i.Z),t.OutIdx=-1},e.ClipperBase.prototype.InitEdge2=function(t,r){t.Curr.Y>=t.Next.Curr.Y?(t.Bot.X=t.Curr.X,t.Bot.Y=t.Curr.Y,e.use_xyz&&(t.Bot.Z=t.Curr.Z),t.Top.X=t.Next.Curr.X,t.Top.Y=t.Next.Curr.Y,e.use_xyz&&(t.Top.Z=t.Next.Curr.Z)):(t.Top.X=t.Curr.X,t.Top.Y=t.Curr.Y,e.use_xyz&&(t.Top.Z=t.Curr.Z),t.Bot.X=t.Next.Curr.X,t.Bot.Y=t.Next.Curr.Y,e.use_xyz&&(t.Bot.Z=t.Next.Curr.Z)),this.SetDx(t),t.PolyTyp=r},e.ClipperBase.prototype.FindNextLocMin=function(t){for(var r;;){for(;e.IntPoint.op_Inequality(t.Bot,t.Prev.Bot)||e.IntPoint.op_Equality(t.Curr,t.Top);)t=t.Next;if(t.Dx!==e.ClipperBase.horizontal&&t.Prev.Dx!==e.ClipperBase.horizontal)break;for(;t.Prev.Dx===e.ClipperBase.horizontal;)t=t.Prev;for(r=t;t.Dx===e.ClipperBase.horizontal;)t=t.Next;if(t.Top.Y!==t.Prev.Bot.Y){r.Prev.Bot.X<t.Bot.X&&(t=r);break}}return t},e.ClipperBase.prototype.ProcessBound=function(t,r){var n,i,o=t;if(o.OutIdx===e.ClipperBase.Skip){if(t=o,r){for(;t.Top.Y===t.Next.Bot.Y;)t=t.Next;for(;t!==o&&t.Dx===e.ClipperBase.horizontal;)t=t.Prev}else{for(;t.Top.Y===t.Prev.Bot.Y;)t=t.Prev;for(;t!==o&&t.Dx===e.ClipperBase.horizontal;)t=t.Next}if(t===o)o=r?t.Next:t.Prev;else{t=r?o.Next:o.Prev;var s=new e.LocalMinima;s.Next=null,s.Y=t.Bot.Y,s.LeftBound=null,s.RightBound=t,t.WindDelta=0,o=this.ProcessBound(t,r),this.InsertLocalMinima(s)}return o}if(t.Dx===e.ClipperBase.horizontal&&((n=r?t.Prev:t.Next).Dx===e.ClipperBase.horizontal?n.Bot.X!==t.Bot.X&&n.Top.X!==t.Bot.X&&this.ReverseHorizontal(t):n.Bot.X!==t.Bot.X&&this.ReverseHorizontal(t)),n=t,r){for(;o.Top.Y===o.Next.Bot.Y&&o.Next.OutIdx!==e.ClipperBase.Skip;)o=o.Next;if(o.Dx===e.ClipperBase.horizontal&&o.Next.OutIdx!==e.ClipperBase.Skip){for(i=o;i.Prev.Dx===e.ClipperBase.horizontal;)i=i.Prev;i.Prev.Top.X>o.Next.Top.X&&(o=i.Prev)}for(;t!==o;)t.NextInLML=t.Next,t.Dx===e.ClipperBase.horizontal&&t!==n&&t.Bot.X!==t.Prev.Top.X&&this.ReverseHorizontal(t),t=t.Next;t.Dx===e.ClipperBase.horizontal&&t!==n&&t.Bot.X!==t.Prev.Top.X&&this.ReverseHorizontal(t),o=o.Next}else{for(;o.Top.Y===o.Prev.Bot.Y&&o.Prev.OutIdx!==e.ClipperBase.Skip;)o=o.Prev;if(o.Dx===e.ClipperBase.horizontal&&o.Prev.OutIdx!==e.ClipperBase.Skip){for(i=o;i.Next.Dx===e.ClipperBase.horizontal;)i=i.Next;(i.Next.Top.X===o.Prev.Top.X||i.Next.Top.X>o.Prev.Top.X)&&(o=i.Next)}for(;t!==o;)t.NextInLML=t.Prev,t.Dx===e.ClipperBase.horizontal&&t!==n&&t.Bot.X!==t.Next.Top.X&&this.ReverseHorizontal(t),t=t.Prev;t.Dx===e.ClipperBase.horizontal&&t!==n&&t.Bot.X!==t.Next.Top.X&&this.ReverseHorizontal(t),o=o.Prev}return o},e.ClipperBase.prototype.AddPath=function(t,r,n){e.use_lines?n||r!==e.PolyType.ptClip||e.Error("AddPath: Open paths must be subject."):n||e.Error("AddPath: Open paths have been disabled.");var i=t.length-1;if(n)for(;i>0&&e.IntPoint.op_Equality(t[i],t[0]);)--i;for(;i>0&&e.IntPoint.op_Equality(t[i],t[i-1]);)--i;if(n&&i<2||!n&&i<1)return!1;for(var o=new Array,s=0;s<=i;s++)o.push(new e.TEdge);var a=!0;o[1].Curr.X=t[1].X,o[1].Curr.Y=t[1].Y,e.use_xyz&&(o[1].Curr.Z=t[1].Z);var l={Value:this.m_UseFullRange};for(this.RangeTest(t[0],l),this.m_UseFullRange=l.Value,l.Value=this.m_UseFullRange,this.RangeTest(t[i],l),this.m_UseFullRange=l.Value,this.InitEdge(o[0],o[1],o[i],t[0]),this.InitEdge(o[i],o[0],o[i-1],t[i]),s=i-1;s>=1;--s)l.Value=this.m_UseFullRange,this.RangeTest(t[s],l),this.m_UseFullRange=l.Value,this.InitEdge(o[s],o[s+1],o[s-1],t[s]);for(var u,h=o[0],c=h,p=h;;)if(c.Curr!==c.Next.Curr||!n&&c.Next===h){if(c.Prev===c.Next)break;if(!n||!e.ClipperBase.SlopesEqual4(c.Prev.Curr,c.Curr,c.Next.Curr,this.m_UseFullRange)||this.PreserveCollinear&&this.Pt2IsBetweenPt1AndPt3(c.Prev.Curr,c.Curr,c.Next.Curr)){if((c=c.Next)===p||!n&&c.Next===h)break}else c===h&&(h=c.Next),p=c=(c=this.RemoveEdge(c)).Prev}else{if(c===c.Next)break;c===h&&(h=c.Next),p=c=this.RemoveEdge(c)}if(!n&&c===c.Next||n&&c.Prev===c.Next)return!1;n||(this.m_HasOpenPaths=!0,h.Prev.OutIdx=e.ClipperBase.Skip),c=h;do{this.InitEdge2(c,r),c=c.Next,a&&c.Curr.Y!==h.Curr.Y&&(a=!1)}while(c!==h);if(a){if(n)return!1;for(c.Prev.OutIdx=e.ClipperBase.Skip,(d=new e.LocalMinima).Next=null,d.Y=c.Bot.Y,d.LeftBound=null,d.RightBound=c,d.RightBound.Side=e.EdgeSide.esRight,d.RightBound.WindDelta=0;c.Bot.X!==c.Prev.Top.X&&this.ReverseHorizontal(c),c.Next.OutIdx!==e.ClipperBase.Skip;)c.NextInLML=c.Next,c=c.Next;return this.InsertLocalMinima(d),this.m_edges.push(o),!0}this.m_edges.push(o);var f=null;for(e.IntPoint.op_Equality(c.Prev.Bot,c.Prev.Top)&&(c=c.Next);(c=this.FindNextLocMin(c))!==f;){var d;null===f&&(f=c),(d=new e.LocalMinima).Next=null,d.Y=c.Bot.Y,c.Dx<c.Prev.Dx?(d.LeftBound=c.Prev,d.RightBound=c,u=!1):(d.LeftBound=c,d.RightBound=c.Prev,u=!0),d.LeftBound.Side=e.EdgeSide.esLeft,d.RightBound.Side=e.EdgeSide.esRight,n?d.LeftBound.Next===d.RightBound?d.LeftBound.WindDelta=-1:d.LeftBound.WindDelta=1:d.LeftBound.WindDelta=0,d.RightBound.WindDelta=-d.LeftBound.WindDelta,(c=this.ProcessBound(d.LeftBound,u)).OutIdx===e.ClipperBase.Skip&&(c=this.ProcessBound(c,u));var y=this.ProcessBound(d.RightBound,!u);y.OutIdx===e.ClipperBase.Skip&&(y=this.ProcessBound(y,!u)),d.LeftBound.OutIdx===e.ClipperBase.Skip?d.LeftBound=null:d.RightBound.OutIdx===e.ClipperBase.Skip&&(d.RightBound=null),this.InsertLocalMinima(d),u||(c=y)}return!0},e.ClipperBase.prototype.AddPaths=function(t,e,r){for(var n=!1,i=0,o=t.length;i<o;++i)this.AddPath(t[i],e,r)&&(n=!0);return n},e.ClipperBase.prototype.Pt2IsBetweenPt1AndPt3=function(t,r,n){return!(e.IntPoint.op_Equality(t,n)||e.IntPoint.op_Equality(t,r)||e.IntPoint.op_Equality(n,r))&&(t.X!==n.X?r.X>t.X==r.X<n.X:r.Y>t.Y==r.Y<n.Y)},e.ClipperBase.prototype.RemoveEdge=function(t){t.Prev.Next=t.Next,t.Next.Prev=t.Prev;var e=t.Next;return t.Prev=null,e},e.ClipperBase.prototype.SetDx=function(t){t.Delta.X=t.Top.X-t.Bot.X,t.Delta.Y=t.Top.Y-t.Bot.Y,0===t.Delta.Y?t.Dx=e.ClipperBase.horizontal:t.Dx=t.Delta.X/t.Delta.Y},e.ClipperBase.prototype.InsertLocalMinima=function(t){if(null===this.m_MinimaList)this.m_MinimaList=t;else if(t.Y>=this.m_MinimaList.Y)t.Next=this.m_MinimaList,this.m_MinimaList=t;else{for(var e=this.m_MinimaList;null!==e.Next&&t.Y<e.Next.Y;)e=e.Next;t.Next=e.Next,e.Next=t}},e.ClipperBase.prototype.PopLocalMinima=function(t,e){return e.v=this.m_CurrentLM,null!==this.m_CurrentLM&&this.m_CurrentLM.Y===t&&(this.m_CurrentLM=this.m_CurrentLM.Next,!0)},e.ClipperBase.prototype.ReverseHorizontal=function(t){var r=t.Top.X;t.Top.X=t.Bot.X,t.Bot.X=r,e.use_xyz&&(r=t.Top.Z,t.Top.Z=t.Bot.Z,t.Bot.Z=r)},e.ClipperBase.prototype.Reset=function(){if(this.m_CurrentLM=this.m_MinimaList,null!==this.m_CurrentLM){this.m_Scanbeam=null;for(var t=this.m_MinimaList;null!==t;){this.InsertScanbeam(t.Y);var r=t.LeftBound;null!==r&&(r.Curr.X=r.Bot.X,r.Curr.Y=r.Bot.Y,e.use_xyz&&(r.Curr.Z=r.Bot.Z),r.OutIdx=e.ClipperBase.Unassigned),null!==(r=t.RightBound)&&(r.Curr.X=r.Bot.X,r.Curr.Y=r.Bot.Y,e.use_xyz&&(r.Curr.Z=r.Bot.Z),r.OutIdx=e.ClipperBase.Unassigned),t=t.Next}this.m_ActiveEdges=null}},e.ClipperBase.prototype.InsertScanbeam=function(t){if(null===this.m_Scanbeam)this.m_Scanbeam=new e.Scanbeam,this.m_Scanbeam.Next=null,this.m_Scanbeam.Y=t;else if(t>this.m_Scanbeam.Y){var r=new e.Scanbeam;r.Y=t,r.Next=this.m_Scanbeam,this.m_Scanbeam=r}else{for(var n=this.m_Scanbeam;null!==n.Next&&t<=n.Next.Y;)n=n.Next;if(t===n.Y)return;var i=new e.Scanbeam;i.Y=t,i.Next=n.Next,n.Next=i}},e.ClipperBase.prototype.PopScanbeam=function(t){return null===this.m_Scanbeam?(t.v=0,!1):(t.v=this.m_Scanbeam.Y,this.m_Scanbeam=this.m_Scanbeam.Next,!0)},e.ClipperBase.prototype.LocalMinimaPending=function(){return null!==this.m_CurrentLM},e.ClipperBase.prototype.CreateOutRec=function(){var t=new e.OutRec;return t.Idx=e.ClipperBase.Unassigned,t.IsHole=!1,t.IsOpen=!1,t.FirstLeft=null,t.Pts=null,t.BottomPt=null,t.PolyNode=null,this.m_PolyOuts.push(t),t.Idx=this.m_PolyOuts.length-1,t},e.ClipperBase.prototype.DisposeOutRec=function(t){var e=this.m_PolyOuts[t];e.Pts=null,e=null,this.m_PolyOuts[t]=null},e.ClipperBase.prototype.UpdateEdgeIntoAEL=function(t){null===t.NextInLML&&e.Error("UpdateEdgeIntoAEL: invalid call");var r=t.PrevInAEL,n=t.NextInAEL;return t.NextInLML.OutIdx=t.OutIdx,null!==r?r.NextInAEL=t.NextInLML:this.m_ActiveEdges=t.NextInLML,null!==n&&(n.PrevInAEL=t.NextInLML),t.NextInLML.Side=t.Side,t.NextInLML.WindDelta=t.WindDelta,t.NextInLML.WindCnt=t.WindCnt,t.NextInLML.WindCnt2=t.WindCnt2,(t=t.NextInLML).Curr.X=t.Bot.X,t.Curr.Y=t.Bot.Y,t.PrevInAEL=r,t.NextInAEL=n,e.ClipperBase.IsHorizontal(t)||this.InsertScanbeam(t.Top.Y),t},e.ClipperBase.prototype.SwapPositionsInAEL=function(t,e){if(t.NextInAEL!==t.PrevInAEL&&e.NextInAEL!==e.PrevInAEL){if(t.NextInAEL===e){var r=e.NextInAEL;null!==r&&(r.PrevInAEL=t);var n=t.PrevInAEL;null!==n&&(n.NextInAEL=e),e.PrevInAEL=n,e.NextInAEL=t,t.PrevInAEL=e,t.NextInAEL=r}else if(e.NextInAEL===t){var i=t.NextInAEL;null!==i&&(i.PrevInAEL=e);var o=e.PrevInAEL;null!==o&&(o.NextInAEL=t),t.PrevInAEL=o,t.NextInAEL=e,e.PrevInAEL=t,e.NextInAEL=i}else{var s=t.NextInAEL,a=t.PrevInAEL;t.NextInAEL=e.NextInAEL,null!==t.NextInAEL&&(t.NextInAEL.PrevInAEL=t),t.PrevInAEL=e.PrevInAEL,null!==t.PrevInAEL&&(t.PrevInAEL.NextInAEL=t),e.NextInAEL=s,null!==e.NextInAEL&&(e.NextInAEL.PrevInAEL=e),e.PrevInAEL=a,null!==e.PrevInAEL&&(e.PrevInAEL.NextInAEL=e)}null===t.PrevInAEL?this.m_ActiveEdges=t:null===e.PrevInAEL&&(this.m_ActiveEdges=e)}},e.ClipperBase.prototype.DeleteFromAEL=function(t){var e=t.PrevInAEL,r=t.NextInAEL;null===e&&null===r&&t!==this.m_ActiveEdges||(null!==e?e.NextInAEL=r:this.m_ActiveEdges=r,null!==r&&(r.PrevInAEL=e),t.NextInAEL=null,t.PrevInAEL=null)},e.Clipper=function(t){void 0===t&&(t=0),this.m_PolyOuts=null,this.m_ClipType=e.ClipType.ctIntersection,this.m_Scanbeam=null,this.m_Maxima=null,this.m_ActiveEdges=null,this.m_SortedEdges=null,this.m_IntersectList=null,this.m_IntersectNodeComparer=null,this.m_ExecuteLocked=!1,this.m_ClipFillType=e.PolyFillType.pftEvenOdd,this.m_SubjFillType=e.PolyFillType.pftEvenOdd,this.m_Joins=null,this.m_GhostJoins=null,this.m_UsingPolyTree=!1,this.ReverseSolution=!1,this.StrictlySimple=!1,e.ClipperBase.call(this),this.m_Scanbeam=null,this.m_Maxima=null,this.m_ActiveEdges=null,this.m_SortedEdges=null,this.m_IntersectList=new Array,this.m_IntersectNodeComparer=e.MyIntersectNodeSort.Compare,this.m_ExecuteLocked=!1,this.m_UsingPolyTree=!1,this.m_PolyOuts=new Array,this.m_Joins=new Array,this.m_GhostJoins=new Array,this.ReverseSolution=0!=(1&t),this.StrictlySimple=0!=(2&t),this.PreserveCollinear=0!=(4&t),e.use_xyz&&(this.ZFillFunction=null)},e.Clipper.ioReverseSolution=1,e.Clipper.ioStrictlySimple=2,e.Clipper.ioPreserveCollinear=4,e.Clipper.prototype.Clear=function(){0!==this.m_edges.length&&(this.DisposeAllPolyPts(),e.ClipperBase.prototype.Clear.call(this))},e.Clipper.prototype.InsertMaxima=function(t){var r=new e.Maxima;if(r.X=t,null===this.m_Maxima)this.m_Maxima=r,this.m_Maxima.Next=null,this.m_Maxima.Prev=null;else if(t<this.m_Maxima.X)r.Next=this.m_Maxima,r.Prev=null,this.m_Maxima=r;else{for(var n=this.m_Maxima;null!==n.Next&&t>=n.Next.X;)n=n.Next;if(t===n.X)return;r.Next=n.Next,r.Prev=n,null!==n.Next&&(n.Next.Prev=r),n.Next=r}},e.Clipper.prototype.Execute=function(){var t=arguments,r=t.length,n=t[1]instanceof e.PolyTree;if(4===r&&!n){var i=t[0],o=t[1],s=t[2],a=t[3];if(this.m_ExecuteLocked)return!1;this.m_HasOpenPaths&&e.Error("Error: PolyTree struct is needed for open path clipping."),this.m_ExecuteLocked=!0,e.Clear(o),this.m_SubjFillType=s,this.m_ClipFillType=a,this.m_ClipType=i,this.m_UsingPolyTree=!1;try{(u=this.ExecuteInternal())&&this.BuildResult(o)}finally{this.DisposeAllPolyPts(),this.m_ExecuteLocked=!1}return u}if(4===r&&n){i=t[0];var l=t[1];if(s=t[2],a=t[3],this.m_ExecuteLocked)return!1;this.m_ExecuteLocked=!0,this.m_SubjFillType=s,this.m_ClipFillType=a,this.m_ClipType=i,this.m_UsingPolyTree=!0;try{var u;(u=this.ExecuteInternal())&&this.BuildResult2(l)}finally{this.DisposeAllPolyPts(),this.m_ExecuteLocked=!1}return u}return 2!==r||n?2===r&&n?(i=t[0],l=t[1],this.Execute(i,l,e.PolyFillType.pftEvenOdd,e.PolyFillType.pftEvenOdd)):void 0:(i=t[0],o=t[1],this.Execute(i,o,e.PolyFillType.pftEvenOdd,e.PolyFillType.pftEvenOdd))},e.Clipper.prototype.FixHoleLinkage=function(t){if(null!==t.FirstLeft&&(t.IsHole===t.FirstLeft.IsHole||null===t.FirstLeft.Pts)){for(var e=t.FirstLeft;null!==e&&(e.IsHole===t.IsHole||null===e.Pts);)e=e.FirstLeft;t.FirstLeft=e}},e.Clipper.prototype.ExecuteInternal=function(){try{this.Reset(),this.m_SortedEdges=null,this.m_Maxima=null;var t,e,r,n={},i={};if(!this.PopScanbeam(n))return!1;for(this.InsertLocalMinimaIntoAEL(n.v);this.PopScanbeam(i)||this.LocalMinimaPending();){if(this.ProcessHorizontals(),this.m_GhostJoins.length=0,!this.ProcessIntersections(i.v))return!1;this.ProcessEdgesAtTopOfScanbeam(i.v),n.v=i.v,this.InsertLocalMinimaIntoAEL(n.v)}for(e=0,r=this.m_PolyOuts.length;e<r;e++)null===(t=this.m_PolyOuts[e]).Pts||t.IsOpen||(t.IsHole^this.ReverseSolution)==this.Area$1(t)>0&&this.ReversePolyPtLinks(t.Pts);for(this.JoinCommonEdges(),e=0,r=this.m_PolyOuts.length;e<r;e++)null!==(t=this.m_PolyOuts[e]).Pts&&(t.IsOpen?this.FixupOutPolyline(t):this.FixupOutPolygon(t));return this.StrictlySimple&&this.DoSimplePolygons(),!0}finally{this.m_Joins.length=0,this.m_GhostJoins.length=0}},e.Clipper.prototype.DisposeAllPolyPts=function(){for(var t=0,r=this.m_PolyOuts.length;t<r;++t)this.DisposeOutRec(t);e.Clear(this.m_PolyOuts)},e.Clipper.prototype.AddJoin=function(t,r,n){var i=new e.Join;i.OutPt1=t,i.OutPt2=r,i.OffPt.X=n.X,i.OffPt.Y=n.Y,e.use_xyz&&(i.OffPt.Z=n.Z),this.m_Joins.push(i)},e.Clipper.prototype.AddGhostJoin=function(t,r){var n=new e.Join;n.OutPt1=t,n.OffPt.X=r.X,n.OffPt.Y=r.Y,e.use_xyz&&(n.OffPt.Z=r.Z),this.m_GhostJoins.push(n)},e.Clipper.prototype.SetZ=function(t,r,n){if(null!==this.ZFillFunction){if(0!==t.Z||null===this.ZFillFunction)return;e.IntPoint.op_Equality(t,r.Bot)?t.Z=r.Bot.Z:e.IntPoint.op_Equality(t,r.Top)?t.Z=r.Top.Z:e.IntPoint.op_Equality(t,n.Bot)?t.Z=n.Bot.Z:e.IntPoint.op_Equality(t,n.Top)?t.Z=n.Top.Z:this.ZFillFunction(r.Bot,r.Top,n.Bot,n.Top,t)}},e.Clipper.prototype.InsertLocalMinimaIntoAEL=function(t){for(var r,n,i={};this.PopLocalMinima(t,i);){r=i.v.LeftBound,n=i.v.RightBound;var o=null;if(null===r?(this.InsertEdgeIntoAEL(n,null),this.SetWindingCount(n),this.IsContributing(n)&&(o=this.AddOutPt(n,n.Bot))):null===n?(this.InsertEdgeIntoAEL(r,null),this.SetWindingCount(r),this.IsContributing(r)&&(o=this.AddOutPt(r,r.Bot)),this.InsertScanbeam(r.Top.Y)):(this.InsertEdgeIntoAEL(r,null),this.InsertEdgeIntoAEL(n,r),this.SetWindingCount(r),n.WindCnt=r.WindCnt,n.WindCnt2=r.WindCnt2,this.IsContributing(r)&&(o=this.AddLocalMinPoly(r,n,r.Bot)),this.InsertScanbeam(r.Top.Y)),null!==n&&(e.ClipperBase.IsHorizontal(n)?(null!==n.NextInLML&&this.InsertScanbeam(n.NextInLML.Top.Y),this.AddEdgeToSEL(n)):this.InsertScanbeam(n.Top.Y)),null!==r&&null!==n){if(null!==o&&e.ClipperBase.IsHorizontal(n)&&this.m_GhostJoins.length>0&&0!==n.WindDelta)for(var s=0,a=this.m_GhostJoins.length;s<a;s++){var l=this.m_GhostJoins[s];this.HorzSegmentsOverlap(l.OutPt1.Pt.X,l.OffPt.X,n.Bot.X,n.Top.X)&&this.AddJoin(l.OutPt1,o,l.OffPt)}if(r.OutIdx>=0&&null!==r.PrevInAEL&&r.PrevInAEL.Curr.X===r.Bot.X&&r.PrevInAEL.OutIdx>=0&&e.ClipperBase.SlopesEqual5(r.PrevInAEL.Curr,r.PrevInAEL.Top,r.Curr,r.Top,this.m_UseFullRange)&&0!==r.WindDelta&&0!==r.PrevInAEL.WindDelta){var u=this.AddOutPt(r.PrevInAEL,r.Bot);this.AddJoin(o,u,r.Top)}if(r.NextInAEL!==n){n.OutIdx>=0&&n.PrevInAEL.OutIdx>=0&&e.ClipperBase.SlopesEqual5(n.PrevInAEL.Curr,n.PrevInAEL.Top,n.Curr,n.Top,this.m_UseFullRange)&&0!==n.WindDelta&&0!==n.PrevInAEL.WindDelta&&(u=this.AddOutPt(n.PrevInAEL,n.Bot),this.AddJoin(o,u,n.Top));var h=r.NextInAEL;if(null!==h)for(;h!==n;)this.IntersectEdges(n,h,r.Curr),h=h.NextInAEL}}}},e.Clipper.prototype.InsertEdgeIntoAEL=function(t,e){if(null===this.m_ActiveEdges)t.PrevInAEL=null,t.NextInAEL=null,this.m_ActiveEdges=t;else if(null===e&&this.E2InsertsBeforeE1(this.m_ActiveEdges,t))t.PrevInAEL=null,t.NextInAEL=this.m_ActiveEdges,this.m_ActiveEdges.PrevInAEL=t,this.m_ActiveEdges=t;else{for(null===e&&(e=this.m_ActiveEdges);null!==e.NextInAEL&&!this.E2InsertsBeforeE1(e.NextInAEL,t);)e=e.NextInAEL;t.NextInAEL=e.NextInAEL,null!==e.NextInAEL&&(e.NextInAEL.PrevInAEL=t),t.PrevInAEL=e,e.NextInAEL=t}},e.Clipper.prototype.E2InsertsBeforeE1=function(t,r){return r.Curr.X===t.Curr.X?r.Top.Y>t.Top.Y?r.Top.X<e.Clipper.TopX(t,r.Top.Y):t.Top.X>e.Clipper.TopX(r,t.Top.Y):r.Curr.X<t.Curr.X},e.Clipper.prototype.IsEvenOddFillType=function(t){return t.PolyTyp===e.PolyType.ptSubject?this.m_SubjFillType===e.PolyFillType.pftEvenOdd:this.m_ClipFillType===e.PolyFillType.pftEvenOdd},e.Clipper.prototype.IsEvenOddAltFillType=function(t){return t.PolyTyp===e.PolyType.ptSubject?this.m_ClipFillType===e.PolyFillType.pftEvenOdd:this.m_SubjFillType===e.PolyFillType.pftEvenOdd},e.Clipper.prototype.IsContributing=function(t){var r,n;switch(t.PolyTyp===e.PolyType.ptSubject?(r=this.m_SubjFillType,n=this.m_ClipFillType):(r=this.m_ClipFillType,n=this.m_SubjFillType),r){case e.PolyFillType.pftEvenOdd:if(0===t.WindDelta&&1!==t.WindCnt)return!1;break;case e.PolyFillType.pftNonZero:if(1!==Math.abs(t.WindCnt))return!1;break;case e.PolyFillType.pftPositive:if(1!==t.WindCnt)return!1;break;default:if(-1!==t.WindCnt)return!1}switch(this.m_ClipType){case e.ClipType.ctIntersection:switch(n){case e.PolyFillType.pftEvenOdd:case e.PolyFillType.pftNonZero:return 0!==t.WindCnt2;case e.PolyFillType.pftPositive:return t.WindCnt2>0;default:return t.WindCnt2<0}case e.ClipType.ctUnion:switch(n){case e.PolyFillType.pftEvenOdd:case e.PolyFillType.pftNonZero:return 0===t.WindCnt2;case e.PolyFillType.pftPositive:return t.WindCnt2<=0;default:return t.WindCnt2>=0}case e.ClipType.ctDifference:if(t.PolyTyp===e.PolyType.ptSubject)switch(n){case e.PolyFillType.pftEvenOdd:case e.PolyFillType.pftNonZero:return 0===t.WindCnt2;case e.PolyFillType.pftPositive:return t.WindCnt2<=0;default:return t.WindCnt2>=0}else switch(n){case e.PolyFillType.pftEvenOdd:case e.PolyFillType.pftNonZero:return 0!==t.WindCnt2;case e.PolyFillType.pftPositive:return t.WindCnt2>0;default:return t.WindCnt2<0}case e.ClipType.ctXor:if(0!==t.WindDelta)return!0;switch(n){case e.PolyFillType.pftEvenOdd:case e.PolyFillType.pftNonZero:return 0===t.WindCnt2;case e.PolyFillType.pftPositive:return t.WindCnt2<=0;default:return t.WindCnt2>=0}}return!0},e.Clipper.prototype.SetWindingCount=function(t){for(var r=t.PrevInAEL;null!==r&&(r.PolyTyp!==t.PolyTyp||0===r.WindDelta);)r=r.PrevInAEL;if(null===r){var n=t.PolyTyp===e.PolyType.ptSubject?this.m_SubjFillType:this.m_ClipFillType;0===t.WindDelta?t.WindCnt=n===e.PolyFillType.pftNegative?-1:1:t.WindCnt=t.WindDelta,t.WindCnt2=0,r=this.m_ActiveEdges}else if(0===t.WindDelta&&this.m_ClipType!==e.ClipType.ctUnion)t.WindCnt=1,t.WindCnt2=r.WindCnt2,r=r.NextInAEL;else if(this.IsEvenOddFillType(t)){if(0===t.WindDelta){for(var i=!0,o=r.PrevInAEL;null!==o;)o.PolyTyp===r.PolyTyp&&0!==o.WindDelta&&(i=!i),o=o.PrevInAEL;t.WindCnt=i?0:1}else t.WindCnt=t.WindDelta;t.WindCnt2=r.WindCnt2,r=r.NextInAEL}else r.WindCnt*r.WindDelta<0?Math.abs(r.WindCnt)>1?r.WindDelta*t.WindDelta<0?t.WindCnt=r.WindCnt:t.WindCnt=r.WindCnt+t.WindDelta:t.WindCnt=0===t.WindDelta?1:t.WindDelta:0===t.WindDelta?t.WindCnt=r.WindCnt<0?r.WindCnt-1:r.WindCnt+1:r.WindDelta*t.WindDelta<0?t.WindCnt=r.WindCnt:t.WindCnt=r.WindCnt+t.WindDelta,t.WindCnt2=r.WindCnt2,r=r.NextInAEL;if(this.IsEvenOddAltFillType(t))for(;r!==t;)0!==r.WindDelta&&(t.WindCnt2=0===t.WindCnt2?1:0),r=r.NextInAEL;else for(;r!==t;)t.WindCnt2+=r.WindDelta,r=r.NextInAEL},e.Clipper.prototype.AddEdgeToSEL=function(t){null===this.m_SortedEdges?(this.m_SortedEdges=t,t.PrevInSEL=null,t.NextInSEL=null):(t.NextInSEL=this.m_SortedEdges,t.PrevInSEL=null,this.m_SortedEdges.PrevInSEL=t,this.m_SortedEdges=t)},e.Clipper.prototype.PopEdgeFromSEL=function(t){if(t.v=this.m_SortedEdges,null===t.v)return!1;var e=t.v;return this.m_SortedEdges=t.v.NextInSEL,null!==this.m_SortedEdges&&(this.m_SortedEdges.PrevInSEL=null),e.NextInSEL=null,e.PrevInSEL=null,!0},e.Clipper.prototype.CopyAELToSEL=function(){var t=this.m_ActiveEdges;for(this.m_SortedEdges=t;null!==t;)t.PrevInSEL=t.PrevInAEL,t.NextInSEL=t.NextInAEL,t=t.NextInAEL},e.Clipper.prototype.SwapPositionsInSEL=function(t,e){if(!(null===t.NextInSEL&&null===t.PrevInSEL||null===e.NextInSEL&&null===e.PrevInSEL)){if(t.NextInSEL===e)null!==(r=e.NextInSEL)&&(r.PrevInSEL=t),null!==(n=t.PrevInSEL)&&(n.NextInSEL=e),e.PrevInSEL=n,e.NextInSEL=t,t.PrevInSEL=e,t.NextInSEL=r;else if(e.NextInSEL===t)null!==(r=t.NextInSEL)&&(r.PrevInSEL=e),null!==(n=e.PrevInSEL)&&(n.NextInSEL=t),t.PrevInSEL=n,t.NextInSEL=e,e.PrevInSEL=t,e.NextInSEL=r;else{var r=t.NextInSEL,n=t.PrevInSEL;t.NextInSEL=e.NextInSEL,null!==t.NextInSEL&&(t.NextInSEL.PrevInSEL=t),t.PrevInSEL=e.PrevInSEL,null!==t.PrevInSEL&&(t.PrevInSEL.NextInSEL=t),e.NextInSEL=r,null!==e.NextInSEL&&(e.NextInSEL.PrevInSEL=e),e.PrevInSEL=n,null!==e.PrevInSEL&&(e.PrevInSEL.NextInSEL=e)}null===t.PrevInSEL?this.m_SortedEdges=t:null===e.PrevInSEL&&(this.m_SortedEdges=e)}},e.Clipper.prototype.AddLocalMaxPoly=function(t,e,r){this.AddOutPt(t,r),0===e.WindDelta&&this.AddOutPt(e,r),t.OutIdx===e.OutIdx?(t.OutIdx=-1,e.OutIdx=-1):t.OutIdx<e.OutIdx?this.AppendPolygon(t,e):this.AppendPolygon(e,t)},e.Clipper.prototype.AddLocalMinPoly=function(t,r,n){var i,o,s;if(e.ClipperBase.IsHorizontal(r)||t.Dx>r.Dx?(i=this.AddOutPt(t,n),r.OutIdx=t.OutIdx,t.Side=e.EdgeSide.esLeft,r.Side=e.EdgeSide.esRight,s=(o=t).PrevInAEL===r?r.PrevInAEL:o.PrevInAEL):(i=this.AddOutPt(r,n),t.OutIdx=r.OutIdx,t.Side=e.EdgeSide.esRight,r.Side=e.EdgeSide.esLeft,s=(o=r).PrevInAEL===t?t.PrevInAEL:o.PrevInAEL),null!==s&&s.OutIdx>=0&&s.Top.Y<n.Y&&o.Top.Y<n.Y){var a=e.Clipper.TopX(s,n.Y),l=e.Clipper.TopX(o,n.Y);if(a===l&&0!==o.WindDelta&&0!==s.WindDelta&&e.ClipperBase.SlopesEqual5(new e.IntPoint2(a,n.Y),s.Top,new e.IntPoint2(l,n.Y),o.Top,this.m_UseFullRange)){var u=this.AddOutPt(s,n);this.AddJoin(i,u,o.Top)}}return i},e.Clipper.prototype.AddOutPt=function(t,r){if(t.OutIdx<0){(i=this.CreateOutRec()).IsOpen=0===t.WindDelta;var n=new e.OutPt;return i.Pts=n,n.Idx=i.Idx,n.Pt.X=r.X,n.Pt.Y=r.Y,e.use_xyz&&(n.Pt.Z=r.Z),n.Next=n,n.Prev=n,i.IsOpen||this.SetHoleState(t,i),t.OutIdx=i.Idx,n}var i,o=(i=this.m_PolyOuts[t.OutIdx]).Pts,s=t.Side===e.EdgeSide.esLeft;return s&&e.IntPoint.op_Equality(r,o.Pt)?o:!s&&e.IntPoint.op_Equality(r,o.Prev.Pt)?o.Prev:((n=new e.OutPt).Idx=i.Idx,n.Pt.X=r.X,n.Pt.Y=r.Y,e.use_xyz&&(n.Pt.Z=r.Z),n.Next=o,n.Prev=o.Prev,n.Prev.Next=n,o.Prev=n,s&&(i.Pts=n),n)},e.Clipper.prototype.GetLastOutPt=function(t){var r=this.m_PolyOuts[t.OutIdx];return t.Side===e.EdgeSide.esLeft?r.Pts:r.Pts.Prev},e.Clipper.prototype.SwapPoints=function(t,r){var n=new e.IntPoint1(t.Value);t.Value.X=r.Value.X,t.Value.Y=r.Value.Y,e.use_xyz&&(t.Value.Z=r.Value.Z),r.Value.X=n.X,r.Value.Y=n.Y,e.use_xyz&&(r.Value.Z=n.Z)},e.Clipper.prototype.HorzSegmentsOverlap=function(t,e,r,n){var i;return t>e&&(i=t,t=e,e=i),r>n&&(i=r,r=n,n=i),t<n&&r<e},e.Clipper.prototype.SetHoleState=function(t,e){for(var r=t.PrevInAEL,n=null;null!==r;)r.OutIdx>=0&&0!==r.WindDelta&&(null===n?n=r:n.OutIdx===r.OutIdx&&(n=null)),r=r.PrevInAEL;null===n?(e.FirstLeft=null,e.IsHole=!1):(e.FirstLeft=this.m_PolyOuts[n.OutIdx],e.IsHole=!e.FirstLeft.IsHole)},e.Clipper.prototype.GetDx=function(t,r){return t.Y===r.Y?e.ClipperBase.horizontal:(r.X-t.X)/(r.Y-t.Y)},e.Clipper.prototype.FirstIsBottomPt=function(t,r){for(var n=t.Prev;e.IntPoint.op_Equality(n.Pt,t.Pt)&&n!==t;)n=n.Prev;var i=Math.abs(this.GetDx(t.Pt,n.Pt));for(n=t.Next;e.IntPoint.op_Equality(n.Pt,t.Pt)&&n!==t;)n=n.Next;var o=Math.abs(this.GetDx(t.Pt,n.Pt));for(n=r.Prev;e.IntPoint.op_Equality(n.Pt,r.Pt)&&n!==r;)n=n.Prev;var s=Math.abs(this.GetDx(r.Pt,n.Pt));for(n=r.Next;e.IntPoint.op_Equality(n.Pt,r.Pt)&&n!==r;)n=n.Next;var a=Math.abs(this.GetDx(r.Pt,n.Pt));return Math.max(i,o)===Math.max(s,a)&&Math.min(i,o)===Math.min(s,a)?this.Area(t)>0:i>=s&&i>=a||o>=s&&o>=a},e.Clipper.prototype.GetBottomPt=function(t){for(var r=null,n=t.Next;n!==t;)n.Pt.Y>t.Pt.Y?(t=n,r=null):n.Pt.Y===t.Pt.Y&&n.Pt.X<=t.Pt.X&&(n.Pt.X<t.Pt.X?(r=null,t=n):n.Next!==t&&n.Prev!==t&&(r=n)),n=n.Next;if(null!==r)for(;r!==n;)for(this.FirstIsBottomPt(n,r)||(t=r),r=r.Next;e.IntPoint.op_Inequality(r.Pt,t.Pt);)r=r.Next;return t},e.Clipper.prototype.GetLowermostRec=function(t,e){null===t.BottomPt&&(t.BottomPt=this.GetBottomPt(t.Pts)),null===e.BottomPt&&(e.BottomPt=this.GetBottomPt(e.Pts));var r=t.BottomPt,n=e.BottomPt;return r.Pt.Y>n.Pt.Y?t:r.Pt.Y<n.Pt.Y?e:r.Pt.X<n.Pt.X?t:r.Pt.X>n.Pt.X||r.Next===r?e:n.Next===n||this.FirstIsBottomPt(r,n)?t:e},e.Clipper.prototype.OutRec1RightOfOutRec2=function(t,e){do{if((t=t.FirstLeft)===e)return!0}while(null!==t);return!1},e.Clipper.prototype.GetOutRec=function(t){for(var e=this.m_PolyOuts[t];e!==this.m_PolyOuts[e.Idx];)e=this.m_PolyOuts[e.Idx];return e},e.Clipper.prototype.AppendPolygon=function(t,r){var n,i=this.m_PolyOuts[t.OutIdx],o=this.m_PolyOuts[r.OutIdx];n=this.OutRec1RightOfOutRec2(i,o)?o:this.OutRec1RightOfOutRec2(o,i)?i:this.GetLowermostRec(i,o);var s=i.Pts,a=s.Prev,l=o.Pts,u=l.Prev;t.Side===e.EdgeSide.esLeft?r.Side===e.EdgeSide.esLeft?(this.ReversePolyPtLinks(l),l.Next=s,s.Prev=l,a.Next=u,u.Prev=a,i.Pts=u):(u.Next=s,s.Prev=u,l.Prev=a,a.Next=l,i.Pts=l):r.Side===e.EdgeSide.esRight?(this.ReversePolyPtLinks(l),a.Next=u,u.Prev=a,l.Next=s,s.Prev=l):(a.Next=l,l.Prev=a,s.Prev=u,u.Next=s),i.BottomPt=null,n===o&&(o.FirstLeft!==i&&(i.FirstLeft=o.FirstLeft),i.IsHole=o.IsHole),o.Pts=null,o.BottomPt=null,o.FirstLeft=i;var h=t.OutIdx,c=r.OutIdx;t.OutIdx=-1,r.OutIdx=-1;for(var p=this.m_ActiveEdges;null!==p;){if(p.OutIdx===c){p.OutIdx=h,p.Side=t.Side;break}p=p.NextInAEL}o.Idx=i.Idx},e.Clipper.prototype.ReversePolyPtLinks=function(t){if(null!==t){var e,r;e=t;do{r=e.Next,e.Next=e.Prev,e.Prev=r,e=r}while(e!==t)}},e.Clipper.SwapSides=function(t,e){var r=t.Side;t.Side=e.Side,e.Side=r},e.Clipper.SwapPolyIndexes=function(t,e){var r=t.OutIdx;t.OutIdx=e.OutIdx,e.OutIdx=r},e.Clipper.prototype.IntersectEdges=function(t,r,n){var i=t.OutIdx>=0,o=r.OutIdx>=0;if(e.use_xyz&&this.SetZ(n,t,r),!e.use_lines||0!==t.WindDelta&&0!==r.WindDelta){if(t.PolyTyp===r.PolyTyp)if(this.IsEvenOddFillType(t)){var s=t.WindCnt;t.WindCnt=r.WindCnt,r.WindCnt=s}else t.WindCnt+r.WindDelta===0?t.WindCnt=-t.WindCnt:t.WindCnt+=r.WindDelta,r.WindCnt-t.WindDelta==0?r.WindCnt=-r.WindCnt:r.WindCnt-=t.WindDelta;else this.IsEvenOddFillType(r)?t.WindCnt2=0===t.WindCnt2?1:0:t.WindCnt2+=r.WindDelta,this.IsEvenOddFillType(t)?r.WindCnt2=0===r.WindCnt2?1:0:r.WindCnt2-=t.WindDelta;var a,l,u,h,c,p;switch(t.PolyTyp===e.PolyType.ptSubject?(a=this.m_SubjFillType,u=this.m_ClipFillType):(a=this.m_ClipFillType,u=this.m_SubjFillType),r.PolyTyp===e.PolyType.ptSubject?(l=this.m_SubjFillType,h=this.m_ClipFillType):(l=this.m_ClipFillType,h=this.m_SubjFillType),a){case e.PolyFillType.pftPositive:c=t.WindCnt;break;case e.PolyFillType.pftNegative:c=-t.WindCnt;break;default:c=Math.abs(t.WindCnt)}switch(l){case e.PolyFillType.pftPositive:p=r.WindCnt;break;case e.PolyFillType.pftNegative:p=-r.WindCnt;break;default:p=Math.abs(r.WindCnt)}if(i&&o)0!==c&&1!==c||0!==p&&1!==p||t.PolyTyp!==r.PolyTyp&&this.m_ClipType!==e.ClipType.ctXor?this.AddLocalMaxPoly(t,r,n):(this.AddOutPt(t,n),this.AddOutPt(r,n),e.Clipper.SwapSides(t,r),e.Clipper.SwapPolyIndexes(t,r));else if(i)0!==p&&1!==p||(this.AddOutPt(t,n),e.Clipper.SwapSides(t,r),e.Clipper.SwapPolyIndexes(t,r));else if(o)0!==c&&1!==c||(this.AddOutPt(r,n),e.Clipper.SwapSides(t,r),e.Clipper.SwapPolyIndexes(t,r));else if(!(0!==c&&1!==c||0!==p&&1!==p)){var f,d;switch(u){case e.PolyFillType.pftPositive:f=t.WindCnt2;break;case e.PolyFillType.pftNegative:f=-t.WindCnt2;break;default:f=Math.abs(t.WindCnt2)}switch(h){case e.PolyFillType.pftPositive:d=r.WindCnt2;break;case e.PolyFillType.pftNegative:d=-r.WindCnt2;break;default:d=Math.abs(r.WindCnt2)}if(t.PolyTyp!==r.PolyTyp)this.AddLocalMinPoly(t,r,n);else if(1===c&&1===p)switch(this.m_ClipType){case e.ClipType.ctIntersection:f>0&&d>0&&this.AddLocalMinPoly(t,r,n);break;case e.ClipType.ctUnion:f<=0&&d<=0&&this.AddLocalMinPoly(t,r,n);break;case e.ClipType.ctDifference:(t.PolyTyp===e.PolyType.ptClip&&f>0&&d>0||t.PolyTyp===e.PolyType.ptSubject&&f<=0&&d<=0)&&this.AddLocalMinPoly(t,r,n);break;case e.ClipType.ctXor:this.AddLocalMinPoly(t,r,n)}else e.Clipper.SwapSides(t,r)}}else{if(0===t.WindDelta&&0===r.WindDelta)return;t.PolyTyp===r.PolyTyp&&t.WindDelta!==r.WindDelta&&this.m_ClipType===e.ClipType.ctUnion?0===t.WindDelta?o&&(this.AddOutPt(t,n),i&&(t.OutIdx=-1)):i&&(this.AddOutPt(r,n),o&&(r.OutIdx=-1)):t.PolyTyp!==r.PolyTyp&&(0!==t.WindDelta||1!==Math.abs(r.WindCnt)||this.m_ClipType===e.ClipType.ctUnion&&0!==r.WindCnt2?0!==r.WindDelta||1!==Math.abs(t.WindCnt)||this.m_ClipType===e.ClipType.ctUnion&&0!==t.WindCnt2||(this.AddOutPt(r,n),o&&(r.OutIdx=-1)):(this.AddOutPt(t,n),i&&(t.OutIdx=-1)))}},e.Clipper.prototype.DeleteFromSEL=function(t){var e=t.PrevInSEL,r=t.NextInSEL;null===e&&null===r&&t!==this.m_SortedEdges||(null!==e?e.NextInSEL=r:this.m_SortedEdges=r,null!==r&&(r.PrevInSEL=e),t.NextInSEL=null,t.PrevInSEL=null)},e.Clipper.prototype.ProcessHorizontals=function(){for(var t={};this.PopEdgeFromSEL(t);)this.ProcessHorizontal(t.v)},e.Clipper.prototype.GetHorzDirection=function(t,r){t.Bot.X<t.Top.X?(r.Left=t.Bot.X,r.Right=t.Top.X,r.Dir=e.Direction.dLeftToRight):(r.Left=t.Top.X,r.Right=t.Bot.X,r.Dir=e.Direction.dRightToLeft)},e.Clipper.prototype.ProcessHorizontal=function(t){var r={Dir:null,Left:null,Right:null};this.GetHorzDirection(t,r);for(var n=r.Dir,i=r.Left,o=r.Right,s=0===t.WindDelta,a=t,l=null;null!==a.NextInLML&&e.ClipperBase.IsHorizontal(a.NextInLML);)a=a.NextInLML;null===a.NextInLML&&(l=this.GetMaximaPair(a));var u=this.m_Maxima;if(null!==u)if(n===e.Direction.dLeftToRight){for(;null!==u&&u.X<=t.Bot.X;)u=u.Next;null!==u&&u.X>=a.Top.X&&(u=null)}else{for(;null!==u.Next&&u.Next.X<t.Bot.X;)u=u.Next;u.X<=a.Top.X&&(u=null)}for(var h=null;;){for(var c=t===a,p=this.GetNextInAEL(t,n);null!==p;){if(null!==u)if(n===e.Direction.dLeftToRight)for(;null!==u&&u.X<p.Curr.X;)t.OutIdx>=0&&!s&&this.AddOutPt(t,new e.IntPoint2(u.X,t.Bot.Y)),u=u.Next;else for(;null!==u&&u.X>p.Curr.X;)t.OutIdx>=0&&!s&&this.AddOutPt(t,new e.IntPoint2(u.X,t.Bot.Y)),u=u.Prev;if(n===e.Direction.dLeftToRight&&p.Curr.X>o||n===e.Direction.dRightToLeft&&p.Curr.X<i)break;if(p.Curr.X===t.Top.X&&null!==t.NextInLML&&p.Dx<t.NextInLML.Dx)break;if(t.OutIdx>=0&&!s){e.use_xyz&&(n===e.Direction.dLeftToRight?this.SetZ(p.Curr,t,p):this.SetZ(p.Curr,p,t)),h=this.AddOutPt(t,p.Curr);for(var f=this.m_SortedEdges;null!==f;){if(f.OutIdx>=0&&this.HorzSegmentsOverlap(t.Bot.X,t.Top.X,f.Bot.X,f.Top.X)){var d=this.GetLastOutPt(f);this.AddJoin(d,h,f.Top)}f=f.NextInSEL}this.AddGhostJoin(h,t.Bot)}if(p===l&&c)return t.OutIdx>=0&&this.AddLocalMaxPoly(t,l,t.Top),this.DeleteFromAEL(t),void this.DeleteFromAEL(l);if(n===e.Direction.dLeftToRight){var y=new e.IntPoint2(p.Curr.X,t.Curr.Y);this.IntersectEdges(t,p,y)}else y=new e.IntPoint2(p.Curr.X,t.Curr.Y),this.IntersectEdges(p,t,y);var v=this.GetNextInAEL(p,n);this.SwapPositionsInAEL(t,p),p=v}if(null===t.NextInLML||!e.ClipperBase.IsHorizontal(t.NextInLML))break;(t=this.UpdateEdgeIntoAEL(t)).OutIdx>=0&&this.AddOutPt(t,t.Bot),r={Dir:n,Left:i,Right:o},this.GetHorzDirection(t,r),n=r.Dir,i=r.Left,o=r.Right}if(t.OutIdx>=0&&null===h){for(h=this.GetLastOutPt(t),f=this.m_SortedEdges;null!==f;)f.OutIdx>=0&&this.HorzSegmentsOverlap(t.Bot.X,t.Top.X,f.Bot.X,f.Top.X)&&(d=this.GetLastOutPt(f),this.AddJoin(d,h,f.Top)),f=f.NextInSEL;this.AddGhostJoin(h,t.Top)}if(null!==t.NextInLML)if(t.OutIdx>=0){if(h=this.AddOutPt(t,t.Top),0===(t=this.UpdateEdgeIntoAEL(t)).WindDelta)return;var m=t.PrevInAEL;v=t.NextInAEL,null!==m&&m.Curr.X===t.Bot.X&&m.Curr.Y===t.Bot.Y&&0===m.WindDelta&&m.OutIdx>=0&&m.Curr.Y>m.Top.Y&&e.ClipperBase.SlopesEqual3(t,m,this.m_UseFullRange)?(d=this.AddOutPt(m,t.Bot),this.AddJoin(h,d,t.Top)):null!==v&&v.Curr.X===t.Bot.X&&v.Curr.Y===t.Bot.Y&&0!==v.WindDelta&&v.OutIdx>=0&&v.Curr.Y>v.Top.Y&&e.ClipperBase.SlopesEqual3(t,v,this.m_UseFullRange)&&(d=this.AddOutPt(v,t.Bot),this.AddJoin(h,d,t.Top))}else t=this.UpdateEdgeIntoAEL(t);else t.OutIdx>=0&&this.AddOutPt(t,t.Top),this.DeleteFromAEL(t)},e.Clipper.prototype.GetNextInAEL=function(t,r){return r===e.Direction.dLeftToRight?t.NextInAEL:t.PrevInAEL},e.Clipper.prototype.IsMinima=function(t){return null!==t&&t.Prev.NextInLML!==t&&t.Next.NextInLML!==t},e.Clipper.prototype.IsMaxima=function(t,e){return null!==t&&t.Top.Y===e&&null===t.NextInLML},e.Clipper.prototype.IsIntermediate=function(t,e){return t.Top.Y===e&&null!==t.NextInLML},e.Clipper.prototype.GetMaximaPair=function(t){return e.IntPoint.op_Equality(t.Next.Top,t.Top)&&null===t.Next.NextInLML?t.Next:e.IntPoint.op_Equality(t.Prev.Top,t.Top)&&null===t.Prev.NextInLML?t.Prev:null},e.Clipper.prototype.GetMaximaPairEx=function(t){var r=this.GetMaximaPair(t);return null===r||r.OutIdx===e.ClipperBase.Skip||r.NextInAEL===r.PrevInAEL&&!e.ClipperBase.IsHorizontal(r)?null:r},e.Clipper.prototype.ProcessIntersections=function(t){if(null===this.m_ActiveEdges)return!0;try{if(this.BuildIntersectList(t),0===this.m_IntersectList.length)return!0;if(1!==this.m_IntersectList.length&&!this.FixupIntersectionOrder())return!1;this.ProcessIntersectList()}catch(t){this.m_SortedEdges=null,this.m_IntersectList.length=0,e.Error("ProcessIntersections error")}return this.m_SortedEdges=null,!0},e.Clipper.prototype.BuildIntersectList=function(t){if(null!==this.m_ActiveEdges){var r=this.m_ActiveEdges;for(this.m_SortedEdges=r;null!==r;)r.PrevInSEL=r.PrevInAEL,r.NextInSEL=r.NextInAEL,r.Curr.X=e.Clipper.TopX(r,t),r=r.NextInAEL;for(var n=!0;n&&null!==this.m_SortedEdges;){for(n=!1,r=this.m_SortedEdges;null!==r.NextInSEL;){var i=r.NextInSEL,o=new e.IntPoint0;if(r.Curr.X>i.Curr.X){this.IntersectPoint(r,i,o),o.Y<t&&(o=new e.IntPoint2(e.Clipper.TopX(r,t),t));var s=new e.IntersectNode;s.Edge1=r,s.Edge2=i,s.Pt.X=o.X,s.Pt.Y=o.Y,e.use_xyz&&(s.Pt.Z=o.Z),this.m_IntersectList.push(s),this.SwapPositionsInSEL(r,i),n=!0}else r=i}if(null===r.PrevInSEL)break;r.PrevInSEL.NextInSEL=null}this.m_SortedEdges=null}},e.Clipper.prototype.EdgesAdjacent=function(t){return t.Edge1.NextInSEL===t.Edge2||t.Edge1.PrevInSEL===t.Edge2},e.Clipper.IntersectNodeSort=function(t,e){return e.Pt.Y-t.Pt.Y},e.Clipper.prototype.FixupIntersectionOrder=function(){this.m_IntersectList.sort(this.m_IntersectNodeComparer),this.CopyAELToSEL();for(var t=this.m_IntersectList.length,e=0;e<t;e++){if(!this.EdgesAdjacent(this.m_IntersectList[e])){for(var r=e+1;r<t&&!this.EdgesAdjacent(this.m_IntersectList[r]);)r++;if(r===t)return!1;var n=this.m_IntersectList[e];this.m_IntersectList[e]=this.m_IntersectList[r],this.m_IntersectList[r]=n}this.SwapPositionsInSEL(this.m_IntersectList[e].Edge1,this.m_IntersectList[e].Edge2)}return!0},e.Clipper.prototype.ProcessIntersectList=function(){for(var t=0,e=this.m_IntersectList.length;t<e;t++){var r=this.m_IntersectList[t];this.IntersectEdges(r.Edge1,r.Edge2,r.Pt),this.SwapPositionsInAEL(r.Edge1,r.Edge2)}this.m_IntersectList.length=0},o.msie?e.Clipper.Round=function(t){return t<0?Math.ceil(t-.5):Math.round(t)}:o.chromium?e.Clipper.Round=function(t){return t<0?-Math.round(Math.abs(t)):Math.round(t)}:o.safari?e.Clipper.Round=function(t){return t<0?(t-=.5)<-2147483648?Math.ceil(t):0|t:(t+=.5)>2147483647?Math.floor(t):0|t}:e.Clipper.Round=function(t){return t<0?Math.ceil(t-.5):Math.floor(t+.5)},e.Clipper.TopX=function(t,r){return r===t.Top.Y?t.Top.X:t.Bot.X+e.Clipper.Round(t.Dx*(r-t.Bot.Y))},e.Clipper.prototype.IntersectPoint=function(t,r,n){var i,o;if(n.X=0,n.Y=0,t.Dx===r.Dx)return n.Y=t.Curr.Y,void(n.X=e.Clipper.TopX(t,n.Y));if(0===t.Delta.X)n.X=t.Bot.X,e.ClipperBase.IsHorizontal(r)?n.Y=r.Bot.Y:(o=r.Bot.Y-r.Bot.X/r.Dx,n.Y=e.Clipper.Round(n.X/r.Dx+o));else if(0===r.Delta.X)n.X=r.Bot.X,e.ClipperBase.IsHorizontal(t)?n.Y=t.Bot.Y:(i=t.Bot.Y-t.Bot.X/t.Dx,n.Y=e.Clipper.Round(n.X/t.Dx+i));else{i=t.Bot.X-t.Bot.Y*t.Dx;var s=((o=r.Bot.X-r.Bot.Y*r.Dx)-i)/(t.Dx-r.Dx);n.Y=e.Clipper.Round(s),Math.abs(t.Dx)<Math.abs(r.Dx)?n.X=e.Clipper.Round(t.Dx*s+i):n.X=e.Clipper.Round(r.Dx*s+o)}if(n.Y<t.Top.Y||n.Y<r.Top.Y){if(t.Top.Y>r.Top.Y)return n.Y=t.Top.Y,n.X=e.Clipper.TopX(r,t.Top.Y),n.X<t.Top.X;n.Y=r.Top.Y,Math.abs(t.Dx)<Math.abs(r.Dx)?n.X=e.Clipper.TopX(t,n.Y):n.X=e.Clipper.TopX(r,n.Y)}n.Y>t.Curr.Y&&(n.Y=t.Curr.Y,Math.abs(t.Dx)>Math.abs(r.Dx)?n.X=e.Clipper.TopX(r,n.Y):n.X=e.Clipper.TopX(t,n.Y))},e.Clipper.prototype.ProcessEdgesAtTopOfScanbeam=function(t){for(var r=this.m_ActiveEdges;null!==r;){var n=this.IsMaxima(r,t);if(n){var i=this.GetMaximaPairEx(r);n=null===i||!e.ClipperBase.IsHorizontal(i)}if(n){this.StrictlySimple&&this.InsertMaxima(r.Top.X);var o=r.PrevInAEL;this.DoMaxima(r),r=null===o?this.m_ActiveEdges:o.NextInAEL}else{if(this.IsIntermediate(r,t)&&e.ClipperBase.IsHorizontal(r.NextInLML)?((r=this.UpdateEdgeIntoAEL(r)).OutIdx>=0&&this.AddOutPt(r,r.Bot),this.AddEdgeToSEL(r)):(r.Curr.X=e.Clipper.TopX(r,t),r.Curr.Y=t),e.use_xyz&&(r.Top.Y===t?r.Curr.Z=r.Top.Z:r.Bot.Y===t?r.Curr.Z=r.Bot.Z:r.Curr.Z=0),this.StrictlySimple&&(o=r.PrevInAEL,r.OutIdx>=0&&0!==r.WindDelta&&null!==o&&o.OutIdx>=0&&o.Curr.X===r.Curr.X&&0!==o.WindDelta)){var s=new e.IntPoint1(r.Curr);e.use_xyz&&this.SetZ(s,o,r);var a=this.AddOutPt(o,s),l=this.AddOutPt(r,s);this.AddJoin(a,l,s)}r=r.NextInAEL}}for(this.ProcessHorizontals(),this.m_Maxima=null,r=this.m_ActiveEdges;null!==r;){if(this.IsIntermediate(r,t)){a=null,r.OutIdx>=0&&(a=this.AddOutPt(r,r.Top)),o=(r=this.UpdateEdgeIntoAEL(r)).PrevInAEL;var u=r.NextInAEL;null!==o&&o.Curr.X===r.Bot.X&&o.Curr.Y===r.Bot.Y&&null!==a&&o.OutIdx>=0&&o.Curr.Y===o.Top.Y&&e.ClipperBase.SlopesEqual5(r.Curr,r.Top,o.Curr,o.Top,this.m_UseFullRange)&&0!==r.WindDelta&&0!==o.WindDelta?(l=this.AddOutPt(ePrev2,r.Bot),this.AddJoin(a,l,r.Top)):null!==u&&u.Curr.X===r.Bot.X&&u.Curr.Y===r.Bot.Y&&null!==a&&u.OutIdx>=0&&u.Curr.Y===u.Top.Y&&e.ClipperBase.SlopesEqual5(r.Curr,r.Top,u.Curr,u.Top,this.m_UseFullRange)&&0!==r.WindDelta&&0!==u.WindDelta&&(l=this.AddOutPt(u,r.Bot),this.AddJoin(a,l,r.Top))}r=r.NextInAEL}},e.Clipper.prototype.DoMaxima=function(t){var r=this.GetMaximaPairEx(t);if(null===r)return t.OutIdx>=0&&this.AddOutPt(t,t.Top),void this.DeleteFromAEL(t);for(var n=t.NextInAEL;null!==n&&n!==r;)this.IntersectEdges(t,n,t.Top),this.SwapPositionsInAEL(t,n),n=t.NextInAEL;-1===t.OutIdx&&-1===r.OutIdx?(this.DeleteFromAEL(t),this.DeleteFromAEL(r)):t.OutIdx>=0&&r.OutIdx>=0?(t.OutIdx>=0&&this.AddLocalMaxPoly(t,r,t.Top),this.DeleteFromAEL(t),this.DeleteFromAEL(r)):e.use_lines&&0===t.WindDelta?(t.OutIdx>=0&&(this.AddOutPt(t,t.Top),t.OutIdx=e.ClipperBase.Unassigned),this.DeleteFromAEL(t),r.OutIdx>=0&&(this.AddOutPt(r,t.Top),r.OutIdx=e.ClipperBase.Unassigned),this.DeleteFromAEL(r)):e.Error("DoMaxima error")},e.Clipper.ReversePaths=function(t){for(var e=0,r=t.length;e<r;e++)t[e].reverse()},e.Clipper.Orientation=function(t){return e.Clipper.Area(t)>=0},e.Clipper.prototype.PointCount=function(t){if(null===t)return 0;var e=0,r=t;do{e++,r=r.Next}while(r!==t);return e},e.Clipper.prototype.BuildResult=function(t){e.Clear(t);for(var r=0,n=this.m_PolyOuts.length;r<n;r++){var i=this.m_PolyOuts[r];if(null!==i.Pts){var o=i.Pts.Prev,s=this.PointCount(o);if(!(s<2)){for(var a=new Array(s),l=0;l<s;l++)a[l]=o.Pt,o=o.Prev;t.push(a)}}}},e.Clipper.prototype.BuildResult2=function(t){t.Clear();for(var r=0,n=this.m_PolyOuts.length;r<n;r++){var i=this.m_PolyOuts[r],o=this.PointCount(i.Pts);if(!(i.IsOpen&&o<2||!i.IsOpen&&o<3)){this.FixHoleLinkage(i);var s=new e.PolyNode;t.m_AllPolys.push(s),i.PolyNode=s,s.m_polygon.length=o;for(var a=i.Pts.Prev,l=0;l<o;l++)s.m_polygon[l]=a.Pt,a=a.Prev}}for(r=0,n=this.m_PolyOuts.length;r<n;r++)null!==(i=this.m_PolyOuts[r]).PolyNode&&(i.IsOpen?(i.PolyNode.IsOpen=!0,t.AddChild(i.PolyNode)):null!==i.FirstLeft&&null!==i.FirstLeft.PolyNode?i.FirstLeft.PolyNode.AddChild(i.PolyNode):t.AddChild(i.PolyNode))},e.Clipper.prototype.FixupOutPolyline=function(t){for(var r=t.Pts,n=r.Prev;r!==n;)if(r=r.Next,e.IntPoint.op_Equality(r.Pt,r.Prev.Pt)){r===n&&(n=r.Prev);var i=r.Prev;i.Next=r.Next,r.Next.Prev=i,r=i}r===r.Prev&&(t.Pts=null)},e.Clipper.prototype.FixupOutPolygon=function(t){var r=null;t.BottomPt=null;for(var n=t.Pts,i=this.PreserveCollinear||this.StrictlySimple;;){if(n.Prev===n||n.Prev===n.Next)return void(t.Pts=null);if(e.IntPoint.op_Equality(n.Pt,n.Next.Pt)||e.IntPoint.op_Equality(n.Pt,n.Prev.Pt)||e.ClipperBase.SlopesEqual4(n.Prev.Pt,n.Pt,n.Next.Pt,this.m_UseFullRange)&&(!i||!this.Pt2IsBetweenPt1AndPt3(n.Prev.Pt,n.Pt,n.Next.Pt)))r=null,n.Prev.Next=n.Next,n.Next.Prev=n.Prev,n=n.Prev;else{if(n===r)break;null===r&&(r=n),n=n.Next}}t.Pts=n},e.Clipper.prototype.DupOutPt=function(t,r){var n=new e.OutPt;return n.Pt.X=t.Pt.X,n.Pt.Y=t.Pt.Y,e.use_xyz&&(n.Pt.Z=t.Pt.Z),n.Idx=t.Idx,r?(n.Next=t.Next,n.Prev=t,t.Next.Prev=n,t.Next=n):(n.Prev=t.Prev,n.Next=t,t.Prev.Next=n,t.Prev=n),n},e.Clipper.prototype.GetOverlap=function(t,e,r,n,i){return t<e?r<n?(i.Left=Math.max(t,r),i.Right=Math.min(e,n)):(i.Left=Math.max(t,n),i.Right=Math.min(e,r)):r<n?(i.Left=Math.max(e,r),i.Right=Math.min(t,n)):(i.Left=Math.max(e,n),i.Right=Math.min(t,r)),i.Left<i.Right},e.Clipper.prototype.JoinHorz=function(t,r,n,i,o,s){var a=t.Pt.X>r.Pt.X?e.Direction.dRightToLeft:e.Direction.dLeftToRight,l=n.Pt.X>i.Pt.X?e.Direction.dRightToLeft:e.Direction.dLeftToRight;if(a===l)return!1;if(a===e.Direction.dLeftToRight){for(;t.Next.Pt.X<=o.X&&t.Next.Pt.X>=t.Pt.X&&t.Next.Pt.Y===o.Y;)t=t.Next;s&&t.Pt.X!==o.X&&(t=t.Next),r=this.DupOutPt(t,!s),e.IntPoint.op_Inequality(r.Pt,o)&&((t=r).Pt.X=o.X,t.Pt.Y=o.Y,e.use_xyz&&(t.Pt.Z=o.Z),r=this.DupOutPt(t,!s))}else{for(;t.Next.Pt.X>=o.X&&t.Next.Pt.X<=t.Pt.X&&t.Next.Pt.Y===o.Y;)t=t.Next;s||t.Pt.X===o.X||(t=t.Next),r=this.DupOutPt(t,s),e.IntPoint.op_Inequality(r.Pt,o)&&((t=r).Pt.X=o.X,t.Pt.Y=o.Y,e.use_xyz&&(t.Pt.Z=o.Z),r=this.DupOutPt(t,s))}if(l===e.Direction.dLeftToRight){for(;n.Next.Pt.X<=o.X&&n.Next.Pt.X>=n.Pt.X&&n.Next.Pt.Y===o.Y;)n=n.Next;s&&n.Pt.X!==o.X&&(n=n.Next),i=this.DupOutPt(n,!s),e.IntPoint.op_Inequality(i.Pt,o)&&((n=i).Pt.X=o.X,n.Pt.Y=o.Y,e.use_xyz&&(n.Pt.Z=o.Z),i=this.DupOutPt(n,!s))}else{for(;n.Next.Pt.X>=o.X&&n.Next.Pt.X<=n.Pt.X&&n.Next.Pt.Y===o.Y;)n=n.Next;s||n.Pt.X===o.X||(n=n.Next),i=this.DupOutPt(n,s),e.IntPoint.op_Inequality(i.Pt,o)&&((n=i).Pt.X=o.X,n.Pt.Y=o.Y,e.use_xyz&&(n.Pt.Z=o.Z),i=this.DupOutPt(n,s))}return a===e.Direction.dLeftToRight===s?(t.Prev=n,n.Next=t,r.Next=i,i.Prev=r):(t.Next=n,n.Prev=t,r.Prev=i,i.Next=r),!0},e.Clipper.prototype.JoinPoints=function(t,r,n){var i=t.OutPt1,o=new e.OutPt,s=t.OutPt2,a=new e.OutPt,l=t.OutPt1.Pt.Y===t.OffPt.Y;if(l&&e.IntPoint.op_Equality(t.OffPt,t.OutPt1.Pt)&&e.IntPoint.op_Equality(t.OffPt,t.OutPt2.Pt)){if(r!==n)return!1;for(o=t.OutPt1.Next;o!==i&&e.IntPoint.op_Equality(o.Pt,t.OffPt);)o=o.Next;var u=o.Pt.Y>t.OffPt.Y;for(a=t.OutPt2.Next;a!==s&&e.IntPoint.op_Equality(a.Pt,t.OffPt);)a=a.Next;return u!==a.Pt.Y>t.OffPt.Y&&(u?(o=this.DupOutPt(i,!1),a=this.DupOutPt(s,!0),i.Prev=s,s.Next=i,o.Next=a,a.Prev=o,t.OutPt1=i,t.OutPt2=o,!0):(o=this.DupOutPt(i,!0),a=this.DupOutPt(s,!1),i.Next=s,s.Prev=i,o.Prev=a,a.Next=o,t.OutPt1=i,t.OutPt2=o,!0))}if(l){for(o=i;i.Prev.Pt.Y===i.Pt.Y&&i.Prev!==o&&i.Prev!==s;)i=i.Prev;for(;o.Next.Pt.Y===o.Pt.Y&&o.Next!==i&&o.Next!==s;)o=o.Next;if(o.Next===i||o.Next===s)return!1;for(a=s;s.Prev.Pt.Y===s.Pt.Y&&s.Prev!==a&&s.Prev!==o;)s=s.Prev;for(;a.Next.Pt.Y===a.Pt.Y&&a.Next!==s&&a.Next!==i;)a=a.Next;if(a.Next===s||a.Next===i)return!1;var h={Left:null,Right:null};if(!this.GetOverlap(i.Pt.X,o.Pt.X,s.Pt.X,a.Pt.X,h))return!1;var c,p=h.Left,f=h.Right,d=new e.IntPoint0;return i.Pt.X>=p&&i.Pt.X<=f?(d.X=i.Pt.X,d.Y=i.Pt.Y,e.use_xyz&&(d.Z=i.Pt.Z),c=i.Pt.X>o.Pt.X):s.Pt.X>=p&&s.Pt.X<=f?(d.X=s.Pt.X,d.Y=s.Pt.Y,e.use_xyz&&(d.Z=s.Pt.Z),c=s.Pt.X>a.Pt.X):o.Pt.X>=p&&o.Pt.X<=f?(d.X=o.Pt.X,d.Y=o.Pt.Y,e.use_xyz&&(d.Z=o.Pt.Z),c=o.Pt.X>i.Pt.X):(d.X=a.Pt.X,d.Y=a.Pt.Y,e.use_xyz&&(d.Z=a.Pt.Z),c=a.Pt.X>s.Pt.X),t.OutPt1=i,t.OutPt2=s,this.JoinHorz(i,o,s,a,d,c)}for(o=i.Next;e.IntPoint.op_Equality(o.Pt,i.Pt)&&o!==i;)o=o.Next;var y=o.Pt.Y>i.Pt.Y||!e.ClipperBase.SlopesEqual4(i.Pt,o.Pt,t.OffPt,this.m_UseFullRange);if(y){for(o=i.Prev;e.IntPoint.op_Equality(o.Pt,i.Pt)&&o!==i;)o=o.Prev;if(o.Pt.Y>i.Pt.Y||!e.ClipperBase.SlopesEqual4(i.Pt,o.Pt,t.OffPt,this.m_UseFullRange))return!1}for(a=s.Next;e.IntPoint.op_Equality(a.Pt,s.Pt)&&a!==s;)a=a.Next;var v=a.Pt.Y>s.Pt.Y||!e.ClipperBase.SlopesEqual4(s.Pt,a.Pt,t.OffPt,this.m_UseFullRange);if(v){for(a=s.Prev;e.IntPoint.op_Equality(a.Pt,s.Pt)&&a!==s;)a=a.Prev;if(a.Pt.Y>s.Pt.Y||!e.ClipperBase.SlopesEqual4(s.Pt,a.Pt,t.OffPt,this.m_UseFullRange))return!1}return!(o===i||a===s||o===a||r===n&&y===v||(y?(o=this.DupOutPt(i,!1),a=this.DupOutPt(s,!0),i.Prev=s,s.Next=i,o.Next=a,a.Prev=o,t.OutPt1=i,t.OutPt2=o,0):(o=this.DupOutPt(i,!0),a=this.DupOutPt(s,!1),i.Next=s,s.Prev=i,o.Prev=a,a.Next=o,t.OutPt1=i,t.OutPt2=o,0)))},e.Clipper.GetBounds=function(t){for(var r=0,n=t.length;r<n&&0===t[r].length;)r++;if(r===n)return new e.IntRect(0,0,0,0);var i=new e.IntRect;for(i.left=t[r][0].X,i.right=i.left,i.top=t[r][0].Y,i.bottom=i.top;r<n;r++)for(var o=0,s=t[r].length;o<s;o++)t[r][o].X<i.left?i.left=t[r][o].X:t[r][o].X>i.right&&(i.right=t[r][o].X),t[r][o].Y<i.top?i.top=t[r][o].Y:t[r][o].Y>i.bottom&&(i.bottom=t[r][o].Y);return i},e.Clipper.prototype.GetBounds2=function(t){var r=t,n=new e.IntRect;for(n.left=t.Pt.X,n.right=t.Pt.X,n.top=t.Pt.Y,n.bottom=t.Pt.Y,t=t.Next;t!==r;)t.Pt.X<n.left&&(n.left=t.Pt.X),t.Pt.X>n.right&&(n.right=t.Pt.X),t.Pt.Y<n.top&&(n.top=t.Pt.Y),t.Pt.Y>n.bottom&&(n.bottom=t.Pt.Y),t=t.Next;return n},e.Clipper.PointInPolygon=function(t,e){var r=0,n=e.length;if(n<3)return 0;for(var i=e[0],o=1;o<=n;++o){var s=o===n?e[0]:e[o];if(s.Y===t.Y&&(s.X===t.X||i.Y===t.Y&&s.X>t.X==i.X<t.X))return-1;if(i.Y<t.Y!=s.Y<t.Y)if(i.X>=t.X)if(s.X>t.X)r=1-r;else{if(0==(a=(i.X-t.X)*(s.Y-t.Y)-(s.X-t.X)*(i.Y-t.Y)))return-1;a>0==s.Y>i.Y&&(r=1-r)}else if(s.X>t.X){var a;if(0==(a=(i.X-t.X)*(s.Y-t.Y)-(s.X-t.X)*(i.Y-t.Y)))return-1;a>0==s.Y>i.Y&&(r=1-r)}i=s}return r},e.Clipper.prototype.PointInPolygon=function(t,e){var r=0,n=e,i=t.X,o=t.Y,s=e.Pt.X,a=e.Pt.Y;do{var l=(e=e.Next).Pt.X,u=e.Pt.Y;if(u===o&&(l===i||a===o&&l>i==s<i))return-1;if(a<o!=u<o)if(s>=i)if(l>i)r=1-r;else{if(0==(h=(s-i)*(u-o)-(l-i)*(a-o)))return-1;h>0==u>a&&(r=1-r)}else if(l>i){var h;if(0==(h=(s-i)*(u-o)-(l-i)*(a-o)))return-1;h>0==u>a&&(r=1-r)}s=l,a=u}while(n!==e);return r},e.Clipper.prototype.Poly2ContainsPoly1=function(t,e){var r=t;do{var n=this.PointInPolygon(r.Pt,e);if(n>=0)return n>0;r=r.Next}while(r!==t);return!0},e.Clipper.prototype.FixupFirstLefts1=function(t,r){for(var n,i,o=0,s=this.m_PolyOuts.length;o<s;o++)n=this.m_PolyOuts[o],i=e.Clipper.ParseFirstLeft(n.FirstLeft),null!==n.Pts&&i===t&&this.Poly2ContainsPoly1(n.Pts,r.Pts)&&(n.FirstLeft=r)},e.Clipper.prototype.FixupFirstLefts2=function(t,r){for(var n,i,o=r.FirstLeft,s=0,a=this.m_PolyOuts.length;s<a;s++)null!==(n=this.m_PolyOuts[s]).Pts&&n!==r&&n!==t&&((i=e.Clipper.ParseFirstLeft(n.FirstLeft))!==o&&i!==t&&i!==r||(this.Poly2ContainsPoly1(n.Pts,t.Pts)?n.FirstLeft=t:this.Poly2ContainsPoly1(n.Pts,r.Pts)?n.FirstLeft=r:n.FirstLeft!==t&&n.FirstLeft!==r||(n.FirstLeft=o)))},e.Clipper.prototype.FixupFirstLefts3=function(t,r){for(var n,i,o=0,s=this.m_PolyOuts.length;o<s;o++)n=this.m_PolyOuts[o],i=e.Clipper.ParseFirstLeft(n.FirstLeft),null!==n.Pts&&i===t&&(n.FirstLeft=r)},e.Clipper.ParseFirstLeft=function(t){for(;null!==t&&null===t.Pts;)t=t.FirstLeft;return t},e.Clipper.prototype.JoinCommonEdges=function(){for(var t=0,e=this.m_Joins.length;t<e;t++){var r,n=this.m_Joins[t],i=this.GetOutRec(n.OutPt1.Idx),o=this.GetOutRec(n.OutPt2.Idx);null!==i.Pts&&null!==o.Pts&&(i.IsOpen||o.IsOpen||(r=i===o?i:this.OutRec1RightOfOutRec2(i,o)?o:this.OutRec1RightOfOutRec2(o,i)?i:this.GetLowermostRec(i,o),this.JoinPoints(n,i,o)&&(i===o?(i.Pts=n.OutPt1,i.BottomPt=null,(o=this.CreateOutRec()).Pts=n.OutPt2,this.UpdateOutPtIdxs(o),this.Poly2ContainsPoly1(o.Pts,i.Pts)?(o.IsHole=!i.IsHole,o.FirstLeft=i,this.m_UsingPolyTree&&this.FixupFirstLefts2(o,i),(o.IsHole^this.ReverseSolution)==this.Area$1(o)>0&&this.ReversePolyPtLinks(o.Pts)):this.Poly2ContainsPoly1(i.Pts,o.Pts)?(o.IsHole=i.IsHole,i.IsHole=!o.IsHole,o.FirstLeft=i.FirstLeft,i.FirstLeft=o,this.m_UsingPolyTree&&this.FixupFirstLefts2(i,o),(i.IsHole^this.ReverseSolution)==this.Area$1(i)>0&&this.ReversePolyPtLinks(i.Pts)):(o.IsHole=i.IsHole,o.FirstLeft=i.FirstLeft,this.m_UsingPolyTree&&this.FixupFirstLefts1(i,o))):(o.Pts=null,o.BottomPt=null,o.Idx=i.Idx,i.IsHole=r.IsHole,r===o&&(i.FirstLeft=o.FirstLeft),o.FirstLeft=i,this.m_UsingPolyTree&&this.FixupFirstLefts3(o,i)))))}},e.Clipper.prototype.UpdateOutPtIdxs=function(t){var e=t.Pts;do{e.Idx=t.Idx,e=e.Prev}while(e!==t.Pts)},e.Clipper.prototype.DoSimplePolygons=function(){for(var t=0;t<this.m_PolyOuts.length;){var r=this.m_PolyOuts[t++],n=r.Pts;if(null!==n&&!r.IsOpen)do{for(var i=n.Next;i!==r.Pts;){if(e.IntPoint.op_Equality(n.Pt,i.Pt)&&i.Next!==n&&i.Prev!==n){var o=n.Prev,s=i.Prev;n.Prev=s,s.Next=n,i.Prev=o,o.Next=i,r.Pts=n;var a=this.CreateOutRec();a.Pts=i,this.UpdateOutPtIdxs(a),this.Poly2ContainsPoly1(a.Pts,r.Pts)?(a.IsHole=!r.IsHole,a.FirstLeft=r,this.m_UsingPolyTree&&this.FixupFirstLefts2(a,r)):this.Poly2ContainsPoly1(r.Pts,a.Pts)?(a.IsHole=r.IsHole,r.IsHole=!a.IsHole,a.FirstLeft=r.FirstLeft,r.FirstLeft=a,this.m_UsingPolyTree&&this.FixupFirstLefts2(r,a)):(a.IsHole=r.IsHole,a.FirstLeft=r.FirstLeft,this.m_UsingPolyTree&&this.FixupFirstLefts1(r,a)),i=n}i=i.Next}n=n.Next}while(n!==r.Pts)}},e.Clipper.Area=function(t){if(!Array.isArray(t))return 0;var e=t.length;if(e<3)return 0;for(var r=0,n=0,i=e-1;n<e;++n)r+=(t[i].X+t[n].X)*(t[i].Y-t[n].Y),i=n;return.5*-r},e.Clipper.prototype.Area=function(t){var e=t;if(null===t)return 0;var r=0;do{r+=(t.Prev.Pt.X+t.Pt.X)*(t.Prev.Pt.Y-t.Pt.Y),t=t.Next}while(t!==e);return.5*r},e.Clipper.prototype.Area$1=function(t){return this.Area(t.Pts)},e.Clipper.SimplifyPolygon=function(t,r){var n=new Array,i=new e.Clipper(0);return i.StrictlySimple=!0,i.AddPath(t,e.PolyType.ptSubject,!0),i.Execute(e.ClipType.ctUnion,n,r,r),n},e.Clipper.SimplifyPolygons=function(t,r){void 0===r&&(r=e.PolyFillType.pftEvenOdd);var n=new Array,i=new e.Clipper(0);return i.StrictlySimple=!0,i.AddPaths(t,e.PolyType.ptSubject,!0),i.Execute(e.ClipType.ctUnion,n,r,r),n},e.Clipper.DistanceSqrd=function(t,e){var r=t.X-e.X,n=t.Y-e.Y;return r*r+n*n},e.Clipper.DistanceFromLineSqrd=function(t,e,r){var n=e.Y-r.Y,i=r.X-e.X,o=n*e.X+i*e.Y;return(o=n*t.X+i*t.Y-o)*o/(n*n+i*i)},e.Clipper.SlopesNearCollinear=function(t,r,n,i){return Math.abs(t.X-r.X)>Math.abs(t.Y-r.Y)?t.X>r.X==t.X<n.X?e.Clipper.DistanceFromLineSqrd(t,r,n)<i:r.X>t.X==r.X<n.X?e.Clipper.DistanceFromLineSqrd(r,t,n)<i:e.Clipper.DistanceFromLineSqrd(n,t,r)<i:t.Y>r.Y==t.Y<n.Y?e.Clipper.DistanceFromLineSqrd(t,r,n)<i:r.Y>t.Y==r.Y<n.Y?e.Clipper.DistanceFromLineSqrd(r,t,n)<i:e.Clipper.DistanceFromLineSqrd(n,t,r)<i},e.Clipper.PointsAreClose=function(t,e,r){var n=t.X-e.X,i=t.Y-e.Y;return n*n+i*i<=r},e.Clipper.ExcludeOp=function(t){var e=t.Prev;return e.Next=t.Next,t.Next.Prev=e,e.Idx=0,e},e.Clipper.CleanPolygon=function(t,r){void 0===r&&(r=1.415);var n=t.length;if(0===n)return new Array;for(var i=new Array(n),o=0;o<n;++o)i[o]=new e.OutPt;for(o=0;o<n;++o)i[o].Pt=t[o],i[o].Next=i[(o+1)%n],i[o].Next.Prev=i[o],i[o].Idx=0;for(var s=r*r,a=i[0];0===a.Idx&&a.Next!==a.Prev;)e.Clipper.PointsAreClose(a.Pt,a.Prev.Pt,s)?(a=e.Clipper.ExcludeOp(a),n--):e.Clipper.PointsAreClose(a.Prev.Pt,a.Next.Pt,s)?(e.Clipper.ExcludeOp(a.Next),a=e.Clipper.ExcludeOp(a),n-=2):e.Clipper.SlopesNearCollinear(a.Prev.Pt,a.Pt,a.Next.Pt,s)?(a=e.Clipper.ExcludeOp(a),n--):(a.Idx=1,a=a.Next);n<3&&(n=0);var l=new Array(n);for(o=0;o<n;++o)l[o]=new e.IntPoint1(a.Pt),a=a.Next;return i=null,l},e.Clipper.CleanPolygons=function(t,r){for(var n=new Array(t.length),i=0,o=t.length;i<o;i++)n[i]=e.Clipper.CleanPolygon(t[i],r);return n},e.Clipper.Minkowski=function(t,r,n,i){var o=i?1:0,s=t.length,a=r.length,l=new Array;if(n)for(var u=0;u<a;u++){for(var h=new Array(s),c=0,p=t.length,f=t[c];c<p;f=t[++c])h[c]=new e.IntPoint2(r[u].X+f.X,r[u].Y+f.Y);l.push(h)}else for(u=0;u<a;u++){for(h=new Array(s),c=0,p=t.length,f=t[c];c<p;f=t[++c])h[c]=new e.IntPoint2(r[u].X-f.X,r[u].Y-f.Y);l.push(h)}var d=new Array;for(u=0;u<a-1+o;u++)for(c=0;c<s;c++){var y=new Array;y.push(l[u%a][c%s]),y.push(l[(u+1)%a][c%s]),y.push(l[(u+1)%a][(c+1)%s]),y.push(l[u%a][(c+1)%s]),e.Clipper.Orientation(y)||y.reverse(),d.push(y)}return d},e.Clipper.MinkowskiSum=function(t,r,n){if(r[0]instanceof Array){u=r;for(var i=new e.Paths,o=(a=new e.Clipper,0);o<u.length;++o){var s=e.Clipper.Minkowski(t,u[o],!0,n);a.AddPaths(s,e.PolyType.ptSubject,!0),n&&(l=e.Clipper.TranslatePath(u[o],t[0]),a.AddPath(l,e.PolyType.ptClip,!0))}return a.Execute(e.ClipType.ctUnion,i,e.PolyFillType.pftNonZero,e.PolyFillType.pftNonZero),i}var a,l=r,u=e.Clipper.Minkowski(t,l,!0,n);return(a=new e.Clipper).AddPaths(u,e.PolyType.ptSubject,!0),a.Execute(e.ClipType.ctUnion,u,e.PolyFillType.pftNonZero,e.PolyFillType.pftNonZero),u},e.Clipper.TranslatePath=function(t,r){for(var n=new e.Path,i=0;i<t.length;i++)n.push(new e.IntPoint2(t[i].X+r.X,t[i].Y+r.Y));return n},e.Clipper.MinkowskiDiff=function(t,r){var n=e.Clipper.Minkowski(t,r,!1,!0),i=new e.Clipper;return i.AddPaths(n,e.PolyType.ptSubject,!0),i.Execute(e.ClipType.ctUnion,n,e.PolyFillType.pftNonZero,e.PolyFillType.pftNonZero),n},e.Clipper.PolyTreeToPaths=function(t){var r=new Array;return e.Clipper.AddPolyNodeToPaths(t,e.Clipper.NodeType.ntAny,r),r},e.Clipper.AddPolyNodeToPaths=function(t,r,n){var i=!0;switch(r){case e.Clipper.NodeType.ntOpen:return;case e.Clipper.NodeType.ntClosed:i=!t.IsOpen}t.m_polygon.length>0&&i&&n.push(t.m_polygon);for(var o=0,s=t.Childs(),a=s.length,l=s[o];o<a;l=s[++o])e.Clipper.AddPolyNodeToPaths(l,r,n)},e.Clipper.OpenPathsFromPolyTree=function(t){for(var r=new e.Paths,n=0,i=t.ChildCount();n<i;n++)t.Childs()[n].IsOpen&&r.push(t.Childs()[n].m_polygon);return r},e.Clipper.ClosedPathsFromPolyTree=function(t){var r=new e.Paths;return e.Clipper.AddPolyNodeToPaths(t,e.Clipper.NodeType.ntClosed,r),r},w(e.Clipper,e.ClipperBase),e.Clipper.NodeType={ntAny:0,ntOpen:1,ntClosed:2},e.ClipperOffset=function(t,r){void 0===t&&(t=2),void 0===r&&(r=e.ClipperOffset.def_arc_tolerance),this.m_destPolys=new e.Paths,this.m_srcPoly=new e.Path,this.m_destPoly=new e.Path,this.m_normals=new Array,this.m_delta=0,this.m_sinA=0,this.m_sin=0,this.m_cos=0,this.m_miterLim=0,this.m_StepsPerRad=0,this.m_lowest=new e.IntPoint0,this.m_polyNodes=new e.PolyNode,this.MiterLimit=t,this.ArcTolerance=r,this.m_lowest.X=-1},e.ClipperOffset.two_pi=6.28318530717959,e.ClipperOffset.def_arc_tolerance=.25,e.ClipperOffset.prototype.Clear=function(){e.Clear(this.m_polyNodes.Childs()),this.m_lowest.X=-1},e.ClipperOffset.Round=e.Clipper.Round,e.ClipperOffset.prototype.AddPath=function(t,r,n){var i=t.length-1;if(!(i<0)){var o=new e.PolyNode;if(o.m_jointype=r,o.m_endtype=n,n===e.EndType.etClosedLine||n===e.EndType.etClosedPolygon)for(;i>0&&e.IntPoint.op_Equality(t[0],t[i]);)i--;o.m_polygon.push(t[0]);for(var s=0,a=0,l=1;l<=i;l++)e.IntPoint.op_Inequality(o.m_polygon[s],t[l])&&(s++,o.m_polygon.push(t[l]),(t[l].Y>o.m_polygon[a].Y||t[l].Y===o.m_polygon[a].Y&&t[l].X<o.m_polygon[a].X)&&(a=s));if(!(n===e.EndType.etClosedPolygon&&s<2)&&(this.m_polyNodes.AddChild(o),n===e.EndType.etClosedPolygon))if(this.m_lowest.X<0)this.m_lowest=new e.IntPoint2(this.m_polyNodes.ChildCount()-1,a);else{var u=this.m_polyNodes.Childs()[this.m_lowest.X].m_polygon[this.m_lowest.Y];(o.m_polygon[a].Y>u.Y||o.m_polygon[a].Y===u.Y&&o.m_polygon[a].X<u.X)&&(this.m_lowest=new e.IntPoint2(this.m_polyNodes.ChildCount()-1,a))}}},e.ClipperOffset.prototype.AddPaths=function(t,e,r){for(var n=0,i=t.length;n<i;n++)this.AddPath(t[n],e,r)},e.ClipperOffset.prototype.FixOrientations=function(){if(this.m_lowest.X>=0&&!e.Clipper.Orientation(this.m_polyNodes.Childs()[this.m_lowest.X].m_polygon))for(var t=0;t<this.m_polyNodes.ChildCount();t++)((r=this.m_polyNodes.Childs()[t]).m_endtype===e.EndType.etClosedPolygon||r.m_endtype===e.EndType.etClosedLine&&e.Clipper.Orientation(r.m_polygon))&&r.m_polygon.reverse();else for(t=0;t<this.m_polyNodes.ChildCount();t++){var r;(r=this.m_polyNodes.Childs()[t]).m_endtype!==e.EndType.etClosedLine||e.Clipper.Orientation(r.m_polygon)||r.m_polygon.reverse()}},e.ClipperOffset.GetUnitNormal=function(t,r){var n=r.X-t.X,i=r.Y-t.Y;if(0===n&&0===i)return new e.DoublePoint2(0,0);var o=1/Math.sqrt(n*n+i*i);return n*=o,i*=o,new e.DoublePoint2(i,-n)},e.ClipperOffset.prototype.DoOffset=function(t){if(this.m_destPolys=new Array,this.m_delta=t,e.ClipperBase.near_zero(t))for(var r=0;r<this.m_polyNodes.ChildCount();r++)(o=this.m_polyNodes.Childs()[r]).m_endtype===e.EndType.etClosedPolygon&&this.m_destPolys.push(o.m_polygon);else{var n;this.MiterLimit>2?this.m_miterLim=2/(this.MiterLimit*this.MiterLimit):this.m_miterLim=.5,n=this.ArcTolerance<=0?e.ClipperOffset.def_arc_tolerance:this.ArcTolerance>Math.abs(t)*e.ClipperOffset.def_arc_tolerance?Math.abs(t)*e.ClipperOffset.def_arc_tolerance:this.ArcTolerance;var i=3.14159265358979/Math.acos(1-n/Math.abs(t));for(this.m_sin=Math.sin(e.ClipperOffset.two_pi/i),this.m_cos=Math.cos(e.ClipperOffset.two_pi/i),this.m_StepsPerRad=i/e.ClipperOffset.two_pi,t<0&&(this.m_sin=-this.m_sin),r=0;r<this.m_polyNodes.ChildCount();r++){var o=this.m_polyNodes.Childs()[r];this.m_srcPoly=o.m_polygon;var s=this.m_srcPoly.length;if(!(0===s||t<=0&&(s<3||o.m_endtype!==e.EndType.etClosedPolygon)))if(this.m_destPoly=new Array,1!==s){for(this.m_normals.length=0,p=0;p<s-1;p++)this.m_normals.push(e.ClipperOffset.GetUnitNormal(this.m_srcPoly[p],this.m_srcPoly[p+1]));if(o.m_endtype===e.EndType.etClosedLine||o.m_endtype===e.EndType.etClosedPolygon?this.m_normals.push(e.ClipperOffset.GetUnitNormal(this.m_srcPoly[s-1],this.m_srcPoly[0])):this.m_normals.push(new e.DoublePoint1(this.m_normals[s-2])),o.m_endtype===e.EndType.etClosedPolygon){var a=s-1;for(p=0;p<s;p++)a=this.OffsetPoint(p,a,o.m_jointype);this.m_destPolys.push(this.m_destPoly)}else if(o.m_endtype===e.EndType.etClosedLine){for(a=s-1,p=0;p<s;p++)a=this.OffsetPoint(p,a,o.m_jointype);this.m_destPolys.push(this.m_destPoly),this.m_destPoly=new Array;var l=this.m_normals[s-1];for(p=s-1;p>0;p--)this.m_normals[p]=new e.DoublePoint2(-this.m_normals[p-1].X,-this.m_normals[p-1].Y);for(this.m_normals[0]=new e.DoublePoint2(-l.X,-l.Y),a=0,p=s-1;p>=0;p--)a=this.OffsetPoint(p,a,o.m_jointype);this.m_destPolys.push(this.m_destPoly)}else{var u;for(a=0,p=1;p<s-1;++p)a=this.OffsetPoint(p,a,o.m_jointype);for(o.m_endtype===e.EndType.etOpenButt?(p=s-1,u=new e.IntPoint2(e.ClipperOffset.Round(this.m_srcPoly[p].X+this.m_normals[p].X*t),e.ClipperOffset.Round(this.m_srcPoly[p].Y+this.m_normals[p].Y*t)),this.m_destPoly.push(u),u=new e.IntPoint2(e.ClipperOffset.Round(this.m_srcPoly[p].X-this.m_normals[p].X*t),e.ClipperOffset.Round(this.m_srcPoly[p].Y-this.m_normals[p].Y*t)),this.m_destPoly.push(u)):(p=s-1,a=s-2,this.m_sinA=0,this.m_normals[p]=new e.DoublePoint2(-this.m_normals[p].X,-this.m_normals[p].Y),o.m_endtype===e.EndType.etOpenSquare?this.DoSquare(p,a):this.DoRound(p,a)),p=s-1;p>0;p--)this.m_normals[p]=new e.DoublePoint2(-this.m_normals[p-1].X,-this.m_normals[p-1].Y);for(this.m_normals[0]=new e.DoublePoint2(-this.m_normals[1].X,-this.m_normals[1].Y),p=(a=s-1)-1;p>0;--p)a=this.OffsetPoint(p,a,o.m_jointype);o.m_endtype===e.EndType.etOpenButt?(u=new e.IntPoint2(e.ClipperOffset.Round(this.m_srcPoly[0].X-this.m_normals[0].X*t),e.ClipperOffset.Round(this.m_srcPoly[0].Y-this.m_normals[0].Y*t)),this.m_destPoly.push(u),u=new e.IntPoint2(e.ClipperOffset.Round(this.m_srcPoly[0].X+this.m_normals[0].X*t),e.ClipperOffset.Round(this.m_srcPoly[0].Y+this.m_normals[0].Y*t)),this.m_destPoly.push(u)):(a=1,this.m_sinA=0,o.m_endtype===e.EndType.etOpenSquare?this.DoSquare(0,1):this.DoRound(0,1)),this.m_destPolys.push(this.m_destPoly)}}else{if(o.m_jointype===e.JoinType.jtRound)for(var h=1,c=0,p=1;p<=i;p++){this.m_destPoly.push(new e.IntPoint2(e.ClipperOffset.Round(this.m_srcPoly[0].X+h*t),e.ClipperOffset.Round(this.m_srcPoly[0].Y+c*t)));var f=h;h=h*this.m_cos-this.m_sin*c,c=f*this.m_sin+c*this.m_cos}else{h=-1,c=-1;for(var p=0;p<4;++p)this.m_destPoly.push(new e.IntPoint2(e.ClipperOffset.Round(this.m_srcPoly[0].X+h*t),e.ClipperOffset.Round(this.m_srcPoly[0].Y+c*t))),h<0?h=1:c<0?c=1:h=-1}this.m_destPolys.push(this.m_destPoly)}}}},e.ClipperOffset.prototype.Execute=function(){var t=arguments,r=t[0]instanceof e.PolyTree;if(r)if(s=t[0],a=t[1],s.Clear(),this.FixOrientations(),this.DoOffset(a),(o=new e.Clipper(0)).AddPaths(this.m_destPolys,e.PolyType.ptSubject,!0),a>0)o.Execute(e.ClipType.ctUnion,s,e.PolyFillType.pftPositive,e.PolyFillType.pftPositive);else if(u=e.Clipper.GetBounds(this.m_destPolys),(l=new e.Path).push(new e.IntPoint2(u.left-10,u.bottom+10)),l.push(new e.IntPoint2(u.right+10,u.bottom+10)),l.push(new e.IntPoint2(u.right+10,u.top-10)),l.push(new e.IntPoint2(u.left-10,u.top-10)),o.AddPath(l,e.PolyType.ptSubject,!0),o.ReverseSolution=!0,o.Execute(e.ClipType.ctUnion,s,e.PolyFillType.pftNegative,e.PolyFillType.pftNegative),1===s.ChildCount()&&s.Childs()[0].ChildCount()>0){var n=s.Childs()[0];s.Childs()[0]=n.Childs()[0],s.Childs()[0].m_Parent=s;for(var i=1;i<n.ChildCount();i++)s.AddChild(n.Childs()[i])}else s.Clear();else{var o,s=t[0],a=t[1];if(e.Clear(s),this.FixOrientations(),this.DoOffset(a),(o=new e.Clipper(0)).AddPaths(this.m_destPolys,e.PolyType.ptSubject,!0),a>0)o.Execute(e.ClipType.ctUnion,s,e.PolyFillType.pftPositive,e.PolyFillType.pftPositive);else{var l,u=e.Clipper.GetBounds(this.m_destPolys);(l=new e.Path).push(new e.IntPoint2(u.left-10,u.bottom+10)),l.push(new e.IntPoint2(u.right+10,u.bottom+10)),l.push(new e.IntPoint2(u.right+10,u.top-10)),l.push(new e.IntPoint2(u.left-10,u.top-10)),o.AddPath(l,e.PolyType.ptSubject,!0),o.ReverseSolution=!0,o.Execute(e.ClipType.ctUnion,s,e.PolyFillType.pftNegative,e.PolyFillType.pftNegative),s.length>0&&s.splice(0,1)}}},e.ClipperOffset.prototype.OffsetPoint=function(t,r,n){if(this.m_sinA=this.m_normals[r].X*this.m_normals[t].Y-this.m_normals[t].X*this.m_normals[r].Y,Math.abs(this.m_sinA*this.m_delta)<1){if(this.m_normals[r].X*this.m_normals[t].X+this.m_normals[t].Y*this.m_normals[r].Y>0)return this.m_destPoly.push(new e.IntPoint2(e.ClipperOffset.Round(this.m_srcPoly[t].X+this.m_normals[r].X*this.m_delta),e.ClipperOffset.Round(this.m_srcPoly[t].Y+this.m_normals[r].Y*this.m_delta))),r}else this.m_sinA>1?this.m_sinA=1:this.m_sinA<-1&&(this.m_sinA=-1);if(this.m_sinA*this.m_delta<0)this.m_destPoly.push(new e.IntPoint2(e.ClipperOffset.Round(this.m_srcPoly[t].X+this.m_normals[r].X*this.m_delta),e.ClipperOffset.Round(this.m_srcPoly[t].Y+this.m_normals[r].Y*this.m_delta))),this.m_destPoly.push(new e.IntPoint1(this.m_srcPoly[t])),this.m_destPoly.push(new e.IntPoint2(e.ClipperOffset.Round(this.m_srcPoly[t].X+this.m_normals[t].X*this.m_delta),e.ClipperOffset.Round(this.m_srcPoly[t].Y+this.m_normals[t].Y*this.m_delta)));else switch(n){case e.JoinType.jtMiter:var i=this.m_normals[t].X*this.m_normals[r].X+this.m_normals[t].Y*this.m_normals[r].Y+1;i>=this.m_miterLim?this.DoMiter(t,r,i):this.DoSquare(t,r);break;case e.JoinType.jtSquare:this.DoSquare(t,r);break;case e.JoinType.jtRound:this.DoRound(t,r)}return r=t},e.ClipperOffset.prototype.DoSquare=function(t,r){var n=Math.tan(Math.atan2(this.m_sinA,this.m_normals[r].X*this.m_normals[t].X+this.m_normals[r].Y*this.m_normals[t].Y)/4);this.m_destPoly.push(new e.IntPoint2(e.ClipperOffset.Round(this.m_srcPoly[t].X+this.m_delta*(this.m_normals[r].X-this.m_normals[r].Y*n)),e.ClipperOffset.Round(this.m_srcPoly[t].Y+this.m_delta*(this.m_normals[r].Y+this.m_normals[r].X*n)))),this.m_destPoly.push(new e.IntPoint2(e.ClipperOffset.Round(this.m_srcPoly[t].X+this.m_delta*(this.m_normals[t].X+this.m_normals[t].Y*n)),e.ClipperOffset.Round(this.m_srcPoly[t].Y+this.m_delta*(this.m_normals[t].Y-this.m_normals[t].X*n))))},e.ClipperOffset.prototype.DoMiter=function(t,r,n){var i=this.m_delta/n;this.m_destPoly.push(new e.IntPoint2(e.ClipperOffset.Round(this.m_srcPoly[t].X+(this.m_normals[r].X+this.m_normals[t].X)*i),e.ClipperOffset.Round(this.m_srcPoly[t].Y+(this.m_normals[r].Y+this.m_normals[t].Y)*i)))},e.ClipperOffset.prototype.DoRound=function(t,r){for(var n,i=Math.atan2(this.m_sinA,this.m_normals[r].X*this.m_normals[t].X+this.m_normals[r].Y*this.m_normals[t].Y),o=Math.max(e.Cast_Int32(e.ClipperOffset.Round(this.m_StepsPerRad*Math.abs(i))),1),s=this.m_normals[r].X,a=this.m_normals[r].Y,l=0;l<o;++l)this.m_destPoly.push(new e.IntPoint2(e.ClipperOffset.Round(this.m_srcPoly[t].X+s*this.m_delta),e.ClipperOffset.Round(this.m_srcPoly[t].Y+a*this.m_delta))),n=s,s=s*this.m_cos-this.m_sin*a,a=n*this.m_sin+a*this.m_cos;this.m_destPoly.push(new e.IntPoint2(e.ClipperOffset.Round(this.m_srcPoly[t].X+this.m_normals[t].X*this.m_delta),e.ClipperOffset.Round(this.m_srcPoly[t].Y+this.m_normals[t].Y*this.m_delta)))},e.Error=function(t){try{throw new Error(t)}catch(t){alert(t.message)}},e.JS={},e.JS.AreaOfPolygon=function(t,r){return r||(r=1),e.Clipper.Area(t)/(r*r)},e.JS.AreaOfPolygons=function(t,r){r||(r=1);for(var n=0,i=0;i<t.length;i++)n+=e.Clipper.Area(t[i]);return n/(r*r)},e.JS.BoundsOfPath=function(t,r){return e.JS.BoundsOfPaths([t],r)},e.JS.BoundsOfPaths=function(t,r){r||(r=1);var n=e.Clipper.GetBounds(t);return n.left/=r,n.bottom/=r,n.right/=r,n.top/=r,n},e.JS.Clean=function(t,r){if(!(t instanceof Array))return[];var n=t[0]instanceof Array;if(t=e.JS.Clone(t),"number"!=typeof r||null===r)return e.Error("Delta is not a number in Clean()."),t;if(0===t.length||1===t.length&&0===t[0].length||r<0)return t;n||(t=[t]);for(var i,o,s,a,l,u,h,c=t.length,p=[],f=0;f<c;f++)if(0!==(i=(o=t[f]).length))if(i<3)s=o,p.push(s);else{for(s=o,a=r*r,l=o[0],u=1,h=1;h<i;h++)(o[h].X-l.X)*(o[h].X-l.X)+(o[h].Y-l.Y)*(o[h].Y-l.Y)<=a||(s[u]=o[h],l=o[h],u++);l=o[u-1],(o[0].X-l.X)*(o[0].X-l.X)+(o[0].Y-l.Y)*(o[0].Y-l.Y)<=a&&u--,u<i&&s.splice(u,i-u),s.length&&p.push(s)}return!n&&p.length?p=p[0]:n||0!==p.length?n&&0===p.length&&(p=[[]]):p=[],p},e.JS.Clone=function(t){if(!(t instanceof Array))return[];if(0===t.length)return[];if(1===t.length&&0===t[0].length)return[[]];var e=t[0]instanceof Array;e||(t=[t]);var r,n,i,o,s=t.length,a=new Array(s);for(n=0;n<s;n++){for(r=t[n].length,o=new Array(r),i=0;i<r;i++)o[i]={X:t[n][i].X,Y:t[n][i].Y};a[n]=o}return e||(a=a[0]),a},e.JS.Lighten=function(t,r){if(!(t instanceof Array))return[];if("number"!=typeof r||null===r)return e.Error("Tolerance is not a number in Lighten()."),e.JS.Clone(t);if(0===t.length||1===t.length&&0===t[0].length||r<0)return e.JS.Clone(t);var n,i,o,s,a,l,u,h,c,p,f,d,y,v,m,g,P=t[0]instanceof Array;P||(t=[t]);var _=t.length,b=r*r,x=[];for(n=0;n<_;n++)if(0!==(l=(o=t[n]).length)){for(s=0;s<1e6;s++){for(a=[],o[(l=o.length)-1].X!==o[0].X||o[l-1].Y!==o[0].Y?(f=1,o.push({X:o[0].X,Y:o[0].Y}),l=o.length):f=0,p=[],i=0;i<l-2;i++)u=o[i],c=o[i+1],h=o[i+2],m=u.X,g=u.Y,d=h.X-m,y=h.Y-g,0===d&&0===y||((v=((c.X-m)*d+(c.Y-g)*y)/(d*d+y*y))>1?(m=h.X,g=h.Y):v>0&&(m+=d*v,g+=y*v)),(d=c.X-m)*d+(y=c.Y-g)*y<=b&&(p[i+1]=1,i++);for(a.push({X:o[0].X,Y:o[0].Y}),i=1;i<l-1;i++)p[i]||a.push({X:o[i].X,Y:o[i].Y});if(a.push({X:o[l-1].X,Y:o[l-1].Y}),f&&o.pop(),!p.length)break;o=a}a[(l=a.length)-1].X===a[0].X&&a[l-1].Y===a[0].Y&&a.pop(),a.length>2&&x.push(a)}return P||(x=x[0]),void 0===x&&(x=[]),x},e.JS.PerimeterOfPath=function(t,e,r){if(void 0===t)return 0;var n,i,o=Math.sqrt,s=0,a=0,l=0,u=0,h=0,c=t.length;if(c<2)return 0;for(e&&(t[c]=t[0],c++);--c;)a=(n=t[c]).X,l=n.Y,s+=o((a-(u=(i=t[c-1]).X))*(a-u)+(l-(h=i.Y))*(l-h));return e&&t.pop(),s/r},e.JS.PerimeterOfPaths=function(t,r,n){n||(n=1);for(var i=0,o=0;o<t.length;o++)i+=e.JS.PerimeterOfPath(t[o],r,n);return i},e.JS.ScaleDownPath=function(t,e){var r,n;for(e||(e=1),r=t.length;r--;)(n=t[r]).X=n.X/e,n.Y=n.Y/e},e.JS.ScaleDownPaths=function(t,e){var r,n,i;for(e||(e=1),r=t.length;r--;)for(n=t[r].length;n--;)(i=t[r][n]).X=i.X/e,i.Y=i.Y/e},e.JS.ScaleUpPath=function(t,e){var r,n,i=Math.round;for(e||(e=1),r=t.length;r--;)(n=t[r]).X=i(n.X*e),n.Y=i(n.Y*e)},e.JS.ScaleUpPaths=function(t,e){var r,n,i,o=Math.round;for(e||(e=1),r=t.length;r--;)for(n=t[r].length;n--;)(i=t[r][n]).X=o(i.X*e),i.Y=o(i.Y*e)},e.ExPolygons=function(){return[]},e.ExPolygon=function(){this.outer=null,this.holes=null},e.JS.AddOuterPolyNodeToExPolygons=function(t,r){var n=new e.ExPolygon;n.outer=t.Contour();var i,o,s,a,l,u,h=t.Childs(),c=h.length;for(n.holes=new Array(c),s=0;s<c;s++)for(i=h[s],n.holes[s]=i.Contour(),a=0,u=(l=i.Childs()).length;a<u;a++)o=l[a],e.JS.AddOuterPolyNodeToExPolygons(o,r);r.push(n)},e.JS.ExPolygonsToPaths=function(t){var r,n,i,o,s=new e.Paths;for(r=0,i=t.length;r<i;r++)for(s.push(t[r].outer),n=0,o=t[r].holes.length;n<o;n++)s.push(t[r].holes[n]);return s},e.JS.PolyTreeToExPolygons=function(t){var r,n,i,o,s=new e.ExPolygons;for(n=0,o=(i=t.Childs()).length;n<o;n++)r=i[n],e.JS.AddOuterPolyNodeToExPolygons(r,s);return s}}();var clipper=clipper$1.exports,ClipperLib=_mergeNamespaces({__proto__:null,default:clipper},[clipper$1.exports]),shim$2=ClipperLib?clipper||globalThis.ClipperLib:{},Clipper=shim$2.Clipper,Paths=shim$2.Paths,Path$1=shim$2.Path;shim$2.ClipType,shim$2.PolyType;var PolyFillType=shim$2.PolyFillType,PathFragment=function(){function t(e,r,n){_classCallCheck$1(this,t),Object.defineProperties(this,{path:{value:e,enumerable:!0},pointIndexStart:{value:r,enumerable:!0},pointIndexEnd:{value:n,enumerable:!0}}),this.validate()}return _createClass$1(t,[{key:"validate",value:function(){if(this.pointIndexStart<0)throw new Error("Invalid fragment pointIndexStart ".concat(this.pointIndexStart," found. The value must be non-negative."));if(this.pointIndexEnd>this.path.length-1)throw new Error("Invalid fragment pointIndexEnd ".concat(this.pointIndexEnd," found. Last point in path index is ").concat(this.path.length-1,"."))}},{key:"toPath",value:function(){return this.path.slice(this)}},{key:"toString",value:function(){return"fragment(".concat(this.pointIndexStart," - ").concat(this.pointIndexEnd,")")}}]),t}(),Color=function(){function t(e,r,n){var i=this,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;if(_classCallCheck$1(this,t),this.red=e,this.green=r,this.blue=n,this.alpha=o,o<0||o>1)throw new Error("Invalid alpha ".concat(o," found. The value must be in the interval [0, 1]."));Object.defineProperty(this,"hex",{get:function(){return"#".concat(i.red.toString(16).pad(2,"0")).concat(i.green.toString(16).pad(2,"0")).concat(i.blue.toString(16).pad(2,"0")).concat(Math.round(255*i.alpha).toString(16).pad(2,"0"))},enumerable:!0})}return _createClass$1(t,[{key:"premultiply",value:function(){return{red:this.red/255*this.alpha,green:this.green/255*this.alpha,blue:this.blue/255*this.alpha,alpha:this.alpha}}},{key:"equals",value:function(t){return t&&this.red==t.red&&this.green==t.green&&this.blue==t.blue&&this.alpha==t.alpha}},{key:"toRGB",value:function(){return 1==this.alpha?this:new t(this.red,this.green,this.blue)}},{key:"toRGBA",value:function(e){return new t(this.red,this.green,this.blue,e)}},{key:"toHSLA",value:function(){var t=this.red/255,e=this.green/255,r=this.blue/255,n=Math.min(t,e,r),i=Math.max(t,e,r),o=0,s=0,a=(i+n)/2;if(i!=n){var l=i-n;switch(s=l/(1-Math.abs(2*a-1)),i){case t:o=(e-r)/l%6;break;case e:o=(r-t)/l+2;break;case r:o=(t-e)/l+4}}return(o*=60)<0&&(o+=360),{hue:parseFloat(o.toFixed(0)),saturation:parseFloat((100*s).toFixed(2)),lightness:parseFloat((100*a).toFixed(2)),alpha:this.alpha}}},{key:"toArray",value:function(){return[this.red,this.green,this.blue,this.alpha]}},{key:"toJSON",value:function(){return{red:this.red,green:this.green,blue:this.blue,alpha:this.alpha}}},{key:"toString",value:function(){return 1==this.alpha?"rgb(".concat(this.red,", ").concat(this.green,", ").concat(this.blue,")"):"rgba(".concat(this.red,", ").concat(this.green,", ").concat(this.blue,", ").concat(this.alpha,")")}}],[{key:"postdivide",value:function(e,r,n,i){return new t(parseInt(255*e/i),parseInt(255*r/i),parseInt(255*n/i),i)}},{key:"isColor",value:function(t){return t&&isFinite(t.red)&&isFinite(t.green)&&isFinite(t.blue)}},{key:"fromColor",value:function(e){var r,n,i,o;if("string"==typeof e)if(e.startsWith("rgb"))e=e.substring(e.indexOf("(")+1,e.indexOf(")")).split(/,\s*/g),r=parseInt(e[0]),n=parseInt(e[1]),i=parseInt(e[2]),o=e[3]?parseInt(e[3]):1;else{if(!e.startsWith("#"))throw new Error("Unknown input found: ".concat(e,". Expected data starts with rgba, rgb or #."));e=e.substring(1),r=parseInt(e.substring(0,2),16),n=parseInt(e.substring(2,4),16),i=parseInt(e.substring(4,6),16),o=8==e.length?parseInt(e.substring(6,8),16)/255:1}else Array.isArray(e)?(r=e[0],n=e[1],i=e[2],o=e[3]):(r=e.red,n=e.green,i=e.blue,o=e.alpha);return new t(r,n,i,o)}},{key:"fromHSLA",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3?arguments[3]:void 0;e/=60,r/=100,n/=100;var o=(1-Math.abs(2*n-1))*r,s=o*(1-Math.abs(e%2-1)),a=0,l=0,u=0;e>=0&&e<1?(a=o,l=s):e>=1&&e<2?(a=s,l=o):e>=2&&e<3?(l=o,u=s):e>=3&&e<4?(l=s,u=o):e>=4&&e<5?(a=s,u=o):(a=o,u=s);var h=n-o/2;return a+=h,l+=h,u+=h,new t(Math.round(255*a),Math.round(255*l),Math.round(255*u),i)}},{key:"random",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return new t(Math.randomInt(0,255),Math.randomInt(0,255),Math.randomInt(0,255),e?Math.random():1)}}]),t}();Color.TRANSPERENT=new Color(0,0,0,0),Color.BLACK=new Color(0,0,0,1),Color.WHITE=new Color(255,255,255,1),Color.RED=new Color(255,0,0,1),Color.GREEN=new Color(0,255,0,1),Color.BLUE=new Color(0,0,255,1);var utils={longToByteArray:function(t){for(var e=[0,0,0,0,0,0,0,0],r=0;r<e.length;r++){var n=255&t;e[r]=n,t=(t-n)/256}return e},byteArrayToLong:function(t){for(var e=0,r=t.length-1;r>=0;r--)e=256*e+t[r];return e},crc32:function(){for(var t=new Uint32Array(256),e=256;e--;){for(var r=e,n=8;n--;)r=1&r?3988292384^r>>>1:r>>>1;t[e]=r}return function(e){for(var r=-1,n=0,i=e.length;n<i;n++)r=r>>>8^t[255&r^e[n]];return(-1^r)>>>0}}(),encodeBitMask:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];if(0==t.length)return 0;for(var e="",r=Math.max.apply(Math,_toConsumableArray(t)),n=1;n<=r;n++)e+=t.includes(n)?"1":"0";return parseInt(e.split("").reverse().join(""),2)},decodeBitMask:function(t){for(var e=[],r=t.toString(2).split("").reverse(),n=0;n<r.length;n++)1==r[n]&&e.push(n+1);return e},mapTo:function(t,e,r){return r.min+(utils.clamp(t,e)-e.min)/(e.max-e.min)*(r.max-r.min)},clamp:function(t,e){return Math.min(Math.max(t,e.min),e.max)},debounce:function(t,e){var r=null;return function(){var n=this,i=arguments;clearTimeout(r),r=setTimeout((function(){t.apply(n,i)}),e)}},comparator:function(){var t=Array.prototype.slice.call(arguments),e=function(t,e,r){var n="asc"===r?1:-1;return t>e?1*n:t<e?-1*n:0},r=function(t,e,r){return e.replace("[",".").replace("]","").split(".").forEach((function(e){return t=t[e]})),r?t.toLowerCase():t};return function(n,i){return t.map((function(t){return e(r(n,t.sortBy,t.ignoreCase),r(i,t.sortBy,t.ignoreCase),t.sortOrder)})).reduceRight((function(t,e){return e||t}))}},isValidURL:function(t){if("string"!=typeof t)return!1;try{return new URL(t),!0}catch(t){return!1}},getPropName:function(t,e){var r=t.split("_"),n=r.first.toLowerCase();e&&(n=n.substring(0,1).toUpperCase()+n.substring(1));for(var i=1;i<r.length;i++)n+=r[i].substring(0,1),n+=r[i].substring(1).toLowerCase();return n},getEnumValueName:function(t){for(var e="",r=0;r<t.length;r++)r>0&&t[r]!=t[r].toLowerCase()&&(e+="_"),e+=t[r];return e.toUpperCase()}},InkStyle=function(){function t(e,r){var n=this,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};_classCallCheck$1(this,t),this.layout=e,this.pointProps=r,this.sheet={};var o={};Object.defineProperties(o,{size:{get:this.getComputed.bind(this,"size"),set:this.setStyle.bind(this,"size"),enumerable:!0},red:{get:this.getComputed.bind(this,"red"),set:this.setStyle.bind(this,"red"),enumerable:!0},green:{get:this.getComputed.bind(this,"green"),set:this.setStyle.bind(this,"green"),enumerable:!0},blue:{get:this.getComputed.bind(this,"blue"),set:this.setStyle.bind(this,"blue"),enumerable:!0},alpha:{get:this.getComputed.bind(this,"alpha"),set:this.setStyle.bind(this,"alpha"),enumerable:!0},rotation:{get:this.getComputed.bind(this,"rotation"),set:this.setStyle.bind(this,"rotation"),enumerable:!0},scaleX:{get:this.getComputed.bind(this,"scaleX"),set:this.setStyle.bind(this,"scaleX"),enumerable:!0},scaleY:{get:this.getComputed.bind(this,"scaleY"),set:this.setStyle.bind(this,"scaleY"),enumerable:!0},scaleZ:{get:this.getComputed.bind(this,"scaleZ"),set:this.setStyle.bind(this,"scaleZ"),enumerable:!0},offsetX:{get:this.getComputed.bind(this,"offsetX"),set:this.setStyle.bind(this,"offsetX"),enumerable:!0},offsetY:{get:this.getComputed.bind(this,"offsetY"),set:this.setStyle.bind(this,"offsetY"),enumerable:!0},offsetZ:{get:this.getComputed.bind(this,"offsetZ"),set:this.setStyle.bind(this,"offsetZ"),enumerable:!0},color:{get:this.getComputed.bind(this,"color"),set:this.setStyle.bind(this,"color"),enumerable:!0},blendMode:{get:this.getComputed.bind(this,"blendMode"),set:this.setStyle.bind(this,"blendMode"),enumerable:!0},visibility:{get:this.getComputed.bind(this,"visibility"),set:this.setStyle.bind(this,"visibility"),enumerable:!0},reset:{value:function(t){t&&(i=t),n.clear(),Object.keys(i).forEach((function(t){return n.setStyle(t,i[t])}))}},clear:{value:this.clear.bind(this)}}),this.style=Object.freeze(o),this.style.reset(i)}return _createClass$1(t,[{key:"setStyle",value:function(e,r){if(null==r&&(r=void 0),t.validate(this.layout,e,r),"color"==e&&r)return this.sheet.red=r.red,this.sheet.green=r.green,this.sheet.blue=r.blue,void(this.sheet.alpha=r.alpha);null==r?delete this.sheet[e]:this.sheet[e]=r}},{key:"getStyle",value:function(t){var e=this.sheet[t];return"visibility"==t?"boolean"!=typeof e&&(e=!0):"color"==t&&Color.isColor(this.sheet)&&(e=Color.fromColor(this.sheet)),e}},{key:"getComputed",value:function(t){var e=this.getStyle(t);if(null==e)if("color"==t){var r={red:isFinite(this.sheet.red)?this.sheet.red:this.pointProps.red,green:isFinite(this.sheet.green)?this.sheet.green:this.pointProps.green,blue:isFinite(this.sheet.blue)?this.sheet.blue:this.pointProps.blue,alpha:isFinite(this.sheet.alpha)?this.sheet.alpha:this.pointProps.alpha};Color.isColor(r)&&(e=Color.fromColor(r))}else e=this.pointProps[t];return e}},{key:"clear",value:function(){this.sheet={}}}],[{key:"validate",value:function(t,e,r,n){var i;if(r&&t.includes(PathPoint.Property[utils.getEnumValueName(e)])){if(!n)throw new Error("Property ".concat(e," value ").concat(r," is not applicable. This is a dynamic property and is part of the layout."));console.warn("Property ".concat(e," value ").concat(r," is not applicable. This is a dynamic property and is part of the layout.")),r=void 0}if("color"==e)!r||r instanceof Color||(i="Property ".concat(e," is not an instance of Color"));else if("blendMode"==e)""==r&&(r=void 0);else if("number"==typeof r)if("size"==e)r<0?i="Property ".concat(e," with value ").concat(r," is not allowed. Value should be a positive number."):0==r&&(r=void 0);else if("red"==e||"green"==e||"blue"==e||"alpha"==e){var o="alpha"==e?{min:0,max:1}:{min:0,max:255};r>=o.min&&r<=o.max||(i="Property ".concat(e," with value ").concat(r," is out of range. Allowd range: [").concat(o.min,", ").concat(o.max,"]."))}else"rotation"==e?0==r&&(r=void 0):"scattering"==e&&r<0&&(r=void 0);if(i)throw new Error(i);return r}}]),t}(),TypedArrayCodec=function(){function t(){_classCallCheck$1(this,t)}return _createClass$1(t,null,[{key:"encode",value:function(e){var r,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t.Encoding.AUTO;if(n==t.Encoding.AUTO?n="undefined"==typeof Buffer?"undefined"!=typeof SharedArrayBuffer&&e.buffer instanceof SharedArrayBuffer?t.Encoding.NONE:t.Encoding.ARRAY:t.Encoding.BUFFER:n==t.Encoding.ARRAY&&e instanceof Array&&(n=t.Encoding.NONE),n==t.Encoding.NONE)r=e;else if(n==t.Encoding.ARRAY)r=e.toArray();else{if("undefined"==typeof Buffer)throw new Error("Buffer not found, unable to serialize. Please provide Buffer in global scope.");var i=Buffer.from(e.buffer);switch(n){case t.Encoding.BUFFER:r=i.toJSON();break;case t.Encoding.BASE64:r=i.toString("base64");break;default:throw new Error("Invalid encoding provided: ".concat(n.name))}}return{encoding:n.name,type:e.constructor.name,content:r}}},{key:"decode",value:function(e){var r,n=t.Encoding[e.encoding];if(n==t.Encoding.NONE)r=e.content;else if(n==t.Encoding.ARRAY)r=e.content.toFloat32Array();else{var i;switch(n){case t.Encoding.BUFFER:i="undefined"==typeof Buffer?e.content.data:Buffer.from(e.content);break;case t.Encoding.BASE64:i="undefined"==typeof Buffer?atob(e.content).toCharArray():Buffer.from(e.content,"base64");break;default:throw new Error("Invalid encoding provided: ".concat(n.name))}var o=new Uint8Array(i);r=new globalThis[e.type](o.buffer)}return r}},{key:"isTypedArrayData",value:function(t){return t&&t.encoding&&t.type&&t.type.endsWith("Array")}}]),t}();function _classPrivateFieldInitSpec$c(t,e,r){_checkPrivateRedeclaration$c(t,e),e.set(t,r)}function _checkPrivateRedeclaration$c(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}Object.defineEnum(TypedArrayCodec,"Encoding",["AUTO","NONE","ARRAY","BUFFER","BASE64"]);var DEFAULT_LAYOUT=[PathPoint.Property.X,PathPoint.Property.Y],_points=new WeakMap,_point=new WeakMap,Path=function(){function t(e){var r=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:DEFAULT_LAYOUT;if(_classCallCheck$1(this,t),_classPrivateFieldInitSpec$c(this,_points,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$c(this,_point,{writable:!0,value:void 0}),_classPrivateFieldSet(this,_points,e),Object.defineProperty(this,"points",{get:function(){return _classPrivateFieldGet(r,_points)},set:function(t){if(_classPrivateFieldGet(r,_points)instanceof Float32Array)throw new Error("Points setter is not accessible wehn points type is Float32Array.");_classPrivateFieldSet(r,_points,t),r.validate()},enumerable:!0}),Object.defineProperty(this,"buffer",{get:function(){return _classPrivateFieldGet(r,_points).buffer},set:function(t){if(Array.isArray(_classPrivateFieldGet(r,_points)))throw new Error("Underlying points buffer is Array. This property is applicable for TypedArray only.");if("undefined"!=typeof SharedArrayBuffer&&_classPrivateFieldGet(r,_points).buffer instanceof SharedArrayBuffer)throw new Error("Underlying buffer is SharedArrayBuffer and cannot be restored");if(_classPrivateFieldGet(r,_points).buffer.byteLength>0)throw new Error("Cannot restore buffer when underlying buffer is not empty");if(t.byteLength/Float32Array.BYTES_PER_ELEMENT/r.stride!=r.length)throw new Error("Value exceeds expected memory length");_classPrivateFieldSet(r,_points,new Float32Array(t))}}),!Object.isSealed(n))for(var o in n)void 0!==n[o]&&(n[o]=InkStyle.validate(i,o,n[o],!0));i.includes(PathPoint.Property.ROTATION)||"rotation"in n||(n.rotation=void 0),i.includes(PathPoint.Property.SIZE)||n.size||(n.size=1),Object.defineProperties(this,{stride:{value:i.length,enumerable:!0},layout:{value:Object.freeze(i),enumerable:!0},pointProps:{value:Object.seal(n),enumerable:!0}}),e instanceof Float32Array?Object.defineProperty(this,"length",{value:_classPrivateFieldGet(this,_points).length/this.stride,enumerable:!0}):Object.defineProperty(this,"length",{get:function(){return _classPrivateFieldGet(r,_points).length/r.stride},enumerable:!0}),i.forEach((function(t,e){var n=utils.getPropName(t.name,!0);Object.defineProperty(r,"setPoint".concat(n),{value:r.setPointPropertyValue.bind(r,e)}),Object.defineProperty(r,"getPoint".concat(n),{value:r.getPointPropertyValue.bind(r,e)})}))}return _createClass$1(t,[{key:"validate",value:function(){if(this.points.length%this.stride!=0)throw new Error("Path length doesn't match the stride provided via the layout")}},{key:"setPointPropertyValue",value:function(t,e,r){if(isNaN(e))throw new Error("Point index is required");if(e>=this.length||e<0)throw new Error("Index ".concat(t," out of range - (0, ").concat(this.length-1,")"));if(isNaN(r))throw new Error("value is required");this.points[e*this.layout.length+t]=r}},{key:"getPointPropertyValue",value:function(t,e){if(isNaN(e))throw new Error("Point index is required");if(e>=this.length||e<0)throw new Error("Index ".concat(t," out of range - (0, ").concat(this.length-1,")"));return this.points[e*this.layout.length+t]}},{key:"setPoint",value:function(t,e){var r=this,n=t*this.stride;this.layout.forEach((function(t,i){return r.points[n+i]=e.getProperty(t)}))}},{key:"getPoint",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.pointProps;if(t>=this.length||t<0)throw new Error("Index ".concat(t," out of range - (0, ").concat(this.length-1,")"));return PathPoint.createInstance(this.layout,e,this.points,t)}},{key:"getPointRef",value:function(t){if(arguments.length>1&&void 0!==arguments[1]||this.pointProps,t>=this.length||t<0)throw new Error("Index ".concat(t," out of range - (0, ").concat(this.length-1,")"));return _classPrivateFieldGet(this,_point)||_classPrivateFieldSet(this,_point,PathPoint.createInstance(this.layout)),_classPrivateFieldGet(this,_point).fill(t,this.points,this.layout,this.pointProps),_classPrivateFieldGet(this,_point)}},{key:"getChannelData",value:function(t){var e=new([PathPoint.Property.RED,PathPoint.Property.GREEN,PathPoint.Property.BLUE].includes(t)?Uint8Array:Float32Array)(this.length),r=this.layout.indexOf(t);if(-1==r)throw new Error("Property ".concat(t.name," is not part from the spline layout ").concat(this.layout.map((function(t){return t.name})).join(", ")));for(var n=0;n<this.length;n++)e[n]=this.points[n*this.stride+r];return e}},{key:"transform",value:function(t){for(var e=t.scaleX,r=t.rotation,n=0;n<this.length;n++){var i=n*this.stride,o=fromValues$2(this.getPointX(n),this.getPointY(n),0,1);transformMat4$1(o,o,t.value);for(var s=0;s<this.stride;s++){var a=i+s;switch(this.layout[s]){case PathPoint.Property.X:this.points[a]=o[0]/o[3];break;case PathPoint.Property.Y:this.points[a]=o[1]/o[3];break;case PathPoint.Property.Z:this.points[a]=o[2]/o[3];break;case PathPoint.Property.ROTATION:this.points[a]+=r;break;case PathPoint.Property.SIZE:this.points[a]*=e}}}this.layout.includes(PathPoint.Property.ROTATION)||(this.pointProps.rotation=0==r?void 0:r)}},{key:"clone",value:function(){return new t(_classPrivateFieldGet(this,_points).clone(),Object.clone(this.pointProps),this.layout.slice())}},{key:"getFragment",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.length-1;return new PathFragment(this,t,e)}},{key:"slice",value:function(e){return new t(this.slicePoints(e.pointIndexStart,e.pointIndexEnd),Object.clone(this.pointProps),this.layout.slice())}},{key:"validateFragment",value:function(t,e){if(t<0)throw new Error("Invalid fragment pointIndexStart ".concat(t," found. The value must be non-negative."));if(e>this.length-1)throw new Error("Invalid fragment pointIndexEnd ".concat(e," found. Last point in path index is ").concat(this.length-1,"."))}},{key:"slicePoints",value:function(t,e){var r;if(this.validateFragment(t,e),"undefined"!=typeof SharedArrayBuffer&&this.buffer instanceof SharedArrayBuffer){var n=this.points.subarray(t*this.stride,(e+1)*this.stride),i=new SharedArrayBuffer(n.length*Float32Array.BYTES_PER_ELEMENT);(r=new Float32Array(i)).set(n)}else r=this.points.slice(t*this.stride,(e+1)*this.stride);return r}},{key:"toSVGPath",value:function(){for(var t=[],e=0;e<this.length;e++)t.push("".concat(this.getPointX(e),",").concat(this.getPointY(e)));return"M ".concat(t.join(" L ")," Z")}},{key:"toJSON",value:function(){return{type:"Path",points:TypedArrayCodec.encode(_classPrivateFieldGet(this,_points),this.encoding),pointProps:this.pointProps,layout:this.layout.map((function(t){return t.name}))}}}],[{key:"fromJSON",value:function(e){if("Path"!=e.type)throw new Error("Path deserialization failed. JSON type is ".concat(e.type,", expected Path."));return new t(TypedArrayCodec.decode(e.points),e.pointProps,e.layout.map((function(t){return PathPoint.Property[t]})))}},{key:"fromRect",value:function(e,r){return new t([e.left,e.top,e.right,e.top,e.right,e.bottom,e.left,e.bottom,e.left,e.top],r)}},{key:"createInstance",value:function(e,r,n){return new t(e,r,n)}},{key:"createSharedInstance",value:function(e,r,n){return new t(Float32Array.createSharedInstance(e),r,n)}}]),t}();function _createForOfIteratorHelper$d(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=_unsupportedIterableToArray$d(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function _unsupportedIterableToArray$d(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray$d(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$d(t,e):void 0}}function _arrayLikeToArray$d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var CLIPPER_RANGE_MIN=-32767,CLIPPER_RANGE_MAX=32767,CLIPPER_RANGE_SIZE=CLIPPER_RANGE_MAX-CLIPPER_RANGE_MIN,ClipperContext=function(){function t(e,r){if(_classCallCheck$1(this,t),!Array.isArray(e))throw new Error("Unexpected polygons type found");var n=CLIPPER_RANGE_SIZE/(r.width+1e-16),i=CLIPPER_RANGE_SIZE/(r.height+1e-16),o=Math.floor(Math.min(n,i));if(0==o)throw new Error("Insufficent clipper range - (".concat(CLIPPER_RANGE_MIN," - ").concat(CLIPPER_RANGE_MAX,"), scale failed - scaleX: ").concat(n,", scaleY: ").concat(i));var s=r.left+.5*CLIPPER_RANGE_SIZE/o,a=r.top+.5*CLIPPER_RANGE_SIZE/o;this.solution=new Paths,this.bounds=r,this.transform={scale:o,offsetX:s,offsetY:a},this.subject=this.apply(e)}return _createClass$1(t,[{key:"convertPoint",value:function(t){var e=(t.x-this.transform.offsetX)*this.transform.scale,r=(t.y-this.transform.offsetY)*this.transform.scale;return{X:e<0?Math.ceil(e):Math.floor(e),Y:r<0?Math.ceil(r):Math.floor(r)}}},{key:"containsPoint",value:function(t){return Clipper.PointInPolygon(this.convertPoint(t),this.solution)}},{key:"apply",value:function(t){var e,r=new Paths,n=_createForOfIteratorHelper$d(t);try{for(n.s();!(e=n.n()).done;){for(var i=e.value,o=new Path$1,s=i.shape,a=0;a<s.length;a++)o.push(this.convertPoint({x:s.getPointX(a),y:s.getPointY(a)}));r.push(o)}}catch(t){n.e(t)}finally{n.f()}return r}},{key:"toPaths",value:function(){var t=[];this.lastPoint={};var e,r=_createForOfIteratorHelper$d(this.solution);try{for(r.s();!(e=r.n()).done;){var n=e.value;if(0!=n.length){var i=this.flatPath(n);i.length>0&&t.push(i)}}}catch(t){r.e(t)}finally{r.f()}return t}},{key:"flatPath",value:function(t){var e,r=[],n=_createForOfIteratorHelper$d(t);try{for(n.s();!(e=n.n()).done;){var i=e.value;this.lastPoint.X==i.X&&this.lastPoint.Y==i.Y||(r.push(i.X/this.transform.scale+this.transform.offsetX,i.Y/this.transform.scale+this.transform.offsetY),this.lastPoint=i)}}catch(t){n.e(t)}finally{n.f()}return r.length<6&&(console.warn("Invalid contour found: [".concat(r.join(", "),"]")),r.clear()),r}}]),t}(),QuickSort=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:2;_classCallCheck$1(this,t),this.stride=e}return _createClass$1(t,[{key:"sort",value:function(t,e){return this.sortArrayPart(t,0,t.length-this.stride,e),t}},{key:"partition",value:function(t,e,r,n){for(var i=t[r],o=t[r+1],s=e-this.stride,a=e;a<r;a+=2)n?n(i,o,t[a],t[a+1])&&(s+=this.stride,this.swap(t,s,a)):(i>t[a]||i==t[a]&&o>t[a+1])&&(s+=this.stride,this.swap(t,s,a));return this.swap(t,s+this.stride,r),s+this.stride}},{key:"swap",value:function(t,e,r){var n=t[e],i=t[e+1];return t[e]=t[r],t[e+1]=t[r+1],t[r]=n,t[r+1]=i,t}},{key:"sortArrayPart",value:function(t,e,r,n){if(e<r){var i=this.partition(t,e,r,n);this.sortArrayPart(t,e,i-this.stride,n),this.sortArrayPart(t,i+this.stride,r,n)}}}]),t}();function cross(t,e,r,n,i,o){return(r-t)*(o-e)-(n-e)*(i-t)}function perpendicularDistance(t,e,r,n,i,o){var s=t-r,a=i-r,l=s*(o-n)-a*(e-n);l*=l;var u=(s=i-r)*s+(a=o-n)*a;return u>0?Math.sqrt(l/u):Math.sqrt((r-t)*(r-t)+(n-e)*(n-e))}var ConvexHullProducer$1=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Float32Array;_classCallCheck$1(this,t),this.ArrayType=e,this.quickSort=new QuickSort}return _createClass$1(t,[{key:"monotoneChain",value:function(t){if(t.length<=0)return new this.ArrayType;this.quickSort.sort(t);for(var e=new this.ArrayType(t.length),r=0,n=0;n<t.length;n+=2){for(;r>=4&&cross(e[r-4],e[r-3],e[r-2],e[r-1],t[n],t[n+1])<=0;)r-=2;e[r]=t[n],e[r+1]=t[n+1],r+=2}e=e.slice(0,r);var i,o=new this.ArrayType(t.length);r=0;for(var s=t.length-2;s>=0;s-=2){for(;r>=4&&cross(o[r-4],o[r-3],o[r-2],o[r-1],t[s],t[s+1])<=0;)r-=2;o[r]=t[s],o[r+1]=t[s+1],r+=2}if(o=o.slice(0,r-2),this.ArrayType==Float32Array){var a=o.length+e.length;(i=Float32Array.createSharedInstance(a)).set(o),i.set(e,o.length)}else i=o.concat(e);return i}}]),t}();function _createForOfIteratorHelper$c(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=_unsupportedIterableToArray$c(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function _unsupportedIterableToArray$c(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray$c(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$c(t,e):void 0}}function _arrayLikeToArray$c(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var RectGL=function(){function t(e,r,n,i){_classCallCheck$1(this,t);var o=e,s=r,a=e+n,l=r+i;Object.defineProperties(this,{left:{value:o,enumerable:!0},x:{value:e,enumerable:!0},bottom:{value:s,enumerable:!0},y:{value:r,enumerable:!0},right:{value:a,enumerable:!0},top:{value:l,enumerable:!0},width:{value:n,enumerable:!0},height:{value:i,enumerable:!0}})}return _createClass$1(t,[{key:"union",value:function(e){if(e&&!(e instanceof t))throw new TypeError("rect must be instance of RectGL");return e?t.ofEdges(Math.min(this.left,e.left),Math.min(this.bottom,e.bottom),Math.max(this.right,e.right),Math.max(this.top,e.top)):this}},{key:"intersect",value:function(e){if(e&&!(e instanceof t))throw new TypeError("rect must be instance of RectGL");if(!e)return null;var r=t.ofEdges(Math.max(this.left,e.left),Math.max(this.bottom,e.bottom),Math.min(this.right,e.right),Math.min(this.top,e.top));return r.width>0&&r.height>0?r:null}},{key:"ceil",value:function(){return t.ofEdges(Math.floor(this.left),Math.floor(this.bottom),Math.ceil(this.right),Math.ceil(this.top))}},{key:"floor",value:function(){return t.ofEdges(Math.ceil(this.left),Math.ceil(this.bottom),Math.floor(this.right),Math.floor(this.top))}},{key:"transform",value:function(e){if(!e)return this;var r=Point$3.fromPoint({x:this.left,y:this.bottom}).transform(e),n=Point$3.fromPoint({x:this.right,y:this.bottom}).transform(e),i=Point$3.fromPoint({x:this.left,y:this.top}).transform(e),o=Point$3.fromPoint({x:this.right,y:this.top}).transform(e),s=Math.min(i.x,o.x,r.x,n.x),a=Math.min(i.y,o.y,r.y,n.y),l=Math.max(i.x,o.x,r.x,n.x),u=Math.max(i.y,o.y,r.y,n.y);return t.ofEdges(s,a,l,u)}},{key:"toQuad",value:function(t){var e;if(t){var r=Point$3.fromPoint({x:this.left,y:this.bottom}).transform(t),n=Point$3.fromPoint({x:this.right,y:this.bottom}).transform(t),i=Point$3.fromPoint({x:this.left,y:this.top}).transform(t),o=Point$3.fromPoint({x:this.right,y:this.top}).transform(t);e=fromValues$1(r.x,r.y,n.x,n.y,i.x,i.y,o.x,o.y)}else e=fromValues$1(this.left,this.bottom,this.right,this.bottom,this.left,this.top,this.right,this.top);return e}},{key:"toString",value:function(){return"gl-rect(".concat(this.x,", ").concat(this.y,", ").concat(this.width,", ").concat(this.height,")")}}],[{key:"ofEdges",value:function(e,r,n,i){return new t(e,r,n-e,i-r)}},{key:"calculateBrushGLSegmentBounds",value:function(e){var r,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2?arguments[2]:void 0,o=.5*e.size,s=Math.abs(n*o);if(i){var a=new Point$3(e.x,e.y,e.x,e.z);r=a.transform(i)}else r=e;var l,u=r.x,h=r.y,c=e.scaleX*o,p=e.scaleY*o,f=e.offsetX,d=-e.offsetY,y=Math.cos(e.rotation),v=Math.sin(e.rotation),m=Number.MAX_SAFE_INTEGER,g=Number.MIN_SAFE_INTEGER,P=Number.MAX_SAFE_INTEGER,_=Number.MIN_SAFE_INTEGER,b=_createForOfIteratorHelper$c(t.SQURE);try{for(b.s();!(l=b.n()).done;){var x=l.value,E=x.x*c+f,I=x.y*p+d,S=y*E+v*I+u,C=-v*E+y*I+h,O=S-s;m=Math.min(m,O),g=Math.max(g,O),O=S+s,m=Math.min(m,O),g=Math.max(g,O);var A=C-s;P=Math.min(P,A),_=Math.max(_,A),A=C+s,P=Math.min(P,A),_=Math.max(_,A)}}catch(t){b.e(t)}finally{b.f()}return t.ofEdges(m,P,g,_)}}]),t}();Object.defineProperty(RectGL,"SQURE",{value:Object.freeze([Object.freeze({x:-1,y:-1}),Object.freeze({x:1,y:-1}),Object.freeze({x:-1,y:1}),Object.freeze({x:1,y:1})]),enumerable:!0});var Rect=function(){function t(e,r,n,i){_classCallCheck$1(this,t);var o,s,a,l=e,u=r,h=e+n,c=r+i;Object.defineProperties(this,{left:{value:l,enumerable:!0},top:{value:u,enumerable:!0},right:{value:h,enumerable:!0},bottom:{value:c,enumerable:!0},x:{value:e,enumerable:!0},y:{value:r,enumerable:!0},width:{value:n,enumerable:!0},height:{value:i,enumerable:!0},size:{get:function(){return o||(o={width:n,height:i}),o},enumerable:!0},area:{get:function(){return s||(s=n*i),s},enumerable:!0},center:{get:function(){return a||(a={x:(l+h)/2,y:(u+c)/2}),a},enumerable:!0}})}return _createClass$1(t,[{key:"union",value:function(e){return e?t.ofEdges(Math.min(this.left,e.left),Math.min(this.top,e.top),Math.max(this.right,e.right),Math.max(this.bottom,e.bottom)):this}},{key:"intersect",value:function(e){if(!e)return null;var r=t.ofEdges(Math.max(this.left,e.left),Math.max(this.top,e.top),Math.min(this.right,e.right),Math.min(this.bottom,e.bottom));return r.width>0&&r.height>0?r:null}},{key:"intersects",value:function(t){return this.left<=t.right&&this.right>=t.left&&this.top<=t.bottom&&this.bottom>=t.top}},{key:"ceil",value:function(e){var r=Math.floor(this.left),n=Math.floor(this.top),i=Math.ceil(this.right),o=Math.ceil(this.bottom);if(e){var s=i-r,a=o-n;i=r+(s+=s%2),o=n+(a+=a%2)}return t.ofEdges(r,n,i,o)}},{key:"floor",value:function(e){var r=Math.ceil(this.left),n=Math.ceil(this.top),i=Math.floor(this.right),o=Math.floor(this.bottom);if(e){var s=i-r,a=o-n;i=r+(s-=s%2),o=n+(a-=a%2)}return t.ofEdges(r,n,i,o)}},{key:"contains",value:function(t){return this.left<=t.x&&this.right>=t.x&&this.top<=t.y&&this.bottom>=t.y}},{key:"includes",value:function(t){return this.left<=t.left&&this.right>=t.right&&this.top<=t.top&&this.bottom>=t.bottom}},{key:"transform",value:function(e){if(!e)return this;var r=Point$3.fromPoint({x:this.left,y:this.top}).transform(e),n=Point$3.fromPoint({x:this.right,y:this.top}).transform(e),i=Point$3.fromPoint({x:this.left,y:this.bottom}).transform(e),o=Point$3.fromPoint({x:this.right,y:this.bottom}).transform(e),s=Math.min(r.x,n.x,i.x,o.x),a=Math.min(r.y,n.y,i.y,o.y),l=Math.max(r.x,n.x,i.x,o.x),u=Math.max(r.y,n.y,i.y,o.y);return t.ofEdges(s,a,l,u)}},{key:"toPath",value:function(t){throw new Error("Rect.toPath is deprecated. Path.fromRect(rect, [pointProps]) instead")}},{key:"toGLRect",value:function(){return new RectGL(this.x,this.y,this.width,this.height)}},{key:"toString",value:function(){return"rect(".concat(this.x,", ").concat(this.y,", ").concat(this.width,", ").concat(this.height,")")}},{key:"toJSON",value:function(){return{x:this.left,y:this.top,width:this.width,height:this.height}}}],[{key:"fromGLRect",value:function(e){if(!e)return null;if(!(e instanceof RectGL))throw new TypeError("rect must be instance of RectGL");return new t(e.left,e.bottom,e.width,e.height)}},{key:"isRect",value:function(t){return t&&isFinite(t.left)&&isFinite(t.top)&&isFinite(t.width)&&isFinite(t.height)}},{key:"fromString",value:function(e){return e=e.substring(e.indexOf("(")+1,e.indexOf(")")).split(/,\s*/g),new t(parseFloat(e[0]),parseFloat(e[1]),parseFloat(e[2]),parseFloat(e[3]))}},{key:"fromRect",value:function(e){return"string"==typeof e?t.fromString(e):new t(e.x,e.y,e.width,e.height)}},{key:"ofPolygon",value:function(e){if(e.shape&&(e=e.shape),0==e.length)return null;for(var r=Number.MAX_SAFE_INTEGER,n=Number.MAX_SAFE_INTEGER,i=Number.MIN_SAFE_INTEGER,o=Number.MIN_SAFE_INTEGER,s=0;s<e.length;s++){var a=e.getPointX(s),l=e.getPointY(s);r=Math.min(r,a),n=Math.min(n,l),i=Math.max(i,a),o=Math.max(o,l)}return t.ofEdges(r,n,i,o)}},{key:"ofSpline",value:function(e){for(var r,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=0;i<e.length;i++)r=RectGL.calculateBrushGLSegmentBounds(e.getPointRef(i),n).union(r);return t.fromGLRect(r)}},{key:"ofEdges",value:function(e,r,n,i){var o=Math.min(e,n),s=Math.min(r,i);return new t(o,s,Math.max(e,n)-o,Math.max(r,i)-s)}},{key:"union",value:function(t,e){return t?e?t.union(e):t:e}},{key:"intersect",value:function(t,e){return t&&e?t.intersect(e):null}}]),t}();function _createForOfIteratorHelper$b(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=_unsupportedIterableToArray$b(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function _unsupportedIterableToArray$b(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray$b(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$b(t,e):void 0}}function _arrayLikeToArray$b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Polygon=function(){function t(e){var r,n=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(_classCallCheck$1(this,t),!(e instanceof Path))throw new Error("Expected shape type is Path. Use createInstance or createSharedInstance Polygon methods to allocate instance.");if(i.some((function(t){return!(t instanceof Path)})))throw new Error("Expected hole type is Path. Use createInstance or createSharedInstance Polygon methods to allocate instance.");this.holesDirection=t.PointsDirection.CLOCKWISE,Object.defineProperty(this,"shape",{value:e,enumerable:!0}),Object.defineProperty(this,"holes",{value:i,enumerable:!0}),Object.defineProperty(this,"contours",{value:[e].concat(_toConsumableArray(i)),enumerable:!0}),Object.defineProperty(this,"ArrayType",{value:e.points instanceof Float32Array?Float32Array:Array}),Object.defineProperty(this,"bounds",{get:function(){return Rect.ofPolygon(n)},enumerable:!0}),Object.defineProperty(this,"vertices",{get:function(){return r||(r=n.triangulate()),r},set:function(t){return r=t},enumerable:!0}),Object.defineProperty(this,"verticesValue",{get:function(){return r}})}return _createClass$1(t,[{key:"clone",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],r=this.shape.clone(e),n=this.holes.map((function(t){return t.clone(e)})),i=new t(r,n);return this.verticesValue&&(i.vertices=this.vertices.slice()),i}},{key:"fit",value:function(t){var e,r=this.bounds,n=t.width/r.width,i=t.height/r.height,o=n>0&&i>0?Math.min(n,i):Math.max(n,i),s=_createForOfIteratorHelper$b(this.contours);try{for(s.s();!(e=s.n()).done;)for(var a=e.value,l=0;l<a.length;l++)a.setPointX(l,a.getPointX(l)*o),a.setPointY(l,a.getPointY(l)*o)}catch(t){s.e(t)}finally{s.f()}}},{key:"center",value:function(){var t,e=this.bounds,r=_createForOfIteratorHelper$b(this.contours);try{for(r.s();!(t=r.n()).done;)for(var n=t.value,i=0;i<n.length;i++)n.setPointX(i,n.getPointX(i)-e.center.x),n.setPointY(i,n.getPointY(i)-e.center.y)}catch(t){r.e(t)}finally{r.f()}}},{key:"transform",value:function(t){this.contours.forEach((function(e){return e.transform(t)}))}},{key:"intersects",value:function(e){if(!(e instanceof t))throw new Error("Expected 'poly' type is Polygon");(this.holes.length>0||e.holes.length)&&console.warn("Polygon intersection is for contours only. Holes are ignored.");for(var r=this.shape,n=e.shape,i=0;i<2;i++)for(var o=0==i?r:n,s=0;s<o.length;s++){for(var a=s+1==o.length?0:s+1,l=o.getPointX(s),u=o.getPointY(s),h=o.getPointX(a),c=o.getPointY(a)-u,p=l-h,f=Number.POSITIVE_INFINITY,d=Number.NEGATIVE_INFINITY,y=0;y<r.length;y++){var v=c*r.getPointX(y)+p*r.getPointY(y);v<f&&(f=v),v>d&&(d=v)}for(var m=Number.POSITIVE_INFINITY,g=Number.NEGATIVE_INFINITY,P=0;P<n.length;P++){var _=c*n.getPointX(P)+p*n.getPointY(P);_<m&&(m=_),_>g&&(g=_)}if(d<m||g<f)return!1}return!0}},{key:"containsPoint",value:function(t){var e=!1,r=this.shape;this.holes.length>0&&console.warn("Polygon intersection is for contours only. Holes are ignored.");for(var n=0,i=r.length-1;n<r.length;i=n++)r.getPointY(n)>t.y!=r.getPointY(i)>t.y&&t.x<(r.getPointX(i)-r.getPointX(n))*(t.y-r.getPointY(n))/(r.getPointY(i)-r.getPointY(n))+r.getPointX(n)&&(e=!e);return e}},{key:"triangulate",value:function(){var t,e,r=[],n=_createForOfIteratorHelper$b(this.contours);try{for(n.s();!(t=n.n()).done;){for(var i=t.value,o=[],s=0;s<i.length;s++){var a=new Point(i.getPointX(s),i.getPointY(s));if(s>0){if(o.last.x==a.x&&o.last.y==a.y)continue;if(s==i.length-1&&o.first.x==a.x&&o.first.y==a.y)continue}o.push(a)}r.push(o)}}catch(t){n.e(t)}finally{n.f()}try{e=new SweepContext(r.shift())}catch(t){return console.error(t),new Float32Array}for(var l=0,u=r;l<u.length;l++){var h=u[l];try{e.addHole(h)}catch(t){return console.error(t),new Float32Array}}try{e.triangulate()}catch(t){return console.warn(t),new Float32Array}var c,p=e.getTriangles(),f=Float32Array.createSharedInstance(6*p.length),d=0,y=_createForOfIteratorHelper$b(p);try{for(y.s();!(c=y.n()).done;){var v,m=_createForOfIteratorHelper$b(c.value.getPoints());try{for(m.s();!(v=m.n()).done;){var g=v.value;f[d++]=g.x,f[d++]=g.y}}catch(t){m.e(t)}finally{m.f()}}}catch(t){y.e(t)}finally{y.f()}return f}},{key:"convex",value:function(){return this.buildConvex(this.shape.points)}},{key:"union",value:function(t){var e=Array.of.apply(Array,_toConsumableArray(this.shape.points).concat(_toConsumableArray(t.shape.points)));return this.buildConvex(e)}},{key:"buildConvex",value:function(e){this.convexHullProducer||(this.convexHullProducer=new ConvexHullProducer$1(this.ArrayType));var r=this.convexHullProducer.monotoneChain(e);return this.ArrayType==Float32Array?t.createSharedInstance(r):t.createInstance(r)}},{key:"simplify",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.1,r=new ClipperContext([this],this.bounds);return r.subject=Clipper.SimplifyPolygons(r.subject,PolyFillType.pftNonZero),r.solution=Clipper.CleanPolygons(r.subject,e*r.transform.scale),1==r.subject.length&&0==r.solution.first.length&&(r.solution=r.subject),t.toPolygonArray(r.toPaths())}},{key:"simplifyRamerDouglasPeucker",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.1;if(e<=0)throw new Error("epsilon expected value > 0");this.epsilon=e;var r,n=this.simplifyPath(this.shape),i=[],o=_createForOfIteratorHelper$b(this.holes);try{for(o.s();!(r=o.n()).done;){var s=r.value,a=this.simplifyPath(s);a.length>0&&i.push(a)}}catch(t){o.e(t)}finally{o.f()}return this.ArrayType==Float32Array?t.createSharedInstance(n,i):t.createInstance(n,i)}},{key:"simplifyPath",value:function(t){if(t.length<3)return t.points;var e=Array.of.apply(Array,_toConsumableArray(t.points).concat([t.getPointX(0),t.getPointY(0)])),r=this.simplifyPolyline(e);return r.length<8?e.slice(0,e.length-2):r.slice(0,r.length-2)}},{key:"simplifyPolyline",value:function(t){if(t.length<4)return t;for(var e=0,r=0,n=2;n<t.length-2;n+=2){var i=perpendicularDistance(t[n],t[n+1],t[0],t[1],t[t.length-2],t[t.length-1]);i>e&&(r=n,e=i)}if(e>this.epsilon){var o=this.simplifyPolyline(t.slice(0,r+2)),s=this.simplifyPolyline(t.slice(r,t.length));return o.concat(s.slice(2,s.length))}return[t[0],t[1],t[t.length-2],t[t.length-1]]}},{key:"toSVGPath",value:function(){return this.contours.map((function(t){return t.toSVGPath()})).join(" ")}},{key:"toJSON",value:function(){return{type:"Polygon",shape:this.shape.toJSON(),holes:this.holes.map((function(t){return t.toJSON()})),holesDirection:this.holesDirection.name,vertices:this.verticesValue}}}],[{key:"fromJSON",value:function(e){if("Polygon"!=e.type)throw new Error("Polygon deserialization failed. JSON type is ".concat(e.type,", expected Polygon."));var r=new t(Path.fromJSON(e.shape),e.holes.map((function(t){return Path.fromJSON(t)})));return r.holesDirection=t.PointsDirection[e.holesDirection],r.vertices=e.vertices,r}},{key:"fromRect",value:function(e){return t.createInstance([e.left,e.top,e.right,e.top,e.right,e.bottom,e.left,e.bottom,e.left,e.top])}},{key:"createInstance",value:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return new t(Path.createInstance(e),r.map((function(t){return Path.createInstance(t)})))}},{key:"createSharedInstance",value:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=new t(Path.createSharedInstance(e),r.map((function(t){return Path.createSharedInstance(t)})));return Object.defineProperty(n,"encoding",{get:function(){return n.shape.encoding},set:function(t){n.contours.forEach((function(e){return e.encoding=t}))},enumerable:!0}),n}}]),t}();function _classPrivateFieldInitSpec$b(t,e,r){_checkPrivateRedeclaration$b(t,e),e.set(t,r)}function _checkPrivateRedeclaration$b(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}Object.defineEnum(Polygon,"PointsDirection",["CLOCKWISE","COUNTERCLOCKWISE"]);var _path$3=new WeakMap,DataSequenceProcessor=function(){function t(){var e=this;_classCallCheck$1(this,t),_classPrivateFieldInitSpec$b(this,_path$3,{writable:!0,value:void 0}),this.keepAllData=!1,Object.defineProperty(this,"allData",{get:function(){if(!e.keepAllData)throw new Error("All data is not accumulated. By default keepAllData property is false.");return _classPrivateFieldGet(e,_path$3)||_classPrivateFieldSet(e,_path$3,new e.constructor.ARRAY_TYPE),e.getOutput(_classPrivateFieldGet(e,_path$3),t.OutputType.ALL_DATA)},enumerable:!0})}return _createClass$1(t,[{key:"build",value:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t.OutputType.PROCESSOR,n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return console.warn("use process instead"),this.process(e,r,n)}},{key:"process",value:function(e){var r,n,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t.OutputType.PROCESSOR,o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];switch(i){case t.OutputType.ADDITION:r=this.add(e,o);break;case t.OutputType.PREDICTION:r=this.predict(e);break;case t.OutputType.PROCESSOR:this.reset(),r=this.processImpl(e,i);break;default:throw new Error("Unexpected OutputType found. Allowed type is oneof(ADDITION, PREDICTION, PROCESSOR)")}i!=t.OutputType.PREDICTION&&(this.keepAllData&&(_classPrivateFieldGet(this,_path$3)||_classPrivateFieldSet(this,_path$3,new this.constructor.ARRAY_TYPE),r instanceof Polygon?_classPrivateFieldGet(this,_path$3).push(r):i==t.OutputType.PROCESSOR?_classPrivateFieldSet(this,_path$3,r):(n=_classPrivateFieldGet(this,_path$3)).push.apply(n,_toConsumableArray(r))));return this.debug&&console.log(this.constructor.name,i.name,o,r),this.getOutput(r,i)}},{key:"add",value:function(e){return this.processImpl(e,t.OutputType.ADDITION)}},{key:"predict",value:function(e){return this.processImpl(e,t.OutputType.PREDICTION)}},{key:"processImpl",value:function(t,e){throw new Error("Abstract method processImpl(input, type) of DataSequenceProcessor should be implemented")}},{key:"getOutput",value:function(t,e){return t}},{key:"reset",value:function(){_classPrivateFieldGet(this,_path$3)&&_classPrivateFieldSet(this,_path$3,new this.constructor.ARRAY_TYPE)}}]),t}();_defineProperty(DataSequenceProcessor,"ARRAY_TYPE",Array),Object.defineEnum(DataSequenceProcessor,"OutputType",["ADDITION","PREDICTION","ALL_DATA","PROCESSOR"]);var wasm=null;try{wasm=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(e){}function Long(t,e,r){this.low=0|t,this.high=0|e,this.unsigned=!!r}function isLong(t){return!0===(t&&t.__isLong__)}function ctz32(t){var e=Math.clz32(t&-t);return t?31-e:e}Long.prototype.__isLong__,Object.defineProperty(Long.prototype,"__isLong__",{value:!0}),Long.isLong=isLong;var INT_CACHE={},UINT_CACHE={};function fromInt(t,e){var r,n,i;return e?(i=0<=(t>>>=0)&&t<256)&&(n=UINT_CACHE[t])?n:(r=fromBits(t,0,!0),i&&(UINT_CACHE[t]=r),r):(i=-128<=(t|=0)&&t<128)&&(n=INT_CACHE[t])?n:(r=fromBits(t,t<0?-1:0,!1),i&&(INT_CACHE[t]=r),r)}function fromNumber(t,e){if(isNaN(t))return e?UZERO:ZERO;if(e){if(t<0)return UZERO;if(t>=TWO_PWR_64_DBL)return MAX_UNSIGNED_VALUE}else{if(t<=-TWO_PWR_63_DBL)return MIN_VALUE;if(t+1>=TWO_PWR_63_DBL)return MAX_VALUE}return t<0?fromNumber(-t,e).neg():fromBits(t%TWO_PWR_32_DBL|0,t/TWO_PWR_32_DBL|0,e)}function fromBits(t,e,r){return new Long(t,e,r)}Long.fromInt=fromInt,Long.fromNumber=fromNumber,Long.fromBits=fromBits;var pow_dbl=Math.pow;function fromString(t,e,r){if(0===t.length)throw Error("empty string");if("number"==typeof e?(r=e,e=!1):e=!!e,"NaN"===t||"Infinity"===t||"+Infinity"===t||"-Infinity"===t)return e?UZERO:ZERO;if((r=r||10)<2||36<r)throw RangeError("radix");var n;if((n=t.indexOf("-"))>0)throw Error("interior hyphen");if(0===n)return fromString(t.substring(1),e,r).neg();for(var i=fromNumber(pow_dbl(r,8)),o=ZERO,s=0;s<t.length;s+=8){var a=Math.min(8,t.length-s),l=parseInt(t.substring(s,s+a),r);if(a<8){var u=fromNumber(pow_dbl(r,a));o=o.mul(u).add(fromNumber(l))}else o=(o=o.mul(i)).add(fromNumber(l))}return o.unsigned=e,o}function fromValue(t,e){return"number"==typeof t?fromNumber(t,e):"string"==typeof t?fromString(t,e):fromBits(t.low,t.high,"boolean"==typeof e?e:t.unsigned)}Long.fromString=fromString,Long.fromValue=fromValue;var TWO_PWR_16_DBL=65536,TWO_PWR_24_DBL=1<<24,TWO_PWR_32_DBL=TWO_PWR_16_DBL*TWO_PWR_16_DBL,TWO_PWR_64_DBL=TWO_PWR_32_DBL*TWO_PWR_32_DBL,TWO_PWR_63_DBL=TWO_PWR_64_DBL/2,TWO_PWR_24=fromInt(TWO_PWR_24_DBL),ZERO=fromInt(0);Long.ZERO=ZERO;var UZERO=fromInt(0,!0);Long.UZERO=UZERO;var ONE=fromInt(1);Long.ONE=ONE;var UONE=fromInt(1,!0);Long.UONE=UONE;var NEG_ONE=fromInt(-1);Long.NEG_ONE=NEG_ONE;var MAX_VALUE=fromBits(-1,2147483647,!1);Long.MAX_VALUE=MAX_VALUE;var MAX_UNSIGNED_VALUE=fromBits(-1,-1,!0);Long.MAX_UNSIGNED_VALUE=MAX_UNSIGNED_VALUE;var MIN_VALUE=fromBits(0,-2147483648,!1);Long.MIN_VALUE=MIN_VALUE;var LongPrototype=Long.prototype;LongPrototype.toInt=function(){return this.unsigned?this.low>>>0:this.low},LongPrototype.toNumber=function(){return this.unsigned?(this.high>>>0)*TWO_PWR_32_DBL+(this.low>>>0):this.high*TWO_PWR_32_DBL+(this.low>>>0)},LongPrototype.toString=function(t){if((t=t||10)<2||36<t)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(this.eq(MIN_VALUE)){var e=fromNumber(t),r=this.div(e),n=r.mul(e).sub(this);return r.toString(t)+n.toInt().toString(t)}return"-"+this.neg().toString(t)}for(var i=fromNumber(pow_dbl(t,6),this.unsigned),o=this,s="";;){var a=o.div(i),l=(o.sub(a.mul(i)).toInt()>>>0).toString(t);if((o=a).isZero())return l+s;for(;l.length<6;)l="0"+l;s=""+l+s}},LongPrototype.getHighBits=function(){return this.high},LongPrototype.getHighBitsUnsigned=function(){return this.high>>>0},LongPrototype.getLowBits=function(){return this.low},LongPrototype.getLowBitsUnsigned=function(){return this.low>>>0},LongPrototype.getNumBitsAbs=function(){if(this.isNegative())return this.eq(MIN_VALUE)?64:this.neg().getNumBitsAbs();for(var t=0!=this.high?this.high:this.low,e=31;e>0&&0==(t&1<<e);e--);return 0!=this.high?e+33:e+1},LongPrototype.isZero=function(){return 0===this.high&&0===this.low},LongPrototype.eqz=LongPrototype.isZero,LongPrototype.isNegative=function(){return!this.unsigned&&this.high<0},LongPrototype.isPositive=function(){return this.unsigned||this.high>=0},LongPrototype.isOdd=function(){return 1==(1&this.low)},LongPrototype.isEven=function(){return 0==(1&this.low)},LongPrototype.equals=function(t){return isLong(t)||(t=fromValue(t)),(this.unsigned===t.unsigned||this.high>>>31!=1||t.high>>>31!=1)&&(this.high===t.high&&this.low===t.low)},LongPrototype.eq=LongPrototype.equals,LongPrototype.notEquals=function(t){return!this.eq(t)},LongPrototype.neq=LongPrototype.notEquals,LongPrototype.ne=LongPrototype.notEquals,LongPrototype.lessThan=function(t){return this.comp(t)<0},LongPrototype.lt=LongPrototype.lessThan,LongPrototype.lessThanOrEqual=function(t){return this.comp(t)<=0},LongPrototype.lte=LongPrototype.lessThanOrEqual,LongPrototype.le=LongPrototype.lessThanOrEqual,LongPrototype.greaterThan=function(t){return this.comp(t)>0},LongPrototype.gt=LongPrototype.greaterThan,LongPrototype.greaterThanOrEqual=function(t){return this.comp(t)>=0},LongPrototype.gte=LongPrototype.greaterThanOrEqual,LongPrototype.ge=LongPrototype.greaterThanOrEqual,LongPrototype.compare=function(t){if(isLong(t)||(t=fromValue(t)),this.eq(t))return 0;var e=this.isNegative(),r=t.isNegative();return e&&!r?-1:!e&&r?1:this.unsigned?t.high>>>0>this.high>>>0||t.high===this.high&&t.low>>>0>this.low>>>0?-1:1:this.sub(t).isNegative()?-1:1},LongPrototype.comp=LongPrototype.compare,LongPrototype.negate=function(){return!this.unsigned&&this.eq(MIN_VALUE)?MIN_VALUE:this.not().add(ONE)},LongPrototype.neg=LongPrototype.negate,LongPrototype.add=function(t){isLong(t)||(t=fromValue(t));var e=this.high>>>16,r=65535&this.high,n=this.low>>>16,i=65535&this.low,o=t.high>>>16,s=65535&t.high,a=t.low>>>16,l=0,u=0,h=0,c=0;return h+=(c+=i+(65535&t.low))>>>16,u+=(h+=n+a)>>>16,l+=(u+=r+s)>>>16,l+=e+o,fromBits((h&=65535)<<16|(c&=65535),(l&=65535)<<16|(u&=65535),this.unsigned)},LongPrototype.subtract=function(t){return isLong(t)||(t=fromValue(t)),this.add(t.neg())},LongPrototype.sub=LongPrototype.subtract,LongPrototype.multiply=function(t){if(this.isZero())return this;if(isLong(t)||(t=fromValue(t)),wasm)return fromBits(wasm.mul(this.low,this.high,t.low,t.high),wasm.get_high(),this.unsigned);if(t.isZero())return this.unsigned?UZERO:ZERO;if(this.eq(MIN_VALUE))return t.isOdd()?MIN_VALUE:ZERO;if(t.eq(MIN_VALUE))return this.isOdd()?MIN_VALUE:ZERO;if(this.isNegative())return t.isNegative()?this.neg().mul(t.neg()):this.neg().mul(t).neg();if(t.isNegative())return this.mul(t.neg()).neg();if(this.lt(TWO_PWR_24)&&t.lt(TWO_PWR_24))return fromNumber(this.toNumber()*t.toNumber(),this.unsigned);var e=this.high>>>16,r=65535&this.high,n=this.low>>>16,i=65535&this.low,o=t.high>>>16,s=65535&t.high,a=t.low>>>16,l=65535&t.low,u=0,h=0,c=0,p=0;return c+=(p+=i*l)>>>16,h+=(c+=n*l)>>>16,c&=65535,h+=(c+=i*a)>>>16,u+=(h+=r*l)>>>16,h&=65535,u+=(h+=n*a)>>>16,h&=65535,u+=(h+=i*s)>>>16,u+=e*l+r*a+n*s+i*o,fromBits((c&=65535)<<16|(p&=65535),(u&=65535)<<16|(h&=65535),this.unsigned)},LongPrototype.mul=LongPrototype.multiply,LongPrototype.divide=function(t){if(isLong(t)||(t=fromValue(t)),t.isZero())throw Error("division by zero");var e,r,n;if(wasm)return this.unsigned||-2147483648!==this.high||-1!==t.low||-1!==t.high?fromBits((this.unsigned?wasm.div_u:wasm.div_s)(this.low,this.high,t.low,t.high),wasm.get_high(),this.unsigned):this;if(this.isZero())return this.unsigned?UZERO:ZERO;if(this.unsigned){if(t.unsigned||(t=t.toUnsigned()),t.gt(this))return UZERO;if(t.gt(this.shru(1)))return UONE;n=UZERO}else{if(this.eq(MIN_VALUE))return t.eq(ONE)||t.eq(NEG_ONE)?MIN_VALUE:t.eq(MIN_VALUE)?ONE:(e=this.shr(1).div(t).shl(1)).eq(ZERO)?t.isNegative()?ONE:NEG_ONE:(r=this.sub(t.mul(e)),n=e.add(r.div(t)));if(t.eq(MIN_VALUE))return this.unsigned?UZERO:ZERO;if(this.isNegative())return t.isNegative()?this.neg().div(t.neg()):this.neg().div(t).neg();if(t.isNegative())return this.div(t.neg()).neg();n=ZERO}for(r=this;r.gte(t);){e=Math.max(1,Math.floor(r.toNumber()/t.toNumber()));for(var i=Math.ceil(Math.log(e)/Math.LN2),o=i<=48?1:pow_dbl(2,i-48),s=fromNumber(e),a=s.mul(t);a.isNegative()||a.gt(r);)a=(s=fromNumber(e-=o,this.unsigned)).mul(t);s.isZero()&&(s=ONE),n=n.add(s),r=r.sub(a)}return n},LongPrototype.div=LongPrototype.divide,LongPrototype.modulo=function(t){return isLong(t)||(t=fromValue(t)),wasm?fromBits((this.unsigned?wasm.rem_u:wasm.rem_s)(this.low,this.high,t.low,t.high),wasm.get_high(),this.unsigned):this.sub(this.div(t).mul(t))},LongPrototype.mod=LongPrototype.modulo,LongPrototype.rem=LongPrototype.modulo,LongPrototype.not=function(){return fromBits(~this.low,~this.high,this.unsigned)},LongPrototype.countLeadingZeros=function(){return this.high?Math.clz32(this.high):Math.clz32(this.low)+32},LongPrototype.clz=LongPrototype.countLeadingZeros,LongPrototype.countTrailingZeros=function(){return this.low?ctz32(this.low):ctz32(this.high)+32},LongPrototype.ctz=LongPrototype.countTrailingZeros,LongPrototype.and=function(t){return isLong(t)||(t=fromValue(t)),fromBits(this.low&t.low,this.high&t.high,this.unsigned)},LongPrototype.or=function(t){return isLong(t)||(t=fromValue(t)),fromBits(this.low|t.low,this.high|t.high,this.unsigned)},LongPrototype.xor=function(t){return isLong(t)||(t=fromValue(t)),fromBits(this.low^t.low,this.high^t.high,this.unsigned)},LongPrototype.shiftLeft=function(t){return isLong(t)&&(t=t.toInt()),0==(t&=63)?this:t<32?fromBits(this.low<<t,this.high<<t|this.low>>>32-t,this.unsigned):fromBits(0,this.low<<t-32,this.unsigned)},LongPrototype.shl=LongPrototype.shiftLeft,LongPrototype.shiftRight=function(t){return isLong(t)&&(t=t.toInt()),0==(t&=63)?this:t<32?fromBits(this.low>>>t|this.high<<32-t,this.high>>t,this.unsigned):fromBits(this.high>>t-32,this.high>=0?0:-1,this.unsigned)},LongPrototype.shr=LongPrototype.shiftRight,LongPrototype.shiftRightUnsigned=function(t){return isLong(t)&&(t=t.toInt()),0==(t&=63)?this:t<32?fromBits(this.low>>>t|this.high<<32-t,this.high>>>t,this.unsigned):fromBits(32===t?this.high:this.high>>>t-32,0,this.unsigned)},LongPrototype.shru=LongPrototype.shiftRightUnsigned,LongPrototype.shr_u=LongPrototype.shiftRightUnsigned,LongPrototype.rotateLeft=function(t){var e;return isLong(t)&&(t=t.toInt()),0==(t&=63)?this:32===t?fromBits(this.high,this.low,this.unsigned):t<32?(e=32-t,fromBits(this.low<<t|this.high>>>e,this.high<<t|this.low>>>e,this.unsigned)):(e=32-(t-=32),fromBits(this.high<<t|this.low>>>e,this.low<<t|this.high>>>e,this.unsigned))},LongPrototype.rotl=LongPrototype.rotateLeft,LongPrototype.rotateRight=function(t){var e;return isLong(t)&&(t=t.toInt()),0==(t&=63)?this:32===t?fromBits(this.high,this.low,this.unsigned):t<32?(e=32-t,fromBits(this.high<<e|this.low>>>t,this.low<<e|this.high>>>t,this.unsigned)):(e=32-(t-=32),fromBits(this.low<<e|this.high>>>t,this.high<<e|this.low>>>t,this.unsigned))},LongPrototype.rotr=LongPrototype.rotateRight,LongPrototype.toSigned=function(){return this.unsigned?fromBits(this.low,this.high,!1):this},LongPrototype.toUnsigned=function(){return this.unsigned?this:fromBits(this.low,this.high,!0)},LongPrototype.toBytes=function(t){return t?this.toBytesLE():this.toBytesBE()},LongPrototype.toBytesLE=function(){var t=this.high,e=this.low;return[255&e,e>>>8&255,e>>>16&255,e>>>24,255&t,t>>>8&255,t>>>16&255,t>>>24]},LongPrototype.toBytesBE=function(){var t=this.high,e=this.low;return[t>>>24,t>>>16&255,t>>>8&255,255&t,e>>>24,e>>>16&255,e>>>8&255,255&e]},Long.fromBytes=function(t,e,r){return r?Long.fromBytesLE(t,e):Long.fromBytesBE(t,e)},Long.fromBytesLE=function(t,e){return new Long(t[0]|t[1]<<8|t[2]<<16|t[3]<<24,t[4]|t[5]<<8|t[6]<<16|t[7]<<24,e)},Long.fromBytesBE=function(t,e){return new Long(t[4]<<24|t[5]<<16|t[6]<<8|t[7],t[0]<<24|t[1]<<16|t[2]<<8|t[3],e)};var md5$2={exports:{}};(function(module){(function(){var ERROR="input is invalid type",WINDOW="object"==typeof window,root=WINDOW?window:{};root.JS_MD5_NO_WINDOW&&(WINDOW=!1);var WEB_WORKER=!WINDOW&&"object"==typeof self,NODE_JS=!root.JS_MD5_NO_NODE_JS&&"object"==typeof process&&process.versions&&process.versions.node;NODE_JS?root=commonjsGlobal:WEB_WORKER&&(root=self);var COMMON_JS=!root.JS_MD5_NO_COMMON_JS&&module.exports,ARRAY_BUFFER=!root.JS_MD5_NO_ARRAY_BUFFER&&"undefined"!=typeof ArrayBuffer,HEX_CHARS="0123456789abcdef".split(""),EXTRA=[128,32768,8388608,-2147483648],SHIFT=[0,8,16,24],OUTPUT_TYPES=["hex","array","digest","buffer","arrayBuffer","base64"],BASE64_ENCODE_CHAR="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),blocks=[],buffer8;if(ARRAY_BUFFER){var buffer=new ArrayBuffer(68);buffer8=new Uint8Array(buffer),blocks=new Uint32Array(buffer)}!root.JS_MD5_NO_NODE_JS&&Array.isArray||(Array.isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)}),!ARRAY_BUFFER||!root.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW&&ArrayBuffer.isView||(ArrayBuffer.isView=function(t){return"object"==typeof t&&t.buffer&&t.buffer.constructor===ArrayBuffer});var createOutputMethod=function(t){return function(e){return new Md5(!0).update(e)[t]()}},createMethod=function(){var t=createOutputMethod("hex");NODE_JS&&(t=nodeWrap(t)),t.create=function(){return new Md5},t.update=function(e){return t.create().update(e)};for(var e=0;e<OUTPUT_TYPES.length;++e){var r=OUTPUT_TYPES[e];t[r]=createOutputMethod(r)}return t},nodeWrap=function(method){var crypto=eval("require('crypto')"),Buffer=eval("require('buffer').Buffer"),nodeMethod=function(t){if("string"==typeof t)return crypto.createHash("md5").update(t,"utf8").digest("hex");if(null==t)throw ERROR;return t.constructor===ArrayBuffer&&(t=new Uint8Array(t)),Array.isArray(t)||ArrayBuffer.isView(t)||t.constructor===Buffer?crypto.createHash("md5").update(new Buffer(t)).digest("hex"):method(t)};return nodeMethod};function Md5(t){if(t)blocks[0]=blocks[16]=blocks[1]=blocks[2]=blocks[3]=blocks[4]=blocks[5]=blocks[6]=blocks[7]=blocks[8]=blocks[9]=blocks[10]=blocks[11]=blocks[12]=blocks[13]=blocks[14]=blocks[15]=0,this.blocks=blocks,this.buffer8=buffer8;else if(ARRAY_BUFFER){var e=new ArrayBuffer(68);this.buffer8=new Uint8Array(e),this.blocks=new Uint32Array(e)}else this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];this.h0=this.h1=this.h2=this.h3=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}Md5.prototype.update=function(t){if(!this.finalized){var e,r=typeof t;if("string"!==r){if("object"!==r)throw ERROR;if(null===t)throw ERROR;if(ARRAY_BUFFER&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!(Array.isArray(t)||ARRAY_BUFFER&&ArrayBuffer.isView(t)))throw ERROR;e=!0}for(var n,i,o=0,s=t.length,a=this.blocks,l=this.buffer8;o<s;){if(this.hashed&&(this.hashed=!1,a[0]=a[16],a[16]=a[1]=a[2]=a[3]=a[4]=a[5]=a[6]=a[7]=a[8]=a[9]=a[10]=a[11]=a[12]=a[13]=a[14]=a[15]=0),e)if(ARRAY_BUFFER)for(i=this.start;o<s&&i<64;++o)l[i++]=t[o];else for(i=this.start;o<s&&i<64;++o)a[i>>2]|=t[o]<<SHIFT[3&i++];else if(ARRAY_BUFFER)for(i=this.start;o<s&&i<64;++o)(n=t.charCodeAt(o))<128?l[i++]=n:n<2048?(l[i++]=192|n>>6,l[i++]=128|63&n):n<55296||n>=57344?(l[i++]=224|n>>12,l[i++]=128|n>>6&63,l[i++]=128|63&n):(n=65536+((1023&n)<<10|1023&t.charCodeAt(++o)),l[i++]=240|n>>18,l[i++]=128|n>>12&63,l[i++]=128|n>>6&63,l[i++]=128|63&n);else for(i=this.start;o<s&&i<64;++o)(n=t.charCodeAt(o))<128?a[i>>2]|=n<<SHIFT[3&i++]:n<2048?(a[i>>2]|=(192|n>>6)<<SHIFT[3&i++],a[i>>2]|=(128|63&n)<<SHIFT[3&i++]):n<55296||n>=57344?(a[i>>2]|=(224|n>>12)<<SHIFT[3&i++],a[i>>2]|=(128|n>>6&63)<<SHIFT[3&i++],a[i>>2]|=(128|63&n)<<SHIFT[3&i++]):(n=65536+((1023&n)<<10|1023&t.charCodeAt(++o)),a[i>>2]|=(240|n>>18)<<SHIFT[3&i++],a[i>>2]|=(128|n>>12&63)<<SHIFT[3&i++],a[i>>2]|=(128|n>>6&63)<<SHIFT[3&i++],a[i>>2]|=(128|63&n)<<SHIFT[3&i++]);this.lastByteIndex=i,this.bytes+=i-this.start,i>=64?(this.start=i-64,this.hash(),this.hashed=!0):this.start=i}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}},Md5.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var t=this.blocks,e=this.lastByteIndex;t[e>>2]|=EXTRA[3&e],e>=56&&(this.hashed||this.hash(),t[0]=t[16],t[16]=t[1]=t[2]=t[3]=t[4]=t[5]=t[6]=t[7]=t[8]=t[9]=t[10]=t[11]=t[12]=t[13]=t[14]=t[15]=0),t[14]=this.bytes<<3,t[15]=this.hBytes<<3|this.bytes>>>29,this.hash()}},Md5.prototype.hash=function(){var t,e,r,n,i,o,s=this.blocks;this.first?e=((e=((t=((t=s[0]-680876937)<<7|t>>>25)-271733879<<0)^(r=((r=(-271733879^(n=((n=(-1732584194^2004318071&t)+s[1]-117830708)<<12|n>>>20)+t<<0)&(-271733879^t))+s[2]-1126478375)<<17|r>>>15)+n<<0)&(n^t))+s[3]-1316259209)<<22|e>>>10)+r<<0:(t=this.h0,e=this.h1,r=this.h2,e=((e+=((t=((t+=((n=this.h3)^e&(r^n))+s[0]-680876936)<<7|t>>>25)+e<<0)^(r=((r+=(e^(n=((n+=(r^t&(e^r))+s[1]-389564586)<<12|n>>>20)+t<<0)&(t^e))+s[2]+606105819)<<17|r>>>15)+n<<0)&(n^t))+s[3]-1044525330)<<22|e>>>10)+r<<0),e=((e+=((t=((t+=(n^e&(r^n))+s[4]-176418897)<<7|t>>>25)+e<<0)^(r=((r+=(e^(n=((n+=(r^t&(e^r))+s[5]+1200080426)<<12|n>>>20)+t<<0)&(t^e))+s[6]-1473231341)<<17|r>>>15)+n<<0)&(n^t))+s[7]-45705983)<<22|e>>>10)+r<<0,e=((e+=((t=((t+=(n^e&(r^n))+s[8]+1770035416)<<7|t>>>25)+e<<0)^(r=((r+=(e^(n=((n+=(r^t&(e^r))+s[9]-1958414417)<<12|n>>>20)+t<<0)&(t^e))+s[10]-42063)<<17|r>>>15)+n<<0)&(n^t))+s[11]-1990404162)<<22|e>>>10)+r<<0,e=((e+=((t=((t+=(n^e&(r^n))+s[12]+1804603682)<<7|t>>>25)+e<<0)^(r=((r+=(e^(n=((n+=(r^t&(e^r))+s[13]-40341101)<<12|n>>>20)+t<<0)&(t^e))+s[14]-1502002290)<<17|r>>>15)+n<<0)&(n^t))+s[15]+1236535329)<<22|e>>>10)+r<<0,e=((e+=((n=((n+=(e^r&((t=((t+=(r^n&(e^r))+s[1]-165796510)<<5|t>>>27)+e<<0)^e))+s[6]-1069501632)<<9|n>>>23)+t<<0)^t&((r=((r+=(t^e&(n^t))+s[11]+643717713)<<14|r>>>18)+n<<0)^n))+s[0]-373897302)<<20|e>>>12)+r<<0,e=((e+=((n=((n+=(e^r&((t=((t+=(r^n&(e^r))+s[5]-701558691)<<5|t>>>27)+e<<0)^e))+s[10]+38016083)<<9|n>>>23)+t<<0)^t&((r=((r+=(t^e&(n^t))+s[15]-660478335)<<14|r>>>18)+n<<0)^n))+s[4]-405537848)<<20|e>>>12)+r<<0,e=((e+=((n=((n+=(e^r&((t=((t+=(r^n&(e^r))+s[9]+568446438)<<5|t>>>27)+e<<0)^e))+s[14]-1019803690)<<9|n>>>23)+t<<0)^t&((r=((r+=(t^e&(n^t))+s[3]-187363961)<<14|r>>>18)+n<<0)^n))+s[8]+1163531501)<<20|e>>>12)+r<<0,e=((e+=((n=((n+=(e^r&((t=((t+=(r^n&(e^r))+s[13]-1444681467)<<5|t>>>27)+e<<0)^e))+s[2]-51403784)<<9|n>>>23)+t<<0)^t&((r=((r+=(t^e&(n^t))+s[7]+1735328473)<<14|r>>>18)+n<<0)^n))+s[12]-1926607734)<<20|e>>>12)+r<<0,e=((e+=((o=(n=((n+=((i=e^r)^(t=((t+=(i^n)+s[5]-378558)<<4|t>>>28)+e<<0))+s[8]-2022574463)<<11|n>>>21)+t<<0)^t)^(r=((r+=(o^e)+s[11]+1839030562)<<16|r>>>16)+n<<0))+s[14]-35309556)<<23|e>>>9)+r<<0,e=((e+=((o=(n=((n+=((i=e^r)^(t=((t+=(i^n)+s[1]-1530992060)<<4|t>>>28)+e<<0))+s[4]+1272893353)<<11|n>>>21)+t<<0)^t)^(r=((r+=(o^e)+s[7]-155497632)<<16|r>>>16)+n<<0))+s[10]-1094730640)<<23|e>>>9)+r<<0,e=((e+=((o=(n=((n+=((i=e^r)^(t=((t+=(i^n)+s[13]+681279174)<<4|t>>>28)+e<<0))+s[0]-358537222)<<11|n>>>21)+t<<0)^t)^(r=((r+=(o^e)+s[3]-722521979)<<16|r>>>16)+n<<0))+s[6]+76029189)<<23|e>>>9)+r<<0,e=((e+=((o=(n=((n+=((i=e^r)^(t=((t+=(i^n)+s[9]-640364487)<<4|t>>>28)+e<<0))+s[12]-421815835)<<11|n>>>21)+t<<0)^t)^(r=((r+=(o^e)+s[15]+530742520)<<16|r>>>16)+n<<0))+s[2]-995338651)<<23|e>>>9)+r<<0,e=((e+=((n=((n+=(e^((t=((t+=(r^(e|~n))+s[0]-198630844)<<6|t>>>26)+e<<0)|~r))+s[7]+1126891415)<<10|n>>>22)+t<<0)^((r=((r+=(t^(n|~e))+s[14]-1416354905)<<15|r>>>17)+n<<0)|~t))+s[5]-57434055)<<21|e>>>11)+r<<0,e=((e+=((n=((n+=(e^((t=((t+=(r^(e|~n))+s[12]+1700485571)<<6|t>>>26)+e<<0)|~r))+s[3]-1894986606)<<10|n>>>22)+t<<0)^((r=((r+=(t^(n|~e))+s[10]-1051523)<<15|r>>>17)+n<<0)|~t))+s[1]-2054922799)<<21|e>>>11)+r<<0,e=((e+=((n=((n+=(e^((t=((t+=(r^(e|~n))+s[8]+1873313359)<<6|t>>>26)+e<<0)|~r))+s[15]-30611744)<<10|n>>>22)+t<<0)^((r=((r+=(t^(n|~e))+s[6]-1560198380)<<15|r>>>17)+n<<0)|~t))+s[13]+1309151649)<<21|e>>>11)+r<<0,e=((e+=((n=((n+=(e^((t=((t+=(r^(e|~n))+s[4]-145523070)<<6|t>>>26)+e<<0)|~r))+s[11]-1120210379)<<10|n>>>22)+t<<0)^((r=((r+=(t^(n|~e))+s[2]+718787259)<<15|r>>>17)+n<<0)|~t))+s[9]-343485551)<<21|e>>>11)+r<<0,this.first?(this.h0=t+1732584193<<0,this.h1=e-271733879<<0,this.h2=r-1732584194<<0,this.h3=n+271733878<<0,this.first=!1):(this.h0=this.h0+t<<0,this.h1=this.h1+e<<0,this.h2=this.h2+r<<0,this.h3=this.h3+n<<0)},Md5.prototype.hex=function(){this.finalize();var t=this.h0,e=this.h1,r=this.h2,n=this.h3;return HEX_CHARS[t>>4&15]+HEX_CHARS[15&t]+HEX_CHARS[t>>12&15]+HEX_CHARS[t>>8&15]+HEX_CHARS[t>>20&15]+HEX_CHARS[t>>16&15]+HEX_CHARS[t>>28&15]+HEX_CHARS[t>>24&15]+HEX_CHARS[e>>4&15]+HEX_CHARS[15&e]+HEX_CHARS[e>>12&15]+HEX_CHARS[e>>8&15]+HEX_CHARS[e>>20&15]+HEX_CHARS[e>>16&15]+HEX_CHARS[e>>28&15]+HEX_CHARS[e>>24&15]+HEX_CHARS[r>>4&15]+HEX_CHARS[15&r]+HEX_CHARS[r>>12&15]+HEX_CHARS[r>>8&15]+HEX_CHARS[r>>20&15]+HEX_CHARS[r>>16&15]+HEX_CHARS[r>>28&15]+HEX_CHARS[r>>24&15]+HEX_CHARS[n>>4&15]+HEX_CHARS[15&n]+HEX_CHARS[n>>12&15]+HEX_CHARS[n>>8&15]+HEX_CHARS[n>>20&15]+HEX_CHARS[n>>16&15]+HEX_CHARS[n>>28&15]+HEX_CHARS[n>>24&15]},Md5.prototype.toString=Md5.prototype.hex,Md5.prototype.digest=function(){this.finalize();var t=this.h0,e=this.h1,r=this.h2,n=this.h3;return[255&t,t>>8&255,t>>16&255,t>>24&255,255&e,e>>8&255,e>>16&255,e>>24&255,255&r,r>>8&255,r>>16&255,r>>24&255,255&n,n>>8&255,n>>16&255,n>>24&255]},Md5.prototype.array=Md5.prototype.digest,Md5.prototype.arrayBuffer=function(){this.finalize();var t=new ArrayBuffer(16),e=new Uint32Array(t);return e[0]=this.h0,e[1]=this.h1,e[2]=this.h2,e[3]=this.h3,t},Md5.prototype.buffer=Md5.prototype.arrayBuffer,Md5.prototype.base64=function(){for(var t,e,r,n="",i=this.array(),o=0;o<15;)t=i[o++],e=i[o++],r=i[o++],n+=BASE64_ENCODE_CHAR[t>>>2]+BASE64_ENCODE_CHAR[63&(t<<4|e>>>4)]+BASE64_ENCODE_CHAR[63&(e<<2|r>>>6)]+BASE64_ENCODE_CHAR[63&r];return t=i[o],n+=BASE64_ENCODE_CHAR[t>>>2]+BASE64_ENCODE_CHAR[t<<4&63]+"=="};var exports=createMethod();COMMON_JS?module.exports=exports:root.md5=exports})()})(md5$2);var md5=md5$2.exports,md5$1=_mergeNamespaces({__proto__:null,default:md5},[md5$2.exports]),shim$1=md5$1?md5||globalThis.md5:{},uuid={mask:"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx",generate:function(){var t=Date.now(),e=this.mask.replace(/[xy]/g,(function(e){var r=(t+16*Math.random())%16|0;return t=Math.floor(t/16),("x"==e?r:3&r|8).toString(16)}));return e},validate:function(t){return"string"==typeof t&&!!t.match(/^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$/)},format:function(t){var e=[],r=0;return this.mask.split("-").forEach((function(n){e.push(t.substring(r,r+n.length)),r+=n.length})),e.join("-")},fromString:function(t){return this.fromBytes(new Uint8Array(shim$1.arrayBuffer(t)))},toBytes:function(t){var e=[];return t.split("-").map((function(t,r){(r<3?t.match(/.{1,2}/g).reverse():t.match(/.{1,2}/g)).map((function(t){return e.push(parseInt(t,16))}))})),new Uint8Array(e)},fromBytes:function(t){var e=Array.from(t).map((function(t){return t.toString(16)})).map((function(t){return(1==t.length?"0":"")+t}));return e.slice(0,4).reverse().join("")+"-"+e.slice(4,6).reverse().join("")+"-"+e.slice(6,8).reverse().join("")+"-"+e.slice(8,10).join("")+"-"+e.slice(10).join("")},toUint32Array:function(t){var e=new Uint32Array(4),r=this.toBytes(t);return e[0]=new Uint32Array(r.slice(0,4).buffer)[0],e[1]=new Uint32Array(r.slice(4,8).buffer)[0],e[2]=new Uint32Array(r.slice(8,12).buffer)[0],e[3]=new Uint32Array(r.slice(12).buffer)[0],e},fromUint32Array:function(t){return this.fromBytes(new Uint8Array(t.buffer))},toUint64Array:function(t){var e=new BigUint64Array(2),r=this.toBytes(t);return e[0]=new BigUint64Array(r.slice(0,8).buffer)[0],e[1]=new BigUint64Array(r.slice(8).buffer)[0],e},fromUint64Array:function(t){return this.fromBytes(new Uint8Array(t.buffer))},toLongArray:function(t){var e=new Array(2),r=this.toBytes(t);return e[0]=Long.fromBytes(r.slice(0,8)),e[1]=Long.fromBytes(r.slice(8)),e},fromLongArray:function(t){var e=t[0].toBytes().concat(t[1].toBytes());return this.fromBytes(e)}};function _classPrivateFieldInitSpec$a(t,e,r){_checkPrivateRedeclaration$a(t,e),e.set(t,r)}function _checkPrivateRedeclaration$a(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}var _id$2=new WeakMap,SplineFragment=function(){function t(e,r,n,i,o){var s=this;_classCallCheck$1(this,t),_classPrivateFieldInitSpec$a(this,_id$2,{writable:!0,value:void 0}),1==n&&(r++,n=0),0==o&&(i--,o=1),Object.defineProperties(this,{spline:{value:e,enumerable:!0},segmentIndexStart:{value:r,enumerable:!0},segmentIndexEnd:{value:i,enumerable:!0},ts:{value:n,enumerable:!0},tf:{value:o,enumerable:!0}}),Object.defineProperty(this,"pointIndexStart",{value:r,enumerable:!0}),Object.defineProperty(this,"pointIndexEnd",{value:i+3,enumerable:!0}),Object.defineProperty(this,"length",{value:this.pointIndexEnd-this.pointIndexStart+1,enumerable:!0}),Object.defineProperty(this,"id",{get:function(){return _classPrivateFieldGet(s,_id$2)||_classPrivateFieldSet(s,_id$2,uuid.generate()),_classPrivateFieldGet(s,_id$2)},set:function(t){if(_classPrivateFieldGet(s,_id$2))throw new Error("id is immutable");_classPrivateFieldSet(s,_id$2,t)}}),this.validate()}return _createClass$1(t,[{key:"validate",value:function(){var t=this.length-3;if(this.pointIndexStart<0)throw new Error("Invalid fragment pointIndexStart ".concat(this.pointIndexStart," found. The value must be non-negative."));if(this.pointIndexEnd>this.spline.length-1)throw new Error("Invalid fragment pointIndexEnd ".concat(this.pointIndexEnd," found. Last point in spline index is ").concat(this.spline.length-1,"."));if(this.ts<0||this.ts>=1)throw new Error("Invalid fragment ts ".concat(this.ts," found. The value must be in the interval [0, 1)."));if(this.tf<=0||this.tf>1)throw new Error("Invalid fragment tf ".concat(this.tf," found. The value must be in the interval (0, 1]."));if(t<1)throw new Error("Invalid fragment points range {".concat(this.pointIndexStart,", ").concat(this.pointIndexEnd,"} found. At least 4 points are needed to define spline."));if(1==t&&this.ts>this.tf)throw new Error("Invalid fragment T range ".concat(this.ts," - ").concat(this.tf," found. Spline has only one segment and ts <= tf."))}},{key:"union",value:function(e){var r=[this,e];r.sort(t.compare);var n=r.first,i=r.last,o=n.segmentIndexEnd,s=n.tf;if(1==s&&(o++,s=0),o!=i.segmentIndexStart||s!=i.ts)throw new Error("Fragments ".concat(n," and ").concat(i," are not adjacent."));var a=new t(this.spline,n.segmentIndexStart,n.ts,i.segmentIndexEnd,i.tf);return _classPrivateFieldSet(a,_id$2,_classPrivateFieldGet(this,_id$2)),this.inside&&(a.inside=this.inside),a}},{key:"overlaps",value:function(t){if(t.spline!=this.spline)return!1;var e=t.segmentIndexStart<this.segmentIndexEnd||t.segmentIndexStart==this.segmentIndexEnd&&t.ts<=this.tf,r=t.segmentIndexEnd>this.segmentIndexStart||t.segmentIndexEnd==this.segmentIndexStart&&t.tf>=this.ts;return e&&r}},{key:"toString",value:function(){return"fragment(".concat(this.segmentIndexStart,", ").concat(this.ts," - ").concat(this.segmentIndexEnd,", ").concat(this.tf,")")}}],[{key:"compare",value:function(t,e){return t.segmentIndexStart<e.segmentIndexStart?-1:t.segmentIndexStart>e.segmentIndexStart?1:t.ts<e.ts?-1:t.ts>e.ts?1:0}},{key:"getInstance",value:function(e,r,n){return new t(e,r.segmentIndex,r.t,n.segmentIndex,n.t)}}]),t}();function _createSuper$o(t){var e=_isNativeReflectConstruct$o();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$o(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}var InterpolatedSpline=function(t){_inherits$1(r,t);var e=_createSuper$o(r);function r(t,n,i){var o,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];_classCallCheck$1(this,r),o=e.call(this,n,i,t),s.length>0&&!Object.isFrozen(s.first)&&s.forEach((function(t){return Object.freeze(t)})),Object.defineProperty(_assertThisInitialized$1(o),"splineParameters",{value:Object.freeze(s),enumerable:!0});var a=new InkStyle(t,i);return Object.defineProperty(_assertThisInitialized$1(o),"style",{get:function(){return a.style},set:function(t){return a.style.reset(t)},enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(o),"color",{get:function(){return o.style.color},set:function(t){if(!t)throw new Error("Spline color cannot be removed");if(!(t instanceof Color))throw new Error("Expected value should be Color instance");"red"in o.pointProps&&(o.pointProps.red=t.red),"green"in o.pointProps&&(o.pointProps.green=t.green),"blue"in o.pointProps&&(o.pointProps.blue=t.blue),"alpha"in o.pointProps&&(o.pointProps.alpha=t.alpha)},enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(o),"bounds",{get:function(){return Rect.ofSpline(_assertThisInitialized$1(o),o.pointProps.scattering).ceil()},enumerable:!0}),o.validate(),o}return _createClass$1(r,[{key:"validate",value:function(){var t=this.points instanceof Float32Array;if(_get(_getPrototypeOf$1(r.prototype),"validate",this).call(this),!this.layout.includes(PathPoint.Property.X))throw new Error("Layout doesn't contains required properties X");if(!this.layout.includes(PathPoint.Property.Y))throw new Error("Layout doesn't contains required properties Y");if(t&&0==this.points.length)throw new Error("Empty spline is not allowed")}},{key:"clone",value:function(){return new r(this.layout.slice(),this.points.clone(),Object.clone(this.pointProps),this.splineParameters.slice())}},{key:"slice",value:function(t){var e=this.slicePoints(t.pointIndexStart,t.pointIndexEnd);return new r(this.layout.slice(),e,Object.clone(this.pointProps),this.splineParameters.slice())}},{key:"getPoint",value:function(t){return _get(_getPrototypeOf$1(r.prototype),"getPoint",this).call(this,t,this.style)}},{key:"getPointRef",value:function(t){return _get(_getPrototypeOf$1(r.prototype),"getPointRef",this).call(this,t,this.style)}},{key:"getPointSegmentIndex",value:function(t){return this.splineParameters[t]?this.splineParameters[t].index:void 0}},{key:"getPointT",value:function(t){return this.splineParameters[t]?this.splineParameters[t].t:void 0}},{key:"getPointParameter",value:function(t){return this.splineParameters[t]}},{key:"toJSON",value:function(){return{type:"InterpolatedSpline",layout:this.layout.map((function(t){return t.name})),points:TypedArrayCodec.encode(this.points,this.encoding),pointProps:this.pointProps,splineParameters:this.splineParameters}}}],[{key:"fromJSON",value:function(t){var e=TypedArrayCodec.decode(t.points);return new r(t.layout.map((function(t){return PathPoint.Property[t]})),e,t.pointProps,t.splineParameters.map(SplineParameter.fromJSON))}},{key:"fromRect",value:function(t,e){throw new Error("InterpolatedSpline.fromRect is not supported. Try Spline.fromRect and interpolate with particular Spline interpolator.")}},{key:"createInstance",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return new r(t,n,e)}},{key:"createSharedInstance",value:function(t,e,n,i){return new r(t,Float32Array.createSharedInstance(e),n,i)}}]),r}(Path);function _createSuper$n(t){var e=_isNativeReflectConstruct$n();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$n(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function _classPrivateFieldInitSpec$9(t,e,r){_checkPrivateRedeclaration$9(t,e),e.set(t,r)}function _checkPrivateRedeclaration$9(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}var CATMULL_ROM_POLYNOMIAL_COEFFICENTS_MATRIX=fromValues$4(0,-.5,1,-.5,1,0,-2.5,1.5,0,.5,2,-1.5,0,0,-.5,.5),_addition$3=new WeakMap,_prediction$3=new WeakMap,_processor$2=new WeakMap,SplineInterpolator=function(t){_inherits$1(r,t);var e=_createSuper$n(r);function r(){var t,n=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return _classCallCheck$1(this,r),_classPrivateFieldInitSpec$9(_assertThisInitialized$1(t=e.call(this)),_addition$3,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$9(_assertThisInitialized$1(t),_prediction$3,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$9(_assertThisInitialized$1(t),_processor$2,{writable:!0,value:void 0}),t.calculateDerivates=n,t.keepSplineParameters=i,t.state={segmentIndex:-1,lastPointPosition:void 0,lastPointSize:0},t}return _createClass$1(r,[{key:"initState",value:function(t){var e=this;this.state.layout={},t.layout.forEach((function(t,r){e.state.layout[t.name]={index:r,polynomials:create$1()}})),this.keepSplineParameters?this.state.splineParameters?this.state.splineParameters.clear():this.state.splineParameters=[]:delete this.state.splineParameters,this.splineLayout=t.layout,this.pathPointProps=Object.clone(t.pointProps),this.scattering&&(this.pathPointProps.scattering=this.scattering),this.layout=this.calculateDerivates?t.layout.concat([PathPoint.Property.D_X,PathPoint.Property.D_Y]):t.layout,this.state.ready=!0}},{key:"predict",value:function(t){if(!t)return[];this.state.ready||this.initState(t),_classPrivateFieldGet(this,_prediction$3)?_classPrivateFieldGet(this,_prediction$3).points.clear():_classPrivateFieldSet(this,_prediction$3,InterpolatedSpline.createInstance(this.layout,this.pathPointProps)),this.path=_classPrivateFieldGet(this,_prediction$3);var e=Object.clone(this.state);return delete this.state.splineParameters,this.resetState(),this.discretize(t),this.state=e,this.path.points}},{key:"processImpl",value:function(t,e){return t?(t instanceof SplineFragment&&(t=(n=t).spline),this.state.ready||this.initState(t),e==r.OutputType.ADDITION?(_classPrivateFieldGet(this,_addition$3)?_classPrivateFieldGet(this,_addition$3).points.clear():_classPrivateFieldSet(this,_addition$3,InterpolatedSpline.createInstance(this.layout,this.pathPointProps)),this.path=_classPrivateFieldGet(this,_addition$3)):(_classPrivateFieldSet(this,_processor$2,InterpolatedSpline.createInstance(this.layout,this.pathPointProps)),this.path=_classPrivateFieldGet(this,_processor$2)),this.discretize(t,n),this.path.points):[];var n}},{key:"getOutput",value:function(t,e){if(0!=t.length){if(e==r.OutputType.PROCESSOR||e==r.OutputType.ALL_DATA){var n=this.state.splineParameters;return n&&(n=n.slice()),InterpolatedSpline.createSharedInstance(this.layout,t,this.pathPointProps,n)}return this.path}}},{key:"calculateInterpolatedPoint",value:function(t,e,r){var n=this;this.initState(t);var i=new PathPoint(0,0,this.splineLayout.includes(PathPoint.Property.Z)?0:void 0),o=fromValues$2(1,r,r*r,r*r*r);return this.calculatePolynomials(t,e),this.splineLayout.forEach((function(t){var e=dot$1(n.state.layout[t.name].polynomials,o);i[utils.getPropName(t.name)]=e})),i}},{key:"discretize",value:function(t,e){throw new Error("This method is abstract and should be implemented")}},{key:"storeLastPoint",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.state.lastPointPosition=new Point$3(this.getPropValue(PathPoint.Property.X,t,e),this.getPropValue(PathPoint.Property.Y,t,e),this.getPropValue(PathPoint.Property.Z,t,e)),this.state.lastPointSize=this.getPropValue(PathPoint.Property.SIZE,t,e)}},{key:"getPropValue",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return this.state.layout[t.name]?e[r+this.state.layout[t.name].index]:void 0}},{key:"calculatePolynomials",value:function(t,e){var r=this,n=t.points,i=this.splineLayout.length*(e+0),o=this.splineLayout.length*(e+1),s=this.splineLayout.length*(e+2),a=this.splineLayout.length*(e+3);this.splineLayout.forEach((function(t,e){var l=fromValues$2(n[i+e],n[o+e],n[s+e],n[a+e]);transformMat4$1(r.state.layout[t.name].polynomials,l,CATMULL_ROM_POLYNOMIAL_COEFFICENTS_MATRIX)}))}},{key:"samplePoint",value:function(t){var e=this,r=[],n=fromValues$2(1,t,t*t,t*t*t);return this.splineLayout.forEach((function(t){var i=dot$1(e.state.layout[t.name].polynomials,n);r.push(i)})),this.calculateDerivates&&(r.push(this.getDerivativeOf(this.state.layout.X.polynomials,n)),r.push(this.getDerivativeOf(this.state.layout.Y.polynomials,n))),r}},{key:"getDerivativeOf",value:function(t,e){return dot$1(fromValues$2(t[1],2*t[2],3*t[3],0),e)}},{key:"keepSegmentT",value:function(t){this.state.splineParameters&&this.state.splineParameters.push(new SplineParameter(this.state.segmentIndex,t))}},{key:"resetState",value:function(){this.state.segmentIndex=-1}},{key:"reset",value:function(){_get(_getPrototypeOf$1(r.prototype),"reset",this).call(this),this.state.ready=!1,this.state.lastPointPosition=void 0,this.state.lastPointSize=0,this.resetState(),_classPrivateFieldSet(this,_addition$3,null),_classPrivateFieldSet(this,_prediction$3,null),_classPrivateFieldSet(this,_processor$2,null)}}]),r}(DataSequenceProcessor);function _createSuper$m(t){var e=_isNativeReflectConstruct$m();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$m(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}var DEFAULT_ERROR_THRESHOLD=.15,DEFAULT_ROTATION_ERROR=.06,CurvatureBasedInterpolator=function(t){_inherits$1(r,t);var e=_createSuper$m(r);function r(t,n){var i;return _classCallCheck$1(this,r),(i=e.call(this,t,n)).state.lastSegmentIndex=-1,i.state.lastPointRotation=0,i.state.lastPointT=0,i.state.absAccumulatedErrorPos=0,i.state.absAccumulatedErrorS=0,i.setT=new SortedSet,i.samples=[],Object.defineProperty(_assertThisInitialized$1(i),"errorThreshold",{get:function(){return i.error},set:function(t){i.error=t,i.errorDistSq=i.error*i.error,i.error10=10*i.error},enumerable:!0}),i.errorThreshold=DEFAULT_ERROR_THRESHOLD,i}return _createClass$1(r,[{key:"discretize",value:function(t,e){var r=this.path.points,n=0,i=t.segmentsCount-1,o=t.ts,s=t.tf;e&&(n=e.segmentIndexStart,i=e.segmentIndexEnd,o=e.ts,s=e.tf,this.state.segmentIndex=n-1);for(var a=n;a<i+1;a++){this.state.segmentIndex++,this.calculatePolynomials(t,a);var l=this.calculateTValues(a==i,o,s);r.push.apply(r,_toConsumableArray(this.samplePoints(l))),l.length>0&&(this.resetAccumulatedErrors(),this.storeLastPoint(r))}}},{key:"samplePoints",value:function(t){var e=this;return this.samples.clear(),t.toArray().forEach((function(t){var r;e.keepSegmentT(t),(r=e.samples).push.apply(r,_toConsumableArray(e.samplePoint(t)))})),this.samples}},{key:"storeLastPoint",value:function(t){var e=t.length-this.layout.length;_get(_getPrototypeOf$1(r.prototype),"storeLastPoint",this).call(this,t,e),this.state.lastPointRotation=this.getPropValue(PathPoint.Property.ROTATION,t,e),this.state.lastPointT=this.setT.max(),this.state.lastSegmentIndex=this.state.segmentIndex}},{key:"calculateTValues",value:function(t,e,r){var n=0==this.state.segmentIndex?e:0,i=t?r:1;return this.setT.clear(),this.getTForPos(n,i),this.state.layout.SIZE&&this.getTForCubic(n,i,this.state.layout.SIZE.polynomials,this.error),this.mustAddStartT()&&this.setT.add(n),t&&this.setT.add(i),this.state.layout.ROTATION&&this.getTForRotation(n,i),this.setT}},{key:"mustAddStartT",value:function(){if(this.state.lastSegmentIndex<0)return!0;var t=this.state.lastPointT-(this.state.segmentIndex-this.state.lastSegmentIndex),e=this.setT.length>0?this.setT.min():1,r=this.getPosErrorAtT0(e,this.state.lastPointPosition);if(this.state.absAccumulatedErrorPos+=Math.abs(r),this.state.layout.SIZE){var n=this.getErrorAtT0(this.state.layout.SIZE.polynomials,e,t,this.state.lastPointSize);this.state.absAccumulatedErrorS+=Math.abs(n)}return this.state.absAccumulatedErrorPos>this.errorDistSq||this.state.absAccumulatedErrorS>this.error}},{key:"getPosErrorAtT0",value:function(t,e){var r=this.getTPoint(t),n=this.getTPoint(0);return this.minDistanceSq(e,r,n)}},{key:"getErrorAtT0",value:function(t,e,r,n){var i=r,o=n,s=e,a=this.cubicCalc(t,s),l=this.cubicCalc(t,0),u=o+(0-i)*(a-o)/(s-i);return Math.abs(l-u)}},{key:"getTForPos",value:function(t,e){var r=this.getTPoint(t),n=this.getTPoint(e),i=this.subdividePos(r,n);if(i.split)this.subdivideRecursivePos(r,i),this.setT.add(i.t),this.subdivideRecursivePos(i,n);else{var o=this.subdividePos(r,i),s=this.subdividePos(i,n);o.split&&(this.subdivideRecursivePos(r,o),this.setT.add(o.t),this.subdivideRecursivePos(o,i)),(o.split||s.split)&&this.setT.add(i.t),s.split&&(this.subdivideRecursivePos(i,s),this.setT.add(s.t),this.subdivideRecursivePos(s,n))}}},{key:"subdivideRecursivePos",value:function(t,e){var r=this.subdividePos(t,e);r.split&&(this.subdivideRecursivePos(t,r),this.setT.add(r.t),this.subdivideRecursivePos(r,e))}},{key:"subdividePos",value:function(t,e){var r=.5*(t.t+e.t),n=this.getTPoint(r),i=this.minDistanceSq(t,e,n),o=t.add(e).scaleSelf(.5),s=n.subtract(o).absSelf();return n.split=i>this.errorDistSq||s.x>this.error10||s.y>this.error10,this.state.layout.Z&&(n.split=n.split||s.z>this.error10),n}},{key:"getTForCubic",value:function(t,e,r,n){var i={v:this.cubicCalc(r,t),t:t},o={v:this.cubicCalc(r,e),t:e},s=this.subdivide(i,o,r);if(s.diff>n)this.subdivideRecursive(i,s,r,n),this.setT.add(s.t),this.subdivideRecursive(s,o,r,n);else{var a=this.subdivide(i,s,r),l=this.subdivide(s,o,r);a.diff>n&&(this.subdivideRecursive(i,a,r,n),this.setT.add(a.t),this.subdivideRecursive(a,s,r,n)),(a.diff>n||l.diff>n)&&this.setT.add(s.t),l.diff>n&&(this.subdivideRecursive(s,l,r,n),this.setT.add(l.t),this.subdivideRecursive(l,o,r,n))}}},{key:"subdivideRecursive",value:function(t,e,r,n){var i=this.subdivide(t,e,r);i.diff>n&&(this.subdivideRecursive(t,i,r,n),this.setT.add(i.t),this.subdivideRecursive(i,e,r,n))}},{key:"subdivide",value:function(t,e,r){var n=.5*(t.t+e.t),i=this.cubicCalc(r,n),o=.5*(t.v+e.v);return{v:i,t:n,diff:Math.abs(i-o)}}},{key:"getTForRotation",value:function(t,e){var r=this.state.layout.ROTATION.polynomials,n=this.state.lastPointRotation;this.state.lastSegmentIndex<0&&(n=this.cubicCalc(r,t));for(var i=.25*(e-t),o=0;o<4;o++){var s=t+o*i,a=this.cubicCalc(r,s);Math.abs(a-n)>DEFAULT_ROTATION_ERROR&&(this.setT.add(s),n=a)}}},{key:"minDistanceSq",value:function(t,e,r){var n=r.vec,i=n.squaredDistance(t.value,e.value);if(0==i)return n.squaredDistance(r.value,t.value);var o=Math.max(0,Math.min(1,n.dot(r.subtract(t).value,e.subtract(t).value)/i)),s=e.subtract(t).scale(o).add(t);return n.squaredDistance(r.value,s.value)}},{key:"getTPoint",value:function(t){var e=new Point$3(this.cubicCalc(this.state.layout.X.polynomials,t),this.cubicCalc(this.state.layout.Y.polynomials,t),this.state.layout.Z?this.cubicCalc(this.state.layout.Z.polynomials,t):void 0);return e.t=t,e}},{key:"cubicCalc",value:function(t,e){return t[0]+t[1]*e+t[2]*e*e+t[3]*e*e*e}},{key:"resetAccumulatedErrors",value:function(){this.state.absAccumulatedErrorPos=0,this.state.absAccumulatedErrorS=0}},{key:"resetState",value:function(){_get(_getPrototypeOf$1(r.prototype),"resetState",this).call(this),this.state.lastSegmentIndex=-1,this.state.lastPointT=0,this.state.lastPointRotation=0,this.resetAccumulatedErrors()}}]),r}(SplineInterpolator),Manipulator=function(){function t(){var e,r=arguments.length>0&&void 0!==arguments[0]&&arguments[0];_classCallCheck$1(this,t),this.splitStrokes=r,Object.defineProperty(this,"context",{get:function(){if(!e)throw new Error("Spatial context not found. Set value first.");return e},set:function(t){if(!t)throw new Error("Spatial context value is required");e=t},enumerable:!0}),this.splineInterpolator=new CurvatureBasedInterpolator}return _createClass$1(t,[{key:"processStrokePart",value:function(t,e,r,n){var i=t.splineParameters,o=t.shapesPath;1==o.length&&o.push(o[0]);for(var s=i[0],a=o[0],l=a.bounds,u=1;u<o.length;u++){var h=o[u].bounds;if(h.union(l).intersects(n))o[u].union(a).intersects(e)&&(this.splitStrokes?this.split(t,e,r,a,o[u],s,i[u]):this.selected.add(t.strokeID));s=i[u],a=o[u],l=h}}},{key:"split",value:function(t,e,r,n,i,o,s){var a,l,u=this.context.getStroke(t.strokeID),h=u.spline,c=h.segmentsCount-1,p=n.intersects(e),f=i.intersects(e);if(p&&f)0==o.segmentIndex&&o.t==h.ts&&(a=o),s.segmentIndex==c&&s.t==h.tf&&(l=s);else if(p)0==o.segmentIndex&&o.t==h.ts&&(a=o),l=this.seekNonIntersectingPartFromEnd(u,e,i,o,s);else if(f)a=this.seekNonIntersectingPartFromStart(u,e,n,o,s),s.segmentIndex==c&&s.t==h.tf&&(l=s);else{var d=this.seekNonIntersectingPartFromStart(u,e,n,o,s),y=this.seekNonIntersectingPartFromEnd(u,e,i,d,s);(d.segmentIndex==y.segmentIndex&&d.t<y.t||d.segmentIndex<y.segmentIndex)&&(a=d,l=y)}if(a||l){var v=this.splitPoints[u.id];v||(v=[],this.splitPoints[u.id]=v),a&&v.push(new SplineSplitPoint(a,r,!1)),l&&v.push(new SplineSplitPoint(l,r,!0))}}},{key:"seekNonIntersectingPartFromStart",value:function(t,e,r,n,i){for(var o=n,s=i,a=n,l=this.context.getBrushApplier(t.id);SplineParameter.areDistantEnough(o,s,this.splineParameterDistanceThreshold);){var u=SplineParameter.calcMiddleOfSegment(o,s),h=this.splineInterpolator.calculateInterpolatedPoint(t.spline,u.segmentIndex,u.t);l.applyBrush(h).union(r).intersects(e)?s=u:(o=u,a=u)}return a}},{key:"seekNonIntersectingPartFromEnd",value:function(t,e,r,n,i){for(var o=n,s=i,a=i,l=this.context.getBrushApplier(t.id);SplineParameter.areDistantEnough(o,s,this.splineParameterDistanceThreshold);){var u=SplineParameter.calcMiddleOfSegment(o,s),h=this.splineInterpolator.calculateInterpolatedPoint(t.spline,u.segmentIndex,u.t);l.applyBrush(h).union(r).intersects(e)?o=u:(s=u,a=u)}return a}},{key:"reset",value:function(t){t&&(this.context=t),this.fragments={},this.selected=new Set,this.splitStrokes&&(this.splitPoints={}),this.context.tree.canvas&&this.context.tree.canvas.refresh()}}]),t}();function _createSuper$l(t){var e=_isNativeReflectConstruct$l();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$l(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}var Spline=function(t){_inherits$1(r,t);var e=_createSuper$l(r);function r(t,n,i){var o,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1;return _classCallCheck$1(this,r),(o=e.call(this,n,i,t)).points instanceof Float32Array?Object.defineProperty(_assertThisInitialized$1(o),"segmentsCount",{value:o.length-3,configurable:!0}):Object.defineProperty(_assertThisInitialized$1(o),"segmentsCount",{get:function(){return o.length-3},configurable:!0}),o.ts=s,o.tf=a,Object.defineProperty(_assertThisInitialized$1(o),"bounds",{get:function(){return Rect.ofSpline(_assertThisInitialized$1(o),o.pointProps.scattering).ceil()},enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(o),"color",{get:function(){return Color.isColor(o.pointProps)?Color.fromColor(o.pointProps):void 0},set:function(t){if(!t)throw new Error("Spline color cannot be removed");if(!(t instanceof Color))throw new Error("Expected value should be Color instance");"red"in o.pointProps&&(o.pointProps.red=t.red),"green"in o.pointProps&&(o.pointProps.green=t.green),"blue"in o.pointProps&&(o.pointProps.blue=t.blue),"alpha"in o.pointProps&&(o.pointProps.alpha=t.alpha)},enumerable:!0}),o.validate(),o}return _createClass$1(r,[{key:"validate",value:function(){var t=this.points instanceof Float32Array;if(_get(_getPrototypeOf$1(r.prototype),"validate",this).call(this),!this.layout.includes(PathPoint.Property.X))throw new Error("Layout doesn't contains required properties X");if(!this.layout.includes(PathPoint.Property.Y))throw new Error("Layout doesn't contains required properties Y");if(t&&0==this.points.length)throw new Error("Empty spline is not allowed");if(this.ts<0||this.ts>=1)throw new Error("Invalid spline ts ".concat(this.ts," found. The value must be in the interval [0, 1)."));if(this.tf<=0||this.tf>1)throw new Error("Invalid spline tf ".concat(this.tf," found. The value must be in the interval (0, 1]."));if(1==this.segmentsCount&&this.ts>this.tf)throw new Error("Invalid spline t range ".concat(this.ts," - ").concat(this.tf," found. Spline has only one segment and ts <= tf."));if(t&&this.segmentsCount<1)throw new Error("Incompleted spline found. Spline is defined with at least 4 control points.")}},{key:"clone",value:function(){return new r(this.layout.slice(),this.points.clone(),Object.clone(this.pointProps),this.ts,this.tf)}},{key:"slice",value:function(t){var e=this.slicePoints(t.pointIndexStart,t.pointIndexEnd),n=new r(this.layout.slice(),e,Object.clone(this.pointProps),t.ts,t.tf);return n.id=t.id,n}},{key:"getSegment",value:function(t){var e=t,r=t+3,n=0==e?this.ts:0,i=r+1==this.length?this.tf:1;return this.slice({pointIndexStart:e,pointIndexEnd:r,ts:n,tf:i})}},{key:"getFragment",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.ts,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.segmentsCount-1,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:this.tf,i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o=t,s=i?r-3:r;return new SplineFragment(this,o,e,s,n)}},{key:"toPlainPath",value:function(){for(var t=new Path([]),e=0;e<this.length;e++)t.points.push(this.getPointX(),this.getPointY());return t}},{key:"toJSON",value:function(){return{type:"Spline",id:this.id,layout:this.layout.map((function(t){return t.name})),points:TypedArrayCodec.encode(this.points,this.encoding),pointProps:this.pointProps,ts:this.ts,tf:this.tf}}}],[{key:"fromJSON",value:function(t){var e=TypedArrayCodec.decode(t.points),n=new r(t.layout.map((function(t){return PathPoint.Property[t]})),e,t.pointProps,t.ts,t.tf);return n.id=t.id,n}},{key:"fromRect",value:function(t,e){return new r(void 0,[t.left,t.top,t.left,t.top,t.right,t.top,t.right,t.bottom,t.left,t.bottom,t.left,t.top,t.left,t.top],e)}},{key:"createInstance",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],i=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;return new r(t,n,e,i,o)}},{key:"createSharedInstance",value:function(t,e,n,i,o){return new r(t,Float32Array.createSharedInstance(e),n,i,o)}}]),r}(Path),DataProcessor=function(){function t(){_classCallCheck$1(this,t),this.phase=t.Phase.END}return _createClass$1(t,[{key:"add",value:function(t,e,r){if(!this.move(t))throw new Error("Cannot move from phase ".concat(this.phase.name," to phase ").concat(t.name));return this.debug&&(console.log("-------------------------------------"),console.log(this.constructor.name,t.name)),this.addImpl(e,r)}},{key:"addImpl",value:function(t,e){throw new Error("Abstract method addImpl of DataProcessor should be implemented")}},{key:"move",value:function(e){return(this.phase!=t.Phase.END||e==t.Phase.BEGIN)&&((this.phase!=t.Phase.BEGIN||e==t.Phase.UPDATE||e==t.Phase.END)&&((this.phase!=t.Phase.UPDATE||e==t.Phase.UPDATE||e==t.Phase.END)&&(e==t.Phase.BEGIN&&this.reset(),this.phase=e,!0)))}},{key:"reset",value:function(){this.phase=t.Phase.END}}]),t}();function _createSuper$k(t){var e=_isNativeReflectConstruct$k();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$k(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}Object.defineEnum(DataProcessor,"Phase",["BEGIN","UPDATE","END"]);var PathProducer=function(t){_inherits$1(r,t);var e=_createSuper$k(r);function r(t,n){var i;return _classCallCheck$1(this,r),(i=e.call(this)).layout=t,i.pathPointCalculator=n,i.inputBuffer=[],i.prediction=!0,i}return _createClass$1(r,[{key:"togglePrediction",value:function(t){console.warn("PathProducer togglePrediction method is deprecated. Use InkBuilder instance prediction property to configure prediction behaviour."),this.prediction=t}},{key:"addImpl",value:function(t,e){if(t.phase!=this.phase)throw new Error("The phase of the addition (".concat(t.phase.name,") doesn't match the phase of the PathProducer (").concat(this.phase.name,")"));var r=[],n=[];t&&this.inputBuffer.push(t);var i=this.inputBuffer.length>=3?this.inputBuffer[this.inputBuffer.length-3]:null,o=this.inputBuffer.length>=2?this.inputBuffer[this.inputBuffer.length-2]:null,s=this.inputBuffer.length>=1?this.inputBuffer[this.inputBuffer.length-1]:null,a=this.calculate(i,o,s);return t&&a&&r.push.apply(r,_toConsumableArray(a.toArray(this.layout))),this.phase==DataProcessor.Phase.END?(a=this.calculate(o,s,null))&&r.push.apply(r,_toConsumableArray(a.toArray(this.layout))):this.prediction&&(this.phase==DataProcessor.Phase.UPDATE||e)&&(a=this.calculate(o,s,e))&&(n.push.apply(n,_toConsumableArray(a.toArray(this.layout))),(a=this.calculate(s,e,null))&&n.push.apply(n,_toConsumableArray(a.toArray(this.layout)))),{added:r,predicted:n}}},{key:"calculate",value:function(t,e,r){return e?this.pathPointCalculator(t,e,r):null}},{key:"reset",value:function(){_get(_getPrototypeOf$1(r.prototype),"reset",this).call(this),this.inputBuffer.clear()}}]),r}(DataProcessor);function _createSuper$j(t){var e=_isNativeReflectConstruct$j();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$j(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function _classPrivateFieldInitSpec$8(t,e,r){_checkPrivateRedeclaration$8(t,e),e.set(t,r)}function _checkPrivateRedeclaration$8(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}var DEFAULT_PREDICTION_POINTS_COUNT=4,DEFAULT_MOVING_AVERAGE_WINDOW_SIZE=15,DEFAULT_FILTER=[-6e-6,-139e-6,-185e-6,414e-6,.002357,.003357,-.003135,-.023928,-.042909,-.017858,.096525,.254692,.347072,.26881,.114933],_buffer=new WeakMap,_pointsCount=new WeakMap,Smoother=function(t){_inherits$1(r,t);var e=_createSuper$j(r);function r(t){var n,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:15;return _classCallCheck$1(this,r),_classPrivateFieldInitSpec$8(_assertThisInitialized$1(n=e.call(this)),_buffer,{writable:!0,value:[]}),_classPrivateFieldInitSpec$8(_assertThisInitialized$1(n),_pointsCount,{writable:!0,value:0}),n.filter=DEFAULT_FILTER.slice(),Object.defineProperty(_assertThisInitialized$1(n),"movingAverageWindowSize",{get:function(){return i},set:function(t){i=t,this.filter.length!=t&&(this.filter=r.resample(DEFAULT_FILTER,t)),this.predictionPointsCount=DEFAULT_PREDICTION_POINTS_COUNT*t/DEFAULT_MOVING_AVERAGE_WINDOW_SIZE,this.windowSize=this.filter.length},enumerable:!0}),n.dimsCount=t,n.movingAverageWindowSize=i,n}return _createClass$1(r,[{key:"add",value:function(t,e){return e?this.project(t):this.addSequence(t)}},{key:"processImpl",value:function(t){return this.project(t)}},{key:"project",value:function(t){if(t.length%this.dimsCount!=0)throw new Error("Points size ('".concat(t.length,"') must be multiple of the dimensions count ('").concat(this.dimsCount,"')."));if(0==t.length)return[];var e=[],r=_classPrivateFieldGet(this,_buffer).slice(),n=t.slice(0,t.length-this.dimsCount),i=this.addSequence(n);e.push.apply(e,_toConsumableArray(i));for(var o=t.slice(t.length-this.dimsCount,t.length),s=this.predictionPointsCount,a=0;a<s;a++){var l=this.addPoint(o);s-a<=_classPrivateFieldGet(this,_pointsCount)&&e.push.apply(e,_toConsumableArray(l))}return _classPrivateFieldSet(this,_buffer,r),e}},{key:"addSequence",value:function(t){if(t.length%this.dimsCount!=0)throw new Error("Points size ('".concat(t.length,"') must be multiple of the dimensions count ('").concat(this.dimsCount,"')."));for(var e=[],r=t.length/this.dimsCount,n=0;n<r;n++){var i=this.addPoint(t.slice(n*this.dimsCount,(n+1)*this.dimsCount));e.push.apply(e,_toConsumableArray(i))}return _classPrivateFieldSet(this,_pointsCount,_classPrivateFieldGet(this,_pointsCount)+r),e}},{key:"addPoint",value:function(t){var e;for((e=_classPrivateFieldGet(this,_buffer)).push.apply(e,_toConsumableArray(t));_classPrivateFieldGet(this,_buffer).length<this.windowSize*this.dimsCount;){var r;(r=_classPrivateFieldGet(this,_buffer)).unshift.apply(r,_toConsumableArray(_classPrivateFieldGet(this,_buffer).slice(0,this.dimsCount)))}for(;_classPrivateFieldGet(this,_buffer).length>this.windowSize*this.dimsCount;)_classPrivateFieldSet(this,_buffer,_classPrivateFieldGet(this,_buffer).slice(this.dimsCount));return this.filterBuffer()}},{key:"filterBuffer",value:function(){for(var t=[],e=0;e<this.windowSize;e++)for(var r=0;r<this.dimsCount;r++)isNaN(t[r])&&(t[r]=0),t[r]+=_classPrivateFieldGet(this,_buffer)[e*this.dimsCount+r]*this.filter[e];return t}},{key:"reset",value:function(){_get(_getPrototypeOf$1(r.prototype),"reset",this).call(this),_classPrivateFieldSet(this,_pointsCount,0),_classPrivateFieldGet(this,_buffer).clear()}}],[{key:"resample",value:function(t,e){for(var r=new Float32Array(e),n=t.length/e,i=0,o=0;o<e;o++){var s=(t.length-1)*o/(e-1),a=Math.floor(s),l=Math.ceil(s),u=s-a,h=n*(t[a]*(1-u)+t[l]*u);i+=h,r[o]=h}for(var c=1/i,p=0;p<e;p++)r[p]*=c;return r}}]),r}(DataSequenceProcessor);function _createSuper$i(t){var e=_isNativeReflectConstruct$i();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$i(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function _classPrivateFieldInitSpec$7(t,e,r){_checkPrivateRedeclaration$7(t,e),e.set(t,r)}function _checkPrivateRedeclaration$7(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}var _addition$2=new WeakMap,_prediction$2=new WeakMap,SplineProducer=function(t){_inherits$1(r,t);var e=_createSuper$i(r);function r(t){var n;return _classCallCheck$1(this,r),_classPrivateFieldInitSpec$7(_assertThisInitialized$1(n=e.call(this)),_addition$2,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$7(_assertThisInitialized$1(n),_prediction$2,{writable:!0,value:void 0}),n.layout=t,n.pathPointProps={},n}return _createClass$1(r,[{key:"add",value:function(t,e){_classPrivateFieldGet(this,_addition$2)||_classPrivateFieldSet(this,_addition$2,Spline.createInstance(this.layout,this.pathPointProps)),0==_classPrivateFieldGet(this,_addition$2).points.length&&t.length>0&&t.unshift.apply(t,_toConsumableArray(t.slice(0,this.layout.length))),e&&(t.length>=this.layout.length?t.push.apply(t,_toConsumableArray(t.slice(t.length-this.layout.length,t.length))):t.push.apply(t,_toConsumableArray(this.getLastPart())));var r=this.getFirstPart();return _classPrivateFieldGet(this,_addition$2).points=r.concat(t),t}},{key:"predict",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];if(0==t.length)return t;_classPrivateFieldGet(this,_prediction$2)?_classPrivateFieldGet(this,_prediction$2).points.clear():_classPrivateFieldSet(this,_prediction$2,Spline.createInstance(this.layout,this.pathPointProps));var e=_classPrivateFieldGet(this,_prediction$2).points,r=this.getFirstPart();for(e.push.apply(e,_toConsumableArray(r)),e.push.apply(e,_toConsumableArray(t)),e.push.apply(e,_toConsumableArray(e.slice(e.length-this.layout.length,e.length)));e.length<4*this.layout.length;)e.unshift(e.slice(0,this.layout.length));return e}},{key:"processImpl",value:function(t){var e=[];return t.length>0&&(e.push.apply(e,_toConsumableArray(t.slice(0,this.layout.length))),e.push.apply(e,_toConsumableArray(t)),t.length>=this.layout.length&&e.push.apply(e,_toConsumableArray(t.slice(t.length-this.layout.length,t.length)))),t}},{key:"getOutput",value:function(t,e){if(e==r.OutputType.ADDITION){if(_classPrivateFieldGet(this,_addition$2).points.length>=4*this.layout.length)return _classPrivateFieldGet(this,_addition$2)}else if(e==r.OutputType.PREDICTION){if(_classPrivateFieldGet(this,_prediction$2).points.length>0)return _classPrivateFieldGet(this,_prediction$2)}else if(t.length>0)return Spline.createSharedInstance(this.layout,t,this.pathPointProps)}},{key:"getFirstPart",value:function(){return _classPrivateFieldGet(this,_addition$2).points.slice(Math.max(0,_classPrivateFieldGet(this,_addition$2).points.length-3*this.layout.length),_classPrivateFieldGet(this,_addition$2).points.length)}},{key:"getLastPart",value:function(){return _classPrivateFieldGet(this,_addition$2).points.slice(_classPrivateFieldGet(this,_addition$2).points.length-this.layout.length,_classPrivateFieldGet(this,_addition$2).points.length)}},{key:"reset",value:function(){_get(_getPrototypeOf$1(r.prototype),"reset",this).call(this),_classPrivateFieldSet(this,_addition$2,null),_classPrivateFieldSet(this,_prediction$2,null)}}]),r}(DataSequenceProcessor);function _createSuper$h(t){var e=_isNativeReflectConstruct$h();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$h(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}var DistanceBasedInterpolator=function(t){_inherits$1(r,t);var e=_createSuper$h(r);function r(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.1,i=arguments.length>1?arguments[1]:void 0,o=arguments.length>2?arguments[2]:void 0;return _classCallCheck$1(this,r),(t=e.call(this,i,o)).spacing=n,t}return _createClass$1(r,[{key:"split",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:8,r=this.spacing;this.spacing=1,this.splitCount=e;var n=this.process(t);return this.spacing=r,delete this.splitCount,n}},{key:"discretize",value:function(t,e){var r,n,i=this.path.points,o=this.splitCount,s=Math.max(1,10*(this.spacing>1?1:this.spacing)),a=0,l=t.segmentsCount-1,u=t.ts,h=t.tf;e&&(a=e.segmentIndexStart,l=e.segmentIndexEnd,u=e.ts,h=e.tf,this.state.segmentIndex=a-1);for(var c=a;c<l+1;c++){if(this.state.segmentIndex++,this.calculatePolynomials(t,c),isNaN(this.splitCount)){r||(r=PathPoint.createInstance(t.layout),n=PathPoint.createInstance(t.layout)),r.fill(c+1,t.points,t.layout,t.pointProps),n.fill(c+2,t.points,t.layout,t.pointProps);var p=distance(r.value,n.value),f=this.pathPointProps.size;this.state.layout.SIZE&&(f=Math.min(r.size,n.size)),o=Math.floor(s*(p/f)/this.spacing)+1}for(var d=1/o,y=0;y<=o;y++){var v=!this.state.lastPointPosition,m=y/o;if(0==c&&m<u){if(!(m+d>=u))continue;m=u,v=this.spacing<=1}if(c==l&&m>=h){if(!(m<h+d))continue;m=h,v=this.lastSegment&&this.spacing<=1}if(!(c>0&&0==m)){var g=this.samplePoint(m);if(!v&&this.state.lastPointPosition){var P=new Point$3(this.getPropValue(PathPoint.Property.X,g),this.getPropValue(PathPoint.Property.Y,g),this.getPropValue(PathPoint.Property.Z,g)),_=this.state.lastPointPosition.vec.squaredDistance(this.state.lastPointPosition.value,P.value),b=(this.state.layout.SIZE?(this.state.lastPointSize+g[this.state.layout.SIZE.index])/2:this.pathPointProps.size)*this.spacing;v=_>=b*b}v&&(i.push.apply(i,_toConsumableArray(g)),this.storeLastPoint(g),this.keepSegmentT(m))}}}}}]),r}(SplineInterpolator);function _isNativeFunction(t){return-1!==Function.toString.call(t).indexOf("[native code]")}function _wrapNativeSuper(t){var e="function"==typeof Map?new Map:void 0;return _wrapNativeSuper=function(t){if(null===t||!_isNativeFunction(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,r)}function r(){return _construct(t,arguments,_getPrototypeOf$1(this).constructor)}return r.prototype=Object.create(t.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),_setPrototypeOf$1(r,t)},_wrapNativeSuper(t)}function _createSuper$g(t){var e=_isNativeReflectConstruct$g();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$g(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}var PolygonArray=function(t,e){_inherits$1(n,t);var r=_createSuper$g(n);function n(){var t;_classCallCheck$1(this,n);for(var e=arguments.length,i=new Array(e),o=0;o<e;o++)i[o]=arguments[o];if(t=r.call.apply(r,[this].concat(i)),i.some((function(t){return!(t instanceof Polygon)})))throw new Error("Expected data item type is Polygon");return Object.defineProperty(_assertThisInitialized$1(t),"bounds",{get:function(){var e;return t.length>0&&(t.forEach((function(t){return e=t.bounds.union(e)})),e=e.ceil()),e},enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(t),"vertices",{get:function(){return t.triangulate()},enumerable:!0}),t}return _createClass$1(n,[{key:"clone",value:function(){return _construct(this.constructor,_toConsumableArray(this.map((function(t){return t.clone()}))))}},{key:"push",value:function(){for(var t,e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];if(r.some((function(t){return!(t instanceof Polygon)})))throw new Error("Expected data item type is Polygon");(t=_get(_getPrototypeOf$1(n.prototype),"push",this)).call.apply(t,[this].concat(r))}},{key:"triangulate",value:function(){var t;return(t=Float32Array.createSharedInstance()).concat.apply(t,_toConsumableArray(this.map((function(t){return t.vertices}))))}},{key:"transform",value:function(t){this.forEach((function(e){return e.transform(t)}))}},{key:"toJSON",value:function(){return{type:"PolygonArray",polygons:this.map((function(t){return t.toJSON()}))}}}],[{key:e,get:function(){return Array}},{key:"fromJSON",value:function(t){if("PolygonArray"!=t.type)throw new Error("PolygonArray deserialization failed. JSON type is ".concat(t.type,", expected PolygonArray."));return _construct(n,_toConsumableArray(t.polygons.map((function(t){return Polygon.fromJSON(t)}))))}},{key:"fromPathsData",value:function(t){return _construct(n,_toConsumableArray(t.map((function(t){return Polygon.createInstance(t)}))))}}]),n}(_wrapNativeSuper(Array),Symbol.species);function _createForOfIteratorHelper$a(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=_unsupportedIterableToArray$a(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function _unsupportedIterableToArray$a(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray$a(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$a(t,e):void 0}}function _arrayLikeToArray$a(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function _createSuper$f(t){var e=_isNativeReflectConstruct$f();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$f(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}Polygon.toPolygonArray=PolygonArray.fromPathsData;var InkPath2D=function(t){_inherits$1(r,t);var e=_createSuper$f(r);function r(){var t,n;_classCallCheck$1(this,r);for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return t=e.call.apply(e,[this].concat(o)),Object.defineProperty(_assertThisInitialized$1(t),"encoding",{get:function(){return n},set:function(e){n=e,t.forEach((function(t){return t.encoding=e}))},enumerable:!0}),t}return _createClass$1(r,[{key:"union",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.1,r=new ClipperContext(this,this.bounds);r.subject=Clipper.SimplifyPolygons(r.subject,PolyFillType.pftNonZero),r.solution=Clipper.CleanPolygons(r.subject,e*r.transform.scale),1==r.subject.length&&0==r.solution.first.length&&(r.solution=r.subject);var n=r.toPaths();return t?Polygon.createSharedInstance(n.first,n.slice(1)):Polygon.createInstance(n.first,n.slice(1))}},{key:"intersects",value:function(t){t instanceof Polygon&&(t=[t]);var e,r=_createForOfIteratorHelper$a(this);try{for(r.s();!(e=r.n()).done;){var n,i=e.value,o=_createForOfIteratorHelper$a(t);try{for(o.s();!(n=o.n()).done;){var s=n.value;if(i.intersects(s))return{poly1:i,poly2:s}}}catch(t){o.e(t)}finally{o.f()}}}catch(t){r.e(t)}finally{r.f()}return null}},{key:"toJSON",value:function(){var t=_get(_getPrototypeOf$1(r.prototype),"toJSON",this).call(this);return t.type="InkPath2D",t}}],[{key:"fromJSON",value:function(t){if("InkPath2D"!=t.type)throw new Error("InkPath2D deserialization failed. JSON type is ".concat(t.type,", expected InkPath2D."));return _construct(r,_toConsumableArray(t.polygons.map((function(t){return Polygon.fromJSON(t)}))))}}]),r}(PolygonArray);function _createSuper$e(t){var e=_isNativeReflectConstruct$e();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$e(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function _classPrivateFieldInitSpec$6(t,e,r){_checkPrivateRedeclaration$6(t,e),e.set(t,r)}function _checkPrivateRedeclaration$6(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}var _addition$1=new WeakMap,_prediction$1=new WeakMap,_processor$1=new WeakMap,_path$2=new WeakMap,BrushApplier=function(t){_inherits$1(r,t);var e=_createSuper$e(r);function r(t){var n;return _classCallCheck$1(this,r),_classPrivateFieldInitSpec$6(_assertThisInitialized$1(n=e.call(this)),_addition$1,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$6(_assertThisInitialized$1(n),_prediction$1,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$6(_assertThisInitialized$1(n),_processor$1,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$6(_assertThisInitialized$1(n),_path$2,{writable:!0,value:void 0}),n.brush=t,n}return _createClass$1(r,[{key:"processImpl",value:function(t,e){return e==r.OutputType.ADDITION?(_classPrivateFieldGet(this,_addition$1)||_classPrivateFieldSet(this,_addition$1,new InkPath2D),_classPrivateFieldSet(this,_path$2,_classPrivateFieldGet(this,_addition$1))):e==r.OutputType.PREDICTION?(_classPrivateFieldGet(this,_prediction$1)||_classPrivateFieldSet(this,_prediction$1,new InkPath2D),_classPrivateFieldSet(this,_path$2,_classPrivateFieldGet(this,_prediction$1))):(_classPrivateFieldGet(this,_processor$1)||_classPrivateFieldSet(this,_processor$1,new InkPath2D),_classPrivateFieldSet(this,_path$2,_classPrivateFieldGet(this,_processor$1))),_classPrivateFieldGet(this,_path$2).clear(),this.generatePolygons(t),_classPrivateFieldGet(this,_path$2)}},{key:"generatePolygons",value:function(t){if(!t)return _classPrivateFieldGet(this,_path$2);for(var e=0;e<t.length;e++){var r=t.getPointRef(e),n=this.applyBrush(r);_classPrivateFieldGet(this,_path$2).push(n)}}},{key:"applyBrush",value:function(t){for(var e=this.createTransform(t),r=this.brush.selectShape(e.maxScale).shape,n=Float32Array.createSharedInstance(2*r.length),i=0;i<r.length;i++){var o=i*r.stride,s=fromValues(r.getPointX(i),r.getPointY(i));transformMat2d(s,s,e),n[o]=s[0],n[o+1]=s[1]}return Polygon.createSharedInstance(n)}},{key:"createTransform",value:function(t){if(isNaN(t.size))throw new Error("Size information not found");var e=create$4(),r=t.size*t.scaleX,n=t.size*t.scaleY,i=Math.max(r,n);return translate(e,e,fromValues(t.x,t.y)),rotate$1(e,e,t.rotation),translate(e,e,fromValues(t.offsetX,t.offsetY)),scale$4(e,e,fromValues(r,n)),e.maxScale=i,e}},{key:"reset",value:function(){_get(_getPrototypeOf$1(r.prototype),"reset",this).call(this),_classPrivateFieldGet(this,_addition$1)&&_classPrivateFieldGet(this,_addition$1).clear(),_classPrivateFieldGet(this,_prediction$1)&&_classPrivateFieldGet(this,_prediction$1).clear(),_classPrivateFieldGet(this,_processor$1)&&_classPrivateFieldSet(this,_processor$1,new InkPath2D)}}]),r}(DataSequenceProcessor);function _createForOfIteratorHelper$9(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=_unsupportedIterableToArray$9(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function _unsupportedIterableToArray$9(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray$9(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$9(t,e):void 0}}function _arrayLikeToArray$9(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function _createSuper$d(t){var e=_isNativeReflectConstruct$d();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$d(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function _classPrivateFieldInitSpec$5(t,e,r){_checkPrivateRedeclaration$5(t,e),e.set(t,r)}function _checkPrivateRedeclaration$5(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}_defineProperty(BrushApplier,"ARRAY_TYPE",InkPath2D);var _addition=new WeakMap,_prediction=new WeakMap,_processor=new WeakMap,_path$1=new WeakMap,_lastPolygon=new WeakMap,ConvexHullChainProducer=function(t){_inherits$1(r,t);var e=_createSuper$d(r);function r(){var t;return _classCallCheck$1(this,r),_classPrivateFieldInitSpec$5(_assertThisInitialized$1(t=e.call(this)),_addition,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$5(_assertThisInitialized$1(t),_prediction,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$5(_assertThisInitialized$1(t),_processor,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$5(_assertThisInitialized$1(t),_path$1,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$5(_assertThisInitialized$1(t),_lastPolygon,{writable:!0,value:void 0}),t}return _createClass$1(r,[{key:"add",value:function(t,e){return _classPrivateFieldGet(this,_addition)||_classPrivateFieldSet(this,_addition,new InkPath2D),_classPrivateFieldSet(this,_path$1,_classPrivateFieldGet(this,_addition)),_classPrivateFieldGet(this,_path$1).clear(),this.buildConvexHulls(t,!0),_classPrivateFieldGet(this,_path$1)}},{key:"processImpl",value:function(t,e){if(!(t instanceof InkPath2D)){if(!(t instanceof Polygon))throw new Error("ConvexHullChainProducer build 'input' type missmatch, expected type is oneof(Polygon, InkPath2D)");t=[t]}return e==r.OutputType.PREDICTION?(_classPrivateFieldGet(this,_prediction)||_classPrivateFieldSet(this,_prediction,new InkPath2D),_classPrivateFieldSet(this,_path$1,_classPrivateFieldGet(this,_prediction))):(_classPrivateFieldGet(this,_processor)||_classPrivateFieldSet(this,_processor,new InkPath2D),_classPrivateFieldSet(this,_path$1,_classPrivateFieldGet(this,_processor))),_classPrivateFieldGet(this,_path$1).clear(),this.buildConvexHulls(t),_classPrivateFieldGet(this,_path$1)}},{key:"buildConvexHulls",value:function(t){var e,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=_classPrivateFieldGet(this,_lastPolygon),i=_createForOfIteratorHelper$9(t);try{for(i.s();!(e=i.n()).done;){var o=e.value;if(n||1==t.length){var s=n?n.union(o):o.convex();_classPrivateFieldGet(this,_path$1).push(s)}n=o}}catch(t){i.e(t)}finally{i.f()}r&&t.length>0&&_classPrivateFieldSet(this,_lastPolygon,t.last)}},{key:"reset",value:function(){_get(_getPrototypeOf$1(r.prototype),"reset",this).call(this),_classPrivateFieldSet(this,_lastPolygon,null),_classPrivateFieldGet(this,_addition)&&_classPrivateFieldGet(this,_addition).clear(),_classPrivateFieldGet(this,_prediction)&&_classPrivateFieldGet(this,_prediction).clear(),_classPrivateFieldGet(this,_processor)&&_classPrivateFieldGet(this,_processor).clear()}}]),r}(DataSequenceProcessor);function _createForOfIteratorHelper$8(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=_unsupportedIterableToArray$8(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function _unsupportedIterableToArray$8(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray$8(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$8(t,e):void 0}}function _arrayLikeToArray$8(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}_defineProperty(ConvexHullChainProducer,"ARRAY_TYPE",InkPath2D);var instantiate=!1,ThreadBridge=function(){function t(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t.WorkerType.CLASSIC;if(_classCallCheck$1(this,t),!instantiate)throw new Error("Constructor is private, use static method getInstance instead.");this.name=e,this.type=n,r&&r.startsWith("file://")&&(r=r.replace("file://","")),this.src=r,this.workers=[],this.transferables=[],this.status=t.Status.CLOSED,this.resolver={};var i=0;Object.defineProperty(this,"nextID",{get:function(){return String(i++)},enumerable:!0,configurable:!0})}var e,r,n;return _createClass$1(t,[{key:"open",value:(n=_asyncToGenerator(regenerator.mark((function e(){var r,n,i,o,s,a,l,u,h=this,c=arguments;return regenerator.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=c.length>0&&void 0!==c[0]?c[0]:this.src,this.status==t.Status.CLOSED){e.next=3;break}throw new Error("".concat(this.name," worker cannot be opened. Current status is ").concat(this.status.name,"."));case 3:if(r){e.next=5;break}throw new Error("".concat(this.name," worker location is not defined."));case 5:if("function"!=typeof Worker){e.next=10;break}n=Worker,i=navigator.hardwareConcurrency||1,e.next=19;break;case 10:return e.next=12,import("os");case 12:return o=e.sent,e.next=15,import("worker_threads");case 15:s=e.sent,a=s.Worker,n=a,i=o.cpus().length;case 19:for(this.ready=0,l=function(t){var e=h.name+t,i=new n(r,{type:h.type,name:e,workerData:{name:e}});i.name=t,i.on("message",(function(t){return"INIT"==t.action?h.confirmWorkerReady():h.recieve(t)})),i.on("error",(function(e){return h.recieveError(e,t)})),h.workers.push(i)},u=0;u<i;u++)l(u);return this.status=t.Status.OPEN_IN_PROGRESS,e.abrupt("return",new Promise((function(t,e){h.workers.forEach((function(t,e){return t.postMessage({action:"INIT",worker:e})})),h.resolve=t})));case 24:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"confirmWorkerReady",value:function(){this.ready++,this.ready==this.workers.length&&(this.resolve(),delete this.ready,delete this.resolve,this.status=t.Status.OPEN)}},{key:"close",value:function(){this.workers.forEach((function(t){return t.terminate()})),this.workers.clear(),this.status=t.Status.CLOSED}},{key:"broadcast",value:(r=_asyncToGenerator(regenerator.mark((function e(r,n){var i=this;return regenerator.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.status==t.Status.OPEN){e.next=2;break}throw new Error("ThreadBridge is not opened yet. Current status is ".concat(this.status.name,". Use open first."));case 2:return e.abrupt("return",new Promise((function(t,e){i.resolver[n]=t;var o,s=_createForOfIteratorHelper$8(i.workers);try{for(s.s();!(o=s.n()).done;){var a=o.value,l=i.buildRequestMessage(r,n);if(!l)break;i.send(a.name,l)}}catch(t){s.e(t)}finally{s.f()}})));case 3:case"end":return e.stop()}}),e,this)}))),function(t,e){return r.apply(this,arguments)})},{key:"broadcastMessage",value:(e=_asyncToGenerator(regenerator.mark((function e(r){var n=this;return regenerator.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.status==t.Status.OPEN){e.next=2;break}throw new Error("ThreadBridge is not opened yet. Current status is ".concat(this.status.name,". Use open first."));case 2:if(r.actionID){e.next=4;break}throw new Error("message actionID is required");case 4:return e.abrupt("return",new Promise((function(t,e){n.resolver[r.actionID]=t;var i,o=_createForOfIteratorHelper$8(n.workers);try{for(o.s();!(i=o.n()).done;){var s=i.value;n.send(s.name,r)}}catch(t){o.e(t)}finally{o.f()}})));case 5:case"end":return e.stop()}}),e,this)}))),function(t){return e.apply(this,arguments)})},{key:"send",value:function(e,r){if(this.status!=t.Status.OPEN)throw new Error("ThreadBridge is not opened yet. Current status is ".concat(this.status.name,". Use open first."));if(!r)throw new Error("message is required");this.workers[e].postMessage(r,this.transferables),this.transferables.clear()}},{key:"buildRequestMessage",value:function(t,e){throw new Error("ThreadBridge.buildRequestMessage(action, actionID) is abstract and should be implemented")}},{key:"recieve",value:function(t){throw new Error("ThreadBridge.recieve(message) is abstract and should be implemented")}},{key:"resolve",value:function(t,e){this.resolver[t](e),delete this.resolver[t]}},{key:"recieveError",value:function(t,e){console.warn("".concat(this.name," worker ").concat(e,": ").concat(t.message)),t.filename||console.error(t)}}],[{key:"getInstance",value:function(){return this.instance||(instantiate=!0,this.instance=new this,instantiate=!1),this.instance}}]),t}();function _createSuper$c(t){var e=_isNativeReflectConstruct$c();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$c(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}Object.defineEnum(ThreadBridge,"Status",["OPEN","OPEN_IN_PROGRESS","CLOSED"]),ThreadBridge.WorkerType={CLASSIC:"classic",MODULE:"module"};var ConvexHullProducer=function(t){_inherits$1(n,t);var e,r=_createSuper$c(n);function n(){var t;return _classCallCheck$1(this,n),(t=r.call(this,n.WORKER_NAME,n.buildWorkerURL(),n.buildWorkerURL().contains("/wacom-src/")?ThreadBridge.WorkerType.MODULE:ThreadBridge.WorkerType.CLASSIC)).state={},t}return _createClass$1(n,[{key:"build",value:(e=_asyncToGenerator(regenerator.mark((function t(e,r,n){var i;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=this.nextID,this.state[i]={type:r,input:e,output:new InkPath2D,queue:_toConsumableArray(e.slice()),lastPolygon:n,expected:e.length,processed:0},t.next=4,this.broadcast("BUILD",i);case 4:return t.abrupt("return",t.sent);case 5:case"end":return t.stop()}}),t,this)}))),function(t,r,n){return e.apply(this,arguments)})},{key:"buildRequestMessage",value:function(t,e){var r={action:t,actionID:e},n=this.state[e];if("BUILD"!=t)throw new Error("Unknow data action found: ".concat(t));var i=n.queue.shift();if(i){var o,s=n.input.indexOf(i),a=0==s?n.lastPolygon:n.input[s-1];return a?o=Array.of.apply(Array,_toConsumableArray(a.shape.points).concat(_toConsumableArray(i.shape.points))):(a=i,(i=n.queue.shift())?(o=Array.of.apply(Array,_toConsumableArray(a.shape.points).concat(_toConsumableArray(i.shape.points))),n.updateIndex=!0,n.expected--,s++):o=a.shape.points),r.index=s,r.data=o,r}}},{key:"recieve",value:function(t){var e=this.state[t.actionID],r=e.updateIndex?t.index-1:t.index;if(e.output[r]=Polygon.createSharedInstance(t.data),e.processed++,e.processed==e.expected){var n;if(delete this.state[t.actionID],this.keepAllData&&e.type==DataSequenceProcessor.OutputType.ADDITION)this.path||(this.path=new InkPath2D),(n=this.path).push.apply(n,_toConsumableArray(e.output));this.resolve(t.actionID,e.output)}else{var i=this.buildRequestMessage(t.action,t.actionID);i&&this.send(t.worker,i)}}}],[{key:"buildWorkerURL",value:function(){if("function"!=typeof DedicatedWorkerGlobalScope){var t=void 0===__location?document.currentScript&&document.currentScript.src||new URL("SplitPointsProvider.js",document.baseURI).href:__location;return(t=t.substring(0,t.lastIndexOf("/"))).endsWith("workers")||(t+="/workers"),t+="/".concat(n.WORKER_NAME,".js")}}}]),n}(ThreadBridge);function _createSuper$b(t){var e=_isNativeReflectConstruct$b();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$b(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}_defineProperty(ConvexHullProducer,"WORKER_NAME","ConvexHullProvider");var ConvexHullChainProducerAsync=function(t){_inherits$1(i,t);var e,r,n=_createSuper$b(i);function i(){var t;return _classCallCheck$1(this,i),(t=n.call(this)).lastPolygon,t.convexHullProducer=ConvexHullProducer.getInstance(),Object.defineProperty(_assertThisInitialized$1(t),"closed",{get:function(){return t.convexHullProducer.status==ThreadBridge.Status.CLOSED},enumerable:!0}),t}return _createClass$1(i,[{key:"open",value:(r=_asyncToGenerator(regenerator.mark((function t(){return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.convexHullProducer.open();case 2:case"end":return t.stop()}}),t,this)}))),function(){return r.apply(this,arguments)})},{key:"close",value:function(){this.convexHullProducer.close()}},{key:"process",value:(e=_asyncToGenerator(regenerator.mark((function t(e){var r,n,o,s,a=arguments;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=a.length>1&&void 0!==a[1]?a[1]:i.OutputType.PROCESSOR,n=!(a.length>2&&void 0!==a[2])||a[2],o=this.lastPolygon,r==i.OutputType.ADDITION&&e.length>0&&(this.lastPolygon=e.last),n&&(this.lastPolygon=null),t.next=7,this.convexHullProducer.build(e,r,o);case 7:return s=t.sent,t.abrupt("return",this.getOutput(s));case 9:case"end":return t.stop()}}),t,this)}))),function(t){return e.apply(this,arguments)})},{key:"reset",value:function(){_get(_getPrototypeOf$1(i.prototype),"reset",this).call(this),this.lastPolygon=null}}],[{key:"buildWorkerURL",value:function(){return ConvexHullProducer.buildWorkerURL()}}]),i}(DataSequenceProcessor);function _createSuper$a(t){var e=_isNativeReflectConstruct$a();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$a(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}_defineProperty(ConvexHullChainProducerAsync,"ARRAY_TYPE",InkPath2D);var PolygonMerger=function(t){_inherits$1(r,t);var e=_createSuper$a(r);function r(){return _classCallCheck$1(this,r),e.apply(this,arguments)}return _createClass$1(r,[{key:"predict",value:function(t){return console.warn("Prediction merge is not recommended"),t}},{key:"processImpl",value:function(t,e){return this.merge(t,e==r.OutputType.PROCESSOR)}},{key:"merge",value:function(t,e){if(0!=t.length)return t.union(e)}}]),r}(DataSequenceProcessor);function _createSuper$9(t){var e=_isNativeReflectConstruct$9();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$9(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}_defineProperty(PolygonMerger,"ARRAY_TYPE",InkPath2D);var PolygonSimplifier=function(t){_inherits$1(r,t);var e=_createSuper$9(r);function r(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.1;return _classCallCheck$1(this,r),(t=e.call(this)).epsilon=n,t}return _createClass$1(r,[{key:"predict",value:function(t){return console.warn("Prediction simplify is not recommended"),t}},{key:"processImpl",value:function(t){var e=this;return t instanceof Polygon?t.simplifyRamerDouglasPeucker(this.epsilon):_construct(t.constructor,_toConsumableArray(t.map((function(t){return t.simplify(e.epsilon)}))))}}]),r}(DataSequenceProcessor);function Pipeline(){}_defineProperty(PolygonSimplifier,"ARRAY_TYPE",InkPath2D),Object.defineEnum(Pipeline,"Stage",["PATH_PRODUCER","SMOOTHER","SPLINE_PRODUCER","SPLINE_INTERPOLATOR","BRUSH_APPLIER","CONVEX_HULL_CHAIN_PRODUCER","POLYGON_MERGER","POLYGON_SIMPLIFIER"]);var Stage=Pipeline.Stage,Phase=DataProcessor.Phase,OutputType=DataSequenceProcessor.OutputType,PointerData=function(){function t(e){var r=this;_classCallCheck$1(this,t),this.phase=e.phase;var n=isNaN(e.altitude)||isNaN(e.azimuth)?void 0:{altitude:e.altitude,azimuth:e.azimuth};Object.defineProperty(this,"x",{value:e.x,enumerable:!0}),Object.defineProperty(this,"y",{value:e.y,enumerable:!0}),Object.defineProperty(this,"z",{value:e.z,enumerable:!0}),Object.defineProperty(this,"timestamp",{value:e.timestamp,enumerable:!0,writable:!0}),Object.defineProperty(this,"force",{value:e.pressure,enumerable:!0}),Object.defineProperty(this,"pressure",{value:e.pressure,enumerable:!0}),Object.defineProperty(this,"rotation",{value:e.rotation,enumerable:!0}),Object.defineProperty(this,"radiusX",{value:e.radiusX,enumerable:!0}),Object.defineProperty(this,"radiusY",{value:e.radiusY,enumerable:!0}),Object.defineProperty(this,"altitude",{get:function(){return n||(n=r.computeTilt(e)||{}),n.altitude},enumerable:!0}),Object.defineProperty(this,"azimuth",{get:function(){return n||(n=r.computeTilt(e)||{}),n.azimuth},enumerable:!0}),e.pointer&&Object.defineProperty(this,"pointer",{value:e.pointer,enumerable:!0}),this.computedAzimuth=void 0}return _createClass$1(t,[{key:"createPathPoint",value:function(t){return new PathPoint(this.x,this.y,this.z,t)}},{key:"computeTilt",value:function(t){if(!isNaN(t.tiltX)&&!isNaN(t.tiltY)){var e=t.tiltX,r=t.tiltY,n=Math.tan(Math.toRadians(e)),i=Math.tan(Math.toRadians(r)),o=Math.sqrt(n*n+i*i);return{altitude:Math.atan2(1,o),azimuth:Math.atan2(i,n)}}}},{key:"speed",value:function(e,r){var n={x:0,y:0,time:0};return(n=e&&!r?this.minus(e):r&&!e?r.minus(this):r.minus(e)).time>0?t.getMagnitude(n.x,n.y)/(n.time/1e3):(0==n.time||console.warn("Speed out of range: ".concat(n.time,"ms")),0)}},{key:"computeNearestAzimuthAngle",value:function(t){var e;if(isNaN(this.azimuth))return 0;if(t){if(isNaN(t.azimuth))return 0;var r=2*Math.PI,n=t.computedAzimuth||t.azimuth,i=parseInt(n/r),o=(e=this.azimuth+i*r)-n;o>=Math.PI?e-=r:o<-Math.PI&&(e+=r)}else e=this.azimuth;return this.computedAzimuth=e,e}},{key:"minus",value:function(t){return{x:this.x-t.x,y:this.y-t.y,time:this.timestamp-t.timestamp}}}],[{key:"getMagnitude",value:function(t,e){return Math.sqrt(t*t+e*e)}}]),t}();Object.defineEnum(PointerData,"Property",["X","Y","Z","PHASE","TIMESTAMP","PRESSURE","RADIUS_X","RADIUS_Y","ALTITUDE","AZIMUTH","ROTATION"]);var PathSegment=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];_classCallCheck$1(this,t),this.accumulatedAddition=e,this.lastPrediction=r,this.first=!1,this.last=!1}return _createClass$1(t,[{key:"add",value:function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];t==DataProcessor.Phase.BEGIN?this.reset(!0):t==DataProcessor.Phase.END&&(this.last=!0),e&&(r=this.accumulatedAddition).push.apply(r,_toConsumableArray(e)),this.lastPrediction=n}},{key:"clone",value:function(){var e=new t(this.accumulatedAddition.slice(),this.lastPrediction.slice());return e.first=this.first,e.last=this.last,e}},{key:"reset",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.first=t,this.last=!1,this.accumulatedAddition.clear(),this.lastPrediction.clear()}}]),t}(),version="1.5.0";Object.defineProperty(globalThis,"DIGITAL_INK_ENV",{value:"AUTO",enumerable:!0,configurable:!0});var env={version:version};Object.defineEnum(env,"Type",["WEB","WORKER","NODE","SHELL"]),Object.defineEnum(env,"Type2D",["SCREEN","OFFSCREEN"]),Object.defineEnum(env,"TypeGL",["WEB","STACK"]),function(t){var e,r="BROWSER"!=DIGITAL_INK_ENV&&"object"===("undefined"==typeof process?"undefined":_typeof$1(process))&&"function"==typeof require;e="object"===("undefined"==typeof window?"undefined":_typeof$1(window))?"WEB":"function"==typeof importScripts?"WORKER":r?"NODE":"SHELL";var n="undefined"==typeof Screen?"OFFSCREEN":"SCREEN",i="undefined"==typeof WebGLRenderingContext?"STACK":"WEB";Object.defineProperty(env,"commonJS",{value:r,enumerable:!0}),Object.defineProperty(env,"type",{value:env.Type[e],enumerable:!0}),Object.defineProperty(env,"type2D",{value:env.Type2D[n],enumerable:!0}),Object.defineProperty(env,"typeGL",{value:env.TypeGL[i],enumerable:!0})}();var shim={};if(env.type==env.Type.WEB)shim.Image=globalThis.Image,shim.ImageData=globalThis.ImageData,shim.CanvasRenderingContext2D=globalThis.CanvasRenderingContext2D,void 0===globalThis.OffscreenCanvas?(shim.OffscreenCanvas=function(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,r},shim.OffscreenCanvasRenderingContext2D=globalThis.CanvasRenderingContext2D):(shim.OffscreenCanvas=globalThis.OffscreenCanvas,shim.OffscreenCanvasRenderingContext2D=globalThis.OffscreenCanvasRenderingContext2D);else if(void 0!==globalThis.OffscreenCanvas)shim.Image=globalThis.Image,shim.ImageData=globalThis.ImageData,shim.OffscreenCanvas=globalThis.OffscreenCanvas,shim.OffscreenCanvasRenderingContext2D=globalThis.OffscreenCanvasRenderingContext2D;else if(env.commonJS){var _require=require("canvas"),Canvas=_require.Canvas,_CanvasRenderingContext2D=_require.CanvasRenderingContext2D,_Image=_require.Image,_ImageData=_require.ImageData;shim.Image=_Image,shim.ImageData=_ImageData,shim.OffscreenCanvas=Canvas,shim.OffscreenCanvasRenderingContext2D=_CanvasRenderingContext2D}else console.warn("Current env - ".concat(env.type.name,", do not provides OffscreenCanvas support"));function clearCanvas(t){this.clearRect(0,0,this.canvas.width,this.canvas.height),t&&(this.fillStyle=t,this.fillRect(0,0,this.canvas.width,this.canvas.height))}shim.CanvasRenderingContext2D&&(shim.CanvasRenderingContext2D.prototype.clearCanvas=clearCanvas),shim.OffscreenCanvasRenderingContext2D&&(shim.OffscreenCanvasRenderingContext2D.prototype.clearCanvas=clearCanvas);var Image$1=shim.Image,OffscreenCanvas=shim.OffscreenCanvas,CanvasRenderingContext2D=shim.CanvasRenderingContext2D,OffscreenCanvasRenderingContext2D=shim.OffscreenCanvasRenderingContext2D,instance;function _classPrivateFieldInitSpec$4(t,e,r){_checkPrivateRedeclaration$4(t,e),e.set(t,r)}function _checkPrivateRedeclaration$4(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}var _registry=new WeakMap,URIResolver=function(){function t(){var e=this;if(_classCallCheck$1(this,t),_classPrivateFieldInitSpec$4(this,_registry,{writable:!0,value:{}}),instance)throw new Error("URIResolver instance already available");instance=this,Object.defineProperty(this,"items",{get:function(){return Object.values(_classPrivateFieldGet(e,_registry))},enumerable:!0}),this.init.apply(this,arguments)}return _createClass$1(t,[{key:"init",value:function(){throw new Error("URIResolver.init(...args) is abstract and should be implemented")}},{key:"get",value:function(t){return _classPrivateFieldGet(this,_registry)[t]}},{key:"register",value:function(t,e){_classPrivateFieldGet(this,_registry)[t]=e}},{key:"resolve",value:function(t){var e;if(t.includes("?")){var r=t.split("?")[0],n=_classPrivateFieldGet(this,_registry)[r];if(n){var i=t.split("?")[1],o=[];i.split("&").forEach((function(t){var e=t.split("=")[1],r=parseFloat(e);isFinite(r)?e=r:"true"==e?e=!0:"false"==e&&(e=!1),o.push(e)})),e=function(){return n.apply(void 0,_toConsumableArray(Array.from(arguments).concat(o)))}}}else e=_classPrivateFieldGet(this,_registry)[t];if(!e)throw new Error("Failed to resolve ".concat(t));return e}}]),t}();Object.defineProperty(URIResolver,"instance",{get:function(){return instance}});var ResourceDescriptor=function(){function t(e,r){_classCallCheck$1(this,t),this.name=e,!e||utils.isValidURL(e)||t.repetitionsCache.has(e)||(t.repetitionsCache.add(e),console.warn("The string ".concat(e," is not a well formed URI"))),Object.defineProperty(this,"value",{get:function(){if(!r){if(!this.name)throw new Error("Resource descriptor identifier not found. Cannot resolve resource content.");if("function"==typeof this.resolve&&(r=this.resolve(this.name)),!r){if(!URIResolver.instance)throw new Error("Resource URI ".concat(this.name," cannot be resolved. URIResolver not implemented yet. Please implement and instantiate."));r=URIResolver.instance.resolve(this.name)}if(!r)throw new Error("Resource URI ".concat(this.name," cannot be resolved. Please provide resource definition in URIResolver init implementation."))}return r},set:function(t){r=t},enumerable:!0})}return _createClass$1(t,[{key:"toJSON",value:function(){var t=this.value;return ArrayBuffer.isTypedArray(t)?t=TypedArrayCodec.encode(t,this.encoding):"function"==typeof t&&(t=t()),{name:this.name,value:t}}}],[{key:"fromJSON",value:function(e){var r=e.value;return TypedArrayCodec.isTypedArrayData(r)&&(r=TypedArrayCodec.decode(r)),new t(e.name,r)}},{key:"getInstance",value:function(e,r){return new t(r,e)}}]),t}();_defineProperty(ResourceDescriptor,"repetitionsCache",new Set);var Brush=function(){function t(e){_classCallCheck$1(this,t),utils.isValidURL(e)||(ResourceDescriptor.repetitionsCache.has(e)||(ResourceDescriptor.repetitionsCache.add(e),console.warn("Brush URI ".concat(e," is not a well formed URI"))),e=this.constructor.onInvalidName(e)),Object.defineProperty(this,"id",{value:e}),Object.defineProperty(this,"uri",{value:e}),Object.defineProperty(this,"name",{value:e,enumerable:!0})}return _createClass$1(t,[{key:"toJSON",value:function(){throw new Error("Brush.toJSON() should be implemented")}}],[{key:"fromJSON",value:function(t){throw new Error("static Brush.fromJSON() should be implemented")}},{key:"onInvalidName",value:function(t){return t}}]),t}(),ShapeFactory=function(){function t(){_classCallCheck$1(this,t)}return _createClass$1(t,null,[{key:"createCircle",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:t.defaults.CIRCLE_PRECISION,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t.defaults.CIRCLE_RADIUS,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{x:0,y:0};return t.createEllipse(e,r,r,n)}},{key:"createEllipse",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:t.defaults.ELLIPSE_PRECISION,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t.defaults.ELLIPSE_RADIUS_X,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t.defaults.ELLIPSE_RADIUS_Y,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{x:0,y:0},o=[],s=2*Math.PI/e;if(r<=0)throw new Error("Invalid radius x found ".concat(r," > 0"));if(n<=0)throw new Error("Invalid radius y found ".concat(n," > 0"));for(var a=0;a<e;a++){var l=a*s,u=r*Math.cos(l),h=n*Math.sin(l);o.push(i.x+u,i.y+h)}return Float32Array.createSharedInstance(o)}},{key:"createStar",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:t.defaults.STAR_POINTS,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t.defaults.STAR_INTERNAL_RADIUS,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t.defaults.STAR_RADIUS,i=[];if(n<=0)throw new Error("Invalid radius found ".concat(n," > 0"));if(r<=0)throw new Error("Invalid internal radius found ".concat(r," > 0"));if(r>n)throw new Error("Invalid internal radius found 0 < ".concat(r," < ").concat(n));for(var o=2*Math.PI/e,s=0;s<e;s++){var a=s*o,l=n*Math.cos(a),u=n*Math.sin(a),h=r*Math.cos(a+o/2),c=r*Math.sin(a+o/2);i.push(l,u,h,c)}return Float32Array.createSharedInstance(i)}}]),t}();_defineProperty(ShapeFactory,"defaults",{CIRCLE_PRECISION:20,CIRCLE_RADIUS:.5,ELLIPSE_PRECISION:20,ELLIPSE_RADIUS_X:.5,ELLIPSE_RADIUS_Y:.25,STAR_POINTS:5,STAR_RADIUS:.5,STAR_INTERNAL_RADIUS:.25});var BrushPrototype=function(){function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;_classCallCheck$1(this,t),this.size=r,Object.defineProperty(this,"descriptor",{value:{shape:void 0},enumerable:!0}),Object.defineProperty(this,"shape",{get:function(){if(!e){if("function"==typeof(e=this.descriptor.shape.value)&&(e=e()),(Array.isArray(e)||e instanceof Float32Array)&&(e=Polygon.createSharedInstance(e)),!(e instanceof Polygon))throw new Error("Expected shape type is Polygon");t.fitShape(e)}return e},set:function(r){if(!r)throw new Error("BrushPrototype: shape not found");"string"==typeof r?r=new ResourceDescriptor(r):r instanceof Polygon||r instanceof Float32Array||Array.isArray(r)?r=ResourceDescriptor.getInstance(r):r instanceof ResourceDescriptor||(r=new ResourceDescriptor(r.name,r.value)),e=null,this.descriptor.shape=r,this.descriptor.shape.resolve=t.resolve},enumerable:!0}),this.shape=e}return _createClass$1(t,[{key:"toJSON",value:function(){return this.shape.encoding=this.encoding,{shape:{name:this.descriptor.shape.name,value:this.shape.toJSON()},size:this.size}}}],[{key:"fromJSON",value:function(e){return new t({name:e.shape.name,value:Polygon.fromJSON(e.shape.value)},e.size)}},{key:"create",value:function(e){for(var r,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=e,o=arguments.length,s=new Array(o>2?o-2:0),a=2;a<o;a++)s[a-2]=arguments[a];switch(e){case t.Type.CIRCLE:r=ShapeFactory.createCircle.apply(ShapeFactory,s),i+="?precision=".concat(s[0]||ShapeFactory.defaults.CIRCLE_PRECISION,"&radius=").concat(s[1]||ShapeFactory.defaults.CIRCLE_RADIUS);break;case t.Type.ELLIPSE:r=ShapeFactory.createEllipse.apply(ShapeFactory,s),i+="?precision=".concat(s[0]||ShapeFactory.defaults.ELLIPSE_PRECISION,"&radiusX=").concat(s[1]||ShapeFactory.defaults.ELLIPSE_RADIUS_X,"&radiusY=").concat(s[2]||ShapeFactory.defaults.ELLIPSE_RADIUS_Y);break;case t.Type.STAR:r=ShapeFactory.createStar.apply(ShapeFactory,s),i+="?points=".concat(s[0]||ShapeFactory.defaults.STAR_POINTS,"&internalRadius=").concat(s[1]||ShapeFactory.defaults.STAR_INTERNAL_RADIUS,"&radius=").concat(s[2]||ShapeFactory.defaults.STAR_RADIUS);break;default:console.error("Brush2D: createShape fails with ".concat(e," type"))}return new t({name:i,shape:r},n)}},{key:"resolve",value:function(e){var r,n=e.split("?"),i=n.first;if(Object.values(t.Type).includes(i)){var o=n.last.split("&"),s={};switch(o.forEach((function(t){s[t.substring(0,t.indexOf("="))]=t.substring(t.indexOf("=")+1)})),i){case t.Type.CIRCLE:var a=s.precision?parseInt(s.precision):void 0,l=s.radius?parseFloat(s.radius):1;r=ShapeFactory.createCircle(a,l);break;case t.Type.ELLIPSE:var u=s.precision?parseInt(s.precision):void 0,h=s.radiusX?parseFloat(s.radiusX):void 0,c=s.radiusY?parseFloat(s.radiusY):void 0;r=ShapeFactory.createEllipse(u,h,c);break;case t.Type.STAR:var p=s.points?parseInt(s.points):void 0,f=s.radius?parseFloat(s.radius):void 0,d=s.internalRadius?parseFloat(s.internalRadius):void 0;r=ShapeFactory.createStar(p,d,f);break;default:console.error("Brush2D: createShape fails with ".concat(i," type"))}}return r}},{key:"fitShape",value:function(e){if(!(e instanceof Polygon))throw new Error("Expected shape type is Polygon");e.center(),e.fit(t.SHAPE_FRAME)}}]),t}();function readFile(t){return _readFile.apply(this,arguments)}function _readFile(){return _readFile=_asyncToGenerator(regenerator.mark((function t(e){var r,n,i,o,s,a,l=arguments;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=l.length>1&&void 0!==l[1]?l[1]:"binary",n=l.length>2&&void 0!==l[2]?l[2]:{},!(e instanceof Uint8Array)){t.next=4;break}return t.abrupt("return",e);case 4:return t.next=6,fetch(e,Object.assign({mode:"no-cors"},n));case 6:if(o=t.sent,"json"!=r){t.next=13;break}return t.next=10,o.json();case 10:i=t.sent,t.next=36;break;case 13:if("text"!=r){t.next=19;break}return t.next=16,o.text();case 16:i=t.sent,t.next=36;break;case 19:if("binary"!=r){t.next=26;break}return t.next=22,o.arrayBuffer();case 22:s=t.sent,i=new Uint8Array(s),t.next=36;break;case 26:return t.next=28,o.blob();case 28:if(a=t.sent,"base64"!=r){t.next=35;break}return t.next=32,dataURL(a);case 32:i=t.sent,t.next=36;break;case 35:i=a;case 36:return t.abrupt("return",i);case 37:case"end":return t.stop()}}),t)}))),_readFile.apply(this,arguments)}function loadFile(t){return _loadFile.apply(this,arguments)}function _loadFile(){return _loadFile=_asyncToGenerator(regenerator.mark((function t(e){var r,n,i=arguments;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=i.length>1&&void 0!==i[1]?i[1]:"binary",n=i.length>2&&void 0!==i[2]?i[2]:{},t.abrupt("return",readFile(e,r,n));case 3:case"end":return t.stop()}}),t)}))),_loadFile.apply(this,arguments)}function dataURL(t){return new Promise((function(e,r){var n=new FileReader;n.onloadend=function(){return e("data:"==n.result?"":n.result)},n.onerror=r,n.readAsDataURL(t)}))}function fetchImage(t){return new Promise((function(e,r){var n,i=new Image$1;i.crossOrigin="anonymous",i.onload=function(){if(env.type2D==env.Type2D.OFFSCREEN){var t=new OffscreenCanvas(i.width,i.height);t.getContext("2d").drawImage(i,0,0),e(t)}else n&&URL.revokeObjectURL(n),e(i)},i.onerror=r,"string"==typeof t?i.src=t:env.type2D==env.Type2D.OFFSCREEN?t instanceof Uint8Array?i.src=Buffer.from(t):t instanceof OffscreenCanvas?e(t):i.src=t:(t instanceof Uint8Array&&(t.byteLength!=t.buffer.byteLength&&(t=t.slice()),t=t.buffer),t instanceof ArrayBuffer&&(t=new Blob([t],{type:"image/png"})),n=URL.createObjectURL(t),i.src=n)}))}function loadImage(t){return _loadImage.apply(this,arguments)}function _loadImage(){return(_loadImage=_asyncToGenerator(regenerator.mark((function t(e){var r;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("string"!=typeof e&&"undefined"!=typeof createImageBitmap){t.next=6;break}return t.next=3,fetchImage(e);case 3:r=t.sent,t.next=15;break;case 6:if(!(e instanceof ArrayBuffer||e instanceof Uint8Array)){t.next=12;break}return t.next=9,createImageBitmap(new Blob([e],{type:"image/png"}));case 9:r=t.sent,t.next=15;break;case 12:return t.next=14,createImageBitmap(e);case 14:r=t.sent;case 15:return t.abrupt("return",r);case 16:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function _createSuper$8(t){var e=_isNativeReflectConstruct$8();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$8(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}_defineProperty(BrushPrototype,"SHAPE_FRAME",new Rect(-.5,-.5,1,1)),BrushPrototype.Type={ELLIPSE:"will://brush/3.0/shape/Ellipse",CIRCLE:"will://brush/3.0/shape/Circle",STAR:"will://brush/3.0/shape/Star"};var Brush2D=function(t){_inherits$1(n,t);var e,r=_createSuper$8(n);function n(t,e,i){var o,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;return _classCallCheck$1(this,n),o=r.call(this,t),isFinite(i)&&(s=i,i=void 0),s<=0&&(console.warn("Invalid spacing found ".concat(s,". It should be positive number.")),s=1),Object.defineProperty(_assertThisInitialized$1(o),"shape",{get:function(){return e},set:function(t){if(t instanceof Float32Array&&(t=new BrushPrototype(t)),t instanceof BrushPrototype&&(t=[t]),t.some((function(t){return!(t instanceof BrushPrototype)})))throw console.warn(t),new Error("Brush2D: Invalid shape found");t.sort(utils.comparator({sortBy:"size",sortOrder:"asc"})),e=t},enumerable:!0}),o.shape=e,o.fill=i,o.spacing=s,o}return _createClass$1(n,[{key:"configure",value:(e=_asyncToGenerator(regenerator.mark((function t(e){var r;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!this.pattern&&this.fill){t.next=2;break}return t.abrupt("return");case 2:if(e instanceof CanvasRenderingContext2D||e instanceof OffscreenCanvasRenderingContext2D){t.next=4;break}throw new Error("ctx is not instance of CanvasRenderingContext2D or OffscreenCanvasRenderingContext2D");case 4:return t.next=6,loadImage(this.fill);case 6:r=t.sent,this.pattern=e.createPattern(r,"repeat");case 8:case"end":return t.stop()}}),t,this)}))),function(t){return e.apply(this,arguments)})},{key:"selectShape",value:function(t){for(var e,r=1;r<this.shape.length;r++)if(this.shape[r].size>t){e=this.shape[r-1];break}return e||(e=this.shape.last),e.shape}},{key:"toJSON",value:function(){var t=this;return{type:"Brush2D",name:this.name,spacing:this.spacing,shape:this.shape.map((function(e){return e.encoding=t.encoding,e.toJSON()}))}}}],[{key:"fromJSON",value:function(t){var e=1==t.shape.length?BrushPrototype.fromJSON(t.shape[0]):t.shape.map((function(t){return BrushPrototype.fromJSON(t)}));return new n(t.name,e,t.spacing)}}]),n}(Brush);function Rasterization(){}Rasterization.BlendMode={SOURCE_OVER:"source-over",DESTINATION_OVER:"destination-over",DESTINATION_IN:"destination-in",DESTINATION_OUT:"destination-out",LIGHTER:"lighter",COPY:"copy",MIN:"MIN",MAX:"MAX",DIRECT_SOURCE_OUT:"DIRECT_SOURCE_OUT",DIRECT_DESTINATION_OUT:"DIRECT_DESTINATION_OUT"};var BlendMode=Rasterization.BlendMode;function _createForOfIteratorHelper$7(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=_unsupportedIterableToArray$7(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function _unsupportedIterableToArray$7(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray$7(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$7(t,e):void 0}}function _arrayLikeToArray$7(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var BrushTexture=function(){function t(e,r){_classCallCheck$1(this,t),Object.defineProperty(this,"ctx",{value:e,enumerable:!0}),Object.defineProperty(this,"value",{value:r,enumerable:!0}),Object.defineProperty(this,"texture",{value:r,enumerable:!0})}var e;return _createClass$1(t,[{key:"update",value:(e=_asyncToGenerator(regenerator.mark((function t(e,r){var n,i,o,s,a,l,u;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!Array.isArray(e)){t.next=26;break}n=[],i=_createForOfIteratorHelper$7(e),t.prev=4,i.s();case 6:if((o=i.n()).done){t.next=14;break}return s=o.value,t.next=10,loadImage(s);case 10:a=t.sent,n.push(a);case 12:t.next=6;break;case 14:t.next=19;break;case 16:t.prev=16,t.t0=t.catch(4),i.e(t.t0);case 19:return t.prev=19,i.f(),t.finish(19);case 22:this.completeMipMap(n),this.fill(n,r),t.next=31;break;case 26:return l=e,t.next=29,loadImage(l);case 29:u=t.sent,this.fill(u);case 31:case"end":return t.stop()}}),t,this,[[4,16,19,22]])}))),function(t,r){return e.apply(this,arguments)})},{key:"completeMipMap",value:function(t){if(t.sort((function(t,e){return e.width-t.width})),1!=t.last.width)for(var e=t.last.width;e>1;){e/=2;var r=new OffscreenCanvas(t.last.width/2,t.last.height/2);r.getContext("2d").drawImage(t.last,0,0,r.width,r.height),t.push(r)}}},{key:"fill",value:function(t,e){var r=this,n=this.ctx,i=this.value;if(n.bindTexture(n.TEXTURE_2D,i),n.pixelStorei(n.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0),Array.isArray(t)){var o=t;this.size=[],o.forEach((function(t,e){n.texImage2D(n.TEXTURE_2D,e,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,t),r.size.push({width:t.width,height:t.height}),t.close&&t.close()})),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MIN_FILTER,e?n.LINEAR_MIPMAP_LINEAR:n.LINEAR_MIPMAP_NEAREST),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MAG_FILTER,n.LINEAR)}else n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,t),this.size={width:t.width,height:t.height},t.close&&t.close();n.pixelStorei(n.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),n.bindTexture(n.TEXTURE_2D,null),this.logError(this.ctx,i.name)}},{key:"readPixels",value:function(){var t=this.ctx,e=this.value,r=function(r,n){var i=new Uint8Array(r.width*r.height*4);return t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_2D,e,n),t.readPixels(0,0,r.width,r.height,t.RGBA,t.UNSIGNED_BYTE,i),i},n=t.createFramebuffer();t.bindFramebuffer(t.FRAMEBUFFER,n);var i=Array.isArray(this.size)?this.size.map(r):r(this.size,0);return t.deleteFramebuffer(n),i}},{key:"logError",value:function(){var t=this,e=this.ctx.getError();if(e>0){var r=Object.keys(this.ctx.constructor.prototype).filter((function(r){return t.ctx[r]===e})).join(" | ");console.error("WebGL error - ".concat(this.texture.name,": ").concat(e," - ").concat(r))}}}],[{key:"createInstance",value:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.CLAMP_TO_EDGE,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.NEAREST,i=e.createTexture();return e.bindTexture(e.TEXTURE_2D,i),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,r),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,r),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,n),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,n),e.bindTexture(e.TEXTURE_2D,null),new t(e,i)}}]),t}();function _createForOfIteratorHelper$6(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=_unsupportedIterableToArray$6(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function _unsupportedIterableToArray$6(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray$6(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$6(t,e):void 0}}function _arrayLikeToArray$6(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function _createSuper$7(t){var e=_isNativeReflectConstruct$7();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$7(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}var BrushGL=function(t){_inherits$1(u,t);var e,r,n,i,o,s,a,l=_createSuper$7(u);function u(t,e,r){var n,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};_classCallCheck$1(this,u),(n=l.call(this,t)).spacing=i.spacing||.15,n.scattering=i.scattering||0,n.rotationMode=i.rotationMode||u.RotationMode.RANDOM;var s=i.blendMode||BlendMode.SOURCE_OVER,a=void 0;return Object.defineProperty(_assertThisInitialized$1(n),"blendMode",{get:function(){return s},set:function(t){if(!t)throw new Error("BrushGL blendMode is required");s=t}}),Object.defineProperty(_assertThisInitialized$1(n),"particleSettings",{get:function(){return{spacing:n.spacing,scattering:n.scattering,blendMode:n.blendMode,rotationMode:n.rotationMode}},enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(n),"fillTextureSettings",{get:function(){return{randomize:n.randomizeFill,size:n.fillTextureSize,offset:n.fillTextureOffset}},enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(n),"descriptor",{value:{shape:void 0,fill:void 0},enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(n),"shape",{get:function(){return Array.isArray(n.descriptor.shape)?n.descriptor.shape.map((function(t){return t.value})):n.descriptor.shape.value},enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(n),"fill",{get:function(){return n.descriptor.fill.value},enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(n),"encoding",{get:function(){return a},set:function(t){a=t,Array.isArray(n.descriptor.shape)?n.descriptor.shape.forEach((function(e){return e.encoding=t})):n.descriptor.shape.encoding=t,n.descriptor.fill.encoding=t},enumerable:!0}),n.updateShape(e),n.updateFill(r,o),n}return _createClass$1(u,[{key:"updateShape",value:(a=_asyncToGenerator(regenerator.mark((function t(e){return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(Array.isArray(e)?e=e.map((function(t){return"string"==typeof t?ResourceDescriptor.getInstance(t):t instanceof ResourceDescriptor?t:new ResourceDescriptor(t.name,t.value)})):"string"==typeof e?e=ResourceDescriptor.getInstance(e):e instanceof ResourceDescriptor||(e=new ResourceDescriptor(e.name,e.value)),this.descriptor.shape=e,!this.ctx){t.next=5;break}return t.next=5,this.configureShape();case 5:case"end":return t.stop()}}),t,this)}))),function(t){return a.apply(this,arguments)})},{key:"updateFill",value:(s=_asyncToGenerator(regenerator.mark((function t(e){var r,n=arguments;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=n.length>1&&void 0!==n[1]?n[1]:{},this.randomizeFill=!("randomize"in r)||r.randomize,this.fillTextureSize=r.size,this.fillTextureOffset=r.offset||{x:0,y:0},!Array.isArray(e)){t.next=6;break}throw new Error("Mipmap is not compatible whith fill texture");case 6:if("string"==typeof e?e=ResourceDescriptor.getInstance(e):e instanceof ResourceDescriptor||(e=new ResourceDescriptor(e.name,e.value)),this.descriptor.fill=e,!this.ctx){t.next=11;break}return t.next=11,this.configureFill();case 11:case"end":return t.stop()}}),t,this)}))),function(t){return s.apply(this,arguments)})},{key:"configure",value:(o=_asyncToGenerator(regenerator.mark((function t(e){return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this.ctx=e,t.next=3,this.configureShape();case 3:return t.next=5,this.configureFill();case 5:case"end":return t.stop()}}),t,this)}))),function(t){return o.apply(this,arguments)})},{key:"configureShape",value:(i=_asyncToGenerator(regenerator.mark((function t(){return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this.shapeTexture||(this.shapeTexture=BrushTexture.createInstance(this.ctx,this.ctx.CLAMP_TO_EDGE,this.ctx.LINEAR)),t.next=3,this.shapeTexture.update(this.shape,this.spacing<=1);case 3:case"end":return t.stop()}}),t,this)}))),function(){return i.apply(this,arguments)})},{key:"configureFill",value:(n=_asyncToGenerator(regenerator.mark((function t(){return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this.fillTexture||(this.fillTexture=BrushTexture.createInstance(this.ctx,this.ctx.REPEAT,this.ctx.NEAREST)),t.next=3,this.fillTexture.update(this.fill);case 3:this.fillTextureSize||(this.fillTextureSize=this.fillTexture.size);case 4:case"end":return t.stop()}}),t,this)}))),function(){return n.apply(this,arguments)})},{key:"getShapeBinary",value:(r=_asyncToGenerator(regenerator.mark((function t(){var e,r,n,i,o,s;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!Array.isArray(this.shape)){t.next=24;break}r=[],n=_createForOfIteratorHelper$6(this.shape),t.prev=3,n.s();case 5:if((i=n.n()).done){t.next=13;break}return o=i.value,t.next=9,loadFile(o);case 9:s=t.sent,r.push(s);case 11:t.next=5;break;case 13:t.next=18;break;case 15:t.prev=15,t.t0=t.catch(3),n.e(t.t0);case 18:return t.prev=18,n.f(),t.finish(18);case 21:e=r,t.next=27;break;case 24:return t.next=26,loadFile(this.shape);case 26:e=t.sent;case 27:return t.abrupt("return",e);case 28:case"end":return t.stop()}}),t,this,[[3,15,18,21]])}))),function(){return r.apply(this,arguments)})},{key:"getFillBinary",value:(e=_asyncToGenerator(regenerator.mark((function t(){return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,loadFile(this.fill);case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t,this)}))),function(){return e.apply(this,arguments)})},{key:"toJSON",value:function(){return{type:"BrushGL",name:this.name,shape:Array.isArray(this.descriptor.shape)?this.descriptor.shape.map((function(t){return t.toJSON()})):this.descriptor.shape.toJSON(),fill:this.descriptor.fill.toJSON(),particleSettings:{spacing:this.spacing,scattering:this.scattering,blendMode:this.blendMode.name,rotationMode:this.rotationMode.name},fillTextureSettings:{randomize:this.randomizeFill,size:this.fillTextureSize,offset:this.fillTextureOffset}}}},{key:"equals",value:function(t){return t==this&&t.shapeTexture==this.shapeTexture&&t.fillTexture==this.fillTexture}},{key:"delete",value:function(){this.deleteShape(),this.deleteFill(),delete this.ctx}},{key:"deleteShape",value:function(){this.shapeTexture&&(this.ctx.deleteTexture(this.shapeTexture.texture),delete this.shapeTexture)}},{key:"deleteFill",value:function(){this.fillTexture&&(this.ctx.deleteTexture(this.fillTexture.texture),delete this.fillTexture)}}],[{key:"fromJSON",value:function(t){t.particleSettings.blendMode=BlendMode[t.particleSettings.blendMode],t.particleSettings.rotationMode=u.RotationMode[t.particleSettings.rotationMode];var e=Array.isArray(t.shape)?t.shape.map((function(t){return ResourceDescriptor.fromJSON(t)})):ResourceDescriptor.fromJSON(t.shape),r=ResourceDescriptor.fromJSON(t.fill);return new u(t.name,e,r,t.particleSettings,t.fillTextureSettings)}}]),u}(Brush);function _createForOfIteratorHelper$5(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=_unsupportedIterableToArray$5(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function _unsupportedIterableToArray$5(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray$5(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$5(t,e):void 0}}function _arrayLikeToArray$5(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function _classPrivateFieldInitSpec$3(t,e,r){_checkPrivateRedeclaration$3(t,e),e.set(t,r)}function _checkPrivateRedeclaration$3(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}Object.defineEnum(BrushGL,"RotationMode",["NONE","RANDOM","TRAJECTORY"]);var EXCLUDABLE_PIPELINE_STAGES=[Stage.SMOOTHER,Stage.POLYGON_MERGER,Stage.POLYGON_SIMPLIFIER],LAST_PIPELINE_STAGE=[Stage.SPLINE_PRODUCER,Stage.SPLINE_INTERPOLATOR,Stage.BRUSH_APPLIER,Stage.CONVEX_HULL_CHAIN_PRODUCER,Stage.POLYGON_MERGER,Stage.POLYGON_SIMPLIFIER],_keepAllData=new WeakMap,InkBuilderAbstract=function(){function t(){var e=this;_classCallCheck$1(this,t),_classPrivateFieldInitSpec$3(this,_keepAllData,{writable:!0,value:void 0}),this.layout=[PathPoint.Property.X,PathPoint.Property.Y],this.pathSegment=new PathSegment,this.pathProducer=new PathProducer(this.layout),this.smoother=new Smoother(this.layout.length),this.splineProducer=new SplineProducer(this.layout),this.distanceInterpolator=new DistanceBasedInterpolator,this.curvatureInterpolator=new CurvatureBasedInterpolator,this.brushApplier=new BrushApplier,this.polygonMerger=new PolygonMerger,this.polygonSimplifier=new PolygonSimplifier,this.splineProducer.keepAllData=!0,this.phase=void 0,this.pointerID=void 0,this.concatSegments=!1,this.lastPipelineStage=void 0,this.excludedPipelineStages=[],this.configured=!1,Object.defineProperty(this,"allData",{get:function(){var t={};return e.lastPipelineStage&&(e.smoother.keepAllData&&(t.smootherPoints=e.smoother.allData),e.splineInterpolator.keepAllData&&e.lastPipelineStage.value>Stage.SPLINE_INTERPOLATOR.value&&(t.interpolatedSpline=e.splineInterpolator.allData),e.brushApplier.keepAllData&&e.lastPipelineStage.value>Stage.BRUSH_APPLIER.value&&(t.shapesPath=e.brushApplier.allData),e.convexHullChainProducer.keepAllData&&e.lastPipelineStage.value>Stage.CONVEX_HULL_CHAIN_PRODUCER.value&&(t.convexPath=e.convexHullChainProducer.allData)),t}}),Object.defineProperty(this,"prediction",{get:function(){return e.pathProducer.prediction},set:function(t){return e.pathProducer.prediction=t},enumerable:!0})}return _createClass$1(t,[{key:"configure",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this.reset(this.pointerID),t.onBuildComplete)throw new Error("[InkBuilderSettings] onBuildComplete property is deprecated. Use InkBuilder instance onComplete property to set callback.");if("mergePrediction"in t&&console.warn("[InkBuilderSettings] 'mergePrediction' property is deprecated. Do not affects PolygonMerger behaviour."),!t.brush)throw new Error("[InkBuilderSettings] brush property is required");if(t.excludedPipelineStages){if(!Array.isArray(t.excludedPipelineStages))throw new Error("Expected type of excludedPipelineStages is Array instance");var e=t.excludedPipelineStages.filter((function(t){return!EXCLUDABLE_PIPELINE_STAGES.includes(t)}));e.length>0&&console.warn("[InkBuilderSettings] excludedPipelineStages property includes steps which cannot be excluded: ".concat(e.map((function(t){return t.name})).join(", "))),this.excludedPipelineStages=t.excludedPipelineStages.slice()}if(!this.excludedPipelineStages.includes(Stage.SMOOTHER)&&t.movingAverageWindowSize&&(this.smoother.movingAverageWindowSize=t.movingAverageWindowSize),t.lastPipelineStage){if(!LAST_PIPELINE_STAGE.includes(t.lastPipelineStage))throw new Error("[InkBuilderSettings] lastPipelineStage property expects one of: ".concat(LAST_PIPELINE_STAGE.map((function(t){return t.name})).join(", ")));if(this.excludedPipelineStages.includes(t.lastPipelineStage))throw new Error("[InkBuilderSettings] lastPipelineStage ".concat(t.lastPipelineStage.name," is disabled, check excludedPipelineStages configuration"));if(t.brush instanceof BrushGL&&t.lastPipelineStage!=Stage.SPLINE_INTERPOLATOR)throw new Error("[InkBuilderSettings] lastPipelineStage ".concat(t.lastPipelineStage.name," is not compatible with provided brush"));this.lastPipelineStage=t.lastPipelineStage}switch(this.brush=t.brush,this.brush instanceof Brush2D&&(this.brushApplier.brush=this.brush),this.lastPipelineStage||(this.brush instanceof Brush2D?(this.brush.spacing>1?this.lastPipelineStage=Stage.BRUSH_APPLIER:this.excludedPipelineStages.includes(Stage.POLYGON_SIMPLIFIER)&&this.excludedPipelineStages.includes(Stage.POLYGON_MERGER)?this.lastPipelineStage=Stage.CONVEX_HULL_CHAIN_PRODUCER:this.lastPipelineStage=Stage.POLYGON_MERGER,this.lastPipelineStage==Stage.POLYGON_MERGER&&(this.concatSegments=Boolean(t.concatSegments))):this.lastPipelineStage=Stage.SPLINE_INTERPOLATOR),this.lastPipelineStage==Stage.SPLINE_INTERPOLATOR||this.lastPipelineStage==Stage.BRUSH_APPLIER?(this.splineInterpolator=this.distanceInterpolator,this.splineInterpolator.spacing=this.brush.spacing,this.splineInterpolator.scattering=this.brush.scattering,this.splineInterpolator.calculateDerivates=this.brush instanceof BrushGL):(this.splineInterpolator=this.curvatureInterpolator,this.splineInterpolator.errorThreshold=t.errorThreshold||.15),this.splineInterpolator.keepSplineParameters=!!t.keepSplineParameters,this.splineInterpolator.keepAllData=!1,this.brushApplier.keepAllData=!1,this.convexHullChainProducer.keepAllData=!1,this.polygonMerger.keepAllData=!1,this.polygonSimplifier.keepAllData=!1,this.lastPipelineStage){case Stage.SPLINE_PRODUCER:break;case Stage.SPLINE_INTERPOLATOR:this.splineInterpolator.keepAllData=!0;break;case Stage.BRUSH_APPLIER:this.brushApplier.keepAllData=!0;break;case Stage.CONVEX_HULL_CHAIN_PRODUCER:this.convexHullChainProducer.keepAllData=!0;break;case Stage.POLYGON_MERGER:this.polygonMerger.keepAllData=!0;break;case Stage.POLYGON_SIMPLIFIER:this.polygonSimplifier.keepAllData=!0;break;default:throw console.warn(this.lastPipelineStage),new Error("[InkBuilderSettings] Invalid lastPipelineStage found")}if(this.lastPipelineStage==Stage.POLYGON_SIMPLIFIER&&(console.warn("[InkBuilderSettings] Pipeline stage POLYGON_SIMPLIFIER is deprecated. POLYGON_MERGER stage is recommended as last stage."),this.polygonSimplifier.epsilon=t.epsilon||.1),t.keepAllData){t.keepAllData.includes(this.lastPipelineStage)&&(console.warn("[InkBuilderSettings] keepAllData contains last pipeline stage ".concat(this.lastPipelineStage,". Duplicate is dropped.")),t.keepAllData.remove(this.lastPipelineStage)),t.keepAllData.includes(Stage.PATH_PRODUCER)&&(console.warn("[InkBuilderSettings] keepAllData contains stage ".concat(Stage.PATH_PRODUCER,", sensor input is accessible through InputDevice output - SensorData. Dropped from keepAllData.")),t.keepAllData.remove(Stage.PATH_PRODUCER)),t.keepAllData.includes(Stage.SPLINE_PRODUCER)&&(console.warn("[InkBuilderSettings] keepAllData contains stage ".concat(Stage.SPLINE_PRODUCER,". Use getSpline() method to acceess spline data. Dropped from keepAllData.")),t.keepAllData.remove(Stage.SPLINE_PRODUCER));var r,n=_createForOfIteratorHelper$5(t.keepAllData);try{for(n.s();!(r=n.n()).done;){var i=r.value;if(this.excludedPipelineStages.includes(i))throw new Error("[InkBuilderSettings] keepAllData contains stage ".concat(i,", configured as stage in excludedPipelineStages."));switch(i){case Stage.SMOOTHER:this.smoother.keepAllData=!0;break;case Stage.SPLINE_INTERPOLATOR:this.splineInterpolator.keepAllData=!0;break;case Stage.BRUSH_APPLIER:this.brushApplier.keepAllData=!0;break;case Stage.CONVEX_HULL_CHAIN_PRODUCER:this.convexHullChainProducer.keepAllData=!0;break;default:throw console.warn(i),new Error("Invalid stage found")}}}catch(t){n.e(t)}finally{n.f()}_classPrivateFieldSet(this,_keepAllData,t.keepAllData)}else _classPrivateFieldSet(this,_keepAllData,[]);if(t.pathPointCalculator&&(this.calculator=t.pathPointCalculator,this.pathProducer.pathPointCalculator=t.pathPointCalculator),!t.layout)throw new Error("[InkBuilderSettings] layout property is required");var o=t.pathPointProps||{};if(this.layout=t.layout,this.brush instanceof Brush2D){if(this.layout.includes(PathPoint.Property.RED))throw new Error("RED layout channel is not supported for non particles strokes");if(this.layout.includes(PathPoint.Property.GREEN))throw new Error("GREEN layout channel is not supported for non particles strokes");if(this.layout.includes(PathPoint.Property.BLUE))throw new Error("BLUE layout channel is not supported for non particles strokes");if(this.layout.includes(PathPoint.Property.ALPHA))throw new Error("ALPHA layout channel is not supported for non particles strokes")}if(!this.layout.includes(PathPoint.Property.RED)&&isNaN(o.red))throw new Error("Stroke color red channel information is required. Please provide via layout or through configure settings via pathPointProps property.");if(!this.layout.includes(PathPoint.Property.GREEN)&&isNaN(o.green))throw new Error("Stroke color green channel information is required. Please provide via layout or through configure settings via pathPointProps property.");if(!this.layout.includes(PathPoint.Property.BLUE)&&isNaN(o.blue))throw new Error("Stroke color blue channel information is required. Please provide via layout or through configure settings via pathPointProps property.");if(!this.layout.includes(PathPoint.Property.ALPHA)&&isNaN(o.alpha))throw new Error("Stroke color alpha channel information is required. Please provide via layout or through configure settings via pathPointProps property.");this.pathProducer.layout=this.layout,this.smoother.dimsCount=this.layout.length,this.splineProducer.layout=this.layout,this.splineProducer.pathPointProps=o,this.configured=!0}},{key:"add",value:function(t,e){if(!this.configured)throw new Error("InkBuilder instance is not configured yet, use configure method to configure the instance.");if(!this.calculator)throw new Error("InkBuilder instance is not configured properly, pathPointCalculator property is required");if(!t.phase)throw new Error("SensorPoint phase is not found");this.phase=t.phase;var r,n=new PointerData(t);e&&(this.prediction?r=new PointerData(e):console.warn("Prediction sensor point is available, but ignored, prediction is disabled")),this.device&&(this.phase==PathProducer.Phase.BEGIN&&this.device.openStream(t),this.device.add(n),this.phase==PathProducer.Phase.END&&(this.sensorData=this.device.closeStream()));var i=this.pathProducer.add(this.phase,n,r);this.pathSegment.add(this.phase,i.added,i.predicted)}},{key:"ignore",value:function(t){if(!t.phase)throw new Error("SensorPoint phase is not found");this.device&&t&&t.phase==PathProducer.Phase.UPDATE&&this.device.add(new PointerData(t),!0)}},{key:"build",value:function(){throw new Error("InkBuilderAbstract.build() is abstract and should be implemented")}},{key:"processSegment",value:function(t,e,r){throw new Error("InkBuilderAbstract.processSegment(path, type, lastSegment) is abstract and should be implemented")}},{key:"getSensorData",value:function(){return this.sensorData}},{key:"getSpline",value:function(){return this.splineProducer.allData}},{key:"getAllData",value:function(){if(0!=_classPrivateFieldGet(this,_keepAllData).length){var t,e={},r=_createForOfIteratorHelper$5(_classPrivateFieldGet(this,_keepAllData));try{for(r.s();!(t=r.n()).done;){var n=t.value;switch(n){case Stage.SMOOTHER:e.smoother=this.smoother.allData;break;case Stage.SPLINE_INTERPOLATOR:e.interpolatedSpline=this.splineInterpolator.allData;break;case Stage.BRUSH_APPLIER:e.shapesPath=this.brushApplier.allData;break;case Stage.CONVEX_HULL_CHAIN_PRODUCER:e.convexPath=this.convexHullChainProducer.allData;break;default:throw console.warn(n),new Error("Invalid stage found")}}}catch(t){r.e(t)}finally{r.f()}return e}}},{key:"getInkPath",value:function(){var t,e;switch(this.lastPipelineStage){case Stage.SPLINE_PRODUCER:return void console.warn("Pipeline stage SPLINE_PRODUCER is configured as lastPipelineStage. Ink Path is a result from Spline processing.");case Stage.SPLINE_INTERPOLATOR:t=this.splineInterpolator.allData;break;case Stage.BRUSH_APPLIER:t=this.brushApplier.allData;break;case Stage.CONVEX_HULL_CHAIN_PRODUCER:t=this.convexHullChainProducer.allData;break;case Stage.POLYGON_MERGER:t=this.polygonMerger.allData;break;case Stage.POLYGON_SIMPLIFIER:t=this.polygonSimplifier.allData;break;default:throw console.warn(this.lastPipelineStage),new Error("Invalid lastPipelineStage found")}this.concatSegments&&(this.lastPipelineStage!=Stage.POLYGON_MERGER&&this.lastPipelineStage!=Stage.POLYGON_SIMPLIFIER||(e=this.polygonMerger.process(t)),this.lastPipelineStage==Stage.POLYGON_SIMPLIFIER&&(e=this.polygonSimplifier.process(e)),e&&(t=new InkPath2D(e)));return t}},{key:"abort",value:function(){this.device&&this.device.closeStream(!0),this.reset()}},{key:"reset",value:function(t){this.pointerID=t,this.phase=void 0,this.concatSegments=!1,this.lastPipelineStage=void 0,this.excludedPipelineStages.clear(),this.sensorData=void 0,this.pathProducer.reset(),this.smoother.reset(),this.splineProducer.reset(),this.distanceInterpolator.reset(),this.curvatureInterpolator.reset(),this.brushApplier.reset(),this.convexHullChainProducer.reset(),this.polygonMerger.reset(),this.polygonSimplifier.reset(),this.configured=!1}}]),t}();function _createSuper$6(t){var e=_isNativeReflectConstruct$6();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$6(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}InkBuilderAbstract.Phase=PathProducer.Phase;var InkBuilder=function(t){_inherits$1(r,t);var e=_createSuper$6(r);function r(){var t;return _classCallCheck$1(this,r),(t=e.call(this)).convexHullChainProducer=new ConvexHullChainProducer,t}return _createClass$1(r,[{key:"build",value:function(){var t=this.buildSegment();return t.phase=this.phase,t.pointerID=this.pointerID,this.onComplete&&this.onComplete(t),this.phase==Phase.END&&(delete this.phase,delete this.pipeline),t}},{key:"buildSegment",value:function(){var t={};return this.pathSegment.accumulatedAddition.length>0&&(t.added=this.processSegment(this.pathSegment.accumulatedAddition,OutputType.ADDITION,this.pathSegment.last),t.pipeline=this.pipeline,t.added&&(t.added.segment=!0)),this.prediction&&this.pathSegment.lastPrediction.length>0&&(t.predicted=this.processSegment(this.pathSegment.lastPrediction,OutputType.PREDICTION,this.pathSegment.last),t.predicted&&(t.predicted.segment=!0)),this.pathSegment.reset(),t}},{key:"processSegment",value:function(t,e,r){if(this.excludedPipelineStages.includes(Stage.SMOOTHER)||(t=this.smoother.process(t,e,r)),t=this.splineProducer.process(t,e,r))return this.lastPipelineStage==Stage.SPLINE_PRODUCER?t:this.processSpline(t,e,r)}},{key:"processSpline",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:OutputType.PROCESSOR,r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];e==OutputType.ADDITION&&(this.pipeline={}),e==OutputType.ADDITION&&(this.pipeline.spline=t);var n,i=this.splineInterpolator.process(t,e,r);if(this.lastPipelineStage==Stage.SPLINE_INTERPOLATOR)return i;if(i)return e==OutputType.ADDITION&&(this.pipeline.interpolatedSpline=i),i=this.brushApplier.process(i,e,r),this.lastPipelineStage==Stage.BRUSH_APPLIER?i:(e==OutputType.ADDITION&&(this.pipeline.shapesPath=i),i=this.convexHullChainProducer.process(i,e,r),this.lastPipelineStage==Stage.CONVEX_HULL_CHAIN_PRODUCER?i:(e==OutputType.ADDITION&&(this.pipeline.convexPath=i),e==OutputType.PREDICTION?i:this.excludedPipelineStages.includes(Stage.POLYGON_MERGER)||(n=this.polygonMerger.process(i,e,r),this.lastPipelineStage!=Stage.POLYGON_MERGER)?(this.excludedPipelineStages.includes(Stage.POLYGON_SIMPLIFIER)||(n=this.polygonSimplifier.process(n,e,r)),new InkPath2D(n)):new InkPath2D(n)));if(e==OutputType.PROCESSOR)throw new Error("InkBuilder processSpline failed for spline",t)}}]),r}(InkBuilderAbstract),PromiseQueue=function(){function t(){_classCallCheck$1(this,t),this.queue=Promise.resolve(),this.thenables=[]}var e;return _createClass$1(t,[{key:"then",value:function(t,e,r){var n=this;return this.thenables.push(t),this.queue=this.queue.then((function(){return n.thenables.shift(),t.canceled?Promise.resolve():t.apply(void 0,arguments)})),e&&this.then((function(t){return e(t,r)})),this}},{key:"catch",value:function(t){return this.queue=this.queue.catch(t),this}},{key:"cancel",value:function(){this.thenables.forEach((function(t){return t.canceled=!0}))}},{key:"isEmpty",value:function(){return 0==this.thenables.length}}],[{key:"serial",value:(e=_asyncToGenerator(regenerator.mark((function e(r,n){var i;return regenerator.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=new t,r.forEach((function(t,e){return i.then(t,n,e)})),e.abrupt("return",i.queue);case 3:case"end":return e.stop()}}),e)}))),function(t,r){return e.apply(this,arguments)})}]),t}();function _createSuper$5(t){var e=_isNativeReflectConstruct$5();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$5(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}var InkBuilderAsync=function(t){_inherits$1(a,t);var e,r,n,i,o,s=_createSuper$5(a);function a(){var t;return _classCallCheck$1(this,a),(t=s.call(this)).convexHullChainProducer=new ConvexHullChainProducerAsync,Object.defineProperty(_assertThisInitialized$1(t),"closed",{get:function(){return t.convexHullChainProducer.closed},enumerable:!0}),t.queue=new PromiseQueue,t}return _createClass$1(a,[{key:"open",value:(o=_asyncToGenerator(regenerator.mark((function t(){return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.convexHullChainProducer.open();case 2:case"end":return t.stop()}}),t,this)}))),function(){return o.apply(this,arguments)})},{key:"close",value:function(){this.convexHullChainProducer.close()}},{key:"onComplete",value:function(t){throw new Error("InkBuilderAbstract.onComplete(pathSegment) is abstract and should be implemented")}},{key:"build",value:function(){var t=this;if(!this.buildPhase||this.phase==Phase.END){var e=this.phase;this.queue.then((function(){return t.buildPhase=e,t.buildSegment()})).then((function(r){t.buildPhase=null,r.phase=e,r.pointerID=t.pointerID,t.onComplete(r),e==Phase.END&&(delete t.phase,delete t.pipeline)}))}}},{key:"buildChain",value:(i=_asyncToGenerator(regenerator.mark((function t(){var e;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.buildSegment();case 2:return(e=t.sent).phase=this.phase,e.pointerID=this.pointerID,this.onComplete(e),this.phase==Phase.END&&delete this.phase,t.abrupt("return",e);case 8:case"end":return t.stop()}}),t,this)}))),function(){return i.apply(this,arguments)})},{key:"buildSegment",value:(n=_asyncToGenerator(regenerator.mark((function t(){var e;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e={},!(this.pathSegment.accumulatedAddition.length>0)){t.next=7;break}return t.next=4,this.processSegment(this.pathSegment.accumulatedAddition,OutputType.ADDITION,this.pathSegment.last);case 4:e.added=t.sent,e.pipeline=this.pipeline,e.added&&(e.added.segment=!0);case 7:if(!(this.prediction&&this.pathSegment.lastPrediction.length>0)){t.next=12;break}return t.next=10,this.processSegment(this.pathSegment.lastPrediction,OutputType.PREDICTION,this.pathSegment.last);case 10:e.predicted=t.sent,e.predicted&&(e.predicted.segment=!0);case 12:return this.pathSegment.reset(),t.abrupt("return",e);case 14:case"end":return t.stop()}}),t,this)}))),function(){return n.apply(this,arguments)})},{key:"processSegment",value:(r=_asyncToGenerator(regenerator.mark((function t(e,r,n){return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.excludedPipelineStages.includes(Stage.SMOOTHER)||(e=this.smoother.process(e,r,n)),e=this.splineProducer.process(e,r,n)){t.next=4;break}return t.abrupt("return");case 4:if(this.lastPipelineStage!=Stage.SPLINE_PRODUCER){t.next=6;break}return t.abrupt("return",e);case 6:return t.abrupt("return",this.processSpline(e,r,n));case 7:case"end":return t.stop()}}),t,this)}))),function(t,e,n){return r.apply(this,arguments)})},{key:"processSpline",value:(e=_asyncToGenerator(regenerator.mark((function t(e){var r,n,i,o,s=arguments;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=s.length>1&&void 0!==s[1]?s[1]:OutputType.PROCESSOR,n=!(s.length>2&&void 0!==s[2])||s[2],r==OutputType.ADDITION&&(this.pipeline={}),r==OutputType.ADDITION&&(this.pipeline.spline=e),i=this.splineInterpolator.process(e,r,n),this.lastPipelineStage!=Stage.SPLINE_INTERPOLATOR){t.next=7;break}return t.abrupt("return",i);case 7:if(i){t.next=11;break}if(r!=OutputType.PROCESSOR){t.next=10;break}throw new Error("InkBuilderAsync processSpline failed for spline",e);case 10:return t.abrupt("return");case 11:if(r==OutputType.ADDITION&&(this.pipeline.interpolatedSpline=i),i=this.brushApplier.process(i,r,n),this.lastPipelineStage!=Stage.BRUSH_APPLIER){t.next=15;break}return t.abrupt("return",i);case 15:return r==OutputType.ADDITION&&(this.pipeline.shapesPath=i),t.next=18,this.convexHullChainProducer.process(i,r,n);case 18:if(i=t.sent,this.lastPipelineStage!=Stage.CONVEX_HULL_CHAIN_PRODUCER){t.next=21;break}return t.abrupt("return",i);case 21:if(r==OutputType.ADDITION&&(this.pipeline.convexPath=i),r!=OutputType.PREDICTION){t.next=24;break}return t.abrupt("return",i);case 24:if(this.excludedPipelineStages.includes(Stage.POLYGON_MERGER)){t.next=28;break}if(o=this.polygonMerger.process(i,r,n),this.lastPipelineStage!=Stage.POLYGON_MERGER){t.next=28;break}return t.abrupt("return",new InkPath2D(o));case 28:return this.excludedPipelineStages.includes(Stage.POLYGON_SIMPLIFIER)||(o=this.polygonSimplifier.process(o,r,n)),t.abrupt("return",new InkPath2D(o));case 30:case"end":return t.stop()}}),t,this)}))),function(t){return e.apply(this,arguments)})},{key:"abort",value:function(){this.buildPhase=null,this.queue.cancel(),_get(_getPrototypeOf$1(a.prototype),"abort",this).call(this)}}]),a}(InkBuilderAbstract);function _createForOfIteratorHelper$4(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=_unsupportedIterableToArray$4(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function _unsupportedIterableToArray$4(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray$4(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$4(t,e):void 0}}function _arrayLikeToArray$4(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var PRECISIONS=["position","size","rotation","scale","offset"],SHIFT_BITS={position:0,size:4,rotation:8,scale:12,offset:16},PrecisionSchema=function(){function t(){var e=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;_classCallCheck$1(this,t),this.precisions=r;var n,i=_createForOfIteratorHelper$4(PRECISIONS);try{var o=function(){var t=n.value;Object.defineProperty(e,t,{get:function(){return e.get(t)},set:function(r){return e.set(t,r)},enumerable:!0})};for(i.s();!(n=i.n()).done;)o()}catch(t){i.e(t)}finally{i.f()}Object.defineProperty(this,"factors",{get:function(){return Object.assign.apply(Object,[{}].concat(_toConsumableArray(PRECISIONS.map((function(t){return _defineProperty({},t,Math.pow(10,e[t]))})))))},enumerable:!0})}return _createClass$1(t,[{key:"get",value:function(t){return this.precisions>>SHIFT_BITS[t]&15}},{key:"set",value:function(t,e){if(e>15||e<0)throw new Error("Invalid '".concat(t,"' precision value ").concat(e," found. The value must be in the interval [0, 15]."));if(e>this[t])throw new Error("PrecisionSchema '".concat(t,"' update failed. Update value ").concat(e," > ").concat(this[t]," - update value should be less than current value."));e!=this[t]&&(this.precisions=this.precisions&~(15<<SHIFT_BITS[t])|e<<SHIFT_BITS[t])}},{key:"update",value:function(t){var e=this;PRECISIONS.forEach((function(r){var n=t[r];n<e[r]&&(e[r]=n)}))}},{key:"decode",value:function(){var t=this;return Object.assign.apply(Object,[{}].concat(_toConsumableArray(PRECISIONS.map((function(e){return _defineProperty({},e,t[e])})))))}}],[{key:"encode",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=0;return PRECISIONS.forEach((function(r){var n=t[r]||0;if(n>15||n<0)throw new Error("Invalid '".concat(r,"' precision value ").concat(n," found. The value must be in the interval [0, 15]."));e=e&~(15<<SHIFT_BITS[r])|n<<SHIFT_BITS[r]})),e}}]),t}(),URIBuilder=function(){function t(){_classCallCheck$1(this,t)}return _createClass$1(t,null,[{key:"createTreeURI",value:function(t,e){return"uim:tree/".concat(e?"".concat(e,"/"):"").concat(t)}},{key:"createStrokeURI",value:function(t,e){return"uim:stroke/".concat(e?"".concat(e,"/"):"").concat(t)}},{key:"createSensorDataURI",value:function(t,e){return"uim:sensor/".concat(e?"".concat(e,"/"):"").concat(t)}},{key:"createNodeURI",value:function(e,r,n){if(!e)throw new Error("inkTree is required");var i="";return n&&(i="#frag=".concat(n.pointIndexStart,",").concat(n.pointIndexEnd),0==n.ts&&1==n.tf||(i+=",".concat(n.ts.toFixed(5),",").concat(n.tf.toFixed(5)))),"".concat(t.createNodeURISchema(e),"/").concat(r).concat(i)}},{key:"createNodeURISchema",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t.name;return"uim:node".concat(t.id?"/".concat(t.id):"","/").concat(e)}},{key:"createNamedEntityURI",value:function(t,e){return"uim:ne/".concat(e?"".concat(e,"/"):"").concat(t)}}]),t}();function _createForOfIteratorHelper$3(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=_unsupportedIterableToArray$3(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function _unsupportedIterableToArray$3(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray$3(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$3(t,e):void 0}}function _arrayLikeToArray$3(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function _classPrivateFieldInitSpec$2(t,e,r){_checkPrivateRedeclaration$2(t,e),e.set(t,r)}function _checkPrivateRedeclaration$2(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}var SEPARATOR="\n",_id$1=new WeakMap,Identifiable=function(){function t(e){var r,n=this;if(_classCallCheck$1(this,t),_classPrivateFieldInitSpec$2(this,_id$1,{writable:!0,value:void 0}),_classPrivateFieldSet(this,_id$1,e),"function"==typeof this.getMD5Message)r=t.Algorithm.MD5;else if("function"==typeof this.buildURI)r=t.Algorithm.URI;else if(r=t.Algorithm.GUID,e&&!uuid.validate(e))throw new Error("Identifiable ".concat(e," is not a well formed UUID"));Object.defineProperty(this,"algorithm",{value:r}),Object.defineProperty(this,"id",{get:function(){return _classPrivateFieldGet(n,_id$1)||_classPrivateFieldSet(n,_id$1,n.resolveID()),_classPrivateFieldGet(n,_id$1)},set:function(t){if(_classPrivateFieldGet(n,_id$1))throw new Error("id is immutable");_classPrivateFieldSet(n,_id$1,t)},enumerable:!0})}return _createClass$1(t,[{key:"invalidateID",value:function(){if(this.algorithm!=t.Algorithm.MD5)throw new Error("Invalidate id is not applicable for ".concat(this.algorithm," algorithm"));_classPrivateFieldSet(this,_id$1,void 0)}},{key:"resolveID",value:function(){if(this.algorithm==t.Algorithm.MD5){var e,r="",n=_createForOfIteratorHelper$3(this.getMD5Message());try{for(n.s();!(e=n.n()).done;){var i=e.value;if(Array.isArray(i)){var o,s=_createForOfIteratorHelper$3(i);try{for(s.s();!(o=s.n()).done;){r+=o.value,r+=SEPARATOR}}catch(t){s.e(t)}finally{s.f()}}else r+=i;r+=SEPARATOR}}catch(t){n.e(t)}finally{n.f()}if(!r)throw new Error("Empty MD5 message container found");return shim$1(r)}return this.algorithm==t.Algorithm.URI?this.buildURI():uuid.generate()}}],[{key:"SEPARATOR",get:function(){return SEPARATOR}},{key:"buildMD5Tokens",value:function(t){var e=[];return Object.keys(t).sort().forEach((function(r){return e.push(r,t[r])})),e}}]),t}(),inkBuilder,inkBuilderAsync;function _createSuper$4(t){var e=_isNativeReflectConstruct$4();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$4(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function _classPrivateFieldInitSpec$1(t,e,r){_checkPrivateRedeclaration$1(t,e),e.set(t,r)}function _checkPrivateRedeclaration$1(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}Object.defineEnum(Identifiable,"Algorithm",["GUID","MD5","URI"]);var _uri=new WeakMap,_path=new WeakMap,_bounds=new WeakMap,_sensorData=new WeakMap,_randomSeed=new WeakMap,_renderMode=new WeakMap,_precisionSchema=new WeakMap,_compressionType=new WeakMap,Stroke=function(t){_inherits$1(n,t);var e,r=_createSuper$4(n);function n(t,e,i,o){var s;_classCallCheck$1(this,n),_classPrivateFieldInitSpec$1(_assertThisInitialized$1(s=r.call(this,e.id)),_uri,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$1(_assertThisInitialized$1(s),_path,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$1(_assertThisInitialized$1(s),_bounds,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$1(_assertThisInitialized$1(s),_sensorData,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$1(_assertThisInitialized$1(s),_randomSeed,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$1(_assertThisInitialized$1(s),_renderMode,{writable:!0,value:n.RenderMode.SOURCE_OVER}),_classPrivateFieldInitSpec$1(_assertThisInitialized$1(s),_precisionSchema,{writable:!0,value:void 0}),_classPrivateFieldInitSpec$1(_assertThisInitialized$1(s),_compressionType,{writable:!0,value:n.CompressionType.AUTO}),e.id||(e.id=s.id),_classPrivateFieldSet(_assertThisInitialized$1(s),_sensorData,o);var a=new InkStyle(e.layout,e.pointProps);Object.defineProperty(_assertThisInitialized$1(s),"target",{get:function(){return console.warn("Stroke 'target' property is deprecated. Do not affects Stroke behaviour.")},set:function(t){return console.warn("Stroke 'target' property is deprecated. Do not affects Stroke behaviour.")}});var l=!0;return Object.defineProperty(_assertThisInitialized$1(s),"layout",{value:e.layout,enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(s),"points",{get:function(){return e.points},enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(s),"pointProps",{value:e.pointProps,enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(s),"style",{value:a.style,enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(s),"ts",{value:e.ts,enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(s),"tf",{value:e.tf,enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(s),"stride",{value:e.stride,enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(s),"length",{value:e.length,enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(s),"segmentsCount",{value:e.segmentsCount,enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(s),"color",{get:function(){return a.style.color},set:function(t){e.color=t,_classPrivateFieldGet(_assertThisInitialized$1(s),_path)&&(_classPrivateFieldGet(_assertThisInitialized$1(s),_path).color=t)},enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(s),"sensorData",{get:function(){return _classPrivateFieldGet(_assertThisInitialized$1(s),_sensorData)},set:function(t){if(_classPrivateFieldGet(_assertThisInitialized$1(s),_sensorData))throw new Error("sensorData is immutable");_classPrivateFieldSet(_assertThisInitialized$1(s),_sensorData,t)}}),Object.defineProperty(_assertThisInitialized$1(s),"spline",{value:e,enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(s),"path",{get:function(){return _classPrivateFieldGet(_assertThisInitialized$1(s),_path)||s.buildPath(),_classPrivateFieldGet(_assertThisInitialized$1(s),_path)},set:function(t){_classPrivateFieldSet(_assertThisInitialized$1(s),_path,t),_classPrivateFieldGet(_assertThisInitialized$1(s),_path)instanceof InterpolatedSpline&&(_classPrivateFieldGet(_assertThisInitialized$1(s),_path).style=a.style),_classPrivateFieldSet(_assertThisInitialized$1(s),_bounds,null)},enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(s),"bounds",{get:function(){return _classPrivateFieldGet(_assertThisInitialized$1(s),_bounds)||_classPrivateFieldSet(_assertThisInitialized$1(s),_bounds,s.path.bounds),s.matrix?_classPrivateFieldGet(_assertThisInitialized$1(s),_bounds).transform(s.matrix).ceil():_classPrivateFieldGet(_assertThisInitialized$1(s),_bounds)},set:function(t){return _classPrivateFieldSet(_assertThisInitialized$1(s),_bounds,t)},enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(s),"descriptor",{value:{brush:{}},enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(s),"brush",{get:function(){return t||(t=s.descriptor.brush.value),t},set:function(r){if(l||(s.path=null),"string"==typeof r?r=new ResourceDescriptor(r):r instanceof Brush2D||r instanceof BrushGL?r=new ResourceDescriptor(r.name,r):r instanceof ResourceDescriptor||(r=new ResourceDescriptor(r.name,r.value)),r instanceof BrushGL&&!e.randomSeed)throw new Error("Spline do not provides randomSeed. Raster rendering requires it.");t=null,s.descriptor.brush=r},enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(s),"randomSeed",{get:function(){return _classPrivateFieldGet(_assertThisInitialized$1(s),_randomSeed)},set:function(t){if(_classPrivateFieldGet(_assertThisInitialized$1(s),_randomSeed))throw new Error("randomSeed is immutable");_classPrivateFieldSet(_assertThisInitialized$1(s),_randomSeed,t)},enumerable:!0}),Object.defineProperty(_assertThisInitialized$1(s),"renderMode",{get:function(){return _classPrivateFieldGet(_assertThisInitialized$1(s),_renderMode)},set:function(t){if(!t)throw new Error("Stroke renderMode is required");if(!utils.isValidURL(t))throw new Error("The renderMode ".concat(t," is not a well formed URI"));_classPrivateFieldSet(_assertThisInitialized$1(s),_renderMode,t)}}),Object.defineProperty(_assertThisInitialized$1(s),"blendMode",{get:function(){return n.RenderMode.getBlendMode(_classPrivateFieldGet(_assertThisInitialized$1(s),_renderMode))},set:function(t){if(!s.blendMode)throw new Error("Override user defined renderMode '".concat(_classPrivateFieldGet(_assertThisInitialized$1(s),_renderMode),"' is not allowed."));_classPrivateFieldSet(_assertThisInitialized$1(s),_renderMode,n.RenderMode.get(t))}}),Object.defineProperty(_assertThisInitialized$1(s),"precisionSchema",{get:function(){return _classPrivateFieldGet(_assertThisInitialized$1(s),_precisionSchema)},set:function(t){if(_classPrivateFieldGet(_assertThisInitialized$1(s),_precisionSchema))throw new Error("precisionSchema is immutable, precisionSchema.update(schema) is an alternative");if(t){if(!(t instanceof PrecisionSchema))throw new Error("Expected precisionSchema type is PrecisionSchema");_classPrivateFieldSet(_assertThisInitialized$1(s),_precisionSchema,t),_classPrivateFieldSet(_assertThisInitialized$1(s),_compressionType,n.CompressionType.COMPUTED)}else _classPrivateFieldSet(_assertThisInitialized$1(s),_compressionType,n.CompressionType.NONE)}}),Object.defineProperty(_assertThisInitialized$1(s),"compressionType",{get:function(){return _classPrivateFieldGet(_assertThisInitialized$1(s),_compressionType)},set:function(t){if(!t)throw new Error("Stroke compressionType is required");if(_classPrivateFieldGet(_assertThisInitialized$1(s),_compressionType)==n.CompressionType.COMPUTED&&t==n.CompressionType.NONE)throw new Error("compressionType NONE is not applicable for compressed stroke");_classPrivateFieldSet(_assertThisInitialized$1(s),_compressionType,t)}}),Object.defineProperty(_assertThisInitialized$1(s),"uri",{get:function(){return _classPrivateFieldGet(_assertThisInitialized$1(s),_uri)||_classPrivateFieldSet(_assertThisInitialized$1(s),_uri,URIBuilder.createStrokeURI(s.id)),_classPrivateFieldGet(_assertThisInitialized$1(s),_uri)},enumerable:!0}),s.brush=t,s.path=i,s.sensorDataOffset=0,s.sensorDataMapping=[],l=!1,s}return _createClass$1(n,[{key:"buildPath",value:function(){if(this.pathProceessInProgress)throw new Error("Init process in progress. Await init stroke.");inkBuilder||(inkBuilder=new InkBuilder),inkBuilder.configure(this.buildInkBuilderSettings()),this.path=inkBuilder.processSpline(this.spline)}},{key:"init",value:(e=_asyncToGenerator(regenerator.mark((function t(e){var r,n,i,o;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.pathProceessInProgress=!0,inkBuilderAsync){t.next=6;break}if(!(inkBuilderAsync=new InkBuilderAsync).closed){t.next=6;break}return t.next=6,inkBuilderAsync.open();case 6:return inkBuilderAsync.configure(this.buildInkBuilderSettings(e)),t.next=9,inkBuilderAsync.processSpline(this.spline);case 9:this.path=t.sent,Object.keys(inkBuilderAsync.allData).length>0&&(r=inkBuilderAsync.allData,n=r.interpolatedSpline,i=r.shapesPath,o=r.convexPath,this.pipeline={},n&&(this.pipeline.interpolatedSpline=n),i&&(this.pipeline.shapesPath=i),o&&(this.pipeline.convexPath=o)),delete this.pathProceessInProgress;case 12:case"end":return t.stop()}}),t,this)}))),function(t){return e.apply(this,arguments)})},{key:"buildInkBuilderSettings",value:function(t){return n.onPipeline?n.onPipeline(this):Object.assign({},{brush:this.brush,layout:this.layout,pathPointProps:this.pointProps},t)}},{key:"invalidateBounds",value:function(){_classPrivateFieldSet(this,_bounds,null)}},{key:"clone",value:function(){var t,e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];_classPrivateFieldGet(this,_path)&&(t=e?_classPrivateFieldGet(this,_path):_classPrivateFieldGet(this,_path).clone());var i=this.spline.clone();r&&(i.id=this.id);var o=new n(this.descriptor.brush,i,t,this.sensorData);return o.randomSeed=_classPrivateFieldGet(this,_randomSeed),o.renderMode=_classPrivateFieldGet(this,_renderMode),o.sensorDataOffset=this.sensorDataOffset,o.sensorDataMapping=this.sensorDataMapping.clone(),o}},{key:"getSensorPoint",value:function(t){if(t>=this.length||t<0)throw new Error("Index ".concat(t," out of range - (0, ").concat(this.length-1,")"));var e;if(0==this.sensorDataOffset&&t>0&&t--,this.sensorData){var r,n=this.sensorData.inkStream;if(n)this.sensorDataMapping.length>0?r=t>=this.sensorDataMapping.length?this.sensorDataMapping.last:this.sensorDataMapping[t]:(r=this.sensorDataOffset+t)>=n.length&&(r=n.length-1),(e=n.get(r)).index=r,e.timespan=e.timestamp,e.timestamp+=this.sensorData.created}return e}},{key:"getPoint",value:function(t){return this.spline.getPoint(t)}},{key:"setPoint",value:function(t,e){var r=this,n=t*this.stride;this.layout.forEach((function(t,i){return r.points[n+i]=e.getProperty(t)}))}},{key:"pointAt",value:function(t){return this.getPoint(t)}},{key:"getSegment",value:function(t){return this.spline.getSegment(t)}},{key:"getAverageWidth",value:function(){var t=0;if(this.layout.includes(PathPoint.Property.SIZE)){for(var e=0,r=0;r<this.length;r++)e+=this.getPointRef(r).size;t=e/this.length}else t=this.pointProps.size;return t}},{key:"split",value:function(t){var e=this,r=t.map((function(t){return e.slice(t)}));return r.includes(this)?void 0:r}},{key:"slice",value:function(t){if(0==t.pointIndexStart&&t.pointIndexEnd+1==this.length&&t.ts==this.ts&&t.tf==this.tf)return this;var e=this.spline.slice(t),r=new n(this.descriptor.brush,e,void 0,this.sensorData);if(r.randomSeed=_classPrivateFieldGet(this,_randomSeed),r.renderMode=_classPrivateFieldGet(this,_renderMode),this.sensorData){var i,o=t.pointIndexStart;if(0==this.sensorDataOffset&&t.pointIndexStart>0&&(o=t.pointIndexStart-1),r.sensorDataOffset=this.sensorDataOffset+o,this.sensorDataMapping.length>0)i=t.pointIndexEnd>this.sensorDataMapping.length?this.sensorDataMapping.length:0==r.sensorDataOffset?t.pointIndexEnd:t.pointIndexEnd+1,r.sensorDataMapping=this.sensorDataMapping.slice(o,i);else r.sensorDataMapping=[]}return r}},{key:"transform",value:function(t){if(t||(t=this.matrix,this.matrix=null),t){if(this.spline.transform(t),_classPrivateFieldGet(this,_path)&&this.path.transform(t),this.pipeline){var e=this.pipeline,r=e.interpolatedSpline,i=e.shapesPath,o=e.convexPath;r&&r.transform(t),i&&i.transform(t),o&&o.transform(t)}_classPrivateFieldGet(this,_compressionType)!=n.CompressionType.NONE&&(_classPrivateFieldSet(this,_precisionSchema,void 0),_classPrivateFieldSet(this,_compressionType,n.CompressionType.AUTO)),_classPrivateFieldSet(this,_bounds,null)}}},{key:"setTransform",value:function(t){this.matrix=t}}],[{key:"createInstance",value:function(t,e,r){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=arguments.length>4?arguments[4]:void 0,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0,a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:1,l=i.id,u=i.color;l&&delete i.id,u&&(delete i.color,i=Object.assign({},i),r.includes(PathPoint.Property.RED)||(i.red=u.red),r.includes(PathPoint.Property.GREEN)||(i.green=u.green),r.includes(PathPoint.Property.BLUE)||(i.blue=u.blue),r.includes(PathPoint.Property.ALPHA)||(i.alpha=u.alpha));var h=Spline.createSharedInstance(r,e,i,s,a);h.id=l;var c=new n(t,h);return c.randomSeed=o,c}},{key:"validatePath",value:function(t){if(!t)return!1;if(0==t.length)return!1;if(t instanceof InkPath2D)return!0;if(Array.isArray(t))throw new Error("path should be instance of InkPath2D");var e=!1,r=0,n=!1,i=t.pointProps,o=i.size,s=i.red,a=i.green,l=i.blue,u=i.alpha;if(!(t instanceof InterpolatedSpline)){var h=t.points.length,c=t.layout.length;n=0==h||h<4*c,r=h%c}return 0!=r?console.error("The points array (length: ".concat(t.points.length,") does not refer to provided layout (").concat(t.layout.map((function(t){return t.name})).join(", "),")")):n?console.error("Less than needed minimum of points passed (At least 4 points are needed to define a path)!"):!t.layout.includes(PathPoint.Property.SIZE)&&isNaN(o)?console.error("Either the size property must be set or the path layout must include a SIZE property"):!t.layout.includes(PathPoint.Property.RED)&&isNaN(s)?console.error("Either the color property must be set or the path layout must include a RED property"):!t.layout.includes(PathPoint.Property.GREEN)&&isNaN(a)?console.error("Either the color property must be set or the path layout must include a GREEN property"):!t.layout.includes(PathPoint.Property.BLUE)&&isNaN(l)?console.error("Either the color property must be set or the path layout must include a BLUE property"):!t.layout.includes(PathPoint.Property.ALPHA)&&isNaN(u)?console.error("Either the color property must be set or the path layout must include a ALPHA property"):e=!0,e}},{key:"decodeInkPath",value:function(t){if("InkPath2D"==t.type)return InkPath2D.fromJSON(t);if("InterpolatedSpline"==t.type)return InterpolatedSpline.fromJSON(t);throw new Error("Decode ink path faild. Cannot identify type: ".concat(t.type))}}]),n}(Identifiable);function _classPrivateFieldInitSpec(t,e,r){_checkPrivateRedeclaration(t,e),e.set(t,r)}function _checkPrivateRedeclaration(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}Stroke.RenderMode=Object.assign.apply(Object,[{}].concat(_toConsumableArray(Object.keys(BlendMode).map((function(t){return _defineProperty({},t,"will://rasterization/3.0/blend-mode/".concat(utils.getPropName(t,!0)))}))))),Stroke.RenderMode.get=function(t){return Stroke.RenderMode[utils.getEnumValueName(t.replace(/-/g,"_"))]},Stroke.RenderMode.getBlendMode=function(t){return BlendMode[Object.keys(Stroke.RenderMode).filter((function(e){return Stroke.RenderMode[e]==t})).first]},Stroke.Target={},Object.defineProperty(Stroke.Target,"2D",{get:function(){return console.warn("Stroke 'Target[2D]' enum is deprecated")}}),Object.defineProperty(Stroke.Target,"GL",{get:function(){return console.warn("Stroke 'Target[GL]' enum is deprecated")}}),Object.defineEnum(Stroke,"CompressionType",["AUTO","NONE","COMPUTED"]);var _id=new WeakMap,RNode=function(){function t(e,r,n,i,o,s,a,l){var u=this;_classCallCheck$1(this,t),_classPrivateFieldInitSpec(this,_id,{writable:!0,value:void 0}),this.strokeID=e,r<i&&1==n&&(r++,n=0),i>r&&0==o&&(i--,o=1),this.segmentIndexStart=r,this.segmentIndexEnd=i,this.ts=n,this.tf=o,Object.defineProperty(this,"id",{get:function(){return _classPrivateFieldGet(u,_id)||_classPrivateFieldSet(u,_id,"".concat(e,"::").concat(r,"-").concat(i,"::").concat(n.toFixed(5),"-").concat(o.toFixed(5))),_classPrivateFieldGet(u,_id)}}),this.bounds=s,this.shapesPath=l,this.splineParameters=a}return _createClass$1(t,[{key:"toString",value:function(){return"node[".concat(this.strokeID,"](").concat(this.segmentIndexStart,", ").concat(this.ts,", ").concat(this.segmentIndexEnd,", ").concat(this.tf,") - ").concat(this.bounds.toString())}},{key:"toJSON",value:function(){return{strokeID:this.strokeID,segmentIndexStart:this.segmentIndexStart,segmentIndexEnd:this.segmentIndexEnd,ts:this.ts,tf:this.tf}}}],[{key:"fromJSON",value:function(e,r,n,i){return new t(e.strokeID,e.segmentIndexStart,e.ts,e.segmentIndexEnd,e.tf,r,n,i)}}]),t}();function _createForOfIteratorHelper$2(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=_unsupportedIterableToArray$2(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function _unsupportedIterableToArray$2(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray$2(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$2(t,e):void 0}}function _arrayLikeToArray$2(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function _createSuper$3(t){var e=_isNativeReflectConstruct$3();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$3(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}var Intersector=function(t){_inherits$1(i,t);var e,r,n=_createSuper$3(i);function i(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i.Mode.WHOLE_STROKE;return _classCallCheck$1(this,i),_defineProperty(_assertThisInitialized$1(t=n.call(this,e!=i.Mode.WHOLE_STROKE)),"splineParameterDistanceThreshold",.01),t.mode=e,t}return _createClass$1(i,[{key:"intersect",value:(r=_asyncToGenerator(regenerator.mark((function t(e,r){var n,o,s,a;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(e instanceof Stroke)){t.next=5;break}(a=e.pipeline||{}).shapesPath?s=a.shapesPath:(n=a.interpolatedSpline||this.splineInterpolator.process(e.spline),o=this.context.nodeProducer.getBrushApplier(e.brush)),t.next=20;break;case 5:if(r){t.next=7;break}throw new Error("brush expected");case 7:if(r instanceof Brush2D){t.next=9;break}throw new Error("brush should be Brush2D instance");case 9:if(!(e instanceof InterpolatedSpline)){t.next=14;break}o=this.context.nodeProducer.getBrushApplier(r),n=e,t.next=20;break;case 14:if(!(e instanceof Spline)){t.next=19;break}n=this.splineInterpolator.process(e),o=this.context.nodeProducer.getBrushApplier(r),t.next=20;break;case 19:throw new Error("Expected input should be instance of Stroke, InterpolatedSpline, Spline");case 20:return s||(s=o.process(n)),this.reset(),this.context.tree.canvas&&this.context.tree.canvas.fillShape(s),t.next=25,this.processNodes(s);case 25:return this.mode==i.Mode.PARTIAL_STROKE&&this.buildFragments(),t.abrupt("return",{type:"INTERSECTION",intersected:this.fragments,selected:Array.from(this.selected),length:Object.keys(this.fragments).length+this.selected.size});case 27:case"end":return t.stop()}}),t,this)}))),function(t,e){return r.apply(this,arguments)})},{key:"processNodes",value:(e=_asyncToGenerator(regenerator.mark((function t(e){var r,n,o,s,a,l,u,h,c,p,f,d,y,v,m,g,P,_=this;return regenerator.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(0!=e.length){t.next=5;break}return t.abrupt("return");case 5:1==e.length&&e.push(e[0]);case 6:for(r=e[0],n=r.bounds,o={},s=this.mode==i.Mode.PARTIAL_STROKE&&this.splitPointsProducer,a=1;a<e.length;a++){if(l=e[a].bounds,u=l.union(n),(h=this.context.tree.search(u)).length>0)if(c=e[a].union(r),p=a-1,s){f=_createForOfIteratorHelper$2(h);try{for(f.s();!(d=f.n()).done;)y=d.value,o[y.id]||(o[y.id]=[]),o[y.id].push({node:y,eraserHull:c,eraserSegmentIndex:p,eraserSegmentBounds:u})}catch(t){f.e(t)}finally{f.f()}}else{v=_createForOfIteratorHelper$2(h);try{for(v.s();!(m=v.n()).done;)g=m.value,this.processStrokePart(g,c,p,u)}catch(t){v.e(t)}finally{v.f()}}r=e[a],n=l}if(!s){t.next=18;break}if(P=[],Object.values(o).forEach((function(t){var e=_.encodeNodeProcessingInfo(t);P.push(e)})),!(P.length>0)){t.next=18;break}return t.next=17,this.splitPointsProducer.build(P);case 17:this.splitPoints=t.sent;case 18:case"end":return t.stop()}}),t,this)}))),function(t){return e.apply(this,arguments)})},{key:"buildFragments",value:function(){for(var t in this.splitPoints){var e=this.context.getStroke(t),r=this.convertSplitPointsToFragments(e);0==r.length?this.selected.add(e.id):this.fragments[e.id]=r}}},{key:"convertSplitPointsToFragments",value:function(t){var e=[],r=this.splitPoints[t.id];if(0==r.length)return e;r.sort(SplineSplitPoint.compare);var n,i,o,s=new Set,a=t.spline,l=!0;r[0].on?(o=1,n=r[0].splineParameter):(o=0,n=new SplineParameter(0,a.ts));for(var u=o;u<r.length;u++){var h=r[u];if(h.on)s.delete(h.segmentIndex),0==s.size&&(n=h.splineParameter,l=!0);else if(s.add(h.segmentIndex),l&&(i=h.splineParameter,l=!1,n.segmentIndex!=i.segmentIndex||n.t!=i.t)){var c=SplineFragment.getInstance(a,n,i);e.push(c)}}if(l&&(i=new SplineParameter(a.segmentsCount-1,a.tf),n.segmentIndex!=i.segmentIndex||n.t!=i.t)){var p=SplineFragment.getInstance(a,n,i);e.push(p)}return e}},{key:"encodeNodeProcessingInfo",value:function(t){var e,r={splineParameterDistanceThreshold:this.splineParameterDistanceThreshold,input:[]},n=_createForOfIteratorHelper$2(t);try{for(n.s();!(e=n.n()).done;){var i=e.value;if(!r.target){var o=this.context.getStroke(i.node.strokeID);o.spline.id=o.id,r.target={stroke:{brush:o.brush.toJSON(),spline:o.spline.toJSON()},bounds:i.node.bounds.toJSON(),shapesPath:i.node.shapesPath.map((function(t){return t.toJSON()})),splineParameters:i.node.splineParameters.map((function(t){return t.toJSON()}))}}r.input.push({node:i.node.toJSON(),eraserHull:i.eraserHull.toJSON(),eraserSegmentIndex:i.eraserSegmentIndex,eraserSegmentBounds:i.eraserSegmentBounds.toJSON()})}}catch(t){n.e(t)}finally{n.f()}return r}}],[{key:"decodeNodeProcessingInfo",value:function(t){var e=t.target,r=t.input,n=t.splineParameterDistanceThreshold,i=Brush2D.fromJSON(e.stroke.brush),o=Spline.fromJSON(e.stroke.spline),s=new Stroke(i,o),a={bounds:Rect.fromRect(e.bounds),shapesPath:e.shapesPath.map((function(t){return Polygon.fromJSON(t)})),splineParameters:e.splineParameters.map((function(t){return SplineParameter.fromJSON(t)}))};return r=r.map((function(t){return{node:RNode.fromJSON(t.node,a.bounds,a.splineParameters,a.shapesPath),eraserHull:Polygon.fromJSON(t.eraserHull),eraserSegmentIndex:t.eraserSegmentIndex,eraserSegmentBounds:Rect.fromRect(t.eraserSegmentBounds)}})),{stroke:s,input:r,splineParameterDistanceThreshold:n}}}]),i}(Manipulator);Object.defineEnum(Intersector,"Mode",["WHOLE_STROKE","PARTIAL_STROKE"]);var rbush_min={exports:{}};!function(t,e){t.exports=function(){function t(t,n,i,o,s){!function t(r,n,i,o,s){for(;o>i;){if(o-i>600){var a=o-i+1,l=n-i+1,u=Math.log(a),h=.5*Math.exp(2*u/3),c=.5*Math.sqrt(u*h*(a-h)/a)*(l-a/2<0?-1:1);t(r,n,Math.max(i,Math.floor(n-l*h/a+c)),Math.min(o,Math.floor(n+(a-l)*h/a+c)),s)}var p=r[n],f=i,d=o;for(e(r,i,n),s(r[o],p)>0&&e(r,i,o);f<d;){for(e(r,f,d),f++,d--;s(r[f],p)<0;)f++;for(;s(r[d],p)>0;)d--}0===s(r[i],p)?e(r,i,d):e(r,++d,o),d<=n&&(i=d+1),n<=d&&(o=d-1)}}(t,n,i||0,o||t.length-1,s||r)}function e(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function r(t,e){return t<e?-1:t>e?1:0}var n=function(t){void 0===t&&(t=9),this._maxEntries=Math.max(4,t),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),this.clear()};function i(t,e,r){if(!r)return e.indexOf(t);for(var n=0;n<e.length;n++)if(r(t,e[n]))return n;return-1}function o(t,e){s(t,0,t.children.length,e,t)}function s(t,e,r,n,i){i||(i=d(null)),i.minX=1/0,i.minY=1/0,i.maxX=-1/0,i.maxY=-1/0;for(var o=e;o<r;o++){var s=t.children[o];a(i,t.leaf?n(s):s)}return i}function a(t,e){return t.minX=Math.min(t.minX,e.minX),t.minY=Math.min(t.minY,e.minY),t.maxX=Math.max(t.maxX,e.maxX),t.maxY=Math.max(t.maxY,e.maxY),t}function l(t,e){return t.minX-e.minX}function u(t,e){return t.minY-e.minY}function h(t){return(t.maxX-t.minX)*(t.maxY-t.minY)}function c(t){return t.maxX-t.minX+(t.maxY-t.minY)}function p(t,e){return t.minX<=e.minX&&t.minY<=e.minY&&e.maxX<=t.maxX&&e.maxY<=t.maxY}function f(t,e){return e.minX<=t.maxX&&e.minY<=t.maxY&&e.maxX>=t.minX&&e.maxY>=t.minY}function d(t){return{children:t,height:1,leaf:!0,minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}}function y(e,r,n,i,o){for(var s=[r,n];s.length;)if(!((n=s.pop())-(r=s.pop())<=i)){var a=r+Math.ceil((n-r)/i/2)*i;t(e,a,r,n,o),s.push(r,a,a,n)}}return n.prototype.all=function(){return this._all(this.data,[])},n.prototype.search=function(t){var e=this.data,r=[];if(!f(t,e))return r;for(var n=this.toBBox,i=[];e;){for(var o=0;o<e.children.length;o++){var s=e.children[o],a=e.leaf?n(s):s;f(t,a)&&(e.leaf?r.push(s):p(t,a)?this._all(s,r):i.push(s))}e=i.pop()}return r},n.prototype.collides=function(t){var e=this.data;if(!f(t,e))return!1;for(var r=[];e;){for(var n=0;n<e.children.length;n++){var i=e.children[n],o=e.leaf?this.toBBox(i):i;if(f(t,o)){if(e.leaf||p(t,o))return!0;r.push(i)}}e=r.pop()}return!1},n.prototype.load=function(t){if(!t||!t.length)return this;if(t.length<this._minEntries){for(var e=0;e<t.length;e++)this.insert(t[e]);return this}var r=this._build(t.slice(),0,t.length-1,0);if(this.data.children.length)if(this.data.height===r.height)this._splitRoot(this.data,r);else{if(this.data.height<r.height){var n=this.data;this.data=r,r=n}this._insert(r,this.data.height-r.height-1,!0)}else this.data=r;return this},n.prototype.insert=function(t){return t&&this._insert(t,this.data.height-1),this},n.prototype.clear=function(){return this.data=d([]),this},n.prototype.remove=function(t,e){if(!t)return this;for(var r,n,o,s=this.data,a=this.toBBox(t),l=[],u=[];s||l.length;){if(s||(s=l.pop(),n=l[l.length-1],r=u.pop(),o=!0),s.leaf){var h=i(t,s.children,e);if(-1!==h)return s.children.splice(h,1),l.push(s),this._condense(l),this}o||s.leaf||!p(s,a)?n?(r++,s=n.children[r],o=!1):s=null:(l.push(s),u.push(r),r=0,n=s,s=s.children[0])}return this},n.prototype.toBBox=function(t){return t},n.prototype.compareMinX=function(t,e){return t.minX-e.minX},n.prototype.compareMinY=function(t,e){return t.minY-e.minY},n.prototype.toJSON=function(){return this.data},n.prototype.fromJSON=function(t){return this.data=t,this},n.prototype._all=function(t,e){for(var r=[];t;)t.leaf?e.push.apply(e,t.children):r.push.apply(r,t.children),t=r.pop();return e},n.prototype._build=function(t,e,r,n){var i,s=r-e+1,a=this._maxEntries;if(s<=a)return o(i=d(t.slice(e,r+1)),this.toBBox),i;n||(n=Math.ceil(Math.log(s)/Math.log(a)),a=Math.ceil(s/Math.pow(a,n-1))),(i=d([])).leaf=!1,i.height=n;var l=Math.ceil(s/a),u=l*Math.ceil(Math.sqrt(a));y(t,e,r,u,this.compareMinX);for(var h=e;h<=r;h+=u){var c=Math.min(h+u-1,r);y(t,h,c,l,this.compareMinY);for(var p=h;p<=c;p+=l){var f=Math.min(p+l-1,c);i.children.push(this._build(t,p,f,n-1))}}return o(i,this.toBBox),i},n.prototype._chooseSubtree=function(t,e,r,n){for(;n.push(e),!e.leaf&&n.length-1!==r;){for(var i=1/0,o=1/0,s=void 0,a=0;a<e.children.length;a++){var l=e.children[a],u=h(l),c=(p=t,f=l,(Math.max(f.maxX,p.maxX)-Math.min(f.minX,p.minX))*(Math.max(f.maxY,p.maxY)-Math.min(f.minY,p.minY))-u);c<o?(o=c,i=u<i?u:i,s=l):c===o&&u<i&&(i=u,s=l)}e=s||e.children[0]}var p,f;return e},n.prototype._insert=function(t,e,r){var n=r?t:this.toBBox(t),i=[],o=this._chooseSubtree(n,this.data,e,i);for(o.children.push(t),a(o,n);e>=0&&i[e].children.length>this._maxEntries;)this._split(i,e),e--;this._adjustParentBBoxes(n,i,e)},n.prototype._split=function(t,e){var r=t[e],n=r.children.length,i=this._minEntries;this._chooseSplitAxis(r,i,n);var s=this._chooseSplitIndex(r,i,n),a=d(r.children.splice(s,r.children.length-s));a.height=r.height,a.leaf=r.leaf,o(r,this.toBBox),o(a,this.toBBox),e?t[e-1].children.push(a):this._splitRoot(r,a)},n.prototype._splitRoot=function(t,e){this.data=d([t,e]),this.data.height=t.height+1,this.data.leaf=!1,o(this.data,this.toBBox)},n.prototype._chooseSplitIndex=function(t,e,r){for(var n,i,o,a,l,u,c,p=1/0,f=1/0,d=e;d<=r-e;d++){var y=s(t,0,d,this.toBBox),v=s(t,d,r,this.toBBox),m=(i=y,o=v,a=void 0,l=void 0,u=void 0,c=void 0,a=Math.max(i.minX,o.minX),l=Math.max(i.minY,o.minY),u=Math.min(i.maxX,o.maxX),c=Math.min(i.maxY,o.maxY),Math.max(0,u-a)*Math.max(0,c-l)),g=h(y)+h(v);m<p?(p=m,n=d,f=g<f?g:f):m===p&&g<f&&(f=g,n=d)}return n||r-e},n.prototype._chooseSplitAxis=function(t,e,r){var n=t.leaf?this.compareMinX:l,i=t.leaf?this.compareMinY:u;this._allDistMargin(t,e,r,n)<this._allDistMargin(t,e,r,i)&&t.children.sort(n)},n.prototype._allDistMargin=function(t,e,r,n){t.children.sort(n);for(var i=this.toBBox,o=s(t,0,e,i),l=s(t,r-e,r,i),u=c(o)+c(l),h=e;h<r-e;h++){var p=t.children[h];a(o,t.leaf?i(p):p),u+=c(o)}for(var f=r-e-1;f>=e;f--){var d=t.children[f];a(l,t.leaf?i(d):d),u+=c(l)}return u},n.prototype._adjustParentBBoxes=function(t,e,r){for(var n=r;n>=0;n--)a(e[n],t)},n.prototype._condense=function(t){for(var e=t.length-1,r=void 0;e>=0;e--)0===t[e].children.length?e>0?(r=t[e-1].children).splice(r.indexOf(t[e]),1):this.clear():o(t[e],this.toBBox)},n}()}(rbush_min);var RBush=rbush_min.exports,ColorsBox={palettes:{}},colors=["AliceBlue","#F0F8FF","AntiqueWhite","#FAEBD7","Aqua","#00FFFF","Aquamarine","#7FFFD4","Azure","#F0FFFF","Beige","#F5F5DC","Bisque","#FFE4C4","Black","#000000","BlanchedAlmond","#FFEBCD","Blue","#0000FF","BlueViolet","#8A2BE2","Brown","#A52A2A","BurlyWood","#DEB887","CadetBlue","#5F9EA0","Chartreuse","#7FFF00","Chocolate","#D2691E","Coral","#FF7F50","CornflowerBlue","#6495ED","Cornsilk","#FFF8DC","Crimson","#DC143C","Cyan","#00FFFF","DarkBlue","#00008B","DarkCyan","#008B8B","DarkGoldenRod","#B8860B","DarkGray","#A9A9A9","DarkGreen","#006400","DarkKhaki","#BDB76B","DarkMagenta","#8B008B","DarkOliveGreen","#556B2F","DarkOrange","#FF8C00","DarkOrchid","#9932CC","DarkRed","#8B0000","DarkSalmon","#E9967A","DarkSeaGreen","#8FBC8F","DarkSlateBlue","#483D8B","DarkSlateGray","#2F4F4F","DarkTurquoise","#00CED1","DarkViolet","#9400D3","DeepPink","#FF1493","DeepSkyBlue","#00BFFF","DimGray","#696969","DodgerBlue","#1E90FF","FireBrick","#B22222","FloralWhite","#FFFAF0","ForestGreen","#228B22","Fuchsia","#FF00FF","Gainsboro","#DCDCDC","GhostWhite","#F8F8FF","Gold","#FFD700","GoldenRod","#DAA520","Gray","#808080","Green","#008000","GreenYellow","#ADFF2F","HoneyDew","#F0FFF0","HotPink","#FF69B4","IndianRed","#CD5C5C","Indigo","#4B0082","Ivory","#FFFFF0","Khaki","#F0E68C","Lavender","#E6E6FA","LavenderBlush","#FFF0F5","LawnGreen","#7CFC00","LemonChiffon","#FFFACD","LightBlue","#ADD8E6","LightCoral","#F08080","LightCyan","#E0FFFF","LightGoldenRodYellow","#FAFAD2","LightGray","#D3D3D3","LightGreen","#90EE90","LightPink","#FFB6C1","LightSalmon","#FFA07A","LightSeaGreen","#20B2AA","LightSkyBlue","#87CEFA","LightSlateGray","#778899","LightSteelBlue","#B0C4DE","LightYellow","#FFFFE0","Lime","#00FF00","LimeGreen","#32CD32","Linen","#FAF0E6","Magenta","#FF00FF","Maroon","#800000","MediumAquaMarine","#66CDAA","MediumBlue","#0000CD","MediumOrchid","#BA55D3","MediumPurple","#9370DB","MediumSeaGreen","#3CB371","MediumSlateBlue","#7B68EE","MediumSpringGreen","#00FA9A","MediumTurquoise","#48D1CC","MediumVioletRed","#C71585","MidnightBlue","#191970","MintCream","#F5FFFA","MistyRose","#FFE4E1","Moccasin","#FFE4B5","NavajoWhite","#FFDEAD","Navy","#000080","OldLace","#FDF5E6","Olive","#808000","OliveDrab","#6B8E23","Orange","#FFA500","OrangeRed","#FF4500","Orchid","#DA70D6","PaleGoldenRod","#EEE8AA","PaleGreen","#98FB98","PaleTurquoise","#AFEEEE","PaleVioletRed","#DB7093","PapayaWhip","#FFEFD5","PeachPuff","#FFDAB9","Peru","#CD853F","Pink","#FFC0CB","Plum","#DDA0DD","PowderBlue","#B0E0E6","Purple","#800080","RebeccaPurple","#663399","Red","#FF0000","RosyBrown","#BC8F8F","RoyalBlue","#4169E1","SaddleBrown","#8B4513","Salmon","#FA8072","SandyBrown","#F4A460","SeaGreen","#2E8B57","SeaShell","#FFF5EE","Sienna","#A0522D","Silver","#C0C0C0","SkyBlue","#87CEEB","SlateBlue","#6A5ACD","SlateGray","#708090","Snow","#FFFAFA","SpringGreen","#00FF7F","SteelBlue","#4682B4","Tan","#D2B48C","Teal","#008080","Thistle","#D8BFD8","Tomato","#FF6347","Turquoise","#40E0D0","Violet","#EE82EE","Wheat","#F5DEB3","White","#FFFFFF","WhiteSmoke","#F5F5F5","Yellow","#FFFF00","YellowGreen","#9ACD32"],palettes={red:["LightSalmon","Salmon","DarkSalmon","LightCoral","IndianRed","Crimson","FireBrick","Red","DarkRed"],orange:["Coral","Tomato","OrangeRed","Gold","Orange","DarkOrange"],yellow:["LightYellow","LemonChiffon","LightGoldenRodYellow","PapayaWhip","Moccasin","PeachPuff","PaleGoldenRod","Khaki","DarkKhaki","Yellow"],green:["LawnGreen","Chartreuse","LimeGreen","Lime","ForestGreen","Green","DarkGreen","GreenYellow","YellowGreen","SpringGreen","MediumSpringGreen","LightGreen","PaleGreen","DarkSeaGreen","MediumSeaGreen","SeaGreen","Olive","DarkOliveGreen","OliveDrab"],cyan:["LightCyan","Cyan","Aqua","Aquamarine","MediumAquaMarine","PaleTurquoise","Turquoise","MediumTurquoise","DarkTurquoise","LightSeaGreen","CadetBlue","DarkCyan","Teal"],blue:["PowderBlue","LightBlue","LightSkyBlue","SkyBlue","DeepSkyBlue","LightSteelBlue","DodgerBlue","CornflowerBlue","SteelBlue","RoyalBlue","Blue","MediumBlue","DarkBlue","Navy","MidnightBlue","MediumSlateBlue","SlateBlue","DarkSlateBlue"],purple:["Lavender","Thistle","Plum","Violet","Orchid","Fuchsia","Magenta","MediumOrchid","MediumPurple","BlueViolet","DarkViolet","DarkOrchid","DarkMagenta","Purple","RebeccaPurple","Indigo"],pink:["Pink","LightPink","HotPink","DeepPink","PaleVioletRed","MediumVioletRed"],white:["White","Snow","HoneyDew","MintCream","Azure","AliceBlue","GhostWhite","WhiteSmoke","SeaShell","Beige","OldLace","FloralWhite","Ivory","AntiqueWhite","Linen","LavenderBlush","MistyRose"],gray:["Gainsboro","LightGray","Silver","DarkGray","Gray","DimGray","LightSlateGray","SlateGray","DarkSlateGray","Black"],brown:["Cornsilk","BlanchedAlmond","Bisque","NavajoWhite","Wheat","BurlyWood","Tan","RosyBrown","SandyBrown","GoldenRod","Peru","DarkGoldenRod","Chocolate","SaddleBrown","Sienna","Brown","Maroon"]},cache={};function getColor(t,e){return cache[t]||(cache[t]=Color.fromColor(e)),cache[t]}for(var _loop=function(t){var e=utils.getEnumValueName(colors[t]);Object.defineProperty(ColorsBox,e,{get:function(){return getColor(e,colors[t+1])},enumerable:!0})},i=0;i<colors.length;i+=2)_loop(i);for(var paletteName in palettes){var scale=palettes[paletteName];ColorsBox.palettes[paletteName]={scale:scale};for(var _loop2=function(t){var e=utils.getEnumValueName(scale[t]);Object.defineProperty(ColorsBox.palettes[paletteName],e,{get:function(){return ColorsBox[e]},enumerable:!0})},_i=0;_i<scale.length;_i++)_loop2(_i)}function _createForOfIteratorHelper$1(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=_unsupportedIterableToArray$1(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function _unsupportedIterableToArray$1(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray$1(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray$1(t,e):void 0}}function _arrayLikeToArray$1(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var RenderingContext2D=function(){function t(e){_classCallCheck$1(this,t),Object.defineProperty(this,"ctx",{value:e,enumerable:!0})}return _createClass$1(t,[{key:"clearCanvas",value:function(){this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height)}},{key:"setTransform",value:function(t){this.ctx.setTransform(t.a,t.b,t.c,t.d,t.tx,t.ty)}},{key:"drawRect",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ColorsBox.GRAY.toRGBA(.3);this.ctx.strokeStyle=e.toString(),this.ctx.strokeRect(t.left,t.top,t.width,t.height)}},{key:"fillRect",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ColorsBox.GRAY.toRGBA(.3);this.ctx.fillStyle=e.toString(),this.ctx.fillRect(t.left,t.top,t.width,t.height)}},{key:"drawPoint",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:ColorsBox.BLACK;this.ctx.beginPath(),this.ctx.arc(t.x,t.y,e,0,2*Math.PI),this.renderStyle({type:"stroke",style:r})}},{key:"fillPoint",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:ColorsBox.BLACK;this.ctx.beginPath(),this.ctx.arc(t.x,t.y,e,0,2*Math.PI),this.renderStyle({type:"fill",style:r})}},{key:"drawEllipse",value:function(t,e,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:ColorsBox.BLACK;this.ctx.beginPath(),this.ctx.ellipse(t.x,t.y,e,r,0,0,2*Math.PI),this.renderStyle({type:"stroke",style:n})}},{key:"fillEllipse",value:function(t,e,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:ColorsBox.BLACK;this.ctx.beginPath(),this.ctx.ellipse(t.x,t.y,e,r,0,0,2*Math.PI),this.renderStyle({type:"fill",style:n})}},{key:"drawShape",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ColorsBox.DARK_MAGENTA.toRGBA(.5);this.renderShape(t,{type:"stroke",style:e})}},{key:"fillShape",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ColorsBox.ORANGE.toRGBA(.6);this.renderShape(t,{type:"fill",style:e})}},{key:"renderShape",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(t instanceof Path)this.ctx.beginPath(),this.renderPath(t);else if(t instanceof Polygon)this.renderPolygon(t);else{if(!(t instanceof PolygonArray))throw new Error("Unexpected shape type found");this.ctx.beginPath();var r,n=_createForOfIteratorHelper$1(t);try{for(n.s();!(r=n.n()).done;){var i=r.value;this.renderPolygon(i,{segment:!0})}}catch(t){n.e(t)}finally{n.f()}}this.renderStyle(e)}},{key:"renderPolygon",value:function(t){var e=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};r.segment||this.ctx.beginPath(),this.renderPath(t.shape),t.holes.forEach((function(r){return e.renderPath(r,{holesDirection:t.holesDirection})})),r.segment||this.renderStyle(r)}},{key:"renderPath",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=!e.holesDirection||e.holesDirection==Polygon.PointsDirection.CLOCKWISE;if(r){this.ctx.moveTo(t.getPointX(0),t.getPointY(0));for(var n=1;n<t.length;n++)this.ctx.lineTo(t.getPointX(n),t.getPointY(n))}else{this.ctx.moveTo(t.getPointX(t.length-1),t.getPointY(t.length-1));for(var i=t.length-2;i>=0;i--)this.ctx.lineTo(t.getPointX(i),t.getPointY(i))}this.ctx.closePath(),this.renderStyle(e)}},{key:"renderStyle",value:function(t){if(t.type){if("fill"!=t.type&&"stroke"!=t.type)throw new Error("Option type should be oneof(stroke, fill)");t.style&&(this.ctx["".concat(t.type,"Style")]=t.style instanceof Color?t.style.toString():t.style),this.ctx[t.type]()}}}]),t}();function _createSuper$2(t){var e=_isNativeReflectConstruct$2();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$2(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}var RCanvas=function(t){_inherits$1(r,t);var e=_createSuper$2(r);function r(t){return _classCallCheck$1(this,r),e.call(this,t.getContext("2d"))}return _createClass$1(r,[{key:"clear",value:function(){var t=this.ctx.canvas.toRect().transform(this.ctx.getTransform().invert());this.ctx.clearRect(t.left,t.top,t.width,t.height),this.clearCanvas()}},{key:"refresh",value:function(){if(!this.suppressRefresh){var t=[];r.allocateSegments(t,this.data,0),this.clear(),this.drawRange(t)}}},{key:"drawRange",value:function(t){var e=this;t.forEach((function(t){return e.drawRect(t.bounds,ColorsBox.MAGENTA)}))}}],[{key:"allocateSegments",value:function(t,e,n){if(e&&(e.bounds&&t.push(e),e.children))if(10!==n)for(var i=0;i<e.children.length;i++)r.allocateSegments(t,e.children[i],n+1);else console.warn("depth 10")}},{key:"getInstance",value:function(t,e){if(t){var n=new r(t);return Object.defineProperty(n,"data",{get:function(){return e.data},enumerable:!0}),n}}}]),r}(RenderingContext2D);function _createSuper$1(t){var e=_isNativeReflectConstruct$1();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct$1(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}var RTree=function(t){_inherits$1(r,t);var e=_createSuper$1(r);function r(){var t,n;_classCallCheck$1(this,r);for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];return t=e.call.apply(e,[this].concat(o)),"undefined"!=typeof document&&("loading"==document.readyState?addEventListener("DOMContentLoaded",(function(e){return n=RCanvas.getInstance(document.getElementById("rbush"),_assertThisInitialized$1(t))})):n=RCanvas.getInstance(document.getElementById("rbush"),_assertThisInitialized$1(t))),Object.defineProperty(_assertThisInitialized$1(t),"canvas",{get:function(){return t.debug?n:null},enumerable:!0}),t}return _createClass$1(r,[{key:"toBBox",value:function(t){return{minX:t.bounds.left,minY:t.bounds.top,maxX:t.bounds.right,maxY:t.bounds.bottom}}},{key:"compareMinX",value:function(t,e){return t.bounds.x-e.bounds.x}},{key:"compareMinY",value:function(t,e){return t.bounds.y-e.bounds.y}},{key:"search",value:function(t){return _get(_getPrototypeOf$1(r.prototype),"search",this).call(this,{minX:t.left,minY:t.top,maxX:t.right,maxY:t.bottom})}},{key:"find",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.data,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];if(e){if(e.stroke){var i=!0;Object.keys(t).forEach((function(r){i=i&&e[r]==t[r]})),i&&n.push(e)}if(e.children){if(6!==r){for(var o=0;o<e.children.length;o++)this.find(t,e.children[o]||null,r+1,n);return n}console.warn("depth 6")}}}},{key:"load",value:function(t){Array.isArray(t)||(t=[t]),0!=t.length&&(_get(_getPrototypeOf$1(r.prototype),"load",this).call(this,t),this.canvas&&this.canvas.refresh())}},{key:"unload",value:function(t){var e=this;Array.isArray(t)||(t=[t]),t.forEach((function(t){return e.remove(t)})),this.canvas&&this.canvas.refresh()}}]),r}(RBush),RNodeProducer=function(){function t(e){_classCallCheck$1(this,t),this.tree=e,this.brushAppliers={},this.splineInterpolator=new CurvatureBasedInterpolator(!1,!0)}return _createClass$1(t,[{key:"getBrushApplier",value:function(t){return this.brushAppliers[t.name]||(this.brushAppliers[t.name]=new BrushApplier(t)),this.brushAppliers[t.name]}},{key:"buildStrokeNodes",value:function(t){var e,r,n=[],i=0,o=0,s=0,a=0,l=0,u=t.pipeline||{},h=u.interpolatedSpline,c=u.shapesPath;if(delete t.pipeline,!h||!c){var p=this.getBrushApplier(t.brush);h=this.splineInterpolator.process(t.spline),c=p.process(h)}if(0==c.length)return n;for(var f=[],d=[],y=0;y<c.length;y++){var v=h.splineParameters[y],m=c[y].bounds;0==i?(o=v.segmentIndex,a=v.t,r=m):this.mustEndCurrentGroup(i,m,r)?(n.push(new RNode(t.id,o,a,s,l,r,f,d)),f=[f.last],d=[d.last],o=s,a=l,r=m.union(e),i=1):r=this.fragmentBounds,f.push(v),d.push(c[y]),i++,e=m,s=v.segmentIndex,l=v.t}return n.push(new RNode(t.id,o,a,s,l,r,f,d)),this.tree.load(n),n}},{key:"mustEndCurrentGroup",value:function(e,r,n){if(delete this.fragmentBounds,e>50)return!0;var i=r.union(n);return e>1&&i.area>1e4||(!!(e>20&&t.isTooSquare(i))||(this.fragmentBounds=i,!1))}}],[{key:"isTooSquare",value:function(t){var e=t.width/t.height;return e>.2&&e<5}}]),t}(),SpatialContext=function(){function t(){_classCallCheck$1(this,t),this.tree=new RTree,this.nodeProducer=new RNodeProducer(this.tree),this.strokes={},this.nodes={}}return _createClass$1(t,[{key:"getNodes",value:function(t){return this.nodes[t]}},{key:"getStroke",value:function(t){return this.strokes[t]}},{key:"getBrushApplier",value:function(t){return this.nodeProducer.getBrushApplier(this.strokes[t].brush)}},{key:"add",value:function(t){if(!(t.brush instanceof BrushGL)){if(this.strokes[t.id])throw new Error("SpatialContext stroke with id ".concat(t.id," is already available"));this.strokes[t.id]=t,this.nodes[t.id]=this.nodeProducer.buildStrokeNodes(t)}}},{key:"reload",value:function(t){this.tree.unload(this.nodes[t.id]),this.nodes[t.id]=this.nodeProducer.buildStrokeNodes(t)}},{key:"remove",value:function(t){var e="string"==typeof t?t:t.id;this.tree.unload(this.nodes[e]),delete this.nodes[e],delete this.strokes[e]}},{key:"replace",value:function(t,e){var r=this;e.forEach((function(t){return r.add(t)})),this.remove(t)}},{key:"clone",value:function(e){var r=new t;r.nodeProducer.brushAppliers=this.nodeProducer.brushAppliers;var n=[];for(var i in this.strokes){var o=e.getStroke(i);if(!o)throw new Error("Provided ink model do not provides stroke with id ".concat(i));r.strokes[i]=o,r.nodes[i]=this.nodes[i].slice(),n=n.concat(r.nodes[i])}return r.tree.load(n),r}},{key:"reset",value:function(){this.strokes={},this.nodes={},this.tree.clear()}}]),t}();function _createForOfIteratorHelper(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=_unsupportedIterableToArray(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){a=!0,o=t},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function _unsupportedIterableToArray(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(t,e):void 0}}function _arrayLikeToArray(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function _createSuper(t){var e=_isNativeReflectConstruct();return function(){var r,n=_getPrototypeOf$1(t);if(e){var i=_getPrototypeOf$1(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return _possibleConstructorReturn$1(this,r)}}function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}var RNodesProcessor=function(t){_inherits$1(r,t);var e=_createSuper(r);function r(){return _classCallCheck$1(this,r),e.call(this)}return _createClass$1(r,[{key:"process",value:function(t){var e={action:t.action,actionID:t.actionID};if("BUILD"!==t.action)throw new Error("Unknow data action found: ".concat(t.action));var r=Intersector.decodeNodeProcessingInfo(t.data),n=r.stroke,i=r.input,o=r.splineParameterDistanceThreshold,s=this.build(n,i,o);return e.strokeID=s.strokeID,s.splitPoints&&(e.splitPoints=s.splitPoints.map((function(t){return t.toJSON()}))),e}},{key:"build",value:function(t,e,r){var n=e.map((function(t){return t.node})),i=new SpatialContext;i.strokes[t.id]=t,i.nodes[t.id]=n;var o=new Intersector(Intersector.Mode.PARTIAL_STROKE);o.splineParameterDistanceThreshold=r,o.reset(i);var s,a=_createForOfIteratorHelper(e);try{for(a.s();!(s=a.n()).done;){var l=s.value;o.processStrokePart(l.node,l.eraserHull,l.eraserSegmentIndex,l.eraserSegmentBounds)}}catch(t){a.e(t)}finally{a.f()}return{strokeID:t.id,splitPoints:o.splitPoints[t.id]}}}]),r}(ThreadProcessor);RNodesProcessor.connect()})();
