"use strict";class e{static DEPENDENCIES_SRC="/node_modules";get dependenciesESM(){return{quickselect:`${this.src}/quickselect/index.js`,rbush:`${this.src}/rbush/index.js`,long:`${this.src}/long/index.js`,"gl-matrix":`${this.src}/gl-matrix/esm/index.js`,"js-ext":`${this.src}/js-ext/js-ext-min.mjs`,"digital-ink":`${this.src}/digital-ink/digital-ink-min.mjs`}}get devDependenciesESM(){return Object.assign({},this.dependenciesESM,{"js-ext":`${this.src}/js-ext/js-ext.mjs`,"digital-ink":`${this.src}/digital-ink/digital-ink.mjs`})}get dependenciesUMD(){return{"clipper-lib":`${this.src}/clipper-lib/clipper.js`,poly2tri:`${this.src}/poly2tri/dist/poly2tri.min.js`,protobufjs:`${this.src}/protobufjs/dist/protobuf.min.js`,"js-md5":`${this.src}/js-md5/build/md5.min.js`,jszip:`${this.src}/jszip/dist/jszip.min.js`,rbush:`${this.src}/rbush/rbush.js`,long:`${this.src}/long/umd/index.js`,"gl-matrix":`${this.src}/gl-matrix/gl-matrix.js`,"js-ext":`${this.src}/js-ext/js-ext-min.js`,"digital-ink":`${this.src}/digital-ink/digital-ink-min.js`}}get devDependenciesUMD(){return Object.assign({},this.dependenciesUMD,{poly2tri:`${this.src}/poly2tri/dist/poly2tri.js`,protobufjs:`${this.src}/protobufjs/dist/protobuf.js`,"js-md5":`${this.src}/js-md5/src/md5.js`,jszip:`${this.src}/jszip/dist/jszip.js`,"js-ext":`${this.src}/js-ext/js-ext.js`,"digital-ink":`${this.src}/digital-ink/digital-ink.js`})}constructor(t=!0,i=""){this.cdn=t||"function"==typeof DedicatedWorkerGlobalScope,this.src=i+e.DEPENDENCIES_SRC}build(){return this.dependencies||(e.dev?this.cdn?this.dependencies=Object.assign({},this.devDependenciesUMD):this.dependencies=Object.assign({},this.devDependenciesUMD,this.devDependenciesESM):this.cdn?this.dependencies=Object.assign({},this.dependenciesUMD):this.dependencies=Object.assign({},this.dependenciesUMD,this.dependenciesESM)),this.dependencies}include(e={}){this.dependencies=Object.assign(this.build(),e)}integrate(){if(this.build(),this.cdn)if("function"==typeof DedicatedWorkerGlobalScope)Object.values(this.dependencies).forEach((e=>self.importScripts(e)));else{if("undefined"==typeof document)throw new Error("Integration failed. Supported env is browser - main or worker thread!");Object.values(this.dependencies).forEach((e=>document.write(`<script type="text/javascript" src="${e}"><\/script>`)))}else{if("undefined"==typeof document)throw new Error("Integration failed. Supported env is browser - main thread!");{let e={imports:this.dependencies},t=document.createElement("script");t.setAttribute("type","importmap"),t.textContent=JSON.stringify(e,0,4),document.currentScript?document.currentScript.after(t):document.head.appendChild(t)}}}static linkWorkers(...e){for(let t of e){let e=t.buildWorkerURL().split(location.host).last,i=document.createElement("link");i.setAttribute("rel","modulepreload"),i.setAttribute("href",e),document.head.appendChild(i)}}static integrate(t){new e(!0,t).integrate()}static integrateECMA6(t,i){let s=new e(!1,i);s.include(t),s.integrate()}static getCDNImports(t){return new e(!0,t).build()}}module.exports=e;
