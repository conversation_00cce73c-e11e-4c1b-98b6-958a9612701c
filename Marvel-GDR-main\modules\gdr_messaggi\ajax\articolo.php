<?php

include_once __DIR__.'/../../../core.php';

use Carbon\Carbon;

$id = get('id');
$article = $dbo->fetchOne("SELECT * FROM gdr_news WHERE id = ".prepare($id));

if (!empty($article)) {
    // Determina il colore dell'intestazione in base alla testata
    $headerColor = ($article['testata'] == 1) ? '#ed1d24' : '#0066cc';
    $testata = ($article['testata'] == 1) ? 'DAILY BUGGLE' : 'BULLETIN';
    
    // Intestazione
    echo '<div class="card" style="background-color: transparent; margin-bottom: 15px; border: none;">';
    echo '<div class="card-header" style="background-color: '.$headerColor.'; color: white;">';
    echo '<div class="d-flex justify-content-between">';
    echo '<h3>'.$article['titolo'].'</h3>';
    echo '<small>'.$testata.' - '.Carbon::parse($article['data'])->format('d/m/Y H:i').'</small>';
    echo '</div>';
    echo '</div>';
    
    // Corpo
    echo '<div class="card-body" style="background-color: transparent; color: white; max-height: 400px; overflow-y: auto;">';
    
    // Format content - handle [excelsior-img] tags
    $content = $article['contenuto'];
    if (strpos($content, '[excelsior-img]') !== false) {
        $pattern = '/\[excelsior-img\](.*?)\[excelsior-img\]/s';
        $replacement = '<div style="text-align: center;"><img src="$1" style="max-width: 70%;" /></div>';
        $content = preg_replace($pattern, $replacement, $content);
    }
    
    echo $content;
    echo '</div>';
    
    // Footer
    echo '<div class="card-footer text-right" style="background-color: transparent; position: sticky; bottom: 0;">';
    echo '<button class="btn btn-sm btn-secondary" onclick="$(\'#articolo-container\').html(\'\');">Chiudi</button>';
    echo '</div>';
    echo '</div>';
} else {
    echo '<div class="alert alert-danger">Articolo non trovato.</div>';
}
