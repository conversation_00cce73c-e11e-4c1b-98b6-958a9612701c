

.excelsior-flip-card {
  background-color: transparent;
  perspective: 1000px;
  margin: 20px auto;
}

.excelsior-flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.excelsior-flip-card.excelsior-flipped .excelsior-flip-card-inner {
  transform: rotateY(180deg);
}

.excelsior-flip-card-front, .excelsior-flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  background-color: transparent;
}

.excelsior-flip-card-back {
  color: white;
  transform: rotateY(180deg);
}

/* Effetto ORO */
.gold-container {
  background: linear-gradient(90deg, #ffcc00, #ff9900, #ffd700, #ffcc00);
  box-shadow: 0 4px 15px rgba(255, 200, 50, 0.8);
}

/* Effetto ARGENTO */
.silver-container {
  background: linear-gradient(90deg, #b0b0b0, #d6d6d6, #ffffff, #b0b0b0);
  box-shadow: 0 6px 20px rgba(180, 180, 180, 0.8), 0 0 15px rgba(173, 216, 230, 0.6); /* Ombra argentata con un tocco blu */
}

/* Effetto BRONZO */
.bronze-container {
  background: linear-gradient(90deg, #cd7f32, #aa5600, #d2691e, #cd7f32);
  box-shadow: 0 4px 15px rgba(205, 127, 50, 0.8);
}

.standard-container {
  background: white;
}

.standard-container,
.bronze-container,
.silver-container,
.gold-container {
  display: inline-block;
  border-radius: 20px;
  background-size: 300% 300% !important;
  padding: 10px;
  animation: glow 3s infinite linear;
  border: 1px solid lightgray;
}

/* Animazioni per tutti */
@keyframes glow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.gold-container img,
.silver-container img,
.bronze-container img,
.standard-container img {
  display: block;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.card-filter {
  filter: grayscale(100%);
  transition: filter 0.5s;
}

@keyframes removeFilter {
  0% {
    filter: grayscale(100%);
  }
  100% {
    filter: grayscale(0%);
  }
}

.card-filter-timing {
  filter: grayscale(100%);
  transition: filter 0.5s;
  animation: removeFilter 5s forwards;
}

.login-page {
  background-size: cover !important;
  background-attachment: fixed !important;
}

.card-center-medium {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  height: 90vh !important;
  margin: 0 auto !important;
}

.modal-collezione{
  background-size: 100%;
  background-size: cover;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  color: white;
  font-size: 15px;
  font-family: "Comic Sans MS", "Comic Sans";
  border: 3px solid gray;
  background: #1c1c1c;
  width: 100% !important;
}

.modal-dialog {
  max-width: 90% !important;
}