<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
        
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>SJCL demo</title>
  <link rel="stylesheet" type="text/css" href="example.css"/>
  <script type="text/javascript" src="../sjcl.js"></script>
  <script type="text/javascript" src="form.js"></script>
  <script type="text/javascript" src="example.js"></script>
</head>
<body onload="loaded()">
  <h1>SJCL demo</h1>

  <div class="header">
  <p>This page is a demo of the Stanford Javascript Crypto Library. To get started, just type in a password in the left pane and a secret message in the middle pane, then click "encrypt". Encryption takes place in your browser and we never see the plaintext.</p>
  
  <p>SJCL has lots of other options, many of which are shown in the grey boxes.</p>
  </div>

  <form id="theForm" onsubmit="return false;">
  <div class="column" id="ckey">
    <!-- Password and pbkdf2 parameters -->
    <div class="box" id="ppassword">
      <h2>Password</h2>
      <div class="section">
        <label for="password">Password:</label>
        <input type="password" class="wide" name="password" id="password" autocomplete="off" tabindex="1"/>
        <p class="explanation">
          Choose a strong, random password.
        </p>
      </div>
    </div>
    
    <div class="box" id="pkey">
      <h2>Key Derivation</h2>
      <div class="section">
        <div>
          <label for="salt">Salt:</label>
          <a class="random floatright" href="javascript:randomize('salt',2,0)">random</a>
        </div>
        <input type="text" id="salt" class="wide hex" autocomplete="off" size="17" maxlength="35"/>
        <input type="checkbox" name="freshsalt" id="freshsalt" autocomplete="off" checked="checked"/>
        <label for="freshsalt">Use fresh random salt for each new password</label>
        <p class="explanation">
          Salt adds more variability to your key, and prevents attackers
          from using <a href="http://en.wikipedia.org/wiki/Rainbow_table">rainbow tables</a> to attack it.
        </p>     
      </div>

      <div class="section">
        <label for="iter">Strengthen by a factor of:</label>
        <input type="text" name="iter" id="iter" value="1000" class="numeric" size="5" maxlength="5" autocomplete="off"/>
        <p class="explanation">
          Strengthening makes it slower to compute the key corresponding to your
          password.  This makes it take much longer for an attacker to guess it.
        </p>
      </div>

      <div class="section">
        Key size:
        <input type="radio" name="keysize" value="128" id="key128" checked="checked" autocomplete="off" onclick="extendKey(4)"/>
        <label for="key128">128</label>
        <input type="radio" name="keysize" value="192" id="key192" autocomplete="off" onclick="extendKey(6)"/>
        <label for="key192">192</label>
        <input type="radio" name="keysize" value="256" id="key256" autocomplete="off" onclick="extendKey(8)"/>
        <label for="key256">256</label>
        <p class="explanation">
          128 bits should be secure enough, but you can generate a longer
          key if you wish.
        </p>
      </div>

      <!-- cipher key -->
      <div class="section">
        <div>
          <label for="key">Key:</label>
          <!--
          <a class="random floatright" href="javascript:randomizeKey()">random</a>
          -->
        </div>
        <textarea id="key" name="key" class="hex" rows="2" autocomplete="off"></textarea>
        <p class="explanation">
          This key is computed from your password, salt and strengthening factor.  It
          will be used internally by the cipher.  Instead of using a password, you can
          enter a key here directly.  If you do, it should be 32, 48 or 64 hexadecimal
          digits (128, 192 or 256 bits).
        </p>
      </div>
     
    </div>
  </div>
    
    <!-- mode controls -->
  <div class="column" id="cmode">
    <div class="box">
      <h2>Cipher Parameters</h2>
      <p class="explanation">
        SJCL encrypts your data with the <a href="http://en.wikipedia.org/wiki/Advanced_Encryption_Standard"><acronym title="Advanced Encryption Standard">AES</acronym></a> block cipher.
      </p>
      <div class="section">
        Cipher mode:
        <input type="radio" name="mode" value="ccm" id="ccm" checked="checked" autocomplete="off"/>
        <label for="ccm"><acronym title="Counter mode with Cipher block chaining Message authentication code">CCM</acronym></label>
        <input type="radio" name="mode" value="ocb2" id="ocb2" autocomplete="off"/>
        <label for="ocb2"><acronym title="Offset CodeBook mode, version 2.0">OCB2</acronym></label>
        <input type="radio" name="mode" value="gcm" id="gcm" autocomplete="off"/>
        <label for="gcm"><acronym title="Galois Counter Mode">GCM</acronym></label>
        <p class="explanation">
          The cipher mode is a standard for how to use AES and other
          algorithms to encrypt and authenticate your message.
          <a href="http://en.wikipedia.org/wiki/OCB_mode">OCB2 mode</a> (patented) and
          <a href="http://en.wikipedia.org/wiki/GCM_mode">GCM mode</a> (unencumbered)
          are slightly faster and have more features than
          <a href="http://en.wikipedia.org/wiki/CCM_mode">CCM mode</a>.
        </p>
      </div>
      
      <div class="section">
        <div>
          <label for="iv">Initialization vector:</label>
          <a class="random floatright" href="javascript:randomize('iv',4,0)">random</a>
        </div>
        <input type="text" name="iv" id="iv" class="wide hex" size="32" maxlength="35" autocomplete="off"/>
        <input type="checkbox" id="freshiv" autocomplete="off" checked="checked"/>
        <label for="freshiv">Choose a new random IV for every message.</label>
        <p class="explanation">
          The IV needs to be different for every message you send.  It adds
          randomness to your message, so that the same message will look
          different each time you send it.
        </p>
        <p class="explanation">
          Be careful: CCM mode and GCM mode don't use
          the whole IV, so changing just part of it isn't enough.
        </p>
      </div>
      
      <div class="section">
        Authentication strength:
        <input type="radio" name="tag" value="64" id="tag64" autocomplete="off" checked="checked"/>
        <label for="tag64">64</label>
        <input type="radio" name="tag" value="96" id="tag96" autocomplete="off"/>
        <label for="tag96">96</label>
        <input type="radio" name="tag" value="128" id="tag128" autocomplete="off"/>
        <label for="tag128">128</label>
        <p class="explanation">
          SJCL adds a an authentication tag to your message to make sure
          nobody changes it.  The longer the authentication tag, the harder it is
          for somebody to change your encrypted message without you noticing.  64
          bits is probably enough.
        </p>
      </div>
      
      <div class="section">
        <input type="checkbox" name="json" id="json" autocomplete="off" checked="checked"/>
        <label for="json">Send the parameters and authenticated data along
          with the message.</label>
         <p class="explanation">
           These parameters are required to decrypt your message later.  If the
           person you're sending the message to knows them, you don't need to send
           them so your message will be shorter.
         </p>
         <p class="explanation">
           Default parameters won't be sent.  Your password won't be sent, either.
           The salt and iv will be encoded in base64 instead of hex, so they'll
           look different from what's in the box.
         </p>
      </div>
    </div>
  </div>
  
  <div class="column" id="ctexts">
    <div id="pplaintext" class="box">
      <h2>Plaintext</h2>
      <div class="section">
        <label for="plaintext">Secret message:</label>
        <textarea id="plaintext" autocomplete="off" rows="5" tabindex="2"></textarea>
        <div class="explanation">
          This message will be encrypted, so that nobody can read it or change it
          without your password.
        </div>
      </div>  
   
      <div class="section">
        <label for="adata">Authenticated data:</label>
        <textarea id="adata" autocomplete="off" tabindex="3"></textarea>
        <div class="explanation">
          This auxilliary message isn't secret, but its integrity will be checked
          along with the integrity of the message.
        </div>
      </div>
    </div>
    
    <div id="buttons">
      <a href="javascript:doEncrypt()" id="encrypt" tabindex="4"><span class="turnDown">encrypt</span></a>
      <a href="javascript:doDecrypt()" id="decrypt" tabindex="6"><span class="turnUp">decrypt</span></a>
    </div>
    
    <div id="pciphertext" class="box">
      <h2>Ciphertext</h2>
      <label for="ciphertext">Ciphertext:</label>
      <textarea id="ciphertext" autocomplete="off" rows="7" tabindex="5"></textarea>
      <div class="explanation">
        Your message, encrypted and authenticated so that nobody can read it
        or change it without your password.
      </div>
    </div>
  </form>
</body>
</html>
