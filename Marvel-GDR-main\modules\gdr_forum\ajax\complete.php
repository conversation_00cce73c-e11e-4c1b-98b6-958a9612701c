<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

include_once __DIR__.'/../../../core.php';

use Modules\Bacheche\Bacheca;
use Modules\Bacheche\Post;
use Models\User;
use Carbon\Carbon;
use Models\Module;

$structure = Module::find(get('id_module'));
$read_only = $structure->permission == 'r';

switch ($resource) {
    case 'get_post':
        global $rootdir;
        $id_module = get('id_module');
        $user = Auth::user();

        $bacheca = Bacheca::find(get('id_bacheca'));
        $posts = Post::where('id_forum',get('id_bacheca'))->orderBy('created_at', 'asc')->get();

        // Prepara i dati dei like per tutti i post
        $post_ids = $posts->pluck('id')->toArray();
        $likes_data = [];
        $user_likes = [];

        if (!empty($post_ids)) {
            // Conta i like per ogni post
            $likes_count = database()->fetchArray("
                SELECT id_post, COUNT(*) as total_likes
                FROM gdr_forum_likes
                WHERE id_post IN (" . implode(',', $post_ids) . ")
                GROUP BY id_post
            ");

            foreach ($likes_count as $like) {
                $likes_data[$like['id_post']] = $like['total_likes'];
            }

            // Verifica quali post ha già messo "mi piace" l'utente corrente
            $user_likes_result = database()->fetchArray("
                SELECT id_post
                FROM gdr_forum_likes
                WHERE id_post IN (" . implode(',', $post_ids) . ")
                AND id_utente = " . $user->id
            );

            foreach ($user_likes_result as $like) {
                $user_likes[$like['id_post']] = true;
            }
        }

        // CSS personalizzato per la visualizzazione dei post in stile social media
        echo '
        <style>
        /* Stili per la visualizzazione dei post */
        .social-post-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }

        /* Stili per le azioni dei post */
        .post-actions {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }

        .post-stats {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .post-action-btn {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 8px 12px;
            border-radius: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 14px;
            color: #6c757d;
        }

        .post-action-btn:hover {
            background-color: #f8f9fa;
            color: #495057;
        }

        /* Stili per i pulsanti "Mi piace" */
        .btn-like {
            color: #6c757d;
        }

        .btn-like:hover {
            background-color: #f8f9fa;
            color: #e74c3c;
        }

        .btn-like-active {
            color: #e74c3c;
            font-weight: bold;
        }

        .btn-like-active:hover {
            background-color: #fdf2f2;
            color: #c0392b;
        }

        /* Stili per il pulsante condividi */
        .btn-condividi:hover {
            background-color: #f8f9fa;
            color: #28a745;
        }

        /* Stili per il contatore dei like cliccabile */
        #like-count-[id] {
            text-decoration: underline;
            text-decoration-style: dotted;
        }

        [id^="like-count-"]:hover {
            text-decoration: underline;
            color: #e74c3c;
        }

        /* Stili per il tooltip personalizzato */
        .tooltip-inner {
            max-width: 300px;
            text-align: left;
            white-space: pre-line;
        }

        .post-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .back-button {
            color: #667eea;
            font-size: 1.1rem;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .back-button:hover {
            color: #764ba2;
            text-decoration: none;
        }

        .post-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #343a40;
            margin: 0;
        }

        .social-post {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .social-post:hover {
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .post-user-info {
            display: flex;
            align-items: center;
            padding: 20px 20px 15px 20px;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
            border: 3px solid #e9ecef;
            object-fit: cover;
        }

        .user-details h5 {
            margin: 0;
            font-weight: 600;
            color: #343a40;
            font-size: 1.1rem;
        }

        .post-time {
            color: #6c757d;
            font-size: 0.9rem;
            margin: 2px 0 0 0;
        }

        .post-content {
            padding: 0 20px 15px 20px;
            color: #495057;
            line-height: 1.6;
        }

        .post-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-top: 1px solid #f8f9fa;
            background: #f8f9fa;
            border-radius: 0 0 15px 15px;
        }

        .post-stats {
            display: flex;
            gap: 20px;
            color: #6c757d;
            font-size: 0.9rem;
        }

        /* Stile per il pulsante condividi cliccabile */
        .post-stats span[onclick] {
            transition: color 0.3s ease, transform 0.2s ease;
        }

        .post-stats span[onclick]:hover {
            color: #007bff;
            transform: translateY(-1px);
        }

        blockquote {
            background-color:rgb(181, 182, 183) !important;
        }

        .post-controls {
            display: flex;
            gap: 8px;
            position: relative;
            right: -60% !important;
        }

        .btn-post-action {
            border: none;
            background: none;
            color: #6c757d;
            padding: 8px 12px;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn-post-action:hover {
            background: #e9ecef;
            color: #495057;
        }

        .btn-post-action.btn-like:hover {
            color: #dc3545;
        }

        .btn-post-action.btn-share:hover {
            color: #28a745;
        }

        .new-post-form {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .form-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .form-header .user-avatar {
            width: 40px;
            height: 40px;
            margin-right: 12px;
        }

        .form-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #343a40;
            margin: 0;
        }

        .btn-submit-post {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-submit-post:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }

        @media (max-width: 768px) {
            .post-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .post-stats {
                gap: 15px;
            }
            
            .post-controls {
                flex-direction: column;
                gap: 5px;
            }
        }
        </style>';

        // Header della sezione post
        echo '
        <div class="social-post-container">
            <div class="post-header">
                <span class="back-button" onclick="goBack();">
                    <i class="fa fa-arrow-left"></i> '.tr('Torna al Forum').'
                </span>
                <h2 class="post-title">'.$bacheca->nome.'</h2>
            </div>';

        // Lista dei post esistenti
        if(!$posts->isEmpty()) {
            echo '
            <div class="posts-list">';
            
            foreach($posts as $post){
                // Determina l'immagine dell'avatar
                $immagine = '';
                if($post->personaggio) {
                    $immagine = ($post->nome==$post->personaggio->alias ? $post->personaggio->immagine_chat_alias : $post->personaggio->immagine_chat);
                }
                
                if(empty($immagine)) {
                    $immagine = $rootdir.'/assets/dist/img/user.png';
                }

                echo '
                <div class="social-post">
                    <div class="post-user-info">
                        <img src="'.$immagine.'" class="user-avatar" alt="'.$post->nome.'">
                        <div class="user-details">
                            <h5>'.$post->nome.'</h5>
                            <p class="post-time">';
                
                // Mostra informazioni sulla data
                if( $post->created_at != $post->updated_at ){
                    echo '
                                <i class="fa fa-edit"></i> '.tr('Modificato').' '.(new Carbon($post->updated_at))->diffForHumans();
                } else {
                    echo '
                                <i class="fa fa-clock"></i> '.(new Carbon($post->created_at))->diffForHumans();
                }
                
                echo '
                            </p>
                        </div>';
                
                // Controlli per modifica/eliminazione (solo per proprietario o admin)
                if($user->gruppo=='Amministratori' || $post->id_utente==$user->id){
                    echo '
                        <div class="post-controls">
                            <button class="btn btn-warning btn-sm btn-social" onclick="launch_modal(\''.tr('Modifica Post').'\', \''.$rootdir.'/modules/gdr_forum/modals/manage_post.php?id_module='.$id_module.'&id_post='.$post->id.'\');" title="'.tr('Modifica').'">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm btn-social" onclick="eliminaPost('.$post->id.','.get('id_bacheca').');" title="'.tr('Elimina').'">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>';
                }
                
                echo '
                    </div>
                    
                    <div class="post-content">
                        '.$post->messaggio.'
                    </div>
                    
                    <div class="post-actions">
                        <div class="post-stats">';

                // Gestione del pulsante "Mi piace"
                $likes_count = isset($likes_data[$post->id]) ? $likes_data[$post->id] : 0;
                $user_liked = isset($user_likes[$post->id]);
                $like_class = $user_liked ? 'btn-like-active' : 'btn-like';
                $heart_class = $user_liked ? 'fa-heart' : 'fa-heart-o';

                // Mostra il contatore solo se ci sono dei like o se l'utente ha messo like
                $show_count = ($likes_count > 0 || $user_liked);
                $count_display = $show_count ? $likes_count : '';

                echo '
                            <span class="post-action-btn '.$like_class.'" data-post-id="'.$post->id.'">
                                <i class="fa '.$heart_class.'" id="heart-'.$post->id.'" onclick="toggleLike('.$post->id.')" style="cursor: pointer;"></i>
                                <span onclick="'.($likes_count > 0 ? 'showLikesList('.$post->id.')' : 'toggleLike('.$post->id.')').'" style="cursor: pointer;">
                                    <span id="like-count-'.$post->id.'">'.$count_display.'</span>
                                    '.($count_display ? ' ' : '').tr('Mi piace').'
                                </span>
                            </span>
                            <!--span class="post-action-btn">
                                <i class="fa fa-comment"></i>
                                <span>'.tr('Commenti').'</span>
                            </span-->
                            <span class="post-action-btn btn-condividi" data-post-id="'.$post->id.'" data-nome="'.htmlspecialchars($post->nome, ENT_QUOTES).'" data-messaggio="'.htmlspecialchars($post->messaggio, ENT_QUOTES).'">
                                <i class="fa fa-share"></i>
                                <span>'.tr('Condividi').'</span>
                            </span>
                        </div>
                    </div>
                </div>';
            }
            
            echo '
            </div>';
        } else {
            // Messaggio quando non ci sono post
            echo '
            <div class="text-center" style="padding: 40px; color: #6c757d;">
                <i class="fa fa-comments fa-3x" style="margin-bottom: 20px; opacity: 0.5;" ></i>
                <h4>'.tr('Nessun post ancora').'</h4>
                <p>'.tr('Sii il primo a scrivere qualcosa in questa bacheca!').'</p>
            </div>';
        }

        if( ($bacheca->type == 1 || $user->gruppo=='Amministratori') && !$read_only ){
                // Form per nuovo post
                echo '
                <div class="new-post-form">
                    <form action="" method="post" id="rispondi-form" enctype="multipart/form-data">
                        <input type="hidden" name="backto" value="record-edit">
                        <input type="hidden" name="op" value="add_risposta">
                        <input type="hidden" name="id_forum" value="'.get('id_bacheca').'">
                        <input type="hidden" name="id_utente" value="'.$user->id.'">
                        
                        <div class="form-header">';
            
            // Avatar dell'utente corrente per il form
            $user_avatar = '';
            if($user->personaggio && $user->personaggio->immagine_chat) {
                $user_avatar = $user->personaggio->immagine_chat;
            } else {
                $user_avatar = $rootdir.'/assets/dist/img/user.png';
            }
            
            echo '
                            <img src="'.$user_avatar.'" class="user-avatar" alt="Avatar">
                            <h4 class="form-title">'.tr('Scrivi un nuovo post').'</h4>
                        </div>
                        
                        <div class="row">';
    
            // Selezione personaggio per amministratori
            if( $user->gruppo=='Amministratori' ){
                echo '
                            <div class="col-md-6">
                                {["type":"select", "label":"'.tr('Personaggio').'", "name":"idpersonaggio", "ajax-source":"lista_png", "select-options": '.json_encode(['idpersonaggio' => $user->idpersonaggio, "gruppo" => $user->gruppo]).', "value":"'.$user->idpersonaggio.'" ]}
                            </div>';
            }else{
                echo '
                            <input type="hidden" name="idpersonaggio" value="'.$user->idpersonaggio.'">';
            }
    
            echo '
                            <div class="col-md-6">
                                {["type":"select", "label":"'.tr('Nome da visualizzare').'", "name":"nome", "value":"'.$user->personaggio->nome." ".$user->personaggio->cognome.'", "ajax-source":"nome_chat", "select-options":'.json_encode(['idpersonaggio' => $user->idpersonaggio]).' ]}
                            </div>
                        </div>
    
                        <div class="row">
                            <div class="col-md-12">
                                '.input([
                                    'type' => 'ckeditor',
                                    'label' => tr('Il tuo messaggio'),
                                    'name' => 'messaggio',
                                    'required' => 1,
                                    'value' => '',
                                    'extra' => 'style="min-height:120px;"',
                                ]).'
                            </div>
                        </div>
                        
                        <div class="text-right" style="margin-top: 15px;">
                            <button type="button" onclick="rispondi();" class="btn btn-submit-post">
                                <i class="fa fa-paper-plane"></i> '.tr('Pubblica Post').'
                            </button>
                        </div>
                    </form>
                </div>';
        }

        echo '
        </div>'; // Chiusura social-post-container

        // JavaScript per la gestione dei post
        echo '
        <script>
        $(document).ready(function(){
            // Inizializza i componenti della pagina
            init();

            // Animazione di entrata per i post
            $(".social-post").each(function(index) {
                $(this).css("opacity", "0").delay(index * 100).animate({
                    opacity: 1
                }, 500);
            });

            // Assicurati che il CKEditor sia completamente inizializzato
            if (typeof CKEDITOR !== "undefined") {
                CKEDITOR.on("instanceReady", function(event) {
                    console.log("CKEditor instance ready:", event.editor.name);
                });
            }

            // Gestisce il click sui pulsanti condividi
            $(document).on("click", ".btn-condividi", function(e) {
                e.preventDefault();
                console.log("Click rilevato su pulsante condividi");

                var postId = $(this).data("post-id");
                var nome = $(this).data("nome");
                var messaggio = $(this).data("messaggio");

                console.log("Dati estratti:", postId, nome, messaggio);

                // Chiama la funzione condividiPost
                condividiPost(postId, nome, messaggio);
            });
        });

        /**
         * Gestisce il cambio di personaggio per gli amministratori
         */
        $("#idpersonaggio").on("change", function(){
            updateSelectOption("idpersonaggio", $(this).val());
            session_set("superselect,idpersonaggio", $(this).val(), 0);
            $("#nome").selectReset();
        });

        /**
         * Invia un nuovo post alla bacheca
         */
        function rispondi(){
            // Validazione del form
            if(!$("#messaggio").val().trim()) {
                swal("'.tr('Attenzione').'", "'.tr('Inserisci un messaggio prima di pubblicare').'", "warning");
                return;
            }

            // Mostra indicatore di caricamento
            var submitBtn = $(".btn-submit-post");
            var originalText = submitBtn.html();
            submitBtn.html(\'<i class="fa fa-spinner fa-spin"></i> '.tr('Pubblicando...').'\').prop("disabled", true);

            // Invia il form
            salvaForm("#rispondi-form", {
                id_module: "'.$id_module.'",
            }).then(function(response) {
                // Ricarica i post della bacheca
                $("#post").load(globals.rootdir + "/ajax_complete.php?module=Bacheche&op=get_post&id_module='.$id_module.'&id_bacheca='.get('id_bacheca').'");

                // Mostra messaggio di successo
                swal("'.tr('Successo').'", "'.tr('Post pubblicato con successo!').'", "success");
            }).catch(function(error) {
                // Ripristina il pulsante in caso di errore
                submitBtn.html(originalText).prop("disabled", false);
                swal("'.tr('Errore').'", "'.tr('Errore durante la pubblicazione del post').'", "error");
            });
        }

        /**
         * Gestisce la condivisione di un post inserendolo nel CKEditor con tag quote
         */
        function condividiPost(postId, nomeAutore, messaggioPost) {
            console.log("condividiPost chiamata con:", postId, nomeAutore, messaggioPost);

            // Verifica che il form di risposta sia disponibile
            if (!$("#rispondi-form").length) {
                swal("'.tr('Attenzione').'", "'.tr('Non hai i permessi per scrivere in questa bacheca').'", "warning");
                return;
            }

            // Scorri verso il form di risposta prima di tutto
            $("html, body").animate({
                scrollTop: $("#rispondi-form").offset().top - 100
            }, 500);

            // Funzione per inserire il quote nel CKEditor
            function inserisciQuote() {
                // Verifica che il CKEditor sia disponibile
                if (typeof CKEDITOR === "undefined") {
                    swal("'.tr('Errore').'", "'.tr('CKEditor non è caricato').'", "error");
                    return false;
                }

                // Cerca l\'istanza del CKEditor
                var editor = null;
                for (var instance in CKEDITOR.instances) {
                    if (instance === "messaggio" || instance.indexOf("messaggio") !== -1) {
                        editor = CKEDITOR.instances[instance];
                        break;
                    }
                }

                if (!editor) {
                    swal("'.tr('Errore').'", "'.tr('Editor messaggio non trovato').'", "error");
                    return false;
                }

                // Crea il contenuto della citazione con tag blockquote preservando la formattazione HTML
                var quotedContent = "<blockquote style=\"border-left: 4px solid #007bff; padding-left: 15px; margin: 10px 0; color: #666; background-color: #f8f9fa; border-radius: 5px; padding: 15px;\">" +
                    "<p><strong>" + nomeAutore + " '.tr('ha scritto').':</strong></p>" +
                    "<div>" + messaggioPost + "</div>" +
                    "</blockquote>" +
                    "<p><br></p>"; // Aggiunge spazio per la risposta

                try {
                    // Inserisce il contenuto nel CKEditor
                    var currentContent = editor.getData();

                    // Se c\'è già del contenuto, aggiunge la citazione all\'inizio
                    if (currentContent.trim()) {
                        editor.setData(quotedContent + currentContent);
                    } else {
                        editor.setData(quotedContent);
                    }

                    // Posiziona il cursore alla fine
                    setTimeout(function() {
                        editor.focus();
                        // Posiziona il cursore alla fine del contenuto
                        var range = editor.createRange();
                        range.moveToElementEditEnd(editor.document.getBody());
                        editor.getSelection().selectRanges([range]);
                    }, 100);

                    // Mostra messaggio di conferma
                    swal("'.tr('Successo').'", "'.tr('Post citato nel messaggio di risposta').'", "success");
                    return true;
                } catch (error) {
                    console.error("Errore durante l\'inserimento del quote:", error);
                    swal("'.tr('Errore').'", "'.tr('Errore durante l\'inserimento della citazione').'", "error");
                    return false;
                }
            }

            // Prova ad inserire immediatamente
            if (!inserisciQuote()) {
                // Se fallisce, aspetta che il CKEditor sia pronto
                setTimeout(function() {
                    if (!inserisciQuote()) {
                        // Ultimo tentativo dopo un po\' più di tempo
                        setTimeout(function() {
                            inserisciQuote();
                        }, 1000);
                    }
                }, 500);
            }
        }

        /**
         * Gestisce il toggle del "Mi piace" sui post
         */
        function toggleLike(postId) {
            // Mostra un indicatore di caricamento
            var heartIcon = $("#heart-" + postId);
            var likeCount = $("#like-count-" + postId);
            var likeButton = heartIcon.closest("span");

            // Salva lo stato originale
            var originalClass = heartIcon.attr("class");
            var originalCount = parseInt(likeCount.text()) || 0;
            var wasLiked = likeButton.hasClass("btn-like-active");

            // Aggiorna immediatamente l\'interfaccia per feedback rapido
            if (wasLiked) {
                // Rimuovi il like
                heartIcon.removeClass("fa-heart").addClass("fa-heart-o");
                likeButton.removeClass("btn-like-active").addClass("btn-like");
                likeCount.text(Math.max(0, originalCount - 1));
            } else {
                // Aggiungi il like
                heartIcon.removeClass("fa-heart-o").addClass("fa-heart");
                likeButton.removeClass("btn-like").addClass("btn-like-active");
                likeCount.text(originalCount + 1);
            }

            // Invia la richiesta AJAX
            $.ajax({
                url: globals.rootdir + "/modules/gdr_forum/actions.php",
                type: "POST",
                data: {
                    op: "toggle_like",
                    id_post: postId
                },
                dataType: "json",
                success: function(response) {
                    if (response.success) {
                        // Aggiorna il contatore con il valore reale dal server
                        likeCount.text(response.likes_count);

                        // Aggiorna lo stato del pulsante basandosi sulla risposta del server
                        if (response.user_liked) {
                            heartIcon.removeClass("fa-heart-o").addClass("fa-heart");
                            likeButton.removeClass("btn-like").addClass("btn-like-active");
                        } else {
                            heartIcon.removeClass("fa-heart").addClass("fa-heart-o");
                            likeButton.removeClass("btn-like-active").addClass("btn-like");
                        }
                    } else {
                        // Ripristina lo stato originale in caso di errore
                        heartIcon.attr("class", originalClass);
                        likeCount.text(originalCount);
                        if (wasLiked) {
                            likeButton.removeClass("btn-like").addClass("btn-like-active");
                        } else {
                            likeButton.removeClass("btn-like-active").addClass("btn-like");
                        }

                        swal("'.tr('Errore').'", response.message || "'.tr('Errore durante l\'operazione').'", "error");
                    }
                },
                error: function() {
                    // Ripristina lo stato originale in caso di errore di rete
                    heartIcon.attr("class", originalClass);
                    likeCount.text(originalCount);
                    if (wasLiked) {
                        likeButton.removeClass("btn-like").addClass("btn-like-active");
                    } else {
                        likeButton.removeClass("btn-like-active").addClass("btn-like");
                    }

                    swal("'.tr('Errore').'", "'.tr('Errore di connessione').'", "error");
                }
            });
        }

        /**
         * Gestisce l\'hover sui post per effetti visivi
         */
        $(document).on("mouseenter", ".social-post", function() {
            $(this).addClass("shadow-lg");
        }).on("mouseleave", ".social-post", function() {
            $(this).removeClass("shadow-lg");
        });

        /**
         * Mostra la lista completa degli utenti che hanno messo "Mi piace"
         */
        function showLikesList(postId) {
            $.ajax({
                url: globals.rootdir + "/ajax_complete.php",
                type: "GET",
                data: {
                    module: "Bacheche",
                    op: "get_post_likes",
                    id_post: postId
                },
                dataType: "json",
                success: function(response) {
                    if (response.success && response.liked_users.length > 0) {
                        var usersList = "";
                        var totalLikes = response.likes_count;

                        // Crea la lista degli utenti
                        response.liked_users.forEach(function(user, index) {
                            var userName = user.nome && user.cognome ?
                                user.nome + " " + user.cognome :
                                user.username;

                            usersList += "<div style=\"padding: 8px 0; border-bottom: 1px solid #eee;\">";
                            usersList += "<i class=\"fa fa-heart\" style=\"color: #e74c3c; margin-right: 8px;\"></i>";
                            usersList += "<strong>" + userName + "</strong>";
                            if (user.created_at) {
                                var date = new Date(user.created_at);
                                usersList += "<small style=\"color: #6c757d; margin-left: 10px;\">" +
                                    date.toLocaleDateString() + " " + date.toLocaleTimeString() + "</small>";
                            }
                            usersList += "</div>";
                        });

                        // Se ci sono più utenti di quelli mostrati
                        if (totalLikes > response.liked_users.length) {
                            usersList += "<div style=\"padding: 8px 0; color: #6c757d; font-style: italic;\">";
                            usersList += "'.tr('e altri').' " + (totalLikes - response.liked_users.length) + " '.tr('utenti').'...";
                            usersList += "</div>";
                        }

                        // Mostra il modal
                        var modalContent = "<div style=\"max-height: 400px; overflow-y: auto;\">" + usersList + "</div>";

                        swal({
                            title: "'.tr('Mi piace').' (" + totalLikes + ")",
                            html: modalContent,
                            showCloseButton: true,
                            showConfirmButton: false,
                            width: "500px"
                        });
                    } else {
                        swal("'.tr('Informazione').'", "'.tr('Nessun mi piace per questo post').'", "info");
                    }
                },
                error: function() {
                    swal("'.tr('Errore').'", "'.tr('Errore nel caricamento dei dati').'", "error");
                }
            });
        }

        /**
         * Mostra tooltip rapido con alcuni nomi al passaggio del mouse
         */
        $(document).on("mouseenter", "[id^=\'like-count-\']", function() {
            var postId = $(this).attr("id").replace("like-count-", "");
            var likeCount = parseInt($(this).text()) || 0;
            var tooltip = $(this);

            // Mostra tooltip solo se ci sono dei like
            if (likeCount > 0) {
                $.ajax({
                    url: globals.rootdir + "/ajax_complete.php",
                    type: "GET",
                    data: {
                        module: "Bacheche",
                        op: "get_post_likes",
                        id_post: postId
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response.success && response.liked_users.length > 0) {
                            var userNames = response.liked_users.slice(0, 3).map(function(user) {
                                return user.nome && user.cognome ?
                                    user.nome + " " + user.cognome :
                                    user.username;
                            });

                            var tooltipText = "'.tr('Piace a').': " + userNames.join(", ");
                            if (response.likes_count > 3) {
                                tooltipText += " '.tr('e altri').' " + (response.likes_count - 3) + "...";
                            }
                            tooltipText += "\\n'.tr('Clicca per vedere tutti').'";

                            tooltip.attr("title", tooltipText);
                            tooltip.tooltip("show");
                        }
                    }
                });
            }
        }).on("mouseleave", "[id^=\'like-count-\']", function() {
            $(this).tooltip("hide");
        });

        /**
         * Auto-resize del textarea del CKEditor
         */
        if(typeof CKEDITOR !== "undefined") {
            CKEDITOR.on("instanceReady", function(event) {
                var editor = event.editor;
                editor.on("change", function() {
                    // Aggiorna l\'altezza se necessario
                    editor.resize("100%", Math.max(120, editor.document.getBody().$.scrollHeight + 20));
                });
            });
        }

        /**
         * Aggiorna periodicamente i contatori dei "Mi piace" per sincronizzazione
         */
        function updateLikesCounters() {
            $("[id^=\'like-count-\']").each(function() {
                var postId = $(this).attr("id").replace("like-count-", "");
                var currentElement = $(this);
                var heartIcon = $("#heart-" + postId);
                var likeButton = heartIcon.closest("span");

                $.ajax({
                    url: globals.rootdir + "/ajax_complete.php",
                    type: "GET",
                    data: {
                        module: "Bacheche",
                        op: "get_post_likes",
                        id_post: postId
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response.success) {
                            // Aggiorna solo se il valore è cambiato
                            var currentCount = parseInt(currentElement.text()) || 0;
                            if (currentCount !== response.likes_count) {
                                currentElement.text(response.likes_count);
                            }

                            // Aggiorna lo stato del pulsante per l\'utente corrente
                            if (response.user_liked) {
                                heartIcon.removeClass("fa-heart-o").addClass("fa-heart");
                                likeButton.removeClass("btn-like").addClass("btn-like-active");
                            } else {
                                heartIcon.removeClass("fa-heart").addClass("fa-heart-o");
                                likeButton.removeClass("btn-like-active").addClass("btn-like");
                            }
                        }
                    }
                });
            });
        }

        // Aggiorna i contatori ogni 30 secondi (opzionale, può essere disabilitato)
        // setInterval(updateLikesCounters, 30000);

        </script>';

        break;

    case 'get_post_likes':
        // Ottiene informazioni sui "Mi piace" di un post specifico utilizzando i metodi del modello
        $id_post = get('id_post');
        $user = Auth::user();

        if (empty($id_post)) {
            echo json_encode(['error' => tr('ID post non valido')]);
            break;
        }

        // Verifica che il post esista
        $post = Post::find($id_post);
        if (!$post) {
            echo json_encode(['error' => tr('Post non trovato')]);
            break;
        }

        // Utilizza i metodi del modello per ottenere le informazioni
        $likes_count = $post->getLikesCount();
        $user_liked = $user ? $post->isLikedByUser($user->id) : false;
        $liked_users = $post->getLikedUsers(10);

        echo json_encode([
            'success' => true,
            'likes_count' => (int)$likes_count,
            'user_liked' => $user_liked,
            'liked_users' => $liked_users
        ]);
        break;
}
