<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

namespace Modules\Bacheche;

use Common\SimpleModelTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Models\Module;
use Traits\RecordTrait;

class Bacheca extends Model
{
    use SimpleModelTrait;
    use RecordTrait;
    use SoftDeletes;

    protected $table = 'gdr_forum';
    protected $primaryKey = 'id';
    protected $module = 'Bacheche';

    protected $guarded = [];

    public static function build($nome)
    {
        $model = new static();

        $model->nome = $nome;
        $model->save();

        return $model;
    }

    public function save(array $options = [])
    {
        return parent::save($options);
    }

    // Attributi Eloquent
    public function getModuleAttribute()
    {
        return 'Bacheche';
    }
}
