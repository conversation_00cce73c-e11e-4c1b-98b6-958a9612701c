<link rel="stylesheet" type="text/css" media="all" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css"/>
<style>

@import url("https://fonts.googleapis.com/css2?family=Titillium+Web:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700&display=swap");
@import url("https://use.fontawesome.com/releases/v5.13.0/css/all.css");
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
:root {
    /* GENERAL FONT */
    --font1: "Titillium Web", sans-serif;
    --font2: 'Roboto', sans-serif;
    /* GENERAL COLOR */
    --bg-col-1: #0059b3;
    --bg-col-2: #dc3545;
    --bg-col-3: #fff;
    --bg-col-4: #024c96;
    --tx-color-1: #fff;
    --tx-color-2: #000;
    --tx-color-3: #0059b3;
    // FOCUS START
    /* READ MORE COLOR */
    --tx-rm-color-1: #fff;
    --bg-rm-color-1: #fff;
    /* TAG BOTTOM-RIGHT COLOR */
    /* FOOTER CARD COLOR */
    --bg-ft-col-1: #0059b3;
    --tx-ft-color-1: #fff;   
    // focus section
    --tx-focus-section-title: #212529;
    --bg-focus-section: aliceblue;
    --bg-focus-card-overlay: rgba(0, 89, 179, 0.5);
    --tx-focus-hover: #fff;
    --tx-focus-font-weight: 400;
    --tx-focus-tag-color-1: #fff;
    --bg-focus-tag-color-1: #0059b3;
    --bg-focus-cat: #0059b3;
    --tx-focus-cat: #fff;
    --bg-focus-footer: #0059b3;
    --tx-focus-footer: #fff;
    --bg-focus-card: #fff;
    --tx-focus-card: #000;
    //button arrow & archive
    --tx-button: #fff;
    --bg-button: #0059b3;
}
.smartphone {
    height: 90vh;
    width: 55vh;
    border: 16px solid #333;
    border-radius: 36px;
    background-image: url("https://excelsiorgdr.altervista.org/assets/dist/img/skins/xmen/body-bg.jpg");
    background-size: cover;
    background-position: left;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    margin: 20px auto;
}
.navigation {
    display: flex
;
    justify-content: space-around;
    align-items: center;
    height: 60px;
    background-color: #ccc;
    border-bottom-left-radius: 36px;
    border-bottom-right-radius: 36px;
}
.button {
    flex: 1;
    text-align: center;
    padding: 10px;
    cursor: pointer;
    font-weight: bold;
    color: #333;
}
.screen {
    padding: 20px;
    height: calc(100% - 60px);
    overflow: auto;
    color: white;
    background-color: rgba(0, 0, 0, 0.8);
} 
.modal-dialog {
    top: 5vh;
}

.modal-header .close {
    color: white !important;
}

.modal-header h4 {
    display: none;
}
 .focus-section {
    background: var(--bg-focus-section);
    font-family: var(--font1);
    -webkit-transition: all .3s linear 0s;
    transition: all .3s linear 0s;

    .title-section {
        font-weight: 700;
        text-transform: uppercase;
        color: var(--tx-focus-section-title);
    }

    .card {
        font-size: 1rem;
        border: var(--focus-border-card);

        .card-body {
            background: var(--bg-focus-card);
            color: var(--tx-focus-card);

            .card-text {
                font-weight: var(--tx-focus-font-weight);
            }
        }
        .card-footer {
            background: var(--bg-focus-footer);
            color: var(--tx-focus-footer);
            border-radius: 0; /* Remove border radius */
            border-bottom-left-radius: 0 !important; /* Force override Bootstrap */
            border-bottom-right-radius: 0 !important; /* Force override Bootstrap */

            .card-footer__info {
                font-size: 1rem;
                font-weight: 300;
                position: relative;
            }


        .card-image {
            position: relative;

            .image-overlay {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                right: 0;
                height: 100%;
                width: 100%;
                opacity: 0;
                transition: 0.5s ease;
                background: var(--bg-focus-card-overlay);
            }

            &:hover .image-overlay {
                opacity: 1;
            }
        }

        .text-img-over {
            position: relative;


            .overlay {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                right: 0;
                height: 100%;
                width: 100%;
                opacity: 0;
                transition: 0.5s ease;
                background: var(--bg-focus-card-overlay);
            }

            &:hover .overlay {
                opacity: 1;
            }
        }

        .social-hover {
            color: var(--tx-focus-hover);
            font-size: 30px;
            position: absolute;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
            -ms-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
            text-align: center;


            .fa-share-alt {
                color: var(--tx-focus-hover);

                &:hover {
                    color: var(--tx-color-1);
                }
            }


        }

        .hover-text {
            position: relative;
            text-align: center;
            color: var(--tx-focus-hover);
        }

        .bottom-right-tag {
            position: absolute;
            bottom: 0;
            right: 0;
            background: var(--bg-focus-tag-color-1);
            padding: 5px 10px 5px 10px;
            border-top-left-radius: calc(.25rem - 1px);
            font-size: 0.8rem;
            font-weight: 200;
            color: var(--tx-focus-tag-color-1);
            z-index: 1;

            a {
                color: var(--tx-focus-tag-color-1);
                text-decoration: none;
            }

            a:hover {
                color: var(--tx-focus-tag-color-1);
                text-decoration: underline;
            }

        }



        .sub-cat {
            font-weight: 600;
            font-size: 1rem;

            .bg-category {
                background: var(--bg-focus-cat);
                color: var(--tx-focus-cat);
            }

            a {
                color: var(--tx-focus-tag-a);
            }
        }



        .card-title {
            font-family: var(--font1);
            font-weight: 600;
            font-size: 1.3rem;
            /* height: 73px;
  overflow: hidden; */
        }

        .card-footer {
            background: var(--bg-focus-footer);
            color: var(--tx-focus-footer);

            .card-footer__info {
                font-size: 1rem;
                font-weight: 300;
                position: relative;
            }

        }


        .read-more {
            position: absolute;
            right: 0;
            font-weight: 600;
        }

        .read-more-1 {
            text-decoration: none;
            position: relative;
            color: var(--tx-focus-footer);
        }

        .read-more-1::after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 2px;
            background: var(--bg-rm-color-1);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 250ms ease-in;
        }

        .read-more-1:hover {
            color: var(--tx-focus-footer);
        }

        .read-more-1:hover::after {
            transform: scaleX(1);
            transform-origin: right;
            color: var(--tx-focus-footer);
        }

        .read-more-2 {
            text-decoration: none;
            position: relative;
            margin-right: 15px;
            color: var(--tx-focus-footer);
        }

        .read-more-2::after {
            content: "";
            /* You should use \ and not /*/
            font-family: "Font Awesome 5 Free";
            /* This is the correct font-family*/
            position: absolute;
            right: -18px;
            bottom: 0;
            color: var(--tx-focus-footer);
        }

        .read-more-2:hover {
            color: var(--tx-focus-footer);
        }

        .read-more-2:hover::after {
            transform: translate(3px, 0);
            transition: all 0.1s ease-in;
            color: var(--tx-focus-footer);
        }

        .read-more-3 {
            text-decoration: none;
            position: relative;
            color: var(--tx-focus-footer);
        }

        .read-more-3:hover {
            color: var(--tx-focus-footer);
            text-decoration: underline;
        }


    }
}
</style>
<div class="smartphone">
    
    <div id="div-telefono" class="screen">
        <!--NEWS-->
        <section id="focus" class="focus-section">
            <div class="row">
                <div class="col-md-12 text-center"><h2 style="background-color: #dc3545; color: white;">DAILY BUGGLE</h2></div>
            </div>
            <div class="row">
                    <div class="col">
                        <div class="card shadow-sm h-100">
                            <div class="card-image">
                                
                                <div class="image-overlay"></div>
                            </div>
                            <div class="card-body">
                                <h3 class="card-title">Lorem ipsum, dolor sit amet consectetur adipisicing elit</h3>
                                <div class="article-content" style="display: none;">
                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. Vivamus hendrerit arcu sed erat molestie vehicula. Sed auctor neque eu tellus rhoncus ut eleifend nibh porttitor. Ut in nulla enim.</p>
                                    <p>Phasellus molestie magna non est bibendum non venenatis nisl tempor. Suspendisse dictum feugiat nisl ut dapibus. Mauris iaculis porttitor posuere. Praesent id metus massa, ut blandit odio.</p>
                                    <button class="btn btn-sm btn-dark mt-3 close-article">Close</button>
                                </div>
                            </div>
                            <div class="card-footer py-3">
                                <div class="card-footer__info">
                                    <span><i class="far fa-calendar-alt"></i> 01/04/2021</span>
                                    <span class="read-more">
                                        <a class="text-uppercase read-more-1" href="javascript:void(0);">Read more </a>
                                    </span>
                                </div>
                            </div>

                        </div>
                    </div>
            </div>



            <div class="row">
                <div class="col">
                    <div class="card shadow-sm h-100">
                        <div class="card-image">
                            
                            <div class="image-overlay"></div>
                        </div>
                        <div class="card-body">
                            <h3 class="card-title">Lorem ipsum, dolor sit amet consectetur adipisicing elit</h3>
                            <div class="article-content" style="display: none;">
                                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. Vivamus hendrerit arcu sed erat molestie vehicula. Sed auctor neque eu tellus rhoncus ut eleifend nibh porttitor. Ut in nulla enim.</p>
                                <p>Phasellus molestie magna non est bibendum non venenatis nisl tempor. Suspendisse dictum feugiat nisl ut dapibus. Mauris iaculis porttitor posuere. Praesent id metus massa, ut blandit odio.</p>
                                <button class="btn btn-sm btn-dark mt-3 close-article">Close</button>
                            </div>
                        </div>
                        <div class="card-footer py-3">
                            <div class="card-footer__info">
                                <span><i class="far fa-calendar-alt"></i> 01/04/2021</span>
                                <span class="read-more">
                                    <a class="text-uppercase read-more-1" href="javascript:void(0);">Read more </a>
                                </span>
                            </div>
                        </div>

                    </div>
                </div>
        </div>
        </section>
     </div>
   

        <div class="navigation">
            <div class="button" onclick="backPhone();"><i class="fa fa-arrow-left fa-2x"></i></div>
            <div class="button" onclick="caricaHome('home_telefono');"><i class="fa fa-home fa-2x"></i></div>
            <div class="button"><i class="fa fa-square fa-2x"></i></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get all read more links
            const readMoreLinks = document.querySelectorAll('.read-more-1');
            
            // Add click event to each read more link
            readMoreLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    // Find the closest card
                    const card = this.closest('.card');
                    // Find the article content in this card
                    const content = card.querySelector('.article-content');
                    // Toggle the display
                    content.style.display = content.style.display === 'none' ? 'block' : 'none';
                });
            });
            
            // Get all close buttons
            const closeButtons = document.querySelectorAll('.close-article');
            
            // Add click event to each close button
            closeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Find the closest article content
                    const content = this.closest('.article-content');
                    // Hide it
                    content.style.display = 'none';
                });
            });
        });
        </script>