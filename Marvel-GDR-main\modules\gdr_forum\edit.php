<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

include_once __DIR__.'/../../core.php';

use Modules\Bacheche\Bacheca;
use Modules\Bacheche\Post;
use Modules\Schede\Scheda;
use Carbon\Carbon;
use Models\User;

// Recupera le bacheche ON e OFF
$bacheche_on = $dbo->table('gdr_forum')->where('sezione',1)->where('id_parent',NULL)->get();
$bacheche_off = $dbo->table('gdr_forum')->where('sezione',0)->where('id_parent',NULL)->get();
$scheda = Scheda::withTrashed()->find($user->idpersonaggio);
$gruppi = array_column($scheda->gruppi->toArray(), 'id');

/**
 * Funzione ricorsiva per visualizzare le bacheche multilivello con sistema a scomparsa
 * @param object $bacheca - La bacheca da visualizzare
 * @param int $livello - Il livello di profondità (0 = radice)
 * @param bool $sezione_attiva - Se la bacheca appartiene alla sezione attiva (ON) o meno
 */
function visualizzaBachecaRicorsiva($bacheca, $livello = 0, $sezione_attiva = true) {
    global $dbo, $user, $gruppi, $rootdir, $id_module;

    // Verifica i permessi di visualizzazione per la bacheca
    if( !empty($bacheca->id_gruppo) && $user->gruppo != 'Amministratori' && !in_array($bacheca->id_gruppo, $gruppi) ){
        return; // Non mostrare se l'utente non ha i permessi
    }

    // Recupera i figli per determinare se questa bacheca ha sottobacheche
    $figli = $dbo->table('gdr_forum')->where('id_parent', $bacheca->id)->get();
    $ha_figli = !$figli->isEmpty();

    // Calcola l'indentazione basata sul livello
    $indentazione = $livello * 20; // 20px per ogni livello
    $classe_livello = $livello > 0 ? 'subtopic' : 'forum-topic';
    $stile_opacita = $sezione_attiva ? '' : 'style="opacity: 0.7;"';

    // Determina l'azione al click: se ha figli espande/contrae, altrimenti apre i post
    $click_action = $ha_figli ? 'toggleForum('.$bacheca->id.')' : 'openPost('.$bacheca->id.')';

    // ID univoco per il contenitore collassabile
    $collapse_id = 'forum-collapse-'.$bacheca->id;

    echo '
                <div class="'.$classe_livello.' forum-item" data-forum-id="'.$bacheca->id.'" '.$stile_opacita.' style="margin-left: '.$indentazione.'px;">
                    <div class="topic-header">
                        <'.($livello == 0 ? 'h3' : 'span').' class="topic-title forum-clickable" onclick="'.$click_action.';">';

    // Icona per indicare se è espandibile o cliccabile per i post
    if( $ha_figli ){
        echo '<i class="fa fa-chevron-right expand-icon" id="icon-'.$bacheca->id.'"></i> ';
    } else {
        $creatore = User::find($bacheca->created_by);

        echo '<i class="fa fa-comments text-muted" title="'.tr('Creato da _CREATOR_ _DATE_',[
            '_CREATOR_' => $creatore->username,
            '_DATE_' => Carbon::parse($bacheca->created_at)->diffForHumans(),
        ]).'"></i> ';
    }

    echo $bacheca->nome.($bacheca->etichetta ? '<br><small class="text-muted">'.$bacheca->etichetta.'</small>' : '' ).'</'.($livello == 0 ? 'h3' : 'span').'>';

    // Azioni amministratore per ogni livello di bacheca
    if( $user->gruppo == 'Amministratori' ){
        echo '
                        <div class="topic-actions" onclick="event.stopPropagation();">
                            <button class="btn btn-warning btn-sm btn-social" onclick="launch_modal(\''.tr('Modifica Bacheca').'\', \''.$rootdir.'/modules/gdr_forum/modals/manage_bacheca.php?id_module='.$id_module.'&idbacheca='.$bacheca->id.'\');" title="'.tr('Modifica').'">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button class="btn btn-danger btn-sm btn-social" onclick="eliminaBacheca('.$bacheca->id.');" title="'.tr('Elimina').'">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>';
    }

    echo '
                    </div>';

    // Se ci sono figli, crea il contenitore collassabile
    if( $ha_figli ){
        // Tutti i contenitori sono nascosti di default (sistema a scomparsa)
        // Solo i primi elementi (livello 0) rimangono aperti di default
        $classe_visibilita = 'forum-collapsed';

        echo '
                    <div class="forum-children '.$classe_visibilita.'" id="'.$collapse_id.'">';

        // Visualizza ricorsivamente tutti i figli
        foreach($figli as $figlio){
            visualizzaBachecaRicorsiva($figlio, $livello + 1, $sezione_attiva);
        }

        echo '
                    </div>';
    }

    echo '
                </div>';
}

// CSS personalizzato per lo stile social media
echo '
<style>
/* Stile social media per il forum */
.social-forum {
    background-color: transparent;
    min-height: 100vh;
    padding: 20px 0;
}

.forum {
    padding: 2%;
}

.forum-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.forum-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 300;
}

.forum-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
}

.section-title {
    color: #495057;
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.forum-topic {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    position: relative;
}

.forum-topic:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: #667eea;
}

.topic-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.topic-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #343a40;
    margin: 0;
}

.topic-actions {
    display: flex;
    gap: 5px;
}

.subtopics {
    margin-top: 15px;
    padding-left: 20px;
    border-left: 3px solid #e9ecef;
}

.subtopic {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    position: relative;
}

.subtopic:hover {
    background: #e9ecef;
    border-color: #667eea;
}

/* Stili per i livelli multipli di bacheche */
.subtopic[style*="margin-left: 20px"] {
    background: #f1f3f4;
    border-left: 3px solid #667eea;
}

.subtopic[style*="margin-left: 40px"] {
    background: #e8f0fe;
    border-left: 3px solid #4285f4;
}

.subtopic[style*="margin-left: 60px"] {
    background: #fce8e6;
    border-left: 3px solid #ea4335;
}

.subtopic[style*="margin-left: 80px"] {
    background: #e6f4ea;
    border-left: 3px solid #34a853;
}

/* Stile generale per livelli profondi */
.subtopic[style*="margin-left"] {
    border-radius: 6px;
    font-size: 0.95em;
}

/* Indicatore visivo del livello di profondità */
.subtopic::before {
    content: "";
    position: absolute;
    left: -3px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, transparent 0%, rgba(102, 126, 234, 0.3) 50%, transparent 100%);
}

/* Stili per il sistema a scomparsa */
.forum-children {
    overflow: hidden;
    transition: all 0.3s ease;
    max-height: 1000px;
    opacity: 1;
}

.forum-children.forum-collapsed {
    max-height: 0;
    opacity: 0;
    margin: 0;
    padding: 0;
}

/* Icone di espansione */
.expand-icon {
    transition: transform 0.3s ease;
    margin-right: 8px;
    color: #667eea;
}

.expand-icon.expanded {
    transform: rotate(90deg);
}

/* Stili per elementi forum - ora gestiti dalla classe forum-clickable */

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Stili per elementi cliccabili del forum */
.forum-clickable {
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 5px;
    border-radius: 5px;
    display: inline-block;
    width: 100%;
}

.forum-clickable:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(3px);
}

/* Stili specifici per forum con figli */
.forum-clickable:hover .expand-icon {
    color: #764ba2;
    transform: scale(1.2);
}

/* Stili specifici per forum senza figli */
.forum-clickable:hover .fa-comments {
    color: #667eea !important;
    animation: pulse 1s infinite;
}

/* Stili per dispositivi touch */
.forum-clickable.touching {
    background: rgba(102, 126, 234, 0.2);
    transform: scale(0.98);
}

.admin-controls {
    text-align: right;
    margin-bottom: 20px;
}

.btn-social {
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-social:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.post-view {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
}

.post-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.back-button {
    color: #667eea;
    font-size: 1.1rem;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.back-button:hover {
    color: #764ba2;
    text-decoration: none;
}

.post-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #343a40;
    margin: 0;
}

@media (max-width: 768px) {
    .forum-container {
        padding: 0 15px;
    }
    
    .forum-header h1 {
        font-size: 2rem;
    }
    
    .topic-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
</style>';

// Container principale del forum
echo '
<div class="social-forum">
    <div class="forum-container">
        <!-- Header del forum -->
        <div class="forum-header">
            <h1><i class="fa fa-comments"></i> '.tr('Excelsior Forum').'</h1>
            <p class="mb-0">'.tr('Condividi le tue idee e partecipa alle discussioni').'</p>
        </div>';

// Div nascosto per la visualizzazione dei post
echo '
        <div class="forum hidden" id="post"></div>';

// Lista principale delle bacheche
echo '
        <div class="forum" id="lista">';

// Controlli amministratore
if( $user->gruppo=='Amministratori' ){
    echo '
            <div class="admin-controls">
                <button class="btn btn-lg btn-block btn-primary btn-social" onclick="launch_modal(\''.tr('Nuova Bacheca').'\', \''.$rootdir.'/modules/gdr_forum/modals/manage_bacheca.php?id_module='.$id_module.'\');">
                    <i class="fa fa-plus"></i> '.tr('Aggiungi Bacheca').'
                </button>
            </div>';
}

// Sezione Bacheche ON - Utilizzo della funzione ricorsiva
if( !$bacheche_on->isEmpty() ){
    echo '
            <div class="forum-section">
                <div class="section-title '.($user->gruppo!='Amministratori' ? 'hidden' : '' ).'">
                    <i class="fa fa-toggle-on text-success"></i> '.tr('Bacheche Attive').'
                </div>';

    // Visualizza ogni bacheca principale e tutti i suoi sotto-livelli ricorsivamente
    foreach($bacheche_on as $bacheca){
        visualizzaBachecaRicorsiva($bacheca, 0, true);
    }

    echo '
            </div>';
}

// Sezione Bacheche OFF - Utilizzo della funzione ricorsiva
if( !$bacheche_off->isEmpty() ){
    echo '
            <div class="forum-section">
                <div class="section-title">
                    <i class="fa fa-toggle-off text-muted"></i> '.tr('Bacheche Non Attive').'
                </div>';

    // Visualizza ogni bacheca principale e tutti i suoi sotto-livelli ricorsivamente
    foreach($bacheche_off as $bacheca){
        visualizzaBachecaRicorsiva($bacheca, 0, false);
    }

    echo '
            </div>';
}

// Chiusura dei div principali
echo '
        </div> <!-- Fine lista -->
    </div> <!-- Fine forum-container -->
</div> <!-- Fine social-forum -->';

// JavaScript per la gestione del forum
echo '
<script>
// Nasconde l\'header della pagina per un look più pulito
$(document).ready(function(){
    $(".content-header").hide();
});

/**
 * Apre la visualizzazione dei post per una bacheca specifica
 * @param {number} id - ID della bacheca
 */
function openPost(id){
    // Nasconde la lista delle bacheche
    $("#lista").hide();
    // Mostra il contenitore dei post
    $("#post").show();

    // Carica i post della bacheca tramite AJAX
    $("#post").load(globals.rootdir + "/ajax_complete.php?module=Bacheche&op=get_post&id_module='.$id_module.'&id_bacheca=" + id);
}

/**
 * Torna alla visualizzazione della lista delle bacheche
 */
function goBack(){
    // Mostra la lista delle bacheche
    $("#lista").show();
    // Nasconde il contenitore dei post
    $("#post").hide();
}

/**
 * Espande o contrae un forum con sottobacheche utilizzando ID del forum come indice univoco
 * @param {number} forum_id - ID del forum da espandere/contrarre
 */
function toggleForum(forum_id){
    // Utilizza ID del forum come indice univoco per evitare conflitti tra livelli
    var children_container = $("#forum-collapse-" + forum_id);
    var expand_icon = $("#icon-" + forum_id);

    // Verifica che il contenitore esista
    if( children_container.length ){
        // Controlla se è attualmente espanso o contratto
        var is_collapsed = children_container.hasClass("forum-collapsed");

        if( is_collapsed ){
            // Espandi: rimuovi la classe collapsed e ruota l\'icona
            children_container.removeClass("forum-collapsed");
            expand_icon.addClass("expanded");

            // Animazione di espansione fluida con altezza dinamica
            children_container.css({
                "max-height": "0",
                "opacity": "0",
                "display": "block"
            }).animate({
                "max-height": "2000px", // Aumentato per contenere più livelli
                "opacity": 1
            }, 400, function(){
                // Rimuove il limite di altezza dopo animazione per permettere contenuto dinamico
                children_container.css("max-height", "none");

                // Log per debug
                console.log("Espanso forum ID: " + forum_id);
            });

        } else {
            // Contrai: aggiungi la classe collapsed e ripristina l\'icona
            // Prima imposta un\'altezza fissa per l\'animazione
            var current_height = children_container.outerHeight();
            children_container.css("max-height", current_height + "px");

            // Poi anima verso zero
            setTimeout(function(){
                children_container.animate({
                    "max-height": "0",
                    "opacity": 0
                }, 400, function(){
                    children_container.addClass("forum-collapsed");

                    // Log per debug
                    console.log("Contratto forum ID: " + forum_id);
                });
            }, 10);

            expand_icon.removeClass("expanded");
        }
    }
}

/**
 * Elimina un post specifico
 * @param {number} idriga - ID del post da eliminare
 * @param {number} id_bacheca - ID della bacheca contenente il post
 */
function eliminaPost(idriga, id_bacheca){
    swal({
        title: "'.tr('Rimuovere questo post?').'",
        html: "'.tr('Sei sicuro di volere rimuovere questo post?').' '.tr("L'operazione è irreversibile").'.",
        type: "warning",
        showCancelButton: true,
        confirmButtonText: "'.tr('Sì').'",
        cancelButtonText: "'.tr('Annulla').'"
    }).then(function () {
        // Esegue la richiesta AJAX per eliminare il post
        $.ajax({
            url: globals.rootdir + "/actions.php",
            type: "POST",
            dataType: "json",
            data: {
                id_module: '.$id_module.',
                op: "delete_post",
                idriga: idriga,
            },
            success: function (response) {
                // Ricarica i post della bacheca
                $("#post").load(globals.rootdir + "/ajax_complete.php?module=Bacheche&op=get_post&id_module='.$id_module.'&id_bacheca=" + id_bacheca);
                renderMessages();
            },
            error: function() {
                renderMessages();
            }
        });
    }).catch(swal.noop);
}

/**
 * Elimina una bacheca e tutte le sue sottobacheche
 * @param {number} id_bacheca - ID della bacheca da eliminare
 */
function eliminaBacheca(id_bacheca){
    swal({
        title: "'.tr('Rimuovere questa bacheca?').'",
        html: "'.tr('Sei sicuro di volere rimuovere questa bacheca e tutte quelle che contiene?').' '.tr("L'operazione è irreversibile").'.",
        type: "warning",
        showCancelButton: true,
        confirmButtonText: "'.tr('Sì').'",
        cancelButtonText: "'.tr('Annulla').'"
    }).then(function () {
        // Esegue la richiesta AJAX per eliminare la bacheca
        $.ajax({
            url: globals.rootdir + "/actions.php",
            type: "POST",
            dataType: "json",
            data: {
                id_module: '.$id_module.',
                op: "delete_bacheca",
                id_bacheca: id_bacheca,
            },
            success: function (response) {
                // Ricarica la pagina per aggiornare la lista
                location.reload();
                renderMessages();
            },
            error: function() {
                renderMessages();
            }
        });
    }).catch(swal.noop);
}

// Gestione degli eventi per i dispositivi touch
$(document).on("touchstart", ".forum-clickable", function() {
    $(this).addClass("touching");
});

$(document).on("touchend", ".forum-clickable", function() {
    $(this).removeClass("touching");
});

// Inizializzazione del sistema a scomparsa e animazioni
$(document).ready(function() {
    // Inizializza lo stato dei forum collassabili
    initializeForumCollapse();

    // Animazione di entrata per le sezioni del forum
    $(".forum-section").each(function(index) {
        $(this).css("opacity", "0").delay(index * 100).animate({
            opacity: 1
        }, 500);
    });

    // Tooltip per i pulsanti
    $("[title]").tooltip();
});

/**
 * Inizializza lo stato di espansione/contrazione dei forum utilizzando ID univoci
 * Tutti i forum sono chiusi di default ad eccezione dei primi elementi
 */
function initializeForumCollapse(){
    // Trova tutti i contenitori di figli utilizzando gli ID specifici
    $(".forum-children").each(function(){
        var container = $(this);
        var container_id = container.attr("id");

        // Tutti i contenitori iniziano come collassati
        if( container.hasClass("forum-collapsed") ){
            container.css({
                "max-height": "0",
                "opacity": "0",
                "overflow": "hidden"
            });

            // Log per debug (rimuovere in produzione)
            console.log("Inizializzato contenitore collassato: " + container_id);
        }
    });

    // Imposta le icone iniziali per tutti i forum con figli
    $(".expand-icon").each(function(){
        var icon = $(this);
        var icon_id = icon.attr("id");

        // Rimuove la classe expanded da tutte le icone
        icon.removeClass("expanded");

        // Log per debug (rimuovere in produzione)
        console.log("Inizializzata icona: " + icon_id);
    });

    // Messaggio di debug per confermare inizializzazione
    console.log("Sistema forum collassabile inizializzato con ID univoci");
}

</script>';
