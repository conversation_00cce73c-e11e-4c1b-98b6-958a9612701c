# js-ext

## Description

The Javascript extension. Provides additional functionality to classes when they are available and when particular functionality is missing.

 * Object
 * String
 * Number
 * Function
 * Array
 * ArrayBuffer
 * TypedArray
 * Set
 * Screen
 * Location
 * HTMLElement
 * HTMLImageElement
 * Image
 * Canvas (Adds support for OffscreenCanvas when is not available)
 * DOMPoint
 * DOMRect
 * DOMMatrix

Functionallity extensions through not existant classes:
 * DOMSize
