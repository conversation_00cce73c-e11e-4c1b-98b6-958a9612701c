/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

input,
button,
.btn,
header,
.main-sidebar,
.main-footer,
#widget-controller_top,
#widget-controller_right,
.daterangepicker,
.datepicker,
#main-records_filter,
.extra-info,
.deleteicon,
.fa.deleteicon::after,
.fa.deleteicon::before,
.hide,
#back-to-top {
    display: none;
}

td {
    border: 1px solid #ccc;
}

th {
    background: #333;
    color: #fff;
    text-align: left;
    padding: 4px;
}

nav {
    float: left;
}

ul {
    margin: 0;
    padding: 0;
}

li.header i {
    display: none;
}

li.header {
    list-style: none;
    margin: 10px 0;
    padding: 0;
}

li.header > a {
    font-size: 30px;
    text-decoration: none;
    font-weight: bold;
    color: #333;
    text-align: left;
    float: left;
}

.table {
    width: 100%;
}

.text-right {
    text-align: right;
}

.pull-left {
    float: left;
    text-align: left;
}

#totali_colonne td {
    background: #eee;
}

#totali_colonne td big {
    font-size: 22px;
}

.li-widget, .nav-tabs, .dataTables_info, tfoot, #th_selector, td.select-checkbox, th.search.sorting {
    display: none !important;
}

a[href]:after {
    content: none !important;
}
