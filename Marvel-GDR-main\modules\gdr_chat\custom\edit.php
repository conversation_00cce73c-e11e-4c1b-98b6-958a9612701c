<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

$skip_permissions = true;
include_once __DIR__.'/../../core.php';

use Models\Module;
use Modules\Schede\Scheda;

//Se l'utente non ha un personaggio approvato blocco la chat
if( !$user->personaggio->is_approvato && $user->gruppo!='Amministratori' ){
    $read_only = true;

    echo '
    <script>
        $(document).ready(function(){
            $("#nascondi_tools").trigger("click");
        });
    </script>';
}

$id_record = get('id_chat');
$chat = $dbo->table('gdr_chats')->where('id',$id_record)->first();

$scheda = Scheda::find($user->personaggio->id);
$scheda->id_mappa = ($chat->id_mappa>0 ? $chat->id_mappa : 1);
$scheda->id_chat = $id_record;
$scheda->save();

// Definisco $quest prima del suo utilizzo per evitare errori
$quest = $dbo->table('gdr_quest')->where('idchat', $id_record)->where('stato',1)->first();

if( setting('Posizione tools in chat')!='Sotto' ){

    echo '
    <style>
    .table-condensed td {
        border-top: none;
    }

    #fire-text {
        font-family: "Open Sans", sans-serif;
        color: black;
        text-shadow:
        0px -2px 4px #aaa,
        0px -2px 10px #FF3,
        0px -10px 20px #F90,
        0px -20px 40px #C33;
        font-size: 60px;
    }

    </style>';

    echo '
    <div class="col-md-2 text-right">
        <button type="button" class="btn btn-primary" onclick="launch_modal(\''.tr('Griglia di gioco').' '.$quest->nome.'\', \''.base_path().'/modules/gdr_chat/modals/grid.php?id_quest='.$quest->id.'&id_chat='.$id_record.'\', 1);">
            <i class="fa fa-th"></i> '.tr('Griglia').'
        </button>
    </div>';

    $frame_chat = '
    <div class="col-md-9" id="area_chat">
        <a id="nascondi_tools" class="btn btn-sm pull-right" style="color:white;" onclick="openPannello();"><i class="fa fa-bars"></i></a>
        <div id="frame-chat"></div>
    </div>';

    $frame_tools = '
    <div class="col-md-3" id="pannello">
        <form action="" method="post" id="chat-form">
            <input type="hidden" name="backto" value="record-edit">
            <input type="hidden" name="op" value="add">
            <input type="hidden" name="id_module" value="'.$id_module.'">
            <input type="hidden" name="id_chat" value="'.$id_record.'">
            <input type="hidden" name="id_user" value="'.$user->id.'">
            <input type="hidden" name="idpersonaggio" value="'.$user->idpersonaggio.'">
            <input type="hidden" name="nome_chat" id="nome_chat" value="'.$user->personaggio->nome." ".$user->personaggio->cognome.'">

            <div class="row">
                <div class="col-md-6">
                    {["type":"select", "label":"", "name":"type", "values":"list=\"0\":\"'.tr('Azione').'\",\"1\":\"'.tr('Sussurro').'\"'.($user->gruppo!='Giocatori' ? ', \"2\":\"'.tr('Esito Osservatore').'\"' : '').' ", "value":"0"]}
                </div>
                <div class="col-md-6">
                    {["type":"text", "label":"", "name":"tag", "value":"", "placeholder":"'.tr('Tag').'"]}
                </div>';
            if( $user->gruppo=='Amministratori' || $user->gruppo=='Master' ){
                $frame_tools .= '
                <div class="col-md-6">
                    {["type":"select", "label":"", "name":"idpersonaggio", "ajax-source":"lista_png", "select-options": '.json_encode(['idpersonaggio' => $user->idpersonaggio, "gruppo" => $user->gruppo]).', "value":"'.$user->idpersonaggio.'", "placeholder":"'.tr('Usa PNG').'" ]}
                </div>';
            }else{
                $frame_tools .= '
                <input type="hidden" name="idpersonaggio" value="'.$user->idpersonaggio.'">';
            }
            $frame_tools .= '
                <div class="col-md-'.( $user->gruppo=='Amministratori' || $user->gruppo=='Master' ? '6' : '12' ).'">
                    {["type":"select", "label":"", "name":"nome_chat", "value":"'.$user->personaggio->nome." ".$user->personaggio->cognome.'", "ajax-source":"nome_chat", "select-options":'.json_encode(['idpersonaggio' => $user->idpersonaggio]).' ]}
                </div>
                <div class="col-md-12">
                    {["type":"select", "name":"add_dado", "value":"", "ajax-source":"dadi", "placeholder":"'.tr('Lancio dado disattivato').'" ]}
                </div>
                <div class="col-md-10">
                    {["type":"select", "label":"", "placeholder":"'.tr('Seleziona potere').'", "name":"idpotere", "value":"", "ajax-source":"poteri_personaggio", "select-options":'.json_encode(['idpersonaggio' => $user->idpersonaggio, 'is_chat' => true]).' ]}
                </div>
                <div class="col-md-2">
                    {["type":"text", "label":"", "placeholder":"'.tr('Costo').'", "name":"costo", "value":"", "class":"text-center" ]}
                </div>
                <div class="col-md-12">
                    {["type":"select", "label":"", "placeholder":"'.tr('Seleziona oggetto').'", "name":"idoggetto", "value":"", "ajax-source":"oggetti_personaggio", "select-options":'.json_encode(['idpersonaggio' => $user->idpersonaggio]).' ]}
                </div>
                <div class="col-md-6">
                    {["type":"select", "label":"", "placeholder":"'.tr('Seleziona caratteristica').'", "name":"caratteristica", "value":"", "values":"list=\"car_0\":\"'.tr('Mischia').'\",\"car_1\":\"'.tr('Agilità').'\",\"car_2\":\"'.tr('Resilienza').'\",\"car_3\":\"'.tr('Vigilanza').'\",\"car_4\":\"'.tr('Ego').'\",\"car_5\":\"'.tr('Logica').'\" " ]}
                </div>
                <div class="col-md-6">
                    {["type":"text", "label":"", "placeholder":"'.tr('Bonus Osservatore').'", "label":"", "name":"bonus_master", "value":""]}
                </div>
                <div class="col-md-12">
                    {["type":"text", "label":"", "name":"immagine_esito", "value":"", "placeholder":"'.tr('Aggiungi un immagine').'", "icon-before":"<i class=\"fa fa-camera\"></i>"]}
                </div>
                <div class="col-md-12">
                    {[ "type":"textarea", "label":"", "name":"azione", "value":"", "charcounter": 1, "placeholder":"'.tr('Testo azione').'", "extra":"rows=\"20\" "]}
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-md-12">
                    <button type="button" id="btn-invia" class="btn btn-default" onclick="salvaAzione($(this));" style="width:100%;height:40%;margin-bottom:3%;">'.tr('Invia').'</button>
                    <br>
                    <div class="btn-group" style="width:100%;height:40%;">
                        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="width:100%;height:100%;">
                            '.tr('Strumenti').'
                            <span class="caret"></span>
                            <span class="sr-only">Toggle Dropdown</span>
                        </button>

                        <ul class="dropdown-menu">';

                        if( $user->gruppo!='Giocatori' ){
                            $quest = $dbo->table('gdr_quest')->where('idchat', $id_record)->where('stato',1)->first();

                            if( !empty($quest) ){
                                $frame_tools .= '
                                <a class="table-btn clickable dropdown-item" onclick="chiudiQuest('.$quest->id.');"><i class="fa fa-times"></i> '.tr('Chiudi quest').'</a>';
                            }else{
                                $frame_tools .= '
                                <a class="table-btn clickable dropdown-item" onclick="launch_modal(\''.tr('Apri Quest').'\', \''.$rootdir.'/modules/gdr_chat/modals/open_quest.php?id_module='.$id_module.'&id_chat='.get('id_chat').'\');"><i class="fa fa-book"></i> '.tr('Apri quest').'</a>';
                            }

                            // Pulsante Salva chat (spostato qui per essere vicino al pulsante Apri quest)
                            if( $user->gruppo=='Amministratori' ){
                                $frame_tools .= '
                                <a class="table-btn clickable dropdown-item" onclick="salvaChatPDF('.$id_record.');"><i class="fa fa-save"></i> '.tr('Salva chat').'</a>';
                            }
                        } else {
                            // Pulsante Salva chat (disponibile per tutti i gruppi)
                            //$frame_tools .= '
                            //<a class="table-btn clickable dropdown-item" onclick="salvaChatPDF('.$id_record.');"><i class="fa fa-save"></i> '.tr('Salva chat').'</a>';
                        }

                            if( $chat->is_giocabile || in_array($chat->id,permessiChat($user,true)) || $user->gruppo=='Amministratori' ){
                                $frame_tools .= '
                                <a class="table-btn clickable dropdown-item" onclick="launch_modal(\''.tr('Invita giocatori').'\', \''.$rootdir.'/modules/gdr_chat/modals/gestione_inviti.php?id_module='.$id_module.'&id_chat='.get('id_chat').'\');"><i class="fa fa-ticket"></i> '.tr('Invita giocatori').'</a>';
                            }
    $frame_tools .= '
                        </ul>
                    </div>
                </div>
            </div>
        </form>
    </div>';

    echo '
    <div class="row">';
        if( setting('Posizione tools in chat')=='Sinistra' ){
            echo $frame_tools.$frame_chat;
            echo '
            <script>
                $(document).ready(function(){
                    $("#nascondi_tools").removeClass("pull-right");
                });
            </script>';
        }else{
            echo $frame_chat.$frame_tools;
        }
    echo '
    </div>';


    //Avviso in chat per note fato
    if( !empty($chat->note_fato) ){
        echo '
        <script>
            $(document).ready(function(){
                launch_modal("'.tr('Note fato').'", "'.$rootdir.'/modules/gdr_chat/modals/note_fato.php?id_module='.$id_module.'&id_chat='.get('id_chat').'");
            });
        </script>';
    }

    if( setting('Richiedi conferma all\'invio delle azioni in chat')){
        echo '
        <script>
        $(document).on("keydown", function(e) {
            var keyCode = e.keyCode || e.which;
            if (keyCode === 13) {
                salvaAzione($("#btn-invia"));
            }
        });

        function salvaAzione(btn){
            var form = $("#chat-form");

            swal({
                title: "'.tr('Invia azione').'",
                html: "'.tr('Sei sicuro di volere inviare l\'azione? Hai dimenticato nulla?').'<br>(Tag posizione, Azione, Dado, Potere, Statistica, Oggetto)",
                type: "warning",
                showCancelButton: true,
                confirmButtonText: "'.tr('Sì').'",
            }).then(function () {
                var restore = buttonLoading(btn);

                $.ajax({
                    url: globals.rootdir + \'/actions.php\',
                    data: form.serialize(),
                    type: \'POST\',
                    dataType: "json",
                    success: function (response) {
                        renderMessages();
                        caricaAzioni($("#frame-chat"));
                        buttonRestore(btn, restore);
                        $("#azione").val("").trigger("change");
                        $("#idpotere").selectReset();
                        $("#idoggetto").selectReset();
                        $("#caratteristica").val("").trigger("change");

                        $("#bonus_master").val(\'\').trigger("change");
                        $("#add_dado").selectReset();

                        if( response.lancio==\'616\' ){
                            swal({
                                title: "EXCELSIOR!!!",
                                html: \'<div class="dice text-center" style="animation: vibrate 2s linear infinite;">6</div> <div class="dice-m-critico"></div> <div class="dice text-center" style="animation: vibrate 2s linear infinite;">6</div>\',
                                imageUrl: "'.$rootdir.'/assets/dist/img/skins/chat/amazing.png",
                                imageWidth: 400,
                                imageHeight: 200,
                                showConfirmButton: false,
                            });
                            $("#confetti").show();
                            $("#confetti").on("click",function(){
                                $("#confetti").hide();
                                $(".swal2-container").trigger("click");
                            });
                        }

                        if( response.lancio==\'666\' ){
                            swal({
                                title: "<span id=\'fire-text\'>ECCELLENTE!</span>",
                                html: \'<div class="dice text-center" style="animation: vibrate 2s linear infinite;">6</div> <div class="dice-m"  style="animation: vibrate 2s linear infinite;">6</div> <div class="dice text-center" style="animation: vibrate 2s linear infinite;">6</div>\',
                                imageUrl: "'.$rootdir.'/assets/dist/img/skins/chat/maphisto.png",
                                imageWidth: 400,
                                imageHeight: 200,
                                showConfirmButton: false,
                            });
                        }

                        if( response.lancio==\'111\' ){
                            swal({
                                title: "<span>AHAHAHA!</span>",
                                html: \'<div class="dice text-center" style="animation: vibrate 2s linear infinite;">1</div> <div class="dice-m"  style="animation: vibrate 2s linear infinite;">1</div> <div class="dice text-center" style="animation: vibrate 2s linear infinite;">1</div>\',
                                imageUrl: "'.$rootdir.'/assets/dist/img/skins/chat/fallimento.gif",
                                imageWidth: 300,
                                imageHeight: 200,
                                showConfirmButton: false,
                            });
                        }

                        setTimeout(function(){
                            if( response.drop ){
                                showDrop(response.drop);
                            }
                        },2000);

                    },
                    error: function() {
                        renderMessages();
                        buttonRestore(btn, restore);
                    }
                });
            }).catch(swal.noop);
        }
        </script>';
    }else{
        echo '
        <script>
        $(document).on("keydown", function(e) {
            var keyCode = e.keyCode || e.which;
            if (keyCode === 13) {
                salvaAzione($("#btn-invia"));
            }
        });

        function salvaAzione(btn){
            var form = $("#chat-form");
            var restore = buttonLoading(btn);

            $.ajax({
                url: globals.rootdir + \'/actions.php\',
                data: form.serialize(),
                type: \'POST\',
                dataType: "json",
                success: function (response) {
                    renderMessages();
                    caricaAzioni($("#frame-chat"));
                    buttonRestore(btn, restore);
                    $("#azione").val("").trigger("change");
                    $("#idpotere").selectReset();
                    $("#idoggetto").selectReset();
                    $("#caratteristica").val("").trigger("change");

                    $("#bonus_master").val(\'\').trigger("change");
                    $("#add_dado").selectReset();

                    if( response.lancio==\'616\' ){
                        swal({
                            title: "EXCELSIOR!!!",
                            html: \'<div class="dice text-center" style="animation: vibrate 2s linear infinite;">6</div> <div class="dice-m-critico"></div> <div class="dice text-center" style="animation: vibrate 2s linear infinite;">6</div>\',
                            imageUrl: "<?php echo $rootdir;?>/assets/dist/img/skins/chat/amazing.png",
                            imageWidth: 400,
                            imageHeight: 200,
                            showConfirmButton: false,
                        });
                        $("#confetti").show();
                        $("#confetti").on("click",function(){
                            $("#confetti").hide();
                            $(".swal2-container").trigger("click");
                        });
                    }

                    if( response.lancio==\'666\' ){
                        swal({
                            title: "<span id=\'fire-text\'>ECCELLENTE!</span>",
                            html: \'<div class="dice text-center" style="animation: vibrate 2s linear infinite;">6</div> <div class="dice-m"  style="animation: vibrate 2s linear infinite;">6</div> <div class="dice text-center" style="animation: vibrate 2s linear infinite;">6</div>\',
                            imageUrl: "<?php echo $rootdir;?>/assets/dist/img/skins/chat/maphisto.png",
                            imageWidth: 400,
                            imageHeight: 200,
                            showConfirmButton: false,
                        });
                    }

                    if( response.lancio==\'111\' ){
                        swal({
                            title: "<span>AHAHAHA!</span>",
                            html: \'<div class="dice text-center" style="animation: vibrate 2s linear infinite;">1</div> <div class="dice-m"  style="animation: vibrate 2s linear infinite;">1</div> <div class="dice text-center" style="animation: vibrate 2s linear infinite;">1</div>\',
                            imageUrl: "<?php echo $rootdir;?>/assets/dist/img/skins/chat/fallimento.gif",
                            imageWidth: 300,
                            imageHeight: 200,
                            showConfirmButton: false,
                        });
                    }

                    setTimeout(function(){
                        if( response.drop ){
                            showDrop(response.drop);
                        }
                    },2000);

                },
                error: function() {
                    renderMessages();
                    buttonRestore(btn, restore);
                }
            });
        }
        </script>';
    }
    ?>

    <script>
        $(document).load("ajax_complete.php?op=get_tags&id_chat=<?php echo $id_record;?>&id_user=<?php echo $user->id;?>", function(response){
            $("#tag").autocomplete({source: response.split("|")});
        });

        $(document).ready(function(){
            $("#immagine_esito").parent("div").parent("div").hide();
            setTimeout(function() {
                var lunghezza = $("#azione").val().length;
                $("#azione").focus().get(0).setSelectionRange(lunghezza, lunghezza);
            },300);

            if( !$(".layout-fixed").hasClass("sidebar-collapse") ){
                $(".layout-fixed").addClass("sidebar-collapse");
            }

            $(".content-header").html('<h3 class="gdr-header" style="display:inline-block;><?php echo ($chat->note_fato ? '<a class="btn btn-sx btn-default"><i class="fa fa-info-circle bounce"></i></a> ' : '<a class="btn btn-sx btn-default"><i class="fa fa-info-circle"></i></a> ').$chat->nome;?></h3> &emsp;<a class="btn btn-sx btn-default unblockable" style="display:inline-block;margin-top:-0.5%;" onclick="window.location.replace(\'<?php echo $rootdir;?>/controller.php?id_module=<?php echo Module::where('name', 'Mappa')->first()->id;?>&return=1\');"><i class="fa fa-map"></i></a><div class="font-buttons" style="float:right;"><button class="btn btn-sx btn-default" onclick="caricaAzioni($(\'#frame-chat\'));" ><i class="fa fa-refresh"></i></button>&ensp;<button class="btn btn-sx btn-default" onclick="aumentaFont()">A+</button>&ensp;<button class="btn btn-sx btn-default" onclick="diminuisciFont()">A-</button></div>');
            caricaAzioni($("#frame-chat"));

            setInterval(function(){
                caricaAzioni($("#frame-chat"));
            },30000);
        });

        function openPannello() {
            if ($("#pannello").is(":visible")) {
                $("#pannello").fadeOut(300, function() {
                    $("#area_chat").removeClass("col-md-9").addClass("col-md-12");
                });
            } else {
                $("#area_chat").removeClass("col-md-12").addClass("col-md-9");
                $("#pannello").fadeIn(300);
            }
        }

        function aumentaFont() {
            let currentSize = parseInt($("#frame-chat .azione-chat:not(.tag-chat)").css("font-size"));
            $("#frame-chat .azione-chat:not(.tag-chat)").css("font-size", (currentSize + 1) + "px");
        }

        function diminuisciFont() {
            let currentSize = parseInt($("#frame-chat .azione-chat:not(.tag-chat)").css("font-size"));
            if (currentSize > 8) {
                $("#frame-chat .azione-chat:not(.tag-chat)").css("font-size", (currentSize - 1) + "px");
            }
        }

        $("#idpersonaggio").on("change",function(){
            updateSelectOption("idpersonaggio", $(this).val());

            $("#idpotere").selectReset();
            $("#idoggetto").selectReset();
            $("#nome_chat").selectReset();
        });

        $("#idpotere").on("change",function(){
            $("#costo").val(0);

            if( $(this).val() ){
                var data = $(this).selectData();
                if( data.costo ){
                    $("#costo").val(data.costo);
                }
            }
        });

        function caricaAzioni(container){
            let currentSize = parseInt($("#frame-chat .azione-chat:not(.tag-chat)").css("font-size"));

            localLoading(container, true);
            return $.get(globals.rootdir + "/modules/gdr_chat/ajax_chat.php?id_module=<?php echo $id_module;?>&id_record=<?php echo $id_record;?>", function(data) {
                container.html(data);
                localLoading(container, false);
                $("#frame-chat .azione-chat:not(.tag-chat)").css("font-size", currentSize + "px");
            });
        }

        $("#idpersonaggio").change(function(){
            $("#nome_chat").selectReset();
        });

        $("#azione").on("keyup",function(){
            var count = $(this).val().length;
            $("#conta_caratteri").html(count);
        });

        $("#type").change(function(){
            if( $(this).val()==2 || $(this).val()==1 ){
                $("#idpotere").selectReset();
                $("#idpotere").parent("div").parent("div").hide();

                $("#idoggetto").selectReset();
                $("#idoggetto").parent("div").parent("div").hide();

                $("#caratteristica").val("").trigger("change");
                $("#caratteristica").parent("div").parent("div").hide();

                $("#bonus_master").val('').trigger("change");
                $("#bonus_master").hide();

                $("#add_dado").selectReset();
                $("#add_dado").parent("div").parent("div").hide();

                if( $(this).val()==2 ){
                    $("#immagine_esito").parent("div").parent("div").show();
                }else{
                    $("#immagine_esito").val('').trigger("change");
                    $("#immagine_esito").parent("div").parent("div").hide();
                }
            }else{
                $("#add_dado").parent("div").parent("div").show();
                $("#idpotere").parent("div").parent("div").show();
                $("#idoggetto").parent("div").parent("div").show();
                $("#bonus_master").show();
                $("#caratteristica").parent("div").parent("div").show();

                $("#immagine_esito").val('').trigger("change");
                $("#immagine_esito").parent("div").parent("div").hide();
            }
        });

        function chiudiQuest(idquest){
            swal({
                title: "<?php echo tr('Chiudi Quest');?>",
                html: "<?php echo tr('Sei sicuro di volere chiudere questa Quest?');?>",
                type: "warning",
                showCancelButton: true,
                confirmButtonText: "<?php echo tr('Sì');?>"
            }).then(function () {
                $.ajax({
                    url: globals.rootdir + "/actions.php",
                    type: "POST",
                    dataType: "json",
                    data: {
                        id_module: <?php echo $id_module;?>,
                        id_chat: <?php echo get('id_chat');?>,
                        idquest: idquest,
                        op: "chiudi_quest",
                    },
                    success: function (response) {
                        location.reload();
                    },
                    error: function() {
                        renderMessages();
                    }
                });
            }).catch(swal.noop);
        }

        // Funzione per salvare la chat in PDF
        function salvaChatPDF(id_chat) {
            swal({
                title: "<?php echo tr('Salva chat');?>",
                html: "<?php echo tr('Seleziona quante ore di chat vuoi salvare in formato PDF:');?>" +
                      "<br><br>" +
                      "<select id='ore_chat' class='form-control'>" +
                      "<option value='4'>Ultime 2 ore</option>" +
                      "<option value='8'>Ultime 4 ore</option>" +
                      "</select>",
                type: "info",
                showCancelButton: true,
                confirmButtonText: "<?php echo tr('Sì');?>"
            }).then(function () {
                // Ottieni il numero di ore selezionato
                var ore_chat = $('#ore_chat').val();

                /* Mostra un indicatore di caricamento
                swal({
                    title: "<?php echo tr('Generazione PDF in corso...');?>",
                    text: "<?php echo tr('Attendere prego...');?>",
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    onOpen: () => {
                        swal.showLoading();
                    }
                });
                */

                window.open(globals.rootdir + "/pdfgen.php?id_print=57&id_record=" + id_chat + "&ore=" + ore_chat , "_blank");

                /* Invia la richiesta AJAX per generare il PDF
                $.ajax({
                    url: globals.rootdir + "/pdfgen.php",
                    type: "GET",
                    data: {
                        id_module: <?php echo $id_module;?>,
                        id_print: 57,
                        id_record: id_chat,
                        skin: "<?php echo setting('Skin'); ?>",
                        ore: ore_chat
                    },
                    success: function(response) {
                        try {
                            var data = JSON.parse(response);
                            if (data.result) {
                                swal.close();
                                // Apri il PDF in una nuova finestra
                                window.open(data.url, '_blank');
                            } else {
                                swal({
                                    title: "<?php echo tr('Errore');?>",
                                    text: data.message || "<?php echo tr('Si è verificato un errore durante la generazione del PDF.');?>",
                                    type: "error"
                                });
                            }
                        } catch (e) {
                            console.error("Errore durante il parsing della risposta:", e);
                            swal({
                                title: "<?php echo tr('Errore');?>",
                                text: "<?php echo tr('Si è verificato un errore durante la generazione del PDF.');?>",
                                type: "error"
                            });
                        }
                    },
                    error: function() {
                        swal({
                            title: "<?php echo tr('Errore');?>",
                            text: "<?php echo tr('Si è verificato un errore durante la generazione del PDF.');?>",
                            type: "error"
                        });
                    }
                });
                */
            }).catch(swal.noop);
        }
    </script>
<?php
}else{
    include_once __DIR__.'/../edit.php';
}