<?php

declare(strict_types=1);

/*
 * This file is part of the league/config package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace League\Config;

/**
 * Interface for a service which provides a readable configuration object
 */
interface ConfigurationProviderInterface
{
    public function getConfiguration(): ConfigurationInterface;
}
