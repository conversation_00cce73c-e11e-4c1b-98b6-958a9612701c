!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jsExt={})}(this,(function(t){"use strict";function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function r(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t,e,n){return e&&r(t.prototype,e),n&&r(t,n),t}function o(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function a(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&s(t,e)}function u(t){return(u=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function s(t,e){return(s=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function c(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function f(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,r=u(t);if(e){var i=u(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return c(this,n)}}function l(t){return function(t){if(Array.isArray(t))return y(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||h(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){if(t){if("string"==typeof t)return y(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?y(t,e):void 0}}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function p(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=h(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,u=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){u=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(u)throw o}}}}var d=function(){function t(){n(this,t)}return i(t,null,[{key:"overrides",get:function(){return["toString"]}},{key:"extend",value:function(t){var e,n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this;return"string"==typeof t?(e=t,t=globalThis[e]):e=t.name,t?(this.debug&&console.log("============================================================ extend",t.name,r.name),Object.getOwnPropertyNames(r.prototype).filter((function(t){return"constructor"!=t})).forEach((function(e){!(e in t.prototype)||r.overrides.includes(e)?(n.debug&&(r.overrides.includes(e)?console.log("%coverride ".concat(e),"color: chartreuse"):console.log("%cdefine ".concat(e),"color: green")),t.prototype[e]=r.prototype[e]):n.debug&&console.log("%cexclude ".concat(e),"color: red")})),Object.getOwnPropertyNames(r).forEach((function(e){"function"==typeof r[e]&&"extend"!=e&&(!(e in t)||r.overrides.includes(e)?(n.debug&&(r.overrides.includes(e)?console.log("%coverride static ".concat(e),"color: chartreuse"):console.log("%cdefine static ".concat(e),"color: orange")),t[e]=r[e]):n.debug&&console.log("%cexclude static ".concat(e),"color: red"))})),r.properties&&Object.keys(r.properties).forEach((function(e){e in t.prototype?n.debug&&console.log("%cexclude prop ".concat(e),"color: red"):r.properties[e]&&(n.debug&&console.log("%cdefine prop ".concat(e),"color: darkseagreen"),Object.defineProperty(t.prototype,e,r.properties[e]))})),r.classProperties&&Object.keys(r.classProperties).forEach((function(e){e in t?n.debug&&console.log("%cexclude static prop ".concat(e),"color: red"):r.classProperties[e]&&(n.debug&&console.log("%cdefine static prop ".concat(e),"color: chocolate"),Object.defineProperty(t,e,r.classProperties[e]))})),!0):(this.debug&&console.warn("============================================================ Class ".concat(e," not found")),!1)}}]),t}(),v=function(){function t(e,r,i){n(this,t),Object.defineProperty(this,"type",{value:e,enumerable:!0}),Object.defineProperty(this,"name",{value:r,enumerable:!0}),Object.defineProperty(this,"value",{value:i,enumerable:!0})}return i(t,[{key:"toString",value:function(){return this.name}}]),t}(),m=function(t){a(o,t);var r=f(o);function o(){return n(this,o),r.apply(this,arguments)}return i(o,null,[{key:"equals",value:function(t,n){if(t===n)return!0;if(!(t instanceof Object&&n instanceof Object))return!1;if(t.constructor!==n.constructor)return!1;if(t instanceof Array||ArrayBuffer.isTypedArray(t))return t.length==n.length&&t.every((function(t,e){return Object.equals(t,n[e])}));for(var r in t)if(t.hasOwnProperty(r)){if(!n.hasOwnProperty(r))return!1;if(t[r]!==n[r]){if("object"!==e(t[r]))return!1;if(!Object.equals(t[r],n[r]))return!1}}for(var i in n)if(n.hasOwnProperty(i)&&!t.hasOwnProperty(i))return!1;return!0}},{key:"clone",value:function(t,n){var r=new Array;return function t(i){if(null===i)return null;if("object"!==e(i)||i.immutable)return i;if("function"==typeof i.clone)return i.clone();for(var o=0;o<r.length;o++)if(r[o][0]===i)return r[o][1];var a=Object.create(Object.getPrototypeOf(i));for(var u in r.push([i,a]),i)n&&"function"==typeof i[u]||i.hasOwnProperty(u)&&(a[u]=t(i[u]));return a}(t)}},{key:"toSource",value:function(t,n){if("object"==e(t)){var r=[];for(var i in t)t.hasOwnProperty(i)&&r.push("".concat(i,": ").concat(Object.toSource(t[i])));return(n?"".concat(n," = "):"")+"{"+r.join(", ")+"}"}if("function"==typeof t){var o=t.toString();return t.name&&o.startsWith(t.name)&&(o="function "+o),o}return t}},{key:"defineEnum",value:function(t,e,n){if(t[e])throw new Error("Already exist property: ".concat(e));var r={values:n.map((function(t,n){return new v(e,t,n)}))};return r.values.forEach((function(t){Object.defineProperty(r,t.name,{value:t,enumerable:!0}),Object.defineProperty(r,t.value,{value:t,enumerable:!0})})),Object.defineProperty(t,e,{value:r,enumerable:!0}),r}}]),o}(d),g=function(t){a(r,t);var e=f(r);function r(){return n(this,r),e.apply(this,arguments)}return i(r,[{key:"startsWith",value:function(t){return this.length>=t.length&&this.substring(0,t.length)==t}},{key:"endsWith",value:function(t){return this.length>=t.length&&this.substring(this.length-t.length,this.length)==t}},{key:"includes",value:function(t){return-1!=this.indexOf(t)}},{key:"contains",value:function(t){return this.includes(t)}},{key:"containsIgnoreCase",value:function(t){return-1!=this.toUpperCase().indexOf(t.toUpperCase())}},{key:"pad",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"-";return this.length<t?new Array(t-this.length+1).join(e)+this:this.toString()}},{key:"toCharArray",value:function(t){for(var e=[],n=!0,r=0;r<this.length;r++){var i=this.charCodeAt(r);i>255&&(n=!1),e[r]=i}if(t){if(!n)throw new Error("Current value is not byte string");e=new Uint8Array(e)}return e}}],[{key:"fromCharArray",value:function(t){var e="";try{e=String.fromCharCode.apply(null,t)}catch(r){for(var n=0;n<t.length;n++)e+=String.fromCharCode(t[n])}return e}}]),r}(d),b=function(t){a(r,t);var e=f(r);function r(){return n(this,r),e.apply(this,arguments)}return i(r,[{key:"pad",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"0";return String(this).length<t?new Array(t-String(this).length+1).join(e)+String(this):this.toString()}},{key:"toFloat32",value:function(){var t=new Float32Array(1);return t[0]=this,t[0]}}]),r}(d);o(b,"classProperties",{MAX_INT32:{value:2147483647,enumerable:!0},MAX_UINT32:{value:4294967295,enumerable:!0},MAX_INT64:"undefined"==typeof BigInt?void 0:{value:0x7FFFFFFFFFFFFFFFn,enumerable:!0},MAX_UINT64:"undefined"==typeof BigInt?void 0:{value:0xFFFFFFFFFFFFFFFFn,enumerable:!0}}),o(b,"properties",{length:{get:function(){return 1+(0|Math.log10((this^this>>31)-(this>>31)))},configurable:!0}});var x=function(t){a(r,t);var e=f(r);function r(){return n(this,r),e.apply(this,arguments)}return i(r,null,[{key:"createClass",value:function(t,e){return new Function(e?e.name:void 0,"return class ".concat(t).concat(e?" extends ".concat(e.name):""," {\n\t\t\tconstructor() {\n\t\t\t\t").concat(e?"super(...arguments);":"","\n\t\t\t}\n\t\t}"))(e)}}]),r}(d);o(x,"properties",{body:{get:function(){var t=this.toString();return t=t.substring(t.indexOf("{")+1,t.lastIndexOf("}"))},configurable:!0}});var M=function(t){a(r,t);var e=f(r);function r(){return n(this,r),e.apply(this,arguments)}return i(r,[{key:"clear",value:function(){this.length=0}},{key:"includes",value:function(t){return this.indexOf(t)>-1}},{key:"clone",value:function(){if(!Object.clone)throw new Error("Object extension is required");return this.map((function(t){return Object.clone(t)}))}},{key:"unique",value:function(){var t=this;return this.filter((function(e,n){return t.indexOf(e)==n}))}},{key:"add",value:function(t){return!this.includes(t)&&(this.push(t),!0)}},{key:"insert",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.splice(e,0,t)}},{key:"indicesOf",value:function(t){var e=[],n=this.indexOf(t);if(-1!=n){e.push(n);for(var r=this.lastIndexOf(t);r>n;)n=this.indexOf(t,n+1),e.push(n)}return e}},{key:"remove",value:function(){for(var t=this,e=function(t){var e=[],n=-1;return t.forEach((function(t){n-t!=1&&e.push([]),e.last.push(t),n=t})),e},n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];var o=r.map((function(e){return t.indicesOf(e)})).flat().sort((function(t,e){return e-t}));return e(o).forEach((function(e){return t.removeAt(e.last,e.length)})),this}},{key:"removeAt",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return t>-1&&this.splice(t,e),this}},{key:"replace",value:function(t,e,n){var r=this.indexOf(t);return r>-1&&(!n&&e instanceof Array?this.splice.apply(this,[r,1].concat(l(e))):this.splice(r,1,e)),this}}],[{key:"from",value:function(t){return Array.prototype.slice.call(t)}}]),r}(d);o(M,"properties",{first:{get:function(){return this[0]},configurable:!0},last:{get:function(){return this[this.length-1]},configurable:!0}});var O=function(t){a(r,t);var e=f(r);function r(){return n(this,r),e.apply(this,arguments)}return i(r,[{key:"toBase64",value:function(){if(!String.fromCharArray)throw new Error("String extension is required");var t=new Uint8Array(this);return btoa(String.fromCharArray(t))}}],[{key:"fromBase64",value:function(t){if(!String.prototype.toCharArray)throw new Error("String extension is required");return atob(t).toCharArray(!0).buffer}},{key:"isTypedArray",value:function(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}}]),r}(d),w=["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64","BigInt64","BigUint64"],k=function(t){a(r,t);var e=f(r);function r(){return n(this,r),e.apply(this,arguments)}return i(r,[{key:"clone",value:function(){if("undefined"!=typeof SharedArrayBuffer&&this.buffer instanceof SharedArrayBuffer){var t=new SharedArrayBuffer(this.byteLength),e=new this.constructor(t);return e.set(this,this.byteOffset),e}return new this.constructor(this,this.byteOffset,this.length)}},{key:"concat",value:function(){for(var t,e=this,n=this.length,r=this.length,i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return o.forEach((function(t){if(e.constructor!=t.constructor)throw new Error("Concat array from wrong type detected - expected ".concat(e.constructor.name,", found ").concat(t.constructor.name));n+=t.length})),(t="undefined"!=typeof SharedArrayBuffer&&this.buffer instanceof SharedArrayBuffer?this.constructor.createSharedInstance(n):new this.constructor(n)).set(this),o.forEach((function(e){t.set(e,r),r+=e.length})),t}},{key:"toArray",value:function(){return Array.from(this)}}],[{key:"createSharedInstance",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(t instanceof this)return t;if("number"!=typeof t&&!Array.isArray(t))throw new Error("Expected data type is Array");var e="number"==typeof t?t:t.length;if("undefined"==typeof SharedArrayBuffer)return new this("number"==typeof t?e:t);var n=new SharedArrayBuffer(e*this.BYTES_PER_ELEMENT);if("number"==typeof t)return new this(n);var r=new this(n);return r.set(t),r}},{key:"from",value:function(t){return new this(t)}},{key:"extend",value:function(){var t=this;w.forEach((function(e){if(d.extend(e+"Array",t)){var n=globalThis[e+"Array"];Array.prototype["to"+e+"Array"]=function(){return n.from(this)}}}))}}]),r}(d),S=function(t){a(r,t);var e=f(r);function r(){return n(this,r),e.apply(this,arguments)}return i(r,[{key:"map",value:function(t){var e,n=new Set,r=p(this);try{for(r.s();!(e=r.n()).done;){var i=e.value;(i=t(i))&&n.add(i)}}catch(t){r.e(t)}finally{r.f()}return n}},{key:"filter",value:function(t){var e,n=new Set,r=p(this);try{for(r.s();!(e=r.n()).done;){var i=e.value;t(i)&&n.add(i)}}catch(t){r.e(t)}finally{r.f()}return n}}]),r}(d),E=function(t){a(r,t);var e=f(r);function r(){return n(this,r),e.apply(this,arguments)}return r}(d);o(E,"properties",{size:{get:function(){return{width:Math.floor(screen.width),height:Math.floor(screen.height)}},configurable:!0},resolution:{get:function(){return{width:Math.floor(screen.width*devicePixelRatio),height:Math.floor(screen.height*devicePixelRatio)}},configurable:!0}});var P=function(t){a(r,t);var e=f(r);function r(){return n(this,r),e.apply(this,arguments)}return i(r,null,[{key:"properties",get:function(){return{query:{get:function(){if(!this._query){var t=Object.assign.apply(Object,[{}].concat(l(this.search.substring(1).split("&").filter((function(t){return t})).map((function(t){return t.split("=")})).map((function(t){return o({},t[0],decodeURIComponent(t[1]))})))));Object.defineProperty(this,"_query",{value:t})}return this._query},configurable:!0}}}}]),r}(d),D=function(t){a(r,t);var e=f(r);function r(){return n(this,r),e.apply(this,arguments)}return i(r,[{key:"getInlineStyle",value:function(t){return this.style[t]||this.style["-webkit-"+t]||this.style["-khtml-"+t]||this.style["-moz-"+t]||this.style["-ms-"+t]||this.style["-o-"+t]||""}},{key:"setStyle",value:function(t,e){var n=this;["-webkit","-khtml","-moz","-ms","-o"].forEach((function(r){return n.style[r+"-"+t]=e})),this.style[t]=e}},{key:"getStyle",value:function(t){var e=t,n=t.startsWith("-");n&&(e=t.substring(1));for(var r=e.split("-"),i=r.length-1;i>0;i--)r[i]=r[i].substring(0,1).toUpperCase()+r[i].substring(1);e=r.join("");var o=this.style.display,a=this.style.visibility;"none"==o&&(this.style.visibility="hidden",this.style.display="");var u=window.getComputedStyle(this)[e];if(!n&&void 0===u)for(var s=["-webkit","-khtml","-moz","-ms","-o"],c=0;c<s.length&&"undefined"==(u=this.getStyle(s[c]+"-"+t));c++);return"none"==o&&(this.style.visibility=a,this.style.display="none"),u}},{key:"getMathStyle",value:function(t,e){var n=e?this.getInlineStyle(t):this.getStyle(t);return"auto"==n&&(n=0),parseFloat(n)}},{key:"getTransformStyle",value:function(){var t={translate:{x:0,y:0},scale:{x:1,y:1},rotate:{angle:0},skew:{angleX:0,angleY:0},matrix:{a:1,b:0,c:0,d:1,tx:0,ty:0}},e=this.getStyle("transform");if("none"!=e){var n=e.substring(e.indexOf("(")+1,e.indexOf(")")).split(/,\s*/g),r=parseFloat(n[0]),i=parseFloat(n[1]),o=parseFloat(n[2]),a=parseFloat(n[3]),u=parseFloat(n[4]),s=parseFloat(n[5]);t.scale={x:Math.sqrt(r*r+o*o),y:Math.sqrt(a*a+i*i)},t.skew={angleX:Math.tan(o),angleY:Math.tan(i)},t.rotate={angle:Math.atan2(i,r)},t.translate={x:u,y:s},t.matrix={a:r,b:i,c:o,d:a,tx:u,ty:s}}return t}},{key:"toRect",value:function(){var t=this.style.display,e=this.style.visibility;"none"==t&&(this.style.visibility="hidden",this.style.display="");var n=this.offsetWidth+this.getMathStyle("margin-left")+this.getMathStyle("margin-right"),r=this.offsetHeight+this.getMathStyle("margin-top")+this.getMathStyle("margin-bottom"),i=new DOMRect(this.offsetLeft,this.offsetTop,this.offsetWidth,this.offsetHeight);return i.outerSize=new DOMSize(n,r),"none"==t&&(this.style.visibility=e,this.style.display="none"),i}}]),r}(d),A=function(t){a(r,t);var e=f(r);function r(){return n(this,r),e.apply(this,arguments)}return i(r,[{key:"toDataURL",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"png",e=document.createElement("canvas");return e.width=this.width,e.height=this.height,e.getContext("2d").drawImage(this,0,0),e.toDataURL("image/".concat(t))}},{key:"toBlob",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"png";return new Blob([this.toArrayBuffer(t)],{type:"image/".concat(t)})}},{key:"toArrayBuffer",value:function(t){if(!ArrayBuffer.fromBase64)throw new Error("ArrayBuffer extension is required");var e=this.toDataURL(t).split(",")[1];return ArrayBuffer.fromBase64(e)}}]),r}(d),T=function(t){a(r,t);var e=f(r);function r(){return n(this,r),e.apply(this,arguments)}return i(r,null,[{key:"fromBytes",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"png",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:new Image;return new Promise((function(r,i){n.onload=function(){URL.revokeObjectURL(n.src),r(n)},n.onerror=i,n.src=URL.createObjectURL(new Blob([t.buffer||t],{type:"image/".concat(e)}))}))}}]),r}(d),j=function(t){a(r,t);var e=f(r);function r(){return n(this,r),e.apply(this,arguments)}return i(r,null,[{key:"extend",value:function(){"undefined"!=typeof CanvasRenderingContext2D&&(CanvasRenderingContext2D.prototype.clearCanvas=function(t){this.clearRect(0,0,this.canvas.width,this.canvas.height),t&&(this.fillStyle=t,this.fillRect(0,0,this.canvas.width,this.canvas.height))},"undefined"==typeof OffscreenCanvas?(globalThis.OffscreenCanvas=function(t,e){var n=document.createElement("canvas");return n.width=t,n.height=e,n},globalThis.OffscreenCanvasRenderingContext2D=CanvasRenderingContext2D):"undefined"!=typeof OffscreenCanvasRenderingContext2D&&(OffscreenCanvasRenderingContext2D.prototype.clearCanvas=CanvasRenderingContext2D.prototype.clearCanvas))}}]),r}(d),F=function(t){a(r,t);var e=f(r);function r(){return n(this,r),e.apply(this,arguments)}return i(r,[{key:"transform",value:function(t){return t instanceof DOMMatrix||(t=DOMMatrix.fromMatrix(t)),this.matrixTransform(t)}},{key:"toString",value:function(){return"point(".concat(this.x,", ").concat(this.y,", ").concat(this.z,")")}}]),r}(d),C=function(){function t(e,r){n(this,t),this.width=e,this.height=r}return i(t,[{key:"toJSON",value:function(){return{width:this.width,height:this.height}}},{key:"toString",value:function(){return"size(".concat(this.width,", ").concat(this.height,")")}}],[{key:"fromSize",value:function(e){if(e instanceof t)return e;if("string"==typeof e){if(!e.startsWith("size("))throw new Error("Invalid value found. Expected template - size(width, height).");var n=e.substring(e.indexOf("(")+1,e.indexOf(")")).split(/,\s*/g);e={width:parseFloat(n[0]),height:parseFloat(n[1])}}if(isNaN(e.width))throw new Error("Invalid width found, expected number");if(isNaN(e.height))throw new Error("Invalid height found, expected number");return new t(e.width,e.height)}}]),t}(),R=function(t){a(r,t);var e=f(r);function r(){return n(this,r),e.apply(this,arguments)}return i(r,[{key:"union",value:function(t){return t?DOMRect.ofEdges(Math.min(this.left,t.left),Math.min(this.top,t.top),Math.max(this.right,t.right),Math.max(this.bottom,t.bottom)):this}},{key:"intersect",value:function(t){if(t){var e=DOMRect.ofEdges(Math.max(this.left,t.left),Math.max(this.top,t.top),Math.min(this.right,t.right),Math.min(this.bottom,t.bottom));return e.width>0&&e.height>0?e:void 0}}},{key:"ceil",value:function(t){var e=Math.floor(this.left),n=Math.floor(this.top),r=Math.ceil(this.right),i=Math.ceil(this.bottom);if(t){var o=this.width,a=this.height;r=e+(o+=o%2),i=n+(a+=a%2)}return DOMRect.ofEdges(e,n,r,i)}},{key:"floor",value:function(t){var e=Math.ceil(this.left),n=Math.ceil(this.top),r=Math.floor(this.right),i=Math.floor(this.bottom);if(t){var o=this.width,a=this.height;r=e+(o-=o%2),i=n+(a-=a%2)}return DOMRect.ofEdges(e,n,r,i)}},{key:"contains",value:function(t){return this.left<=t.x&&this.right>=t.x&&this.top<=t.y&&this.bottom>=t.y}},{key:"transform",value:function(t){t instanceof DOMMatrix||(t=DOMMatrix.fromMatrix(t));var e=DOMPoint.fromPoint({x:this.left,y:this.top}).transform(t),n=DOMPoint.fromPoint({x:this.right,y:this.top}).transform(t),r=DOMPoint.fromPoint({x:this.left,y:this.bottom}).transform(t),i=DOMPoint.fromPoint({x:this.right,y:this.bottom}).transform(t),o=Math.min(e.x,n.x,r.x,i.x),a=Math.min(e.y,n.y,r.y,i.y),u=Math.max(e.x,n.x,r.x,i.x),s=Math.max(e.y,n.y,r.y,i.y);return DOMRect.ofEdges(o,a,u,s)}},{key:"matrixTransform",value:function(t){return this.transform(t)}},{key:"toPath",value:function(){var t=[];return t.push(this.left,this.top),t.push(this.right,this.top),t.push(this.right,this.bottom),t.push(this.left,this.bottom),t.push(this.left,this.top),t.toFloat32Array()}},{key:"toString",value:function(){return"rect(".concat(this.x,", ").concat(this.y,", ").concat(this.width,", ").concat(this.height,")")}}],[{key:"ofEdges",value:function(t,e,n,r){return new DOMRect(t,e,n-t,r-e)}},{key:"extend",value:function(){d.extend("DOMRect",this)&&(globalThis.DOMSize=C)}}]),r}(d);o(R,"properties",{size:{get:function(){return new C(this.width,this.height)},configurable:!0},center:{get:function(){return new DOMPoint((this.left+this.right)/2,(this.top+this.bottom)/2)},configurable:!0}});var I=function(t){a(r,t);var e=f(r);function r(){return n(this,r),e.apply(this,arguments)}return i(r,[{key:"preMultiply",value:function(t){var e=t.postMultiply(this);return e.multiplicationType=this.multiplicationType,e}},{key:"multiply",value:function(t){if(t instanceof DOMMatrix||(t=DOMMatrix.fromMatrix(t)),this.multiplicationType==DOMMatrix.MultiplicationType.POST)return this.postMultiply(t);var e=this.preMultiply(t);return e.multiplicationType=DOMMatrix.MultiplicationType.PRE,e}},{key:"multiplySelf",value:function(t){t instanceof DOMMatrix||(t=DOMMatrix.fromMatrix(t)),this.multiplicationType==DOMMatrix.MultiplicationType.POST?this.postMultiplySelf(t):this.preMultiplySelf(t)}},{key:"transformPoint",value:function(t){return DOMPoint.fromPoint(t).matrixTransform(this)}},{key:"invert",value:function(){return this.inverse()}},{key:"decompose",value:function(){return{translate:{x:this.tx,y:this.ty},rotate:{angle:Math.atan2(this.b,this.a)},skew:{angleX:Math.tan(this.c),angleY:Math.tan(this.b)},scale:{x:Math.sqrt(this.a*this.a+this.c*this.c),y:Math.sqrt(this.d*this.d+this.b*this.b)},matrix:this}}},{key:"disassemble",value:function(){return console.warn("disassemble is deprecated, use decompose instead"),this.decompose()}},{key:"toString",value:function(t){if(!t)return this.nativeToString();var e=function(t){return((t<0?"":" ")+t.toPrecision(6)).substring(0,8)};return" Matrix 4x4\n"+"-".repeat(39)+"\n".concat(e(this.m11),", ").concat(e(this.m21),", ").concat(e(this.m31),", ").concat(e(this.m41))+"\n".concat(e(this.m12),", ").concat(e(this.m22),", ").concat(e(this.m32),", ").concat(e(this.m42))+"\n".concat(e(this.m13),", ").concat(e(this.m23),", ").concat(e(this.m33),", ").concat(e(this.m43))+"\n".concat(e(this.m14),", ").concat(e(this.m24),", ").concat(e(this.m34),", ").concat(e(this.m44))}}],[{key:"fromMatrix",value:function(t,e){var n;return"string"==typeof t?n=new DOMMatrix(t):("e"in t||(t.e=t.tx||t.dx),"f"in t||(t.f=t.ty||t.dy),n=DOMMatrix.nativeFromMatrix(t)),n.multiplicationType=e||t.multiplicationType||DOMMatrix.MultiplicationType.POST,n}},{key:"fromTranslate",value:function(t){var e=isFinite(t)?{tx:t,ty:t}:{tx:t.x,ty:t.y};return DOMMatrix.fromMatrix(e)}},{key:"fromRotate",value:function(t,e){var n=Math.sin(t),r=Math.cos(t),i={a:r,b:n,c:-n,d:r};return e&&(i.tx=e.x-e.x*r+e.y*n,i.ty=e.y-e.x*n-e.y*r),DOMMatrix.fromMatrix(i)}},{key:"fromScale",value:function(t,e){isFinite(t)&&(t={x:t,y:t});var n={a:t.x,d:t.y};return e&&(n.tx=e.x-e.x*t.x,n.ty=e.y-e.y*t.y),DOMMatrix.fromMatrix(n)}},{key:"fromPoints",value:function(t,e){var n=DOMMatrix.fromMatrix({m11:t[0].x,m21:t[1].x,m31:t[2].x,m12:t[0].y,m22:t[1].y,m32:t[2].y,m13:1,m23:1,m33:1}),r=DOMMatrix.fromMatrix({m11:e[0].x,m21:e[1].x,m31:e[2].x,m12:e[0].y,m22:e[1].y,m32:e[2].y,m13:1,m23:1,m33:1}),i=n.invert().preMultiply(r);return DOMMatrix.fromMatrix({a:i.m11,b:i.m12,c:i.m21,d:i.m22,tx:i.m31,ty:i.m32})}},{key:"extend",value:function(){if("undefined"==typeof DOMMatrix||DOMMatrix.nativeFromMatrix)return!1;DOMMatrix.nativeFromMatrix=DOMMatrix.fromMatrix,DOMMatrix.prototype.nativeToString=DOMMatrix.prototype.toString,DOMMatrix.prototype.postMultiply=DOMMatrix.prototype.multiply,DOMMatrix.prototype.postMultiplySelf=DOMMatrix.prototype.multiplySelf,d.extend("DOMMatrix",this)}}]),r}(d);o(I,"overrides",d.overrides.concat(["fromMatrix","multiply","multiplySelf","transformPoint"])),o(I,"properties",{tx:{get:function(){return this.e},set:function(t){this.e=t},enumerable:!0},ty:{get:function(){return this.f},set:function(t){this.f=t},enumerable:!0},dx:{get:function(){return this.e},set:function(t){this.e=t},enumerable:!0},dy:{get:function(){return this.f},set:function(t){this.f=t},enumerable:!0},multiplicationType:{value:"POST",enumerable:!0,writable:!0}}),o(I,"classProperties",{MultiplicationType:{value:{PRE:"PRE",POST:"POST"},enumerable:!0}});var B=Object.freeze({__proto__:null,ObjectExt:m,StringExt:g,NumberExt:b,FunctionExt:x,ArrayExt:M,ArrayBufferExt:O,TypedArrayExt:k,SetExt:S,ScreenExt:E,LocationExt:P,HTMLElementExt:D,HTMLImageElementExt:A,ImageExt:T,CanvasExt:j,DOMPointExt:F,DOMRectExt:R,DOMMatrixExt:I});"undefined"==typeof globalThis&&("undefined"!=typeof window?window.globalThis=window:"undefined"!=typeof self?self.globalThis=self:"undefined"!=typeof global&&(global.globalThis=global)),Math.toDegrees=function(t){return t*(180/this.PI)},Math.toRadians=function(t){return t*(this.PI/180)},Math.randomInt=function(t,e){return Math.floor(this.random()*(e-t+1))+t},Function.prototype.createEnum=function(t,e){console.warn("createEnum is deprecated, please update ".concat(t," enum with Object.defineEnum(target, name, values)")),Object.defineEnum(this,t,e)},"function"==typeof Worker&&(Worker.prototype.on=function(t,e){this["on".concat(t)]=function(n){var r="message"==t?n.data:n;e(r)}}),"function"==typeof DedicatedWorkerGlobalScope&&(DedicatedWorkerGlobalScope.prototype.on=function(t,e){this["on".concat(t)]=function(n){var r="message"==t?n.data:n;e(r)}}),globalThis.JS_EXT_SCOPE||Object.defineProperty(globalThis,"JS_EXT_SCOPE",{value:["Object","String","Number","Function","Array","ArrayBuffer","TypedArray","Set","Screen","Location","HTMLElement","HTMLImageElement","Image","Canvas","DOMPoint","DOMRect","DOMMatrix"],enumerable:!0,configurable:!0}),globalThis.JS_EXT_SCOPE.forEach((function(t){var e=B["".concat(t,"Ext")];if(!e)throw new Error("Extension ".concat(t," not found"));e.extend(t)})),t.Extension=d,t.version="1.0.2",Object.defineProperty(t,"__esModule",{value:!0})}));
