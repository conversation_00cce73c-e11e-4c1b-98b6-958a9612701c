<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

include_once __DIR__.'/../../core.php';

use Carbon\Carbon;
use Modules\Skills\Skill;

switch (post('op')) {
    case 'restore':
        $potere->restore();
        flash()->info(tr('Skill _NAME_ ripristinata correttamente!', [
            '_NAME_' => post('nome'),
        ]));

        // no break
    case 'update':

        // Informazioni sull'anagrafica
        $disponibile = Skill::where([
            ['nome', post('nome')],
            ['id', '<>', $id_record],
        ])->count() == 0;

        if( $disponibile ){
            $skill->nome = post('nome');
        }
        $skill->descrizione = post('descrizione');
        $skill->is_visible = post('is_visible');
        $skill->sezione = post('sezione');

        $skill->save();

        flash()->info(tr('Informazioni per la skill \"_NAME_\" salvate correttamente.', [
            '_NAME_' => $skill->nome,
        ]));

        break;

    case 'add':

        $disponibile = Skill::where([
            ['nome', post('nome')]
        ])->count() == 0;

        if( $disponibile ){
            $skill = Skill::build(post('nome'),post('descrizione'));
            $id_record = $skill->id;

            if( empty($id_record) ){
                $id_record = $dbo->lastInsertedID();
            }
    
            flash()->info(tr('Skill aggiunta'));
        }else{
            flash()->error(tr('La Skill _NOME_ è già presente.',[
                '_NOME_' => post('nome'),
            ]));
        }

        break;

    case 'delete':

        $skill->deleted_at = Carbon::now();
        $skill->save();

        flash()->info(tr('Skill eliminata!'));

        break;

    case 'manage_livelli':

        $livelli = (!empty($skill->livelli) ? $skill->livelli : []);

        if (empty(post('idriga'))) {
            $livelli[] = [
                'soglia' => post('soglia'),
                'descrizione' => post('descrizione'),
            ];
        } else {
            $livelli[post('idriga')] = [
                'soglia' => post('soglia'),
                'descrizione' => post('descrizione'),
            ];
        }

        $skill->livelli = $livelli;
        $skill->save();

        flash()->info(tr('Livelli aggiornati!'));

        break;

    case 'delete_livello':

        $livelli = (!empty($skill->livelli) ? $skill->livelli : []);

        unset($livelli[post('idriga')]);

        $skill->livelli = $livelli;
        $skill->save();

        flash()->info(tr('Livello eliminato!'));

        break;
}