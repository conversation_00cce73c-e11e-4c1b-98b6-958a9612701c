syntax = "proto3";

package google.protobuf;

import "google/protobuf/any.proto";
import "google/protobuf/source_context.proto";

message Type {

    string name = 1;
    repeated Field fields = 2;
    repeated string oneofs = 3;
    repeated Option options = 4;
    SourceContext source_context = 5;
    Syntax syntax = 6;
}

message Field {

    Kind kind = 1;
    Cardinality cardinality = 2;
    int32 number = 3;
    string name = 4;
    string type_url = 6;
    int32 oneof_index = 7;
    bool packed = 8;
    repeated Option options = 9;
    string json_name = 10;
    string default_value = 11;

    enum Kind {

        TYPE_UNKNOWN = 0;
        TYPE_DOUBLE = 1;
        TYPE_FLOAT = 2;
        TYPE_INT64 = 3;
        TYPE_UINT64 = 4;
        TYPE_INT32 = 5;
        TYPE_FIXED64 = 6;
        TYPE_FIXED32 = 7;
        TYPE_BOOL = 8;
        TYPE_STRING = 9;
        TYPE_GROUP = 10;
        TYPE_MESSAGE = 11;
        TYPE_BYTES = 12;
        TYPE_UINT32 = 13;
        TYPE_ENUM = 14;
        TYPE_SFIXED32 = 15;
        TYPE_SFIXED64 = 16;
        TYPE_SINT32 = 17;
        TYPE_SINT64 = 18;
    }

    enum Cardinality {

        CARDINALITY_UNKNOWN = 0;
        CARDINALITY_OPTIONAL = 1;
        CARDINALITY_REQUIRED = 2;
        CARDINALITY_REPEATED = 3;
    }
}

message Enum {

    string name = 1;
    repeated EnumValue enumvalue = 2;
    repeated Option options = 3;
    SourceContext source_context = 4;
    Syntax syntax = 5;
}

message EnumValue {

    string name = 1;
    int32 number = 2;
    repeated Option options = 3;
}

message Option {

    string name = 1;
    Any value = 2;
}

enum Syntax {

    SYNTAX_PROTO2 = 0;
    SYNTAX_PROTO3 = 1;
}
