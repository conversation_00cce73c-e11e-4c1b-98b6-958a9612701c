<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="source_sans_proregular" horiz-adv-x="497" >
<font-face units-per-em="1000" ascent="750" descent="-250" />
<missing-glyph horiz-adv-x="200" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="333" />
<glyph unicode=" "  horiz-adv-x="200" />
<glyph unicode="&#x09;" horiz-adv-x="200" />
<glyph unicode="&#xa0;" horiz-adv-x="200" />
<glyph unicode="!" horiz-adv-x="289" d="M85 50q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5q-25 0 -42.5 17.5t-17.5 44.5zM103 670h83l-2 -94l-11 -378h-57l-11 378z" />
<glyph unicode="&#x22;" horiz-adv-x="426" d="M80 690h88l-3 -92l-16 -167h-50l-16 167zM257 690h88l-3 -92l-16 -167h-50l-16 167z" />
<glyph unicode="#" d="M35 204v57h87l18 148h-85v58h92l23 183h53l-23 -183h133l24 183h53l-24 -183h81v-58h-87l-18 -148h85v-57h-92l-25 -204h-53l24 204h-132l-25 -204h-54l25 204h-80zM176 261h132l18 148h-132z" />
<glyph unicode="$" d="M52 66l38 57q32 -28 70.5 -47.5t84.5 -19.5q56 0 83.5 28.5t27.5 74.5q0 41 -20 67t-52 45t-67.5 36.5t-67.5 39t-52 53.5t-20 81q0 67 40 111.5t105 54.5v101h60v-99q48 -5 81.5 -26t60.5 -50l-44 -49q-28 27 -54.5 42t-67.5 15q-46 0 -73 -26t-27 -70q0 -36 20 -59.5 t52 -40.5t67.5 -34t67.5 -39.5t52 -57.5t20 -89q0 -71 -42 -117.5t-113 -56.5v-101h-60v99q-48 5 -93 26.5t-77 50.5z" />
<glyph unicode="%" horiz-adv-x="824" d="M35 462q0 98 41 152t108 54t108 -54t41 -152q0 -100 -41 -154t-108 -54t-108 54t-41 154zM97 462q0 -78 24 -117.5t63 -39.5q38 0 62 39.5t24 117.5t-24 116.5t-62 38.5q-39 0 -63 -38.5t-24 -116.5zM203 -12l362 680h56l-362 -680h-56zM492 196q0 98 41 152t108 54 t108 -54t41 -152q0 -100 -41 -154t-108 -54t-108 54t-41 154zM554 196q0 -78 24 -117.5t63 -39.5q38 0 62 39.5t24 117.5t-24 116.5t-62 38.5q-39 0 -63 -38.5t-24 -116.5z" />
<glyph unicode="&#x26;" horiz-adv-x="609" d="M32 170q0 44 18.5 78t47.5 61t63 50q-20 41 -31.5 79.5t-11.5 74.5q0 44 19 79t53.5 55.5t79.5 20.5q61 0 95 -36t34 -94q0 -43 -22 -77t-55.5 -62t-70.5 -55q32 -50 76 -97.5t90 -86.5q30 41 53.5 89.5t38.5 104.5h77q-19 -64 -47.5 -123.5t-67.5 -111.5q34 -23 65 -39 t58 -24l-22 -68q-34 10 -72.5 28.5t-78.5 46.5q-37 -34 -84 -54.5t-105 -20.5q-59 0 -104.5 23.5t-70.5 64.5t-25 94zM112 175q0 -55 37.5 -88t91.5 -33q34 0 65.5 14t59.5 39q-48 42 -92.5 92t-79.5 103q-34 -28 -58 -58.5t-24 -68.5zM189 514q0 -27 8.5 -56t22.5 -59 q45 30 78.5 62.5t33.5 75.5q0 29 -14.5 50.5t-47.5 21.5q-37 0 -59 -27t-22 -68z" />
<glyph unicode="'" horiz-adv-x="249" d="M80 690h88l-3 -92l-16 -167h-50l-16 167z" />
<glyph unicode="(" horiz-adv-x="303" d="M82 278q0 132 35 243t97 211l51 -24q-58 -95 -86.5 -205.5t-28.5 -224.5t28.5 -224.5t86.5 -205.5l-51 -24q-62 101 -97 211.5t-35 242.5z" />
<glyph unicode=")" horiz-adv-x="303" d="M38 -152q58 95 86.5 205.5t28.5 224.5t-28.5 224.5t-86.5 205.5l51 24q62 -100 97 -211t35 -243t-35 -242.5t-97 -211.5z" />
<glyph unicode="*" horiz-adv-x="418" d="M58 583l15 46l102 -25l9 108h49l9 -107l103 24l15 -46l-97 -40l56 -94l-39 -29l-71 86l-71 -86l-40 29l57 94z" />
<glyph unicode="+" d="M34 299v62h182v195h65v-195h182v-62h-182v-195h-65v195h-182z" />
<glyph unicode="," horiz-adv-x="249" d="M47 -122q43 19 67 50.5t23 71.5q-2 -1 -5 -1h-5q-24 0 -41.5 14.5t-17.5 42.5q0 27 18 42.5t43 15.5q32 0 50.5 -26t18.5 -71q0 -64 -35 -112.5t-96 -74.5z" />
<glyph unicode="-" horiz-adv-x="311" d="M41 219v63h230v-63h-230z" />
<glyph unicode="&#xad;" horiz-adv-x="311" d="M41 219v63h230v-63h-230z" />
<glyph unicode="." horiz-adv-x="249" d="M65 50q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5q-25 0 -42.5 17.5t-17.5 44.5z" />
<glyph unicode="/" horiz-adv-x="350" d="M10 -160l267 870h60l-267 -870h-60z" />
<glyph unicode="0" d="M44 321q0 161 54.5 245t150.5 84q95 0 149.5 -84t54.5 -245q0 -160 -54.5 -246.5t-149.5 -86.5q-96 0 -150.5 86.5t-54.5 246.5zM124 321q0 -140 34.5 -203.5t90.5 -63.5t90 63.5t34 203.5t-34 201.5t-90 61.5t-90.5 -61.5t-34.5 -201.5z" />
<glyph unicode="1" d="M79 0v68h146v470h-116v53q44 8 76.5 19.5t58.5 27.5h63v-570h132v-68h-360z" />
<glyph unicode="2" d="M36 553q40 44 85.5 70.5t107.5 26.5q89 0 140 -51.5t51 -136.5q0 -60 -33.5 -121.5t-92 -129t-134.5 -146.5q26 2 54 4t53 2h185v-71h-412v49q96 96 163 168.5t102 131t35 109.5q0 55 -30 90t-91 35q-40 0 -74 -22.5t-62 -54.5z" />
<glyph unicode="3" d="M26 78l42 54q29 -30 68 -53t95 -23q58 0 95 32t37 85q0 38 -19.5 66.5t-64 44.5t-120.5 16v63q68 0 107.5 16t56.5 43t17 60q0 47 -29.5 74t-80.5 27q-40 0 -73.5 -18t-62.5 -47l-44 52q37 35 82 57.5t101 22.5q83 0 137 -42.5t54 -119.5q0 -58 -32 -95t-84 -57v-4 q58 -13 98 -54.5t40 -107.5q0 -56 -28 -97t-75.5 -63t-106.5 -22q-76 0 -127 27.5t-83 62.5z" />
<glyph unicode="4" d="M17 176v54l273 408h92v-396h87v-66h-87v-176h-78v176h-287zM104 242h200v185q0 26 2 62t3 61h-4q-12 -23 -25 -45t-27 -45z" />
<glyph unicode="5" d="M25 75l40 54q29 -28 67 -50.5t94 -22.5q59 0 99.5 39.5t40.5 106.5q0 66 -37 103t-99 37q-33 0 -56.5 -10t-52.5 -29l-44 28l21 307h319v-71h-247l-17 -189q23 12 46 19t52 7q55 0 99.5 -21.5t71.5 -65.5t27 -113t-30.5 -117.5t-79.5 -73.5t-105 -25q-76 0 -126 27 t-83 60z" />
<glyph unicode="6" d="M48 295q0 128 34.5 206t90 113.5t118.5 35.5q52 0 89.5 -19.5t64.5 -48.5l-46 -51q-19 23 -47 36.5t-58 13.5q-45 0 -82 -25.5t-60.5 -85t-25.5 -160.5q30 37 70 58.5t79 21.5q83 0 132.5 -49.5t49.5 -147.5q0 -61 -26 -107t-68.5 -72t-94.5 -26q-62 0 -112 34t-79 102.5 t-29 170.5zM128 244q8 -93 43.5 -142t96.5 -49q47 0 79 38.5t32 101.5q0 62 -29.5 98.5t-88.5 36.5q-31 0 -67 -20t-66 -64z" />
<glyph unicode="7" d="M44 567v71h411v-51q-57 -72 -93 -138.5t-56 -134t-30 -144.5t-14 -170h-85q6 120 25.5 215.5t58 180t101.5 171.5h-318z" />
<glyph unicode="8" d="M41 163q0 42 17.5 74.5t45 57t57.5 40.5v4q-35 25 -62 61t-27 86q0 49 24 86t65 57.5t92 20.5q83 0 131.5 -48t48.5 -122q0 -33 -14 -62.5t-34 -53t-40 -38.5v-4q29 -16 54 -38t41 -52.5t16 -73.5q0 -47 -25.5 -86t-72 -61.5t-108.5 -22.5q-60 0 -107 22.5t-74.5 62.5 t-27.5 90zM115 171q0 -53 39.5 -87.5t97.5 -34.5q56 0 90.5 31.5t34.5 80.5q0 41 -24 67t-63.5 45t-85.5 37q-38 -25 -63.5 -60t-25.5 -79zM148 486q0 -38 20.5 -63t54.5 -42.5t72 -32.5q32 29 49.5 61t17.5 67q0 47 -29 80t-82 33q-44 0 -73.5 -28t-29.5 -75z" />
<glyph unicode="9" d="M40 445q0 61 26 107t68.5 72t93.5 26q63 0 113 -34t78.5 -102t28.5 -171q0 -128 -34.5 -206t-89.5 -113.5t-119 -35.5q-51 0 -89.5 19.5t-64.5 47.5l46 52q20 -23 47.5 -37t57.5 -14q45 0 83 26t61.5 86t24.5 162q-30 -38 -70 -60t-80 -22q-82 0 -131.5 49.5t-49.5 147.5 zM117 445q0 -62 29.5 -98.5t88.5 -36.5q32 0 67.5 20t66.5 65q-8 93 -44 141.5t-97 48.5q-46 0 -78.5 -38.5t-32.5 -101.5z" />
<glyph unicode=":" horiz-adv-x="249" d="M65 50q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5q-25 0 -42.5 17.5t-17.5 44.5zM65 411q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5q-25 0 -42.5 17.5t-17.5 44.5z" />
<glyph unicode=";" horiz-adv-x="249" d="M47 -122q43 19 67 50.5t23 71.5q-2 -1 -5 -1h-5q-24 0 -41.5 14.5t-17.5 42.5q0 27 18 42.5t43 15.5q32 0 50.5 -26t18.5 -71q0 -64 -35 -112.5t-96 -74.5zM65 411q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5q-25 0 -42.5 17.5 t-17.5 44.5z" />
<glyph unicode="&#x3c;" d="M34 299v66l429 168v-71l-211 -78l-134 -50v-4l134 -50l211 -78v-71z" />
<glyph unicode="=" d="M34 192v62h429v-62h-429zM34 406v62h429v-62h-429z" />
<glyph unicode="&#x3e;" d="M34 131v71l211 78l134 50v4l-134 50l-211 78v71l429 -168v-66z" />
<glyph unicode="?" horiz-adv-x="425" d="M38 599q32 36 74.5 59.5t97.5 23.5q76 0 121.5 -43t45.5 -117q0 -39 -16 -70.5t-39.5 -59.5t-45.5 -57t-35.5 -62t-8.5 -75h-72q-7 47 5 83t33.5 66t44 57t38.5 54t16 59q0 40 -24.5 68.5t-72.5 28.5q-33 0 -62.5 -15.5t-52.5 -42.5zM139 50q0 29 17 46.5t42 17.5 t42.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-42.5 -17.5t-42 17.5t-17 44.5z" />
<glyph unicode="@" horiz-adv-x="847" d="M51 203q0 101 33 182.5t91.5 139.5t133.5 89.5t159 31.5q101 0 174.5 -41.5t113.5 -116t40 -173.5q0 -87 -31 -146.5t-77 -89.5t-92 -30q-40 0 -69 19.5t-34 56.5h-2q-25 -29 -57 -48.5t-65 -19.5q-51 0 -85.5 36.5t-34.5 104.5q0 40 13.5 81t38.5 75t60.5 55t79.5 21 q26 0 46.5 -11.5t33.5 -36.5h2l11 40h55l-39 -200q-30 -117 54 -117q32 0 62.5 24.5t51 71t20.5 111.5q0 81 -31.5 144t-93 99t-152.5 36q-67 0 -129.5 -27t-112.5 -77.5t-79 -121.5t-29 -159q0 -97 39 -165.5t106.5 -104.5t152.5 -36q46 0 87 13t75 32l22 -49 q-43 -26 -90.5 -38.5t-99.5 -12.5q-98 0 -178 41t-127 121t-47 196zM315 203q0 -48 20 -69t50 -21q21 0 44 14t48 45l29 159q-14 23 -29 32.5t-35 9.5q-39 0 -67.5 -27t-44 -66t-15.5 -77z" />
<glyph unicode="A" horiz-adv-x="544" d="M3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM172 267h197l-31 100q-18 55 -34 109.5t-32 111.5h-4q-15 -57 -31 -111.5t-34 -109.5z" />
<glyph unicode="B" horiz-adv-x="588" d="M90 0v656h195q67 0 117.5 -16t79 -51t28.5 -93q0 -49 -24.5 -88.5t-71.5 -54.5v-4q59 -11 96.5 -50t37.5 -107q0 -64 -31.5 -107t-87.5 -64t-130 -21h-209zM173 66h114q85 0 132 31.5t47 97.5q0 61 -46.5 89.5t-132.5 28.5h-114v-247zM173 377h97q84 0 121 29t37 79 q0 57 -38.5 81t-115.5 24h-101v-213z" />
<glyph unicode="C" horiz-adv-x="571" d="M52 328q0 106 38 182t104 117t149 41q60 0 106 -24.5t74 -56.5l-45 -54q-26 28 -59 45t-75 17q-62 0 -108.5 -32.5t-72 -91.5t-25.5 -141t25 -142.5t70.5 -93.5t107.5 -33q47 0 84 20t68 54l46 -52q-39 -45 -88 -70t-113 -25q-82 0 -146.5 40.5t-102 116.5t-37.5 183z " />
<glyph unicode="D" horiz-adv-x="615" d="M90 0v656h164q152 0 231 -84.5t79 -240.5q0 -157 -78.5 -244t-227.5 -87h-168zM173 68h75q115 0 172.5 69.5t57.5 193.5t-57.5 190.5t-172.5 66.5h-75v-520z" />
<glyph unicode="E" horiz-adv-x="527" d="M90 0v656h378v-70h-295v-206h249v-71h-249v-238h305v-71h-388z" />
<glyph unicode="F" horiz-adv-x="494" d="M90 0v656h378v-70h-295v-222h250v-70h-250v-294h-83z" />
<glyph unicode="G" horiz-adv-x="617" d="M52 328q0 106 39 182t107 117t155 41q68 0 114.5 -25.5t74.5 -55.5l-46 -54q-25 26 -58 44t-83 18q-66 0 -115 -32.5t-75.5 -91.5t-26.5 -141q0 -124 56.5 -196.5t161.5 -72.5q35 0 67 10.5t51 28.5v171h-139v69h215v-276q-31 -33 -83.5 -54.5t-118.5 -21.5 q-86 0 -153 40.5t-105 116.5t-38 183z" />
<glyph unicode="H" horiz-adv-x="652" d="M90 0v656h83v-275h305v275h84v-656h-84v309h-305v-309h-83z" />
<glyph unicode="I" horiz-adv-x="263" d="M90 0v656h83v-656h-83z" />
<glyph unicode="J" horiz-adv-x="480" d="M31 93l60 42q22 -39 50 -56.5t63 -17.5q53 0 79 32.5t26 107.5v455h84v-463q0 -56 -18.5 -103t-58.5 -74.5t-104 -27.5q-123 0 -181 105z" />
<glyph unicode="K" horiz-adv-x="579" d="M90 0v656h83v-329h3l273 329h94l-205 -250l237 -406h-93l-196 341l-113 -133v-208h-83z" />
<glyph unicode="L" horiz-adv-x="486" d="M90 0v656h83v-585h287v-71h-370z" />
<glyph unicode="M" horiz-adv-x="727" d="M90 0v656h100l126 -350q12 -34 23.5 -68.5t23.5 -68.5h4q12 34 22.5 68.5t22.5 68.5l124 350h101v-656h-78v361q0 44 4 97.5t7 96.5h-4l-52 -149l-124 -340h-55l-124 340l-52 149h-4q3 -43 6.5 -96.5t3.5 -97.5v-361h-75z" />
<glyph unicode="N" horiz-adv-x="647" d="M90 0v656h86l237 -412l71 -136h4q-3 50 -6.5 103.5t-3.5 105.5v339h79v-656h-86l-238 413l-71 135h-4q4 -49 7.5 -101t3.5 -104v-343h-79z" />
<glyph unicode="O" horiz-adv-x="664" d="M52 331q0 105 35.5 180.5t99 116t145.5 40.5q83 0 146 -40.5t99 -116t36 -180.5t-36 -182t-99 -119t-146 -42q-82 0 -145.5 42t-99 119t-35.5 182zM138 331q0 -82 24 -142.5t68 -94t102 -33.5q59 0 102.5 33.5t67.5 94t24 142.5q0 122 -53 193t-141 71t-141 -71t-53 -193 z" />
<glyph unicode="P" horiz-adv-x="566" d="M90 0v656h187q74 0 129 -18t86 -60.5t31 -114.5q0 -103 -67 -153t-175 -50h-108v-260h-83zM173 328h98q86 0 127.5 32.5t41.5 102.5q0 71 -43.5 98.5t-129.5 27.5h-94v-261z" />
<glyph unicode="Q" horiz-adv-x="664" d="M52 331q0 105 35.5 180.5t99 116t145.5 40.5q83 0 146 -40.5t99 -116t36 -180.5q0 -94 -29 -166t-81 -116.5t-122 -56.5q23 -43 65.5 -65t95.5 -22q22 0 39 2.5t30 6.5l16 -64q-15 -6 -40 -10.5t-54 -4.5q-91 0 -152 44.5t-90 111.5q-71 10 -125 54t-84 117t-30 169z M138 331q0 -84 24 -145.5t68 -95t102 -33.5q59 0 102.5 33.5t67.5 95t24 145.5q0 122 -53 193t-141 71t-141 -71t-53 -193z" />
<glyph unicode="R" horiz-adv-x="569" d="M90 0v656h205q67 0 119 -17.5t81.5 -58t29.5 -108.5q0 -76 -40 -122.5t-108 -63.5l167 -286h-94l-158 277h-119v-277h-83zM173 345h110q77 0 118 31.5t41 95.5q0 65 -41 91t-118 26h-110v-244z" />
<glyph unicode="S" horiz-adv-x="534" d="M42 85l50 58q35 -37 82.5 -59.5t98.5 -22.5q65 0 101 29.5t36 77.5q0 34 -14.5 54t-39 34t-55.5 28l-94 41q-31 13 -61.5 34t-51 54t-20.5 81q0 50 26.5 89.5t73.5 62t106 22.5t109 -22.5t85 -58.5l-45 -54q-30 29 -66.5 45.5t-82.5 16.5q-55 0 -88.5 -26t-33.5 -70 q0 -31 16.5 -51.5t41.5 -33.5t51 -24l93 -40q38 -16 68.5 -38.5t48.5 -55t18 -81.5q0 -52 -27 -94.5t-77 -67.5t-119 -25t-128 26.5t-102 70.5z" />
<glyph unicode="T" horiz-adv-x="536" d="M28 586v70h480v-70h-198v-586h-84v586h-198z" />
<glyph unicode="U" horiz-adv-x="645" d="M87 271v385h83v-387q0 -79 20.5 -124.5t55 -64.5t77.5 -19q44 0 79 19t55.5 64.5t20.5 124.5v387h80v-385q0 -105 -31 -167t-84 -89t-120 -27t-120.5 27t-84.5 89t-31 167z" />
<glyph unicode="V" horiz-adv-x="515" d="M0 656h89l105 -354q18 -58 31.5 -108.5t32.5 -108.5h4q18 58 32 108.5t31 108.5l105 354h85l-208 -656h-97z" />
<glyph unicode="W" horiz-adv-x="786" d="M23 656h86l69 -357q9 -53 19 -106t19 -106h4q11 53 23 106.5t23 105.5l91 357h76l91 -357q12 -52 24 -105.5t24 -106.5h4q9 53 18 106.5t19 105.5l69 357h80l-136 -656h-100l-99 395q-9 38 -16.5 74.5t-15.5 74.5h-4q-7 -38 -16 -74.5t-17 -74.5l-97 -395h-99z" />
<glyph unicode="X" horiz-adv-x="513" d="M15 0l191 339l-178 317h92l89 -168q13 -22 24.5 -44t26.5 -51h4q14 29 24.5 51t22.5 44l87 168h88l-179 -321l191 -335h-92l-96 177q-13 24 -26.5 49.5t-29.5 55.5h-4q-14 -30 -27 -55.5t-25 -49.5l-95 -177h-88z" />
<glyph unicode="Y" horiz-adv-x="476" d="M-1 656h89l85 -185q16 -36 31.5 -71t31.5 -72h4q17 37 34 72t32 71l84 185h87l-197 -402v-254h-84v254z" />
<glyph unicode="Z" horiz-adv-x="539" d="M45 0v50l345 536h-314v70h418v-49l-346 -536h349v-71h-452z" />
<glyph unicode="[" horiz-adv-x="303" d="M94 -152v860h179v-47h-117v-766h117v-47h-179z" />
<glyph unicode="\" horiz-adv-x="350" d="M14 710h59l267 -870h-59z" />
<glyph unicode="]" horiz-adv-x="303" d="M31 -105h116v766h-116v47h178v-860h-178v47z" />
<glyph unicode="^" d="M60 284l152 386h73l152 -386h-72l-65 176l-49 133h-4l-50 -133l-65 -176h-72z" />
<glyph unicode="_" horiz-adv-x="500" d="M12 -71h476v-55h-476v55z" />
<glyph unicode="`" horiz-adv-x="542" d="M128 726l58 55l141 -167l-42 -41z" />
<glyph unicode="a" horiz-adv-x="504" d="M52 126q0 80 71.5 122.5t226.5 59.5q0 31 -8.5 59t-30 45.5t-61.5 17.5q-42 0 -79 -16t-66 -36l-32 57q34 22 83.5 42.5t107.5 20.5q89 0 129 -54.5t40 -145.5v-298h-68l-7 58h-3q-34 -28 -74.5 -49t-86.5 -21q-61 0 -101.5 36.5t-40.5 101.5zM132 132q0 -41 25 -59.5 t61 -18.5q35 0 66 17t66 48v135q-121 -15 -169.5 -45t-48.5 -77z" />
<glyph unicode="b" horiz-adv-x="553" d="M82 0v712h82v-194l-2 -88q33 29 72.5 48.5t79.5 19.5q95 0 144 -67t49 -180q0 -83 -29.5 -142t-77 -90t-103.5 -31q-34 0 -70.5 16.5t-68.5 45.5h-3l-7 -50h-66zM164 108q32 -28 63.5 -39.5t55.5 -11.5q60 0 99.5 51.5t39.5 141.5q0 80 -29.5 129.5t-98.5 49.5 q-31 0 -63 -17t-67 -49v-255z" />
<glyph unicode="c" horiz-adv-x="456" d="M46 242q0 82 32.5 139t86 87t115.5 30q48 0 82.5 -17t59.5 -40l-42 -54q-21 19 -44.5 31t-52.5 12q-44 0 -78.5 -23.5t-54 -66t-19.5 -98.5q0 -83 41.5 -134.5t108.5 -51.5q34 0 63 14.5t51 34.5l36 -55q-33 -29 -73 -45.5t-84 -16.5q-64 0 -116 30t-82 87t-30 137z" />
<glyph unicode="d" horiz-adv-x="555" d="M47 242q0 79 29.5 136.5t78 88.5t103.5 31q42 0 73 -15t63 -41l-4 83v187h83v-712h-68l-7 57h-3q-28 -28 -66 -48.5t-81 -20.5q-91 0 -146 66.5t-55 187.5zM132 243q0 -87 35 -136.5t99 -49.5t124 67v254q-31 28 -59.5 39.5t-58.5 11.5q-39 0 -70.5 -23.5t-50.5 -65 t-19 -97.5z" />
<glyph unicode="e" horiz-adv-x="496" d="M46 242q0 80 31.5 137t81.5 88t105 31q93 0 143.5 -62t50.5 -166q0 -13 -0.5 -25.5t-2.5 -21.5h-328q5 -77 48.5 -123t113.5 -46q35 0 64.5 10.5t56.5 27.5l29 -54q-31 -20 -70.5 -35t-89.5 -15q-65 0 -118 30.5t-84 87.5t-31 136zM126 282h260q0 74 -31.5 112.5 t-88.5 38.5q-51 0 -91.5 -39.5t-48.5 -111.5z" />
<glyph unicode="f" horiz-adv-x="292" d="M30 419v62l66 5v77q0 74 34.5 117.5t107.5 43.5q23 0 44 -4.5t37 -11.5l-18 -63q-27 12 -55 12q-68 0 -68 -94v-77h103v-67h-103v-419h-82v419h-66z" />
<glyph unicode="g" horiz-adv-x="504" d="M45 -93q0 31 19 59.5t52 50.5v4q-18 11 -30.5 31t-12.5 48q0 31 17 54t36 36v4q-24 20 -43.5 54t-19.5 77q0 53 25 92t67 60t91 21q20 0 38 -3.5t31 -8.5h169v-63h-100q17 -16 28.5 -42.5t11.5 -57.5q0 -52 -24 -90t-64 -58.5t-90 -20.5q-39 0 -73 17q-13 -11 -22 -24.5 t-9 -33.5q0 -23 18.5 -38t67.5 -15h94q85 0 127.5 -27.5t42.5 -88.5q0 -45 -30 -83t-85 -61.5t-131 -23.5q-89 0 -145 34t-56 97zM117 -82q0 -39 37.5 -62t103.5 -23q70 0 112 30.5t42 68.5q0 34 -25.5 47t-72.5 13h-84q-14 0 -30.5 2t-32.5 6q-26 -19 -38 -40t-12 -42z M143 325q0 -53 30.5 -84.5t72.5 -31.5t72.5 31.5t30.5 84.5t-30 83t-73 30t-73 -30t-30 -83z" />
<glyph unicode="h" horiz-adv-x="544" d="M82 0v712h82v-194l-3 -100q35 33 73.5 56.5t88.5 23.5q77 0 112.5 -48.5t35.5 -141.5v-308h-82v297q0 68 -22 99t-70 31q-37 0 -66.5 -19t-66.5 -56v-352h-82z" />
<glyph unicode="i" horiz-adv-x="246" d="M69 640q0 24 15.5 39t39.5 15t39.5 -15t15.5 -39q0 -25 -15.5 -39.5t-39.5 -14.5t-39.5 14.5t-15.5 39.5zM82 0v486h82v-486h-82z" />
<glyph unicode="j" horiz-adv-x="247" d="M-40 -204l17 62q9 -3 21 -5.5t25 -2.5q36 0 48 25.5t12 69.5v541h82v-541q0 -74 -30 -118t-103 -44q-22 0 -40.5 4t-31.5 9zM68 640q0 24 15.5 39t39.5 15t39.5 -15t15.5 -39q0 -25 -15.5 -39.5t-39.5 -14.5t-39.5 14.5t-15.5 39.5z" />
<glyph unicode="k" horiz-adv-x="495" d="M82 0v712h81v-482h3l207 256h91l-163 -195l185 -291h-90l-142 234l-91 -106v-128h-81z" />
<glyph unicode="l" horiz-adv-x="255" d="M82 98v614h82v-620q0 -19 7 -27.5t16 -8.5q4 0 7.5 0.5l10.5 1.5l11 -62q-8 -4 -19 -6t-28 -2q-46 0 -66.5 28.5t-20.5 81.5z" />
<glyph unicode="m" horiz-adv-x="829" d="M82 0v486h68l7 -70h3q32 35 70 58.5t81 23.5q56 0 87.5 -24.5t46.5 -68.5q38 41 77 67t83 26q75 0 111.5 -48.5t36.5 -141.5v-308h-82v297q0 68 -22 99t-68 31q-54 0 -122 -75v-352h-82v297q0 68 -22 99t-69 31q-54 0 -122 -75v-352h-82z" />
<glyph unicode="n" horiz-adv-x="547" d="M82 0v486h68l7 -70h3q35 35 74 58.5t89 23.5q77 0 112.5 -48.5t35.5 -141.5v-308h-82v297q0 68 -22 99t-70 31q-37 0 -66.5 -19t-66.5 -56v-352h-82z" />
<glyph unicode="o" horiz-adv-x="542" d="M46 242q0 82 31.5 139t83 87t110.5 30q60 0 111 -30t82.5 -87t31.5 -139q0 -80 -31.5 -137t-82.5 -87t-111 -30q-59 0 -110.5 30t-83 87t-31.5 137zM131 242q0 -83 39 -134.5t101 -51.5q63 0 101.5 51.5t38.5 134.5q0 84 -38.5 136t-101.5 52q-62 0 -101 -52t-39 -136z " />
<glyph unicode="p" horiz-adv-x="555" d="M82 -205v691h68l7 -56h3q33 28 72.5 48t82.5 20q94 0 143 -67.5t49 -180.5q0 -82 -29.5 -141t-77 -90t-103.5 -31q-34 0 -67.5 15t-67.5 41l2 -85v-164h-82zM164 108q33 -28 64 -39.5t55 -11.5q60 0 99.5 51.5t39.5 141.5q0 80 -29.5 129.5t-98.5 49.5q-31 0 -62.5 -17 t-67.5 -49v-255z" />
<glyph unicode="q" horiz-adv-x="555" d="M47 242q0 79 29.5 136.5t78 88.5t103.5 31q42 0 74 -14.5t65 -43.5h2l8 46h66v-691h-83v173l4 88q-29 -28 -66.5 -48t-79.5 -20q-91 0 -146 66.5t-55 187.5zM132 243q0 -87 35 -136.5t99 -49.5t124 67v254q-31 28 -59.5 39.5t-58.5 11.5q-39 0 -70.5 -23.5t-50.5 -65 t-19 -97.5z" />
<glyph unicode="r" horiz-adv-x="347" d="M82 0v486h68l7 -88h3q25 46 61 73t77 27q29 0 52 -10l-16 -72q-12 4 -22 6t-25 2q-31 0 -64.5 -25t-58.5 -87v-312h-82z" />
<glyph unicode="s" horiz-adv-x="419" d="M28 55l41 55q32 -25 66 -41.5t77 -16.5q48 0 72 22.5t24 53.5q0 25 -16.5 42t-42 28.5t-52.5 21.5q-34 13 -67 29.5t-54 43t-21 67.5q0 59 44.5 98.5t123.5 39.5q45 0 84 -16t67 -39l-40 -52q-25 19 -52 31t-59 12q-46 0 -67.5 -21t-21.5 -49q0 -23 15 -37.5t39 -25 t51 -21.5q35 -13 69 -29.5t56.5 -44t22.5 -73.5q0 -39 -20.5 -72t-60 -53t-97.5 -20q-52 0 -99 19.5t-82 47.5z" />
<glyph unicode="t" horiz-adv-x="338" d="M24 419v62l76 5l10 136h69v-136h131v-67h-131v-270q0 -45 16.5 -69.5t58.5 -24.5q13 0 28 4.5t27 8.5l16 -62q-20 -7 -43.5 -12.5t-46.5 -5.5q-78 0 -108.5 45t-30.5 117v269h-72z" />
<glyph unicode="u" horiz-adv-x="544" d="M75 178v308h83v-297q0 -68 21.5 -99t69.5 -31q38 0 67.5 19.5t63.5 62.5v345h82v-486h-68l-7 76h-3q-34 -40 -72 -64t-88 -24q-77 0 -113 48.5t-36 141.5z" />
<glyph unicode="v" horiz-adv-x="467" d="M12 486h85l92 -276q11 -36 22.5 -72t22.5 -71h4q11 35 22.5 71t21.5 72l92 276h81l-172 -486h-96z" />
<glyph unicode="w" horiz-adv-x="718" d="M24 486h84l72 -281q8 -34 15 -67.5t14 -67.5h4q8 34 16 67.5t17 67.5l75 281h80l76 -281q9 -34 17.5 -67.5t16.5 -67.5h4q8 34 15.5 67.5t14.5 67.5l71 281h78l-130 -486h-100l-70 261q-9 35 -16.5 69t-16.5 71h-4q-8 -37 -16 -71.5t-18 -69.5l-68 -260h-96z" />
<glyph unicode="x" horiz-adv-x="446" d="M14 0l159 254l-147 232h89l65 -107q11 -19 23 -40t25 -41h4q11 20 22 41t22 40l59 107h86l-147 -241l158 -245h-89l-71 113l-26 44t-27 43h-4q-13 -21 -25 -42.5t-24 -44.5l-66 -113h-86z" />
<glyph unicode="y" horiz-adv-x="467" d="M12 486h85l99 -269q11 -31 23.5 -67t23.5 -70h4q11 33 21 69t20 68l87 269h80l-183 -526q-17 -48 -40.5 -86t-58 -60.5t-83.5 -22.5q-16 0 -30.5 3t-26.5 7l16 65l18 -4.5t19 -2.5q41 0 68 29.5t42 74.5l11 36z" />
<glyph unicode="z" horiz-adv-x="425" d="M31 0v44l256 375h-228v67h332v-44l-256 -375h264v-67h-368z" />
<glyph unicode="{" horiz-adv-x="303" d="M34 252v52q51 1 68.5 25t17.5 52q0 51 -4.5 95t-4.5 99q0 77 29 105t88 28h45v-47h-27q-40 0 -54 -21t-14 -70q0 -46 3 -88t3 -93q0 -49 -14 -74.5t-46 -34.5v-4q32 -9 46 -35t14 -74q0 -51 -3 -93t-3 -88q0 -49 14 -70t54 -21h27v-47h-45q-59 0 -88 28t-29 105 q0 55 4.5 99t4.5 94q0 29 -17.5 53t-68.5 25z" />
<glyph unicode="|" horiz-adv-x="241" d="M92 -250v1000h58v-1000h-58z" />
<glyph unicode="}" horiz-adv-x="303" d="M31 -105h26q41 0 54.5 21t13.5 70q0 46 -2.5 88t-2.5 93q0 48 13.5 74t45.5 35v4q-32 9 -45.5 34.5t-13.5 74.5q0 51 2.5 93t2.5 88q0 49 -13.5 70t-54.5 21h-26v47h44q60 0 88.5 -28t28.5 -105q0 -55 -4.5 -99t-4.5 -95q0 -28 18 -52t68 -25v-52q-50 -1 -68 -25t-18 -53 q0 -50 4.5 -94t4.5 -99q0 -77 -28.5 -105t-88.5 -28h-44v47z" />
<glyph unicode="~" d="M36 313q27 48 60 69t65 21q30 0 53.5 -12.5t43.5 -29.5t39.5 -29.5t40.5 -12.5q22 0 41 14.5t36 46.5l46 -34q-27 -47 -60 -68t-65 -21q-30 0 -53.5 12.5t-43.5 29.5t-39.5 29.5t-40.5 12.5q-22 0 -41 -14.5t-36 -46.5z" />
<glyph unicode="&#xa1;" horiz-adv-x="289" d="M85 436q0 27 17.5 44.5t42.5 17.5q24 0 41.5 -17.5t17.5 -44.5q0 -28 -17.5 -46t-41.5 -18q-25 0 -42.5 18t-17.5 46zM103 -184l2 94l11 378h57l11 -378l2 -94h-83z" />
<glyph unicode="&#xa2;" d="M61 310q0 69 26.5 119t72.5 80t102 38v106h52v-103q44 -2 76 -19t55 -39l-40 -52q-20 18 -42.5 29.5t-48.5 12.5v-344q32 2 58 15.5t46 31.5l36 -52q-29 -26 -65 -43t-75 -20v-103h-52v104q-89 11 -145 72t-56 167zM143 310q0 -67 31.5 -111t87.5 -57v335 q-55 -13 -87 -57t-32 -110z" />
<glyph unicode="&#xa3;" d="M53 285v52l67 4h18q-10 32 -18.5 63t-8.5 62q0 85 51.5 134.5t134.5 49.5q53 0 91 -21.5t64 -53.5l-48 -47q-19 23 -43 38t-58 15q-54 0 -82.5 -32.5t-28.5 -84.5q0 -31 7.5 -61t16.5 -62h159v-56h-146q2 -13 3.5 -27t1.5 -29q0 -52 -16 -87.5t-46 -66.5v-4h281v-71h-399 v50q51 28 78.5 76t27.5 104q0 14 -2 28t-5 27h-100z" />
<glyph unicode="&#xa4;" d="M26 148l64 65q-36 49 -36 116q0 68 36 117l-64 66l44 45l68 -70q48 37 111 37q62 0 110 -37l68 70l44 -45l-65 -66q17 -23 27 -52.5t10 -64.5q0 -34 -10 -63.5t-27 -52.5l65 -65l-44 -45l-68 69q-23 -19 -52 -28.5t-58 -9.5q-64 0 -111 38l-68 -69zM130 329q0 -60 35 -97 t84 -37q48 0 83 37t35 97t-35 97t-83 37q-49 0 -84 -37t-35 -97z" />
<glyph unicode="&#xa5;" d="M23 638h86l78 -171q15 -33 29.5 -66.5t30.5 -68.5h4q17 35 31.5 68.5t29.5 66.5l78 171h84l-164 -320h142v-47h-163v-65h163v-48h-163v-158h-82v158h-162v48h162v65h-162v47h141z" />
<glyph unicode="&#xa6;" horiz-adv-x="241" d="M92 214h58v-464h-58v464zM92 291v459h58v-459h-58z" />
<glyph unicode="&#xa7;" d="M45 343q0 44 24.5 76t61.5 53q-15 16 -23.5 36t-8.5 46q0 52 38.5 91t113.5 39q47 0 86.5 -17t67.5 -40l-40 -53q-24 20 -51 33t-60 13q-42 0 -60.5 -18.5t-18.5 -44.5q0 -27 20 -45.5t51.5 -33t67 -29.5t67 -36t51.5 -52t20 -77q0 -47 -23.5 -77t-61.5 -52 q14 -16 22 -36t8 -46q0 -39 -20.5 -70t-57 -49t-83.5 -18q-53 0 -97.5 19t-75.5 52l50 45q25 -24 54 -38t69 -14t62.5 20.5t22.5 48.5q0 29 -20 47.5t-51.5 33t-66.5 29.5t-66.5 35.5t-51.5 51.5t-20 77zM117 348q0 -33 19.5 -54.5t50.5 -37.5t66.5 -30.5t66.5 -32.5 q29 14 44.5 33t15.5 52q0 34 -19.5 56t-50.5 38t-66 30.5t-66 32.5q-28 -16 -44.5 -36t-16.5 -51z" />
<glyph unicode="&#xa8;" horiz-adv-x="542" d="M125 637q0 21 14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5q-22 0 -36 14.5t-14 35.5zM318 637q0 21 14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5q-21 0 -35 14.5t-14 35.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="744" d="M49 323q0 76 25.5 137t70.5 104t103.5 66t123.5 23t123.5 -23t103.5 -66t70.5 -104t25.5 -137t-25.5 -138t-70.5 -105.5t-103.5 -67t-123.5 -23.5t-123.5 23.5t-103.5 67t-70.5 105.5t-25.5 138zM96 323q0 -87 37.5 -153t100.5 -102.5t138 -36.5q76 0 138.5 36.5 t100 102.5t37.5 153t-37.5 152t-100 101t-138.5 36q-75 0 -138 -36t-100.5 -101t-37.5 -152zM198 323q0 58 26 100.5t68.5 65.5t90.5 23q42 0 71.5 -16.5t53.5 -40.5l-35 -39q-20 20 -40.5 30.5t-46.5 10.5q-55 0 -88.5 -37.5t-33.5 -96.5q0 -66 32.5 -104.5t86.5 -38.5 q32 0 56 12.5t46 31.5l30 -42q-28 -24 -59 -40.5t-76 -16.5q-50 0 -91.5 23.5t-66 68t-24.5 106.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="345" d="M37 481q0 53 48 81t151 38q-1 32 -14 52.5t-48 20.5q-26 0 -53.5 -10.5t-47.5 -23.5l-23 43q25 15 59.5 28.5t73.5 13.5q60 0 87.5 -35t27.5 -98v-196h-50l-7 37h-4q-20 -18 -45 -31.5t-56 -13.5q-44 0 -71.5 25.5t-27.5 68.5zM98 486q0 -25 15.5 -37.5t40.5 -12.5 q39 0 82 40v85q-77 -8 -107.5 -27.5t-30.5 -47.5z" />
<glyph unicode="&#xab;" horiz-adv-x="429" d="M45 221v62l136 155l36 -30l-118 -156l118 -158l-36 -28zM203 221v62l136 155l36 -30l-118 -156l118 -158l-36 -28z" />
<glyph unicode="&#xac;" d="M34 299v62h429v-257h-66v195h-363z" />
<glyph unicode="&#xae;" horiz-adv-x="423" d="M23 516q0 59 25.5 103t68.5 69t94 25q52 0 95 -25t68.5 -69t25.5 -103q0 -58 -25.5 -102.5t-68.5 -69.5t-95 -25q-51 0 -94 25t-68.5 69.5t-25.5 102.5zM63 516q0 -47 19.5 -83.5t53 -56.5t75.5 -20t75.5 20t53.5 56.5t20 83.5t-20 83.5t-53.5 57t-75.5 20.5t-75.5 -20.5 t-53 -57t-19.5 -83.5zM139 417v203h76q32 0 55 -14.5t23 -50.5q0 -18 -10 -33.5t-27 -21.5l46 -83h-46l-35 70h-41v-70h-41zM180 519h26q43 0 43 34q0 15 -9 24t-32 9h-28v-67z" />
<glyph unicode="&#xaf;" horiz-adv-x="542" d="M138 601v57h266v-57h-266z" />
<glyph unicode="&#xb0;" horiz-adv-x="331" d="M41 556q0 59 37.5 94t87.5 35t87.5 -35t37.5 -94q0 -38 -17.5 -67t-45.5 -44.5t-62 -15.5q-33 0 -61.5 15.5t-46 44.5t-17.5 67zM91 556q0 -35 21 -58t54 -23t54 23t21 58q0 37 -21 60t-54 23t-54 -23t-21 -60z" />
<glyph unicode="&#xb1;" d="M34 0v62h429v-62h-429zM34 304v62h182v190h65v-190h182v-62h-182v-177h-65v177h-182z" />
<glyph unicode="&#xb2;" horiz-adv-x="367" d="M40 721q23 34 56.5 55t73.5 21q59 0 94.5 -32t35.5 -94q0 -37 -19 -71.5t-51.5 -70.5t-74.5 -79h165v-55h-268v37q91 82 138 133.5t47 95.5q0 38 -20 60t-55 22q-25 0 -46 -16.5t-38 -40.5z" />
<glyph unicode="&#xb3;" horiz-adv-x="367" d="M35 459l43 33q18 -27 43 -42.5t56 -15.5q32 0 55 18t23 52t-32 51.5t-89 17.5v41q50 0 78 20t28 50q0 28 -19.5 45t-51.5 17q-22 0 -42 -13.5t-37 -33.5l-39 34q26 29 57 46.5t71 17.5q50 0 87 -27.5t37 -77.5q0 -34 -19 -58t-49 -38q33 -8 58.5 -32.5t25.5 -63.5 q0 -54 -41 -85.5t-98 -31.5q-47 0 -84.5 21.5t-60.5 54.5z" />
<glyph unicode="&#xb4;" horiz-adv-x="542" d="M215 614l141 167l58 -55l-157 -153z" />
<glyph unicode="&#xb5;" horiz-adv-x="562" d="M82 -179v665h82v-297q0 -64 21 -97t68 -33q33 0 64 17.5t65 77.5v332h83q-1 -98 -3.5 -200.5t-2.5 -190.5q0 -20 10 -29.5t26 -9.5q13 0 29 6l11 -62q-10 -5 -24 -8.5t-34 -3.5q-41 0 -62 22.5t-26 71.5h-2q-26 -46 -60.5 -69t-74.5 -23q-29 0 -53 9.5t-41 37.5 q1 -48 1.5 -82t2 -64.5t3.5 -69.5h-83z" />
<glyph unicode="&#xb6;" horiz-adv-x="560" d="M41 443q0 79 31.5 125.5t86 67t123.5 20.5h44v-430h-33q-72 0 -129 23t-90 71t-33 123zM380 -80v736h84v-736h-84z" />
<glyph unicode="&#xb7;" horiz-adv-x="249" d="M65 321q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5q-25 0 -42.5 17.5t-17.5 44.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="542" d="M182 -186q63 5 86 16.5t23 33.5q0 20 -17 31t-60 17l44 91h53l-29 -67q35 -8 54 -24t19 -47q0 -43 -42.5 -65t-122.5 -26z" />
<glyph unicode="&#xb9;" horiz-adv-x="367" d="M87 703v42q33 6 55 15.5t42 24.5h52v-390h-64v308h-85z" />
<glyph unicode="&#xba;" horiz-adv-x="365" d="M30 555q0 54 20.5 92t55.5 57.5t76 19.5t75.5 -19.5t55.5 -57.5t21 -92q0 -53 -21 -90.5t-55.5 -57.5t-75.5 -20t-76 20t-55.5 57.5t-20.5 90.5zM93 555q0 -52 24 -84.5t65 -32.5t64.5 32.5t23.5 84.5q0 53 -23.5 85.5t-64.5 32.5t-65 -32.5t-24 -85.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="429" d="M54 94l118 158l-118 156l35 30l137 -155v-62l-137 -155zM212 94l118 158l-118 156l35 30l137 -155v-62l-137 -155z" />
<glyph unicode="&#xbc;" horiz-adv-x="781" d="M63 574v42q33 6 55 15.5t42 24.5h52v-390h-64v308h-85zM180 -12l362 680h56l-362 -680h-56zM455 104v33l164 253h72v-240h58v-46h-58v-104h-58v104h-178zM522 150h111v70l4 109h-4l-50 -81z" />
<glyph unicode="&#xbd;" horiz-adv-x="808" d="M63 574v42q33 6 55 15.5t42 24.5h52v-390h-64v308h-85zM160 -12l362 680h56l-362 -680h-56zM481 326q23 34 56.5 55t73.5 21q59 0 94.5 -32t35.5 -94q0 -37 -19 -71.5t-51.5 -70.5t-74.5 -79h165v-55h-268v37q91 82 138 133.5t47 95.5q0 38 -20 60t-55 22 q-25 0 -46 -16.5t-38 -40.5z" />
<glyph unicode="&#xbe;" horiz-adv-x="796" d="M34 330l43 33q18 -27 43 -42.5t56 -15.5q32 0 55 18t23 52t-32 51.5t-89 17.5v41q50 0 78 20t28 50q0 28 -19.5 45t-51.5 17q-22 0 -42 -13.5t-37 -33.5l-39 34q26 29 57 46.5t71 17.5q50 0 87 -27.5t37 -77.5q0 -34 -19 -58t-49 -38q33 -8 58.5 -32.5t25.5 -63.5 q0 -54 -41 -85.5t-98 -31.5q-47 0 -84.5 21.5t-60.5 54.5zM218 -12l362 680h56l-362 -680h-56zM470 104v33l164 253h72v-240h58v-46h-58v-104h-58v104h-178zM537 150h111v70l4 109h-4l-50 -81z" />
<glyph unicode="&#xbf;" horiz-adv-x="425" d="M48 -36q0 39 16 70.5t39.5 59.5t45.5 57t35 62t8 75h73q7 -47 -5 -83t-33.5 -66t-44 -57t-38.5 -54t-16 -59q0 -40 24 -68t73 -28q33 0 62 15t52 42l48 -43q-32 -35 -74.5 -59t-97.5 -24q-76 0 -121.5 43t-45.5 117zM167 436q0 27 17.5 44.5t42.5 17.5q24 0 41.5 -17.5 t17.5 -44.5q0 -28 -17.5 -46t-41.5 -18q-25 0 -42.5 18t-17.5 46z" />
<glyph unicode="&#xc0;" horiz-adv-x="544" d="M3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM136 812l47 55l142 -128l-37 -42zM172 267h197l-31 100q-18 55 -34 109.5t-32 111.5h-4q-15 -57 -31 -111.5t-34 -109.5z" />
<glyph unicode="&#xc1;" horiz-adv-x="544" d="M3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM172 267h197l-31 100q-18 55 -34 109.5t-32 111.5h-4q-15 -57 -31 -111.5t-34 -109.5zM217 739l142 128l47 -55l-152 -115z" />
<glyph unicode="&#xc2;" horiz-adv-x="544" d="M3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM123 725l105 113h86l105 -113l-36 -26l-110 93h-4l-110 -93zM172 267h197l-31 100q-18 55 -34 109.5t-32 111.5h-4q-15 -57 -31 -111.5t-34 -109.5z" />
<glyph unicode="&#xc3;" horiz-adv-x="544" d="M3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM94 718q3 57 30 90t72 33q28 0 48.5 -10.5t36 -25t30.5 -25t33 -10.5q40 0 48 66l56 -4q-3 -58 -30 -90.5t-72 -32.5q-28 0 -48.5 10.5t-36 25t-30.5 25t-33 10.5q-19 0 -32 -17t-16 -49zM172 267h197l-31 100 q-18 55 -34 109.5t-32 111.5h-4q-15 -57 -31 -111.5t-34 -109.5z" />
<glyph unicode="&#xc4;" horiz-adv-x="544" d="M3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM119 764q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14q-21 0 -35 14t-14 35zM172 267h197l-31 100q-18 55 -34 109.5t-32 111.5h-4q-15 -57 -31 -111.5t-34 -109.5zM325 764 q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5t14 -35.5q0 -21 -14 -35t-35 -14q-22 0 -35.5 14t-13.5 35z" />
<glyph unicode="&#xc5;" horiz-adv-x="544" d="M3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM172 267h197l-31 100q-18 55 -34 109.5t-32 111.5h-4q-15 -57 -31 -111.5t-34 -109.5zM174 789q0 41 27.5 66t69.5 25q41 0 69 -25t28 -66q0 -40 -28 -65t-69 -25q-42 0 -69.5 25t-27.5 65zM219 789 q0 -26 15 -40t37 -14q20 0 35.5 14t15.5 40t-15.5 40t-35.5 14q-22 0 -37 -14t-15 -40z" />
<glyph unicode="&#xc6;" horiz-adv-x="822" d="M8 0l344 656h411v-70h-278v-206h232v-71h-232v-238h288v-71h-372v191h-206l-99 -191h-88zM229 258h172v332h-4q-53 -107 -107 -214z" />
<glyph unicode="&#xc7;" horiz-adv-x="571" d="M52 328q0 106 38 182t104 117t149 41q60 0 106 -24.5t74 -56.5l-45 -54q-26 28 -59 45t-75 17q-62 0 -108.5 -32.5t-72 -91.5t-25.5 -141t25 -142.5t70.5 -93.5t107.5 -33q47 0 84 20t68 54l46 -52q-39 -45 -88 -70t-113 -25q-82 0 -146.5 40.5t-102 116.5t-37.5 183z M248 -186q63 5 86 16.5t23 33.5q0 20 -17 31t-60 17l44 91h53l-29 -67q35 -8 54 -24t19 -47q0 -43 -42.5 -65t-122.5 -26z" />
<glyph unicode="&#xc8;" horiz-adv-x="527" d="M90 0v656h378v-70h-295v-206h249v-71h-249v-238h305v-71h-388zM155 812l47 55l142 -128l-37 -42z" />
<glyph unicode="&#xc9;" horiz-adv-x="527" d="M90 0v656h378v-70h-295v-206h249v-71h-249v-238h305v-71h-388zM236 739l142 128l47 -55l-152 -115z" />
<glyph unicode="&#xca;" horiz-adv-x="527" d="M90 0v656h378v-70h-295v-206h249v-71h-249v-238h305v-71h-388zM142 725l105 113h86l105 -113l-36 -26l-110 93h-4l-110 -93z" />
<glyph unicode="&#xcb;" horiz-adv-x="527" d="M90 0v656h378v-70h-295v-206h249v-71h-249v-238h305v-71h-388zM138 764q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14q-21 0 -35 14t-14 35zM344 764q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5t14 -35.5q0 -21 -14 -35t-35 -14 q-22 0 -35.5 14t-13.5 35z" />
<glyph unicode="&#xcc;" horiz-adv-x="263" d="M-3 812l47 55l142 -128l-37 -42zM90 0v656h83v-656h-83z" />
<glyph unicode="&#xcd;" horiz-adv-x="263" d="M78 739l142 128l47 -55l-152 -115zM90 0v656h83v-656h-83z" />
<glyph unicode="&#xce;" horiz-adv-x="263" d="M-16 725l105 113h86l105 -113l-36 -26l-110 93h-4l-110 -93zM90 0v656h83v-656h-83z" />
<glyph unicode="&#xcf;" horiz-adv-x="263" d="M-20 764q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14q-21 0 -35 14t-14 35zM90 0v656h83v-656h-83zM186 764q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5t14 -35.5q0 -21 -14 -35t-35 -14q-22 0 -35.5 14t-13.5 35z" />
<glyph unicode="&#xd0;" horiz-adv-x="638" d="M33 321v43l79 4v288h164q152 0 231 -84.5t79 -240.5q0 -157 -78.5 -244t-227.5 -87h-168v321h-79zM195 68h75q115 0 172.5 69.5t57.5 193.5t-57.5 190.5t-172.5 66.5h-75v-220h149v-47h-149v-253z" />
<glyph unicode="&#xd1;" horiz-adv-x="647" d="M90 0v656h86l237 -412l71 -136h4q-3 50 -6.5 103.5t-3.5 105.5v339h79v-656h-86l-238 413l-71 135h-4q4 -49 7.5 -101t3.5 -104v-343h-79zM157 718q3 57 30 90t72 33q28 0 48.5 -10.5t36 -25t30.5 -25t33 -10.5q40 0 48 66l56 -4q-3 -58 -30 -90.5t-72 -32.5 q-28 0 -48.5 10.5t-36 25t-30.5 25t-33 10.5q-19 0 -32 -17t-16 -49z" />
<glyph unicode="&#xd2;" horiz-adv-x="664" d="M52 331q0 105 35.5 180.5t99 116t145.5 40.5q83 0 146 -40.5t99 -116t36 -180.5t-36 -182t-99 -119t-146 -42q-82 0 -145.5 42t-99 119t-35.5 182zM138 331q0 -82 24 -142.5t68 -94t102 -33.5q59 0 102.5 33.5t67.5 94t24 142.5q0 122 -53 193t-141 71t-141 -71t-53 -193 zM197 812l47 55l142 -128l-37 -42z" />
<glyph unicode="&#xd3;" horiz-adv-x="664" d="M52 331q0 105 35.5 180.5t99 116t145.5 40.5q83 0 146 -40.5t99 -116t36 -180.5t-36 -182t-99 -119t-146 -42q-82 0 -145.5 42t-99 119t-35.5 182zM138 331q0 -82 24 -142.5t68 -94t102 -33.5q59 0 102.5 33.5t67.5 94t24 142.5q0 122 -53 193t-141 71t-141 -71t-53 -193 zM278 739l142 128l47 -55l-152 -115z" />
<glyph unicode="&#xd4;" horiz-adv-x="664" d="M52 331q0 105 35.5 180.5t99 116t145.5 40.5q83 0 146 -40.5t99 -116t36 -180.5t-36 -182t-99 -119t-146 -42q-82 0 -145.5 42t-99 119t-35.5 182zM138 331q0 -82 24 -142.5t68 -94t102 -33.5q59 0 102.5 33.5t67.5 94t24 142.5q0 122 -53 193t-141 71t-141 -71t-53 -193 zM184 725l105 113h86l105 -113l-36 -26l-110 93h-4l-110 -93z" />
<glyph unicode="&#xd5;" horiz-adv-x="664" d="M52 331q0 105 35.5 180.5t99 116t145.5 40.5q83 0 146 -40.5t99 -116t36 -180.5t-36 -182t-99 -119t-146 -42q-82 0 -145.5 42t-99 119t-35.5 182zM138 331q0 -82 24 -142.5t68 -94t102 -33.5q59 0 102.5 33.5t67.5 94t24 142.5q0 122 -53 193t-141 71t-141 -71t-53 -193 zM155 718q3 57 30 90t72 33q28 0 48.5 -10.5t36 -25t30.5 -25t33 -10.5q40 0 48 66l56 -4q-3 -58 -30 -90.5t-72 -32.5q-28 0 -48.5 10.5t-36 25t-30.5 25t-33 10.5q-19 0 -32 -17t-16 -49z" />
<glyph unicode="&#xd6;" horiz-adv-x="664" d="M52 331q0 105 35.5 180.5t99 116t145.5 40.5q83 0 146 -40.5t99 -116t36 -180.5t-36 -182t-99 -119t-146 -42q-82 0 -145.5 42t-99 119t-35.5 182zM138 331q0 -82 24 -142.5t68 -94t102 -33.5q59 0 102.5 33.5t67.5 94t24 142.5q0 122 -53 193t-141 71t-141 -71t-53 -193 zM180 764q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14q-21 0 -35 14t-14 35zM386 764q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5t14 -35.5q0 -21 -14 -35t-35 -14q-22 0 -35.5 14t-13.5 35z" />
<glyph unicode="&#xd7;" d="M50 171l155 159l-155 158l44 45l155 -159l154 159l44 -45l-155 -158l155 -159l-44 -45l-154 160l-155 -160z" />
<glyph unicode="&#xd8;" horiz-adv-x="664" d="M50 6l70 91q-31 44 -48 103t-17 131q0 105 35.5 180.5t99 116t145.5 40.5q104 0 176 -63l62 81l46 -35l-69 -89q32 -43 49 -101.5t17 -129.5q0 -105 -36 -182t-99 -119t-146 -42q-104 0 -175 65l-64 -83zM141 331q0 -48 8.5 -89.5t24.5 -73.5l288 373q-51 54 -127 54 q-88 0 -141 -71t-53 -193zM209 117q50 -56 126 -56q59 0 102.5 33.5t67.5 94t24 142.5q0 48 -8.5 88.5t-24.5 71.5z" />
<glyph unicode="&#xd9;" horiz-adv-x="645" d="M87 271v385h83v-387q0 -79 20.5 -124.5t55 -64.5t77.5 -19q44 0 79 19t55.5 64.5t20.5 124.5v387h80v-385q0 -105 -31 -167t-84 -89t-120 -27t-120.5 27t-84.5 89t-31 167zM187 812l47 55l142 -128l-37 -42z" />
<glyph unicode="&#xda;" horiz-adv-x="645" d="M87 271v385h83v-387q0 -79 20.5 -124.5t55 -64.5t77.5 -19q44 0 79 19t55.5 64.5t20.5 124.5v387h80v-385q0 -105 -31 -167t-84 -89t-120 -27t-120.5 27t-84.5 89t-31 167zM268 739l142 128l47 -55l-152 -115z" />
<glyph unicode="&#xdb;" horiz-adv-x="645" d="M87 271v385h83v-387q0 -79 20.5 -124.5t55 -64.5t77.5 -19q44 0 79 19t55.5 64.5t20.5 124.5v387h80v-385q0 -105 -31 -167t-84 -89t-120 -27t-120.5 27t-84.5 89t-31 167zM174 725l105 113h86l105 -113l-36 -26l-110 93h-4l-110 -93z" />
<glyph unicode="&#xdc;" horiz-adv-x="645" d="M87 271v385h83v-387q0 -79 20.5 -124.5t55 -64.5t77.5 -19q44 0 79 19t55.5 64.5t20.5 124.5v387h80v-385q0 -105 -31 -167t-84 -89t-120 -27t-120.5 27t-84.5 89t-31 167zM170 764q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14 q-21 0 -35 14t-14 35zM376 764q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5t14 -35.5q0 -21 -14 -35t-35 -14q-22 0 -35.5 14t-13.5 35z" />
<glyph unicode="&#xdd;" horiz-adv-x="476" d="M-1 656h89l85 -185q16 -36 31.5 -71t31.5 -72h4q17 37 34 72t32 71l84 185h87l-197 -402v-254h-84v254zM184 739l142 128l47 -55l-152 -115z" />
<glyph unicode="&#xde;" horiz-adv-x="583" d="M90 0v656h83v-110h118q73 0 127 -18.5t84.5 -60.5t30.5 -114q0 -104 -67 -153.5t-175 -49.5h-118v-150h-83zM173 218h108q86 0 127.5 32.5t41.5 102.5q0 71 -42 98t-127 27h-108v-260z" />
<glyph unicode="&#xdf;" horiz-adv-x="576" d="M82 0v515q0 94 51 150.5t145 56.5q53 0 89 -20t55 -53t19 -73q0 -37 -13.5 -64t-32 -48.5t-32 -42.5t-13.5 -47q0 -29 20 -46.5t49.5 -32t58.5 -33t49 -47.5t20 -78q0 -63 -43 -106t-117 -43q-41 0 -75.5 13.5t-65.5 35.5l33 58q52 -43 106 -43q41 0 62.5 24.5t21.5 55.5 q0 35 -20 56t-49 36t-58.5 31.5t-49.5 41.5t-20 67q0 34 13.5 58.5t30.5 46t30.5 45t13.5 54.5q0 38 -21 62.5t-62 24.5q-53 0 -83 -39t-30 -116v-500h-82z" />
<glyph unicode="&#xe0;" horiz-adv-x="504" d="M52 126q0 80 71.5 122.5t226.5 59.5q0 31 -8.5 59t-30 45.5t-61.5 17.5q-42 0 -79 -16t-66 -36l-32 57q34 22 83.5 42.5t107.5 20.5q89 0 129 -54.5t40 -145.5v-298h-68l-7 58h-3q-34 -28 -74.5 -49t-86.5 -21q-61 0 -101.5 36.5t-40.5 101.5zM124 726l58 55l141 -167 l-42 -41zM132 132q0 -41 25 -59.5t61 -18.5q35 0 66 17t66 48v135q-121 -15 -169.5 -45t-48.5 -77z" />
<glyph unicode="&#xe1;" horiz-adv-x="504" d="M52 126q0 80 71.5 122.5t226.5 59.5q0 31 -8.5 59t-30 45.5t-61.5 17.5q-42 0 -79 -16t-66 -36l-32 57q34 22 83.5 42.5t107.5 20.5q89 0 129 -54.5t40 -145.5v-298h-68l-7 58h-3q-34 -28 -74.5 -49t-86.5 -21q-61 0 -101.5 36.5t-40.5 101.5zM132 132q0 -41 25 -59.5 t61 -18.5q35 0 66 17t66 48v135q-121 -15 -169.5 -45t-48.5 -77zM211 614l141 167l58 -55l-157 -153z" />
<glyph unicode="&#xe2;" horiz-adv-x="504" d="M52 126q0 80 71.5 122.5t226.5 59.5q0 31 -8.5 59t-30 45.5t-61.5 17.5q-42 0 -79 -16t-66 -36l-32 57q34 22 83.5 42.5t107.5 20.5q89 0 129 -54.5t40 -145.5v-298h-68l-7 58h-3q-34 -28 -74.5 -49t-86.5 -21q-61 0 -101.5 36.5t-40.5 101.5zM112 600l114 140h82 l114 -140l-35 -32l-118 113h-4l-118 -113zM132 132q0 -41 25 -59.5t61 -18.5q35 0 66 17t66 48v135q-121 -15 -169.5 -45t-48.5 -77z" />
<glyph unicode="&#xe3;" horiz-adv-x="504" d="M52 126q0 80 71.5 122.5t226.5 59.5q0 31 -8.5 59t-30 45.5t-61.5 17.5q-42 0 -79 -16t-66 -36l-32 57q34 22 83.5 42.5t107.5 20.5q89 0 129 -54.5t40 -145.5v-298h-68l-7 58h-3q-34 -28 -74.5 -49t-86.5 -21q-61 0 -101.5 36.5t-40.5 101.5zM94 585q2 59 25.5 97.5 t75.5 38.5q27 0 46 -12.5t35 -29.5t30.5 -29.5t32.5 -12.5q22 0 33 22.5t13 56.5l55 -4q-2 -58 -25.5 -96.5t-75.5 -38.5q-27 0 -46 12.5t-34.5 29.5t-30.5 29.5t-32 12.5q-22 0 -33.5 -22t-13.5 -57zM132 132q0 -41 25 -59.5t61 -18.5q35 0 66 17t66 48v135 q-121 -15 -169.5 -45t-48.5 -77z" />
<glyph unicode="&#xe4;" horiz-adv-x="504" d="M52 126q0 80 71.5 122.5t226.5 59.5q0 31 -8.5 59t-30 45.5t-61.5 17.5q-42 0 -79 -16t-66 -36l-32 57q34 22 83.5 42.5t107.5 20.5q89 0 129 -54.5t40 -145.5v-298h-68l-7 58h-3q-34 -28 -74.5 -49t-86.5 -21q-61 0 -101.5 36.5t-40.5 101.5zM121 637q0 21 14 35.5 t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5q-22 0 -36 14.5t-14 35.5zM132 132q0 -41 25 -59.5t61 -18.5q35 0 66 17t66 48v135q-121 -15 -169.5 -45t-48.5 -77zM314 637q0 21 14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5q-21 0 -35 14.5 t-14 35.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="504" d="M52 126q0 80 71.5 122.5t226.5 59.5q0 31 -8.5 59t-30 45.5t-61.5 17.5q-42 0 -79 -16t-66 -36l-32 57q34 22 83.5 42.5t107.5 20.5q89 0 129 -54.5t40 -145.5v-298h-68l-7 58h-3q-34 -28 -74.5 -49t-86.5 -21q-61 0 -101.5 36.5t-40.5 101.5zM132 132q0 -41 25 -59.5 t61 -18.5q35 0 66 17t66 48v135q-121 -15 -169.5 -45t-48.5 -77zM157 653q0 42 30 70t80 28t80 -28t30 -70q0 -43 -30 -71t-80 -28t-80 28t-30 71zM210 653q0 -28 16.5 -45t40.5 -17t40.5 17t16.5 45t-16.5 44.5t-40.5 16.5t-40.5 -16.5t-16.5 -44.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="785" d="M58 126q0 80 71.5 122.5t222.5 59.5q0 31 -8 59t-29.5 45.5t-61.5 17.5t-76 -16t-65 -36l-33 57q34 22 82 42.5t102 20.5t89 -28t50 -75q29 47 70 75t90 28q89 0 137 -62.5t48 -166.5q0 -28 -3 -46h-315q3 -76 46 -122t105 -46q35 0 63 11.5t55 28.5l30 -57 q-31 -20 -69.5 -35t-88.5 -15q-61 0 -102 27.5t-69 63.5q-102 -91 -198 -91q-61 0 -102 36.5t-41 101.5zM139 132q0 -41 24.5 -59.5t60.5 -18.5q34 0 74 19.5t73 55.5q-8 19 -13 45.5t-6 54.5v25q-115 -15 -164 -45t-49 -77zM429 278h246q0 75 -29.5 115t-85.5 40 q-50 0 -87 -42t-44 -113z" />
<glyph unicode="&#xe7;" horiz-adv-x="456" d="M46 242q0 82 32.5 139t86 87t115.5 30q48 0 82.5 -17t59.5 -40l-42 -54q-21 19 -44.5 31t-52.5 12q-44 0 -78.5 -23.5t-54 -66t-19.5 -98.5q0 -83 41.5 -134.5t108.5 -51.5q34 0 63 14.5t51 34.5l36 -55q-33 -29 -73 -45.5t-84 -16.5q-64 0 -116 30t-82 87t-30 137z M171 -186q63 5 86 16.5t23 33.5q0 20 -17 31t-60 17l44 91h53l-29 -67q35 -8 54 -24t19 -47q0 -43 -42.5 -65t-122.5 -26z" />
<glyph unicode="&#xe8;" horiz-adv-x="496" d="M46 242q0 80 31.5 137t81.5 88t105 31q93 0 143.5 -62t50.5 -166q0 -13 -0.5 -25.5t-2.5 -21.5h-328q5 -77 48.5 -123t113.5 -46q35 0 64.5 10.5t56.5 27.5l29 -54q-31 -20 -70.5 -35t-89.5 -15q-65 0 -118 30.5t-84 87.5t-31 136zM121 726l58 55l141 -167l-42 -41z M126 282h260q0 74 -31.5 112.5t-88.5 38.5q-51 0 -91.5 -39.5t-48.5 -111.5z" />
<glyph unicode="&#xe9;" horiz-adv-x="496" d="M46 242q0 80 31.5 137t81.5 88t105 31q93 0 143.5 -62t50.5 -166q0 -13 -0.5 -25.5t-2.5 -21.5h-328q5 -77 48.5 -123t113.5 -46q35 0 64.5 10.5t56.5 27.5l29 -54q-31 -20 -70.5 -35t-89.5 -15q-65 0 -118 30.5t-84 87.5t-31 136zM126 282h260q0 74 -31.5 112.5 t-88.5 38.5q-51 0 -91.5 -39.5t-48.5 -111.5zM208 614l141 167l58 -55l-157 -153z" />
<glyph unicode="&#xea;" horiz-adv-x="496" d="M46 242q0 80 31.5 137t81.5 88t105 31q93 0 143.5 -62t50.5 -166q0 -13 -0.5 -25.5t-2.5 -21.5h-328q5 -77 48.5 -123t113.5 -46q35 0 64.5 10.5t56.5 27.5l29 -54q-31 -20 -70.5 -35t-89.5 -15q-65 0 -118 30.5t-84 87.5t-31 136zM109 600l114 140h82l114 -140l-35 -32 l-118 113h-4l-118 -113zM126 282h260q0 74 -31.5 112.5t-88.5 38.5q-51 0 -91.5 -39.5t-48.5 -111.5z" />
<glyph unicode="&#xeb;" horiz-adv-x="496" d="M46 242q0 80 31.5 137t81.5 88t105 31q93 0 143.5 -62t50.5 -166q0 -13 -0.5 -25.5t-2.5 -21.5h-328q5 -77 48.5 -123t113.5 -46q35 0 64.5 10.5t56.5 27.5l29 -54q-31 -20 -70.5 -35t-89.5 -15q-65 0 -118 30.5t-84 87.5t-31 136zM118 637q0 21 14 35.5t36 14.5 q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5q-22 0 -36 14.5t-14 35.5zM126 282h260q0 74 -31.5 112.5t-88.5 38.5q-51 0 -91.5 -39.5t-48.5 -111.5zM311 637q0 21 14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5q-21 0 -35 14.5t-14 35.5z" />
<glyph unicode="&#xec;" horiz-adv-x="246" d="M-19 726l58 55l141 -167l-42 -41zM82 0v486h82v-486h-82z" />
<glyph unicode="&#xed;" horiz-adv-x="246" d="M68 614l141 167l58 -55l-157 -153zM82 0v486h82v-486h-82z" />
<glyph unicode="&#xee;" horiz-adv-x="246" d="M-31 600l114 140h82l114 -140l-35 -32l-118 113h-4l-118 -113zM82 0v486h82v-486h-82z" />
<glyph unicode="&#xef;" horiz-adv-x="246" d="M-22 637q0 21 14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5q-22 0 -36 14.5t-14 35.5zM82 0v486h82v-486h-82zM171 637q0 21 14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5q-21 0 -35 14.5t-14 35.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="545" d="M53 222q0 68 27.5 118.5t74 78.5t104.5 28q38 0 73.5 -16t60.5 -50q-13 62 -41 108.5t-66 83.5l-141 -73l-24 41l127 65q-52 40 -112 72l38 52q36 -19 71 -41.5t67 -49.5l142 73l24 -41l-129 -66q60 -61 98 -145t38 -203q0 -80 -27 -140.5t-75.5 -94.5t-113.5 -34 q-56 0 -105.5 28.5t-80 81t-30.5 124.5zM131 222q0 -50 19 -87.5t51 -58t70 -20.5q68 0 101.5 55t33.5 143q0 14 -0.5 28t-1.5 27q-33 44 -66 59t-67 15q-67 0 -103.5 -46t-36.5 -115z" />
<glyph unicode="&#xf1;" horiz-adv-x="547" d="M82 0v486h68l7 -70h3q35 35 74 58.5t89 23.5q77 0 112.5 -48.5t35.5 -141.5v-308h-82v297q0 68 -22 99t-70 31q-37 0 -66.5 -19t-66.5 -56v-352h-82zM122 585q2 59 25.5 97.5t75.5 38.5q27 0 46 -12.5t35 -29.5t30.5 -29.5t32.5 -12.5q22 0 33 22.5t13 56.5l55 -4 q-2 -58 -25.5 -96.5t-75.5 -38.5q-27 0 -46 12.5t-34.5 29.5t-30.5 29.5t-32 12.5q-22 0 -33.5 -22t-13.5 -57z" />
<glyph unicode="&#xf2;" horiz-adv-x="542" d="M46 242q0 82 31.5 139t83 87t110.5 30q60 0 111 -30t82.5 -87t31.5 -139q0 -80 -31.5 -137t-82.5 -87t-111 -30q-59 0 -110.5 30t-83 87t-31.5 137zM128 726l58 55l141 -167l-42 -41zM131 242q0 -83 39 -134.5t101 -51.5q63 0 101.5 51.5t38.5 134.5q0 84 -38.5 136 t-101.5 52q-62 0 -101 -52t-39 -136z" />
<glyph unicode="&#xf3;" horiz-adv-x="542" d="M46 242q0 82 31.5 139t83 87t110.5 30q60 0 111 -30t82.5 -87t31.5 -139q0 -80 -31.5 -137t-82.5 -87t-111 -30q-59 0 -110.5 30t-83 87t-31.5 137zM131 242q0 -83 39 -134.5t101 -51.5q63 0 101.5 51.5t38.5 134.5q0 84 -38.5 136t-101.5 52q-62 0 -101 -52t-39 -136z M215 614l141 167l58 -55l-157 -153z" />
<glyph unicode="&#xf4;" horiz-adv-x="542" d="M46 242q0 82 31.5 139t83 87t110.5 30q60 0 111 -30t82.5 -87t31.5 -139q0 -80 -31.5 -137t-82.5 -87t-111 -30q-59 0 -110.5 30t-83 87t-31.5 137zM116 600l114 140h82l114 -140l-35 -32l-118 113h-4l-118 -113zM131 242q0 -83 39 -134.5t101 -51.5q63 0 101.5 51.5 t38.5 134.5q0 84 -38.5 136t-101.5 52q-62 0 -101 -52t-39 -136z" />
<glyph unicode="&#xf5;" horiz-adv-x="542" d="M46 242q0 82 31.5 139t83 87t110.5 30q60 0 111 -30t82.5 -87t31.5 -139q0 -80 -31.5 -137t-82.5 -87t-111 -30q-59 0 -110.5 30t-83 87t-31.5 137zM98 585q2 59 25.5 97.5t75.5 38.5q27 0 46 -12.5t35 -29.5t30.5 -29.5t32.5 -12.5q22 0 33 22.5t13 56.5l55 -4 q-2 -58 -25.5 -96.5t-75.5 -38.5q-27 0 -46 12.5t-34.5 29.5t-30.5 29.5t-32 12.5q-22 0 -33.5 -22t-13.5 -57zM131 242q0 -83 39 -134.5t101 -51.5q63 0 101.5 51.5t38.5 134.5q0 84 -38.5 136t-101.5 52q-62 0 -101 -52t-39 -136z" />
<glyph unicode="&#xf6;" horiz-adv-x="542" d="M46 242q0 82 31.5 139t83 87t110.5 30q60 0 111 -30t82.5 -87t31.5 -139q0 -80 -31.5 -137t-82.5 -87t-111 -30q-59 0 -110.5 30t-83 87t-31.5 137zM125 637q0 21 14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5q-22 0 -36 14.5t-14 35.5zM131 242 q0 -83 39 -134.5t101 -51.5q63 0 101.5 51.5t38.5 134.5q0 84 -38.5 136t-101.5 52q-62 0 -101 -52t-39 -136zM318 637q0 21 14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5q-21 0 -35 14.5t-14 35.5z" />
<glyph unicode="&#xf7;" d="M34 299v62h429v-62h-429zM194 150q0 23 16 38t39 15t38.5 -15t15.5 -38q0 -24 -15.5 -39t-38.5 -15t-39 15t-16 39zM194 510q0 23 16 38t39 15t38.5 -15t15.5 -38q0 -24 -15.5 -39t-38.5 -15t-39 15t-16 39z" />
<glyph unicode="&#xf8;" horiz-adv-x="542" d="M46 6l54 65q-25 32 -39.5 75t-14.5 96q0 82 31.5 139t83 87t110.5 30q80 0 139 -50l50 61l36 -29l-54 -65q25 -32 39.5 -75.5t14.5 -97.5q0 -80 -31.5 -137t-82.5 -87t-111 -30q-79 0 -139 49l-49 -60zM127 246q0 -67 23 -113l214 259q-37 40 -93 40q-41 0 -74 -23.5 t-51.5 -65.5t-18.5 -97zM178 93q39 -39 93 -39q63 0 103.5 51.5t40.5 133.5q0 68 -24 114z" />
<glyph unicode="&#xf9;" horiz-adv-x="544" d="M75 178v308h83v-297q0 -68 21.5 -99t69.5 -31q38 0 67.5 19.5t63.5 62.5v345h82v-486h-68l-7 76h-3q-34 -40 -72 -64t-88 -24q-77 0 -113 48.5t-36 141.5zM128 726l58 55l141 -167l-42 -41z" />
<glyph unicode="&#xfa;" horiz-adv-x="544" d="M75 178v308h83v-297q0 -68 21.5 -99t69.5 -31q38 0 67.5 19.5t63.5 62.5v345h82v-486h-68l-7 76h-3q-34 -40 -72 -64t-88 -24q-77 0 -113 48.5t-36 141.5zM215 614l141 167l58 -55l-157 -153z" />
<glyph unicode="&#xfb;" horiz-adv-x="544" d="M75 178v308h83v-297q0 -68 21.5 -99t69.5 -31q38 0 67.5 19.5t63.5 62.5v345h82v-486h-68l-7 76h-3q-34 -40 -72 -64t-88 -24q-77 0 -113 48.5t-36 141.5zM116 600l114 140h82l114 -140l-35 -32l-118 113h-4l-118 -113z" />
<glyph unicode="&#xfc;" horiz-adv-x="544" d="M75 178v308h83v-297q0 -68 21.5 -99t69.5 -31q38 0 67.5 19.5t63.5 62.5v345h82v-486h-68l-7 76h-3q-34 -40 -72 -64t-88 -24q-77 0 -113 48.5t-36 141.5zM125 637q0 21 14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5q-22 0 -36 14.5t-14 35.5zM318 637 q0 21 14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5q-21 0 -35 14.5t-14 35.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="467" d="M12 486h85l99 -269q11 -31 23.5 -67t23.5 -70h4q11 33 21 69t20 68l87 269h80l-183 -526q-17 -48 -40.5 -86t-58 -60.5t-83.5 -22.5q-16 0 -30.5 3t-26.5 7l16 65l18 -4.5t19 -2.5q41 0 68 29.5t42 74.5l11 36zM187 614l141 167l58 -55l-157 -153z" />
<glyph unicode="&#xfe;" horiz-adv-x="555" d="M82 -205v917h82v-193l-1 -84q32 26 70 44.5t78 18.5q96 0 146 -67.5t50 -180.5q0 -82 -29.5 -141t-77 -90t-103.5 -31q-35 0 -68 14.5t-66 39.5l1 -83v-164h-82zM164 108q33 -28 64 -39.5t55 -11.5q60 0 99.5 51.5t39.5 141.5q0 80 -29.5 129.5t-98.5 49.5 q-31 0 -62.5 -17t-67.5 -49v-255z" />
<glyph unicode="&#xff;" horiz-adv-x="467" d="M12 486h85l99 -269q11 -31 23.5 -67t23.5 -70h4q11 33 21 69t20 68l87 269h80l-183 -526q-17 -48 -40.5 -86t-58 -60.5t-83.5 -22.5q-16 0 -30.5 3t-26.5 7l16 65l18 -4.5t19 -2.5q41 0 68 29.5t42 74.5l11 36zM97 637q0 21 14 35.5t36 14.5q21 0 35 -14.5t14 -35.5 t-14 -35.5t-35 -14.5q-22 0 -36 14.5t-14 35.5zM290 637q0 21 14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5q-21 0 -35 14.5t-14 35.5z" />
<glyph unicode="&#x152;" horiz-adv-x="847" d="M52 331q0 156 84.5 240.5t236.5 84.5h415v-70h-278v-206h232v-71h-232v-238h288v-71h-429q-149 0 -233 87t-84 244zM138 331q0 -125 61.5 -194t179.5 -69h48v520h-48q-118 0 -179.5 -66t-61.5 -191z" />
<glyph unicode="&#x153;" horiz-adv-x="839" d="M46 242q0 82 30.5 139t80.5 87t109 30q56 0 103 -31.5t73 -90.5q28 57 73 89.5t98 32.5q90 0 139 -62.5t49 -166.5q0 -28 -3 -46h-319q2 -50 23 -88t55 -59t75 -21q35 0 64 11.5t56 28.5l30 -57q-31 -20 -70.5 -35t-89.5 -15q-57 0 -104 32.5t-75 88.5 q-57 -121 -179 -121q-58 0 -108 30t-80 87t-30 137zM130 242q0 -83 38 -134.5t98 -51.5t98 51.5t38 134.5q0 84 -38 136t-98 52t-98 -52t-38 -136zM479 278h250q0 75 -31 115t-87 40q-50 0 -87.5 -42t-44.5 -113z" />
<glyph unicode="&#x178;" horiz-adv-x="476" d="M-1 656h89l85 -185q16 -36 31.5 -71t31.5 -72h4q17 37 34 72t32 71l84 185h87l-197 -402v-254h-84v254zM86 764q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14q-21 0 -35 14t-14 35zM292 764q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5 t14 -35.5q0 -21 -14 -35t-35 -14q-22 0 -35.5 14t-13.5 35z" />
<glyph unicode="&#x2c6;" horiz-adv-x="542" d="M116 600l114 140h82l114 -140l-35 -32l-118 113h-4l-118 -113z" />
<glyph unicode="&#x2dc;" horiz-adv-x="542" d="M98 585q2 59 25.5 97.5t75.5 38.5q27 0 46 -12.5t35 -29.5t30.5 -29.5t32.5 -12.5q22 0 33 22.5t13 56.5l55 -4q-2 -58 -25.5 -96.5t-75.5 -38.5q-27 0 -46 12.5t-34.5 29.5t-30.5 29.5t-32 12.5q-22 0 -33.5 -22t-13.5 -57z" />
<glyph unicode="&#x2000;" horiz-adv-x="440" />
<glyph unicode="&#x2001;" horiz-adv-x="880" />
<glyph unicode="&#x2002;" horiz-adv-x="440" />
<glyph unicode="&#x2003;" horiz-adv-x="880" />
<glyph unicode="&#x2004;" horiz-adv-x="293" />
<glyph unicode="&#x2005;" horiz-adv-x="220" />
<glyph unicode="&#x2006;" horiz-adv-x="146" />
<glyph unicode="&#x2007;" horiz-adv-x="146" />
<glyph unicode="&#x2008;" horiz-adv-x="110" />
<glyph unicode="&#x2009;" horiz-adv-x="176" />
<glyph unicode="&#x200a;" horiz-adv-x="48" />
<glyph unicode="&#x2010;" horiz-adv-x="311" d="M41 219v63h230v-63h-230z" />
<glyph unicode="&#x2011;" horiz-adv-x="311" d="M41 219v63h230v-63h-230z" />
<glyph unicode="&#x2012;" horiz-adv-x="311" d="M41 219v63h230v-63h-230z" />
<glyph unicode="&#x2013;" horiz-adv-x="480" d="M41 223v57h398v-57h-398z" />
<glyph unicode="&#x2014;" horiz-adv-x="800" d="M41 223v57h718v-57h-718z" />
<glyph unicode="&#x2018;" horiz-adv-x="249" d="M57 518q0 60 27 104t79 74l24 -39q-38 -25 -57 -53.5t-19 -70.5q3 1 9 1q20 0 36.5 -13t16.5 -38t-15 -40t-38 -15q-30 0 -46.5 23.5t-16.5 66.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="249" d="M63 469q38 25 56.5 53.5t18.5 71.5q-3 -1 -8 -1q-21 0 -37 13t-16 37q0 26 15 41t38 15q29 0 46 -23.5t17 -67.5q0 -60 -27.5 -103.5t-79.5 -73.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="249" d="M63 -107q38 25 56.5 53.5t18.5 71.5q-3 -1 -8 -1q-21 0 -37 13t-16 37q0 26 15 41t38 15q29 0 46 -23.5t17 -67.5q0 -60 -27.5 -103.5t-79.5 -73.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="426" d="M57 518q0 60 27 104t79 74l24 -39q-38 -25 -57 -53.5t-19 -70.5q3 1 9 1q20 0 36.5 -13t16.5 -38t-15 -40t-38 -15q-30 0 -46.5 23.5t-16.5 66.5zM234 518q0 60 27 104t79 74l24 -39q-38 -25 -57 -53.5t-19 -70.5q3 1 9 1q20 0 36.5 -13t16.5 -38t-15 -40t-38 -15 q-30 0 -46.5 23.5t-16.5 66.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="426" d="M63 469q38 25 56.5 53.5t18.5 71.5q-3 -1 -8 -1q-21 0 -37 13t-16 37q0 26 15 41t38 15q29 0 46 -23.5t17 -67.5q0 -60 -27.5 -103.5t-79.5 -73.5zM240 469q38 25 56.5 53.5t18.5 71.5q-3 -1 -8 -1q-21 0 -37 13t-16 37q0 26 15 41t38 15q29 0 46 -23.5t17 -67.5 q0 -60 -27.5 -103.5t-79.5 -73.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="426" d="M63 -107q38 25 56.5 53.5t18.5 71.5q-3 -1 -8 -1q-21 0 -37 13t-16 37q0 26 15 41t38 15q29 0 46 -23.5t17 -67.5q0 -60 -27.5 -103.5t-79.5 -73.5zM240 -107q38 25 56.5 53.5t18.5 71.5q-3 -1 -8 -1q-21 0 -37 13t-16 37q0 26 15 41t38 15q29 0 46 -23.5t17 -67.5 q0 -60 -27.5 -103.5t-79.5 -73.5z" />
<glyph unicode="&#x2022;" horiz-adv-x="304" d="M40 263q0 54 33.5 87.5t78.5 33.5t78.5 -33.5t33.5 -87.5t-33.5 -87t-78.5 -33t-78.5 33t-33.5 87z" />
<glyph unicode="&#x2026;" horiz-adv-x="948" d="M94 50q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5q-25 0 -42.5 17.5t-17.5 44.5zM429 50q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5q-25 0 -42.5 17.5t-17.5 44.5zM764 50 q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5q-25 0 -42.5 17.5t-17.5 44.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="176" />
<glyph unicode="&#x2039;" horiz-adv-x="271" d="M45 221v62l136 155l36 -30l-118 -156l118 -158l-36 -28z" />
<glyph unicode="&#x203a;" horiz-adv-x="271" d="M54 94l118 158l-118 156l35 30l137 -155v-62l-137 -155z" />
<glyph unicode="&#x205f;" horiz-adv-x="220" />
<glyph unicode="&#x20ac;" d="M23 235v43l59 4q-1 9 -1 18v18v16.5t1 15.5h-59v44l64 5q17 118 82.5 184.5t163.5 66.5q45 0 84 -22.5t65 -55.5l-49 -47q-21 26 -46 42.5t-57 16.5q-66 0 -106 -49.5t-53 -135.5h254v-49h-258q-1 -7 -1 -14v-15v-19.5t1 -18.5h218v-48h-213q13 -85 52 -133t100 -48 q37 0 65 18.5t53 52.5l49 -44q-33 -43 -75 -68t-97 -25q-90 0 -152.5 65t-79.5 182h-64z" />
<glyph unicode="&#x2122;" horiz-adv-x="637" d="M3 622v54h259v-54h-100v-256h-60v256h-99zM310 366v310h73l47 -116l28 -78h4l28 78l46 116h72v-310h-55v137l7 105h-4l-73 -194h-47l-73 194h-4l7 -105v-137h-56z" />
<glyph unicode="&#xfb01;" horiz-adv-x="538" d="M30 419v62l66 5v77q0 74 34.5 117.5t107.5 43.5q23 0 44 -4.5t37 -11.5l-18 -63q-27 12 -55 12q-68 0 -68 -94v-77h103v-67h-103v-419h-82v419h-66zM361 640q0 24 15.5 39t39.5 15t39.5 -15t15.5 -39q0 -25 -15.5 -39.5t-39.5 -14.5t-39.5 14.5t-15.5 39.5zM374 0v486h82 v-486h-82z" />
<glyph unicode="&#xfb02;" horiz-adv-x="547" d="M30 419v62l66 5v77q0 74 34.5 117.5t107.5 43.5q23 0 44 -4.5t37 -11.5l-18 -63q-27 12 -55 12q-68 0 -68 -94v-77h103v-67h-103v-419h-82v419h-66zM374 98v614h82v-620q0 -19 7 -27.5t16 -8.5q4 0 7.5 0.5l10.5 1.5l11 -62q-8 -4 -19 -6t-28 -2q-46 0 -66.5 28.5 t-20.5 81.5z" />
<glyph unicode="&#xfb03;" horiz-adv-x="823" d="M30 419v62l66 5v64q0 76 37.5 120t113.5 44q50 0 89 -18l-17 -62q-30 13 -65 13q-36 0 -56 -25.5t-20 -73.5v-62h203v77q0 74 34.5 117.5t107.5 43.5q23 0 44 -4.5t37 -11.5l-18 -63q-27 12 -55 12q-68 0 -68 -94v-77h103v-67h-103v-419h-82v419h-203v-419h-82v419h-66z M646 640q0 24 15.5 39t39.5 15t39.5 -15t15.5 -39q0 -25 -15.5 -39.5t-39.5 -14.5t-39.5 14.5t-15.5 39.5zM659 0v486h82v-486h-82z" />
<glyph unicode="&#xfb04;" horiz-adv-x="832" d="M30 419v62l66 5v64q0 76 37.5 120t113.5 44q50 0 89 -18l-17 -62q-30 13 -65 13q-36 0 -56 -25.5t-20 -73.5v-62h203v77q0 74 34.5 117.5t107.5 43.5q23 0 44 -4.5t37 -11.5l-18 -63q-27 12 -55 12q-68 0 -68 -94v-77h103v-67h-103v-419h-82v419h-203v-419h-82v419h-66z M659 98v614h82v-620q0 -19 7 -27.5t16 -8.5q4 0 7.5 0.5l10.5 1.5l11 -62q-8 -4 -19 -6t-28 -2q-46 0 -66.5 28.5t-20.5 81.5z" />
<hkern u1="&#x2f;" u2="&#xef;" k="-65" />
<hkern u1="&#x2f;" u2="&#xee;" k="-65" />
<hkern u1="&#x2f;" u2="&#xec;" k="-40" />
<hkern u1="F" u2="&#xef;" k="-36" />
<hkern u1="F" u2="&#xee;" k="-29" />
<hkern u1="V" u2="&#xef;" k="-64" />
<hkern u1="V" u2="&#xee;" k="-53" />
<hkern u1="V" u2="&#xed;" k="-13" />
<hkern u1="V" u2="&#xec;" k="-20" />
<hkern u1="f" u2="&#xef;" k="-60" />
<hkern u1="f" u2="&#xee;" k="-60" />
<hkern u1="f" u2="&#xec;" k="-60" />
<hkern u1="x" u2="&#x3b;" k="-7" />
<hkern u1="x" u2="&#x2c;" k="-7" />
<hkern g1="backslash" 	g2="g" 	k="-33" />
<hkern g1="backslash" 	g2="v" 	k="20" />
<hkern g1="backslash" 	g2="w" 	k="10" />
<hkern g1="backslash" 	g2="y,yacute,ydieresis" 	k="-13" />
<hkern g1="backslash" 	g2="Eth" 	k="29" />
<hkern g1="backslash" 	g2="j" 	k="-73" />
<hkern g1="backslash" 	g2="T" 	k="85" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="29" />
<hkern g1="backslash" 	g2="V" 	k="53" />
<hkern g1="backslash" 	g2="W" 	k="29" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="73" />
<hkern g1="exclamdown" 	g2="j" 	k="-33" />
<hkern g1="exclamdown" 	g2="V" 	k="32" />
<hkern g1="exclamdown" 	g2="W" 	k="16" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="45" />
<hkern g1="periodcentered" 	g2="T" 	k="64" />
<hkern g1="periodcentered" 	g2="V" 	k="26" />
<hkern g1="periodcentered" 	g2="Y,Yacute,Ydieresis" 	k="58" />
<hkern g1="periodcentered" 	g2="x" 	k="20" />
<hkern g1="periodcentered" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="24" />
<hkern g1="periodcentered" 	g2="S" 	k="24" />
<hkern g1="periodcentered" 	g2="X" 	k="26" />
<hkern g1="periodcentered" 	g2="Z" 	k="38" />
<hkern g1="questiondown" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="62" />
<hkern g1="questiondown" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="21" />
<hkern g1="questiondown" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="44" />
<hkern g1="questiondown" 	g2="j" 	k="-59" />
<hkern g1="questiondown" 	g2="T" 	k="87" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="40" />
<hkern g1="questiondown" 	g2="V" 	k="58" />
<hkern g1="questiondown" 	g2="W" 	k="40" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="89" />
<hkern g1="questiondown" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="35" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="62" />
<hkern g1="questiondown" 	g2="S" 	k="35" />
<hkern g1="questiondown" 	g2="X" 	k="42" />
<hkern g1="slash" 	g2="g" 	k="10" />
<hkern g1="slash" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="slash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="slash" 	g2="t" 	k="-9" />
<hkern g1="slash" 	g2="j" 	k="-25" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="24" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="40" />
<hkern g1="slash" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="-25" />
<hkern g1="slash" 	g2="J" 	k="80" />
<hkern g1="mu" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-7" />
<hkern g1="mu" 	g2="quoteright,quotedblright" 	k="27" />
<hkern g1="mu" 	g2="colon,semicolon" 	k="-7" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="question" 	k="12" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="t" 	k="14" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="T" 	k="24" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="V" 	k="16" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="W" 	k="4" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="Y,Yacute,Ydieresis" 	k="24" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="asterisk" 	k="54" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="67" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="56" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="55" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="periodcentered" 	k="24" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="26" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="55" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk" 	k="94" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="6" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="-23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="6" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="15" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="7" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="x" 	k="4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="-4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="7" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Z" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="40" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="registered" 	k="80" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="52" />
<hkern g1="B" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="B" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="B" 	g2="periodcentered" 	k="20" />
<hkern g1="B" 	g2="t" 	k="10" />
<hkern g1="B" 	g2="T" 	k="24" />
<hkern g1="B" 	g2="V" 	k="6" />
<hkern g1="B" 	g2="W" 	k="4" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="B" 	g2="asterisk" 	k="30" />
<hkern g1="B" 	g2="v" 	k="14" />
<hkern g1="B" 	g2="w" 	k="10" />
<hkern g1="B" 	g2="x" 	k="10" />
<hkern g1="B" 	g2="X" 	k="4" />
<hkern g1="B" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="B" 	g2="Z" 	k="4" />
<hkern g1="B" 	g2="trademark" 	k="20" />
<hkern g1="B" 	g2="J" 	k="16" />
<hkern g1="B" 	g2="S" 	k="14" />
<hkern g1="c,ccedilla" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="c,ccedilla" 	g2="periodcentered" 	k="14" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="c,ccedilla" 	g2="t" 	k="10" />
<hkern g1="c,ccedilla" 	g2="T" 	k="20" />
<hkern g1="c,ccedilla" 	g2="V" 	k="14" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="24" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="c,ccedilla" 	g2="v" 	k="-6" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-6" />
<hkern g1="c,ccedilla" 	g2="x" 	k="-8" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="-6" />
<hkern g1="c,ccedilla" 	g2="registered" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="g" 	k="10" />
<hkern g1="c,ccedilla" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="21" />
<hkern g1="C,Ccedilla" 	g2="hyphen,endash,emdash" 	k="26" />
<hkern g1="C,Ccedilla" 	g2="periodcentered" 	k="52" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="4" />
<hkern g1="C,Ccedilla" 	g2="W" 	k="4" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="7" />
<hkern g1="C,Ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="v" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="4" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="registered" 	k="-10" />
<hkern g1="C,Ccedilla" 	g2="trademark" 	k="-24" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="S" 	k="24" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="hyphen,endash,emdash" 	k="-10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="periodcentered" 	k="4" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="7" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="24" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="26" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk" 	k="34" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="-5" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="-5" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="7" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="-5" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="17" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="J" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="S" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="g" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="24" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="x" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="f" 	g2="hyphen,endash,emdash" 	k="14" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-34" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-34" />
<hkern g1="f" 	g2="quotedbl,quotesingle" 	k="-40" />
<hkern g1="f" 	g2="periodcentered" 	k="20" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="50" />
<hkern g1="f" 	g2="question" 	k="-26" />
<hkern g1="f" 	g2="slash" 	k="14" />
<hkern g1="f" 	g2="parenright,bracketright,braceright" 	k="-49" />
<hkern g1="f" 	g2="T" 	k="-46" />
<hkern g1="f" 	g2="V" 	k="-66" />
<hkern g1="f" 	g2="W" 	k="-46" />
<hkern g1="f" 	g2="Y,Yacute,Ydieresis" 	k="-59" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="f" 	g2="s" 	k="4" />
<hkern g1="f" 	g2="v" 	k="-13" />
<hkern g1="f" 	g2="x" 	k="4" />
<hkern g1="f" 	g2="X" 	k="-33" />
<hkern g1="f" 	g2="backslash" 	k="-62" />
<hkern g1="f" 	g2="registered" 	k="-75" />
<hkern g1="f" 	g2="trademark" 	k="-76" />
<hkern g1="f" 	g2="g" 	k="14" />
<hkern g1="f" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="f" 	g2="j" 	k="10" />
<hkern g1="f" 	g2="z" 	k="14" />
<hkern g1="f" 	g2="exclam" 	k="-14" />
<hkern g1="F" 	g2="periodcentered" 	k="14" />
<hkern g1="F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="72" />
<hkern g1="F" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="F" 	g2="slash" 	k="69" />
<hkern g1="F" 	g2="t" 	k="10" />
<hkern g1="F" 	g2="V" 	k="-4" />
<hkern g1="F" 	g2="W" 	k="-4" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="34" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="37" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="F" 	g2="s" 	k="20" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="F" 	g2="v" 	k="20" />
<hkern g1="F" 	g2="w" 	k="16" />
<hkern g1="F" 	g2="x" 	k="26" />
<hkern g1="F" 	g2="X" 	k="24" />
<hkern g1="F" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="F" 	g2="Z" 	k="30" />
<hkern g1="F" 	g2="registered" 	k="-20" />
<hkern g1="F" 	g2="trademark" 	k="-38" />
<hkern g1="F" 	g2="J" 	k="138" />
<hkern g1="F" 	g2="S" 	k="20" />
<hkern g1="F" 	g2="g" 	k="24" />
<hkern g1="F" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="F" 	g2="z" 	k="30" />
<hkern g1="F" 	g2="m,n,p,r,ntilde" 	k="20" />
<hkern g1="germandbls" 	g2="quoteleft,quotedblleft" 	k="48" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="56" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="67" />
<hkern g1="germandbls" 	g2="question" 	k="16" />
<hkern g1="germandbls" 	g2="t" 	k="16" />
<hkern g1="germandbls" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="4" />
<hkern g1="germandbls" 	g2="v" 	k="16" />
<hkern g1="germandbls" 	g2="w" 	k="14" />
<hkern g1="germandbls" 	g2="x" 	k="-6" />
<hkern g1="germandbls" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="germandbls" 	g2="backslash" 	k="26" />
<hkern g1="germandbls" 	g2="registered" 	k="35" />
<hkern g1="g" 	g2="periodcentered" 	k="8" />
<hkern g1="g" 	g2="question" 	k="36" />
<hkern g1="g" 	g2="slash" 	k="-48" />
<hkern g1="g" 	g2="parenright,bracketright,braceright" 	k="-14" />
<hkern g1="g" 	g2="T" 	k="26" />
<hkern g1="g" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="g" 	g2="asterisk" 	k="33" />
<hkern g1="g" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="g" 	g2="v" 	k="4" />
<hkern g1="g" 	g2="w" 	k="4" />
<hkern g1="g" 	g2="y,yacute,ydieresis" 	k="-15" />
<hkern g1="g" 	g2="registered" 	k="-14" />
<hkern g1="g" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="g" 	g2="j" 	k="-37" />
<hkern g1="g" 	g2="z" 	k="14" />
<hkern g1="G" 	g2="T" 	k="20" />
<hkern g1="G" 	g2="V" 	k="14" />
<hkern g1="G" 	g2="W" 	k="4" />
<hkern g1="G" 	g2="asterisk" 	k="28" />
<hkern g1="G" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="4" />
<hkern g1="G" 	g2="registered" 	k="-8" />
<hkern g1="G" 	g2="trademark" 	k="-12" />
<hkern g1="J" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="24" />
<hkern g1="J" 	g2="J" 	k="40" />
<hkern g1="k" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-13" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="k" 	g2="g" 	k="10" />
<hkern g1="k" 	g2="j" 	k="10" />
<hkern g1="k" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="k" 	g2="t" 	k="21" />
<hkern g1="k" 	g2="T" 	k="35" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="k" 	g2="x" 	k="7" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="11" />
<hkern g1="k" 	g2="z" 	k="7" />
<hkern g1="k" 	g2="colon,semicolon" 	k="-13" />
<hkern g1="k" 	g2="hyphen,endash,emdash" 	k="44" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="26" />
<hkern g1="k" 	g2="guillemotright,guilsinglright" 	k="11" />
<hkern g1="k" 	g2="question" 	k="12" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="26" />
<hkern g1="k" 	g2="asterisk" 	k="21" />
<hkern g1="k" 	g2="periodcentered" 	k="26" />
<hkern g1="k" 	g2="trademark" 	k="20" />
<hkern g1="K" 	g2="j" 	k="10" />
<hkern g1="K" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="7" />
<hkern g1="K" 	g2="t" 	k="33" />
<hkern g1="K" 	g2="T" 	k="17" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="K" 	g2="x" 	k="16" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="K" 	g2="z" 	k="14" />
<hkern g1="K" 	g2="hyphen,endash,emdash" 	k="30" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="K" 	g2="guillemotright,guilsinglright" 	k="4" />
<hkern g1="K" 	g2="question" 	k="7" />
<hkern g1="K" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="K" 	g2="asterisk" 	k="40" />
<hkern g1="K" 	g2="periodcentered" 	k="46" />
<hkern g1="K" 	g2="trademark" 	k="-9" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="K" 	g2="S" 	k="9" />
<hkern g1="K" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="K" 	g2="v" 	k="20" />
<hkern g1="K" 	g2="V" 	k="12" />
<hkern g1="K" 	g2="w" 	k="16" />
<hkern g1="K" 	g2="W" 	k="10" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="K" 	g2="quotedbl,quotesingle" 	k="21" />
<hkern g1="l,uniFB02,uniFB04" 	g2="j" 	k="-8" />
<hkern g1="L" 	g2="g" 	k="4" />
<hkern g1="L" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="12" />
<hkern g1="L" 	g2="t" 	k="20" />
<hkern g1="L" 	g2="T" 	k="120" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="76" />
<hkern g1="L" 	g2="hyphen,endash,emdash" 	k="54" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="34" />
<hkern g1="L" 	g2="question" 	k="32" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="78" />
<hkern g1="L" 	g2="asterisk" 	k="152" />
<hkern g1="L" 	g2="periodcentered" 	k="92" />
<hkern g1="L" 	g2="trademark" 	k="98" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-3" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="26" />
<hkern g1="L" 	g2="S" 	k="22" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="29" />
<hkern g1="L" 	g2="v" 	k="36" />
<hkern g1="L" 	g2="V" 	k="76" />
<hkern g1="L" 	g2="w" 	k="34" />
<hkern g1="L" 	g2="W" 	k="56" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="36" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="78" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="89" />
<hkern g1="L" 	g2="backslash" 	k="80" />
<hkern g1="L" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="14" />
<hkern g1="L" 	g2="registered" 	k="92" />
<hkern g1="h,m,n,ntilde" 	g2="T" 	k="24" />
<hkern g1="h,m,n,ntilde" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="7" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="asterisk" 	k="34" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="V" 	k="10" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="t" 	k="17" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="57" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="x" 	k="17" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="Y,Yacute,Ydieresis" 	k="46" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="6" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="hyphen,endash,emdash" 	k="-6" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="question" 	k="12" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="26" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="asterisk" 	k="25" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v" 	k="4" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="V" 	k="19" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="w" 	k="4" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="y,yacute,ydieresis" 	k="4" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="29" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="backslash" 	k="26" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="registered" 	k="7" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="X" 	k="4" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="40" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="24" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="14" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="z" 	k="7" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="asterisk" 	k="34" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="26" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="10" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="6" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="18" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="registered" 	k="-6" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="17" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="20" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="112" />
<hkern g1="P" 	g2="J" 	k="146" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="43" />
<hkern g1="P" 	g2="g" 	k="30" />
<hkern g1="P" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="24" />
<hkern g1="P" 	g2="T" 	k="24" />
<hkern g1="P" 	g2="x" 	k="14" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="P" 	g2="z" 	k="20" />
<hkern g1="P" 	g2="hyphen,endash,emdash" 	k="33" />
<hkern g1="P" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="P" 	g2="periodcentered" 	k="8" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="50" />
<hkern g1="P" 	g2="S" 	k="10" />
<hkern g1="P" 	g2="slash" 	k="75" />
<hkern g1="P" 	g2="registered" 	k="-30" />
<hkern g1="P" 	g2="X" 	k="24" />
<hkern g1="P" 	g2="Z" 	k="78" />
<hkern g1="P" 	g2="s" 	k="10" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="57" />
<hkern g1="r" 	g2="J" 	k="55" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="24" />
<hkern g1="r" 	g2="g" 	k="10" />
<hkern g1="r" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="r" 	g2="z" 	k="4" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-25" />
<hkern g1="r" 	g2="hyphen,endash,emdash" 	k="24" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-18" />
<hkern g1="r" 	g2="periodcentered" 	k="16" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="r" 	g2="v" 	k="-25" />
<hkern g1="r" 	g2="w" 	k="-19" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-25" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-33" />
<hkern g1="r" 	g2="backslash" 	k="-13" />
<hkern g1="r" 	g2="slash" 	k="34" />
<hkern g1="r" 	g2="registered" 	k="-62" />
<hkern g1="r" 	g2="Z" 	k="10" />
<hkern g1="r" 	g2="s" 	k="4" />
<hkern g1="R" 	g2="J" 	k="18" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="4" />
<hkern g1="R" 	g2="g" 	k="4" />
<hkern g1="R" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="R" 	g2="T" 	k="14" />
<hkern g1="R" 	g2="x" 	k="7" />
<hkern g1="R" 	g2="z" 	k="7" />
<hkern g1="R" 	g2="hyphen,endash,emdash" 	k="31" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="30" />
<hkern g1="R" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="R" 	g2="asterisk" 	k="10" />
<hkern g1="R" 	g2="periodcentered" 	k="10" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="4" />
<hkern g1="R" 	g2="S" 	k="14" />
<hkern g1="R" 	g2="V" 	k="-5" />
<hkern g1="R" 	g2="W" 	k="-6" />
<hkern g1="R" 	g2="registered" 	k="-25" />
<hkern g1="R" 	g2="X" 	k="4" />
<hkern g1="R" 	g2="Z" 	k="10" />
<hkern g1="s" 	g2="t" 	k="20" />
<hkern g1="s" 	g2="T" 	k="24" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="s" 	g2="hyphen,endash,emdash" 	k="-10" />
<hkern g1="s" 	g2="question" 	k="11" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="11" />
<hkern g1="s" 	g2="asterisk" 	k="49" />
<hkern g1="s" 	g2="V" 	k="10" />
<hkern g1="S" 	g2="J" 	k="14" />
<hkern g1="S" 	g2="t" 	k="24" />
<hkern g1="S" 	g2="T" 	k="20" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="S" 	g2="hyphen,endash,emdash" 	k="-14" />
<hkern g1="S" 	g2="quoteright,quotedblright" 	k="11" />
<hkern g1="S" 	g2="asterisk" 	k="16" />
<hkern g1="S" 	g2="periodcentered" 	k="11" />
<hkern g1="S" 	g2="S" 	k="14" />
<hkern g1="S" 	g2="registered" 	k="-3" />
<hkern g1="Thorn" 	g2="asterisk" 	k="62" />
<hkern g1="Thorn" 	g2="trademark" 	k="24" />
<hkern g1="Thorn" 	g2="backslash" 	k="40" />
<hkern g1="Thorn" 	g2="slash" 	k="58" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-7" />
<hkern g1="t" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="21" />
<hkern g1="t" 	g2="g" 	k="10" />
<hkern g1="t" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="t" 	g2="t" 	k="20" />
<hkern g1="t" 	g2="T" 	k="14" />
<hkern g1="t" 	g2="x" 	k="16" />
<hkern g1="t" 	g2="Y,Yacute,Ydieresis" 	k="4" />
<hkern g1="t" 	g2="colon,semicolon" 	k="-14" />
<hkern g1="t" 	g2="hyphen,endash,emdash" 	k="26" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="t" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="t" 	g2="question" 	k="26" />
<hkern g1="t" 	g2="periodcentered" 	k="20" />
<hkern g1="t" 	g2="slash" 	k="-10" />
<hkern g1="t" 	g2="registered" 	k="-34" />
<hkern g1="t" 	g2="s" 	k="10" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="106" />
<hkern g1="T" 	g2="J" 	k="126" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="73" />
<hkern g1="T" 	g2="g" 	k="73" />
<hkern g1="T" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="66" />
<hkern g1="T" 	g2="t" 	k="18" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="46" />
<hkern g1="T" 	g2="x" 	k="39" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="T" 	g2="z" 	k="75" />
<hkern g1="T" 	g2="colon,semicolon" 	k="20" />
<hkern g1="T" 	g2="hyphen,endash,emdash" 	k="73" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="47" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="40" />
<hkern g1="T" 	g2="periodcentered" 	k="64" />
<hkern g1="T" 	g2="trademark" 	k="-34" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="40" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="24" />
<hkern g1="T" 	g2="S" 	k="39" />
<hkern g1="T" 	g2="v" 	k="33" />
<hkern g1="T" 	g2="w" 	k="34" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="T" 	g2="slash" 	k="89" />
<hkern g1="T" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="18" />
<hkern g1="T" 	g2="registered" 	k="-20" />
<hkern g1="T" 	g2="X" 	k="20" />
<hkern g1="T" 	g2="Z" 	k="54" />
<hkern g1="T" 	g2="s" 	k="59" />
<hkern g1="T" 	g2="AE" 	k="85" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="46" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="T" 	k="20" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="Y,Yacute,Ydieresis" 	k="30" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="asterisk" 	k="24" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="V" 	k="14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="47" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="4" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="g" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="x" 	k="7" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="S" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="V" 	k="4" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="31" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="X" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="s" 	k="4" />
<hkern g1="v" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="v" 	g2="J" 	k="40" />
<hkern g1="v" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="v" 	g2="j" 	k="10" />
<hkern g1="v" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="4" />
<hkern g1="v" 	g2="T" 	k="20" />
<hkern g1="v" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="v" 	g2="z" 	k="24" />
<hkern g1="v" 	g2="hyphen,endash,emdash" 	k="7" />
<hkern g1="v" 	g2="asterisk" 	k="11" />
<hkern g1="v" 	g2="trademark" 	k="-9" />
<hkern g1="v" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="11" />
<hkern g1="v" 	g2="V" 	k="9" />
<hkern g1="v" 	g2="slash" 	k="20" />
<hkern g1="v" 	g2="registered" 	k="-45" />
<hkern g1="v" 	g2="Z" 	k="10" />
<hkern g1="V" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="65" />
<hkern g1="V" 	g2="J" 	k="73" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="22" />
<hkern g1="V" 	g2="g" 	k="21" />
<hkern g1="V" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="15" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="30" />
<hkern g1="V" 	g2="x" 	k="15" />
<hkern g1="V" 	g2="z" 	k="25" />
<hkern g1="V" 	g2="hyphen,endash,emdash" 	k="19" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="21" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="19" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-13" />
<hkern g1="V" 	g2="periodcentered" 	k="11" />
<hkern g1="V" 	g2="trademark" 	k="-54" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="V" 	g2="S" 	k="10" />
<hkern g1="V" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="V" 	g2="v" 	k="9" />
<hkern g1="V" 	g2="V" 	k="-10" />
<hkern g1="V" 	g2="w" 	k="9" />
<hkern g1="V" 	g2="y,yacute,ydieresis" 	k="9" />
<hkern g1="V" 	g2="slash" 	k="47" />
<hkern g1="V" 	g2="registered" 	k="-53" />
<hkern g1="V" 	g2="Z" 	k="19" />
<hkern g1="V" 	g2="s" 	k="12" />
<hkern g1="V" 	g2="m,n,p,r,ntilde" 	k="20" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="w" 	g2="J" 	k="30" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="w" 	g2="j" 	k="10" />
<hkern g1="w" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="4" />
<hkern g1="w" 	g2="T" 	k="24" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="24" />
<hkern g1="w" 	g2="z" 	k="17" />
<hkern g1="w" 	g2="asterisk" 	k="11" />
<hkern g1="w" 	g2="trademark" 	k="-10" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="4" />
<hkern g1="w" 	g2="V" 	k="9" />
<hkern g1="w" 	g2="slash" 	k="8" />
<hkern g1="w" 	g2="registered" 	k="-38" />
<hkern g1="w" 	g2="X" 	k="14" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="34" />
<hkern g1="W" 	g2="J" 	k="65" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="W" 	g2="g" 	k="13" />
<hkern g1="W" 	g2="z" 	k="10" />
<hkern g1="W" 	g2="hyphen,endash,emdash" 	k="10" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="16" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-13" />
<hkern g1="W" 	g2="trademark" 	k="-34" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="6" />
<hkern g1="W" 	g2="S" 	k="6" />
<hkern g1="W" 	g2="slash" 	k="29" />
<hkern g1="W" 	g2="registered" 	k="-39" />
<hkern g1="W" 	g2="Z" 	k="6" />
<hkern g1="x" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="7" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="x" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="17" />
<hkern g1="x" 	g2="t" 	k="24" />
<hkern g1="x" 	g2="T" 	k="33" />
<hkern g1="x" 	g2="Y,Yacute,Ydieresis" 	k="34" />
<hkern g1="x" 	g2="hyphen,endash,emdash" 	k="14" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="24" />
<hkern g1="x" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="x" 	g2="asterisk" 	k="23" />
<hkern g1="x" 	g2="periodcentered" 	k="20" />
<hkern g1="x" 	g2="trademark" 	k="-9" />
<hkern g1="x" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="x" 	g2="S" 	k="4" />
<hkern g1="x" 	g2="V" 	k="15" />
<hkern g1="x" 	g2="registered" 	k="-38" />
<hkern g1="x" 	g2="X" 	k="10" />
<hkern g1="x" 	g2="exclam" 	k="14" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="X" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="7" />
<hkern g1="X" 	g2="t" 	k="16" />
<hkern g1="X" 	g2="T" 	k="20" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="X" 	g2="x" 	k="10" />
<hkern g1="X" 	g2="z" 	k="14" />
<hkern g1="X" 	g2="hyphen,endash,emdash" 	k="27" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="X" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="7" />
<hkern g1="X" 	g2="asterisk" 	k="10" />
<hkern g1="X" 	g2="periodcentered" 	k="40" />
<hkern g1="X" 	g2="trademark" 	k="-21" />
<hkern g1="X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-3" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="17" />
<hkern g1="X" 	g2="S" 	k="14" />
<hkern g1="X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="X" 	g2="v" 	k="16" />
<hkern g1="X" 	g2="w" 	k="14" />
<hkern g1="X" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="7" />
<hkern g1="X" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="X" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="16" />
<hkern g1="X" 	g2="registered" 	k="-8" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="y,yacute,ydieresis" 	g2="J" 	k="40" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="j" 	k="10" />
<hkern g1="y,yacute,ydieresis" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="4" />
<hkern g1="y,yacute,ydieresis" 	g2="T" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="x" 	k="14" />
<hkern g1="y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="7" />
<hkern g1="y,yacute,ydieresis" 	g2="z" 	k="24" />
<hkern g1="y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="4" />
<hkern g1="y,yacute,ydieresis" 	g2="guillemotright,guilsinglright" 	k="4" />
<hkern g1="y,yacute,ydieresis" 	g2="trademark" 	k="-13" />
<hkern g1="y,yacute,ydieresis" 	g2="V" 	k="4" />
<hkern g1="y,yacute,ydieresis" 	g2="slash" 	k="14" />
<hkern g1="y,yacute,ydieresis" 	g2="registered" 	k="-45" />
<hkern g1="y,yacute,ydieresis" 	g2="X" 	k="4" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="91" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="67" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="60" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="28" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="34" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="34" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="25" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,endash,emdash" 	k="66" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="55" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="question" 	k="7" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-9" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="periodcentered" 	k="46" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="trademark" 	k="-46" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="24" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="67" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="registered" 	k="-33" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="26" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="40" />
<hkern g1="z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="23" />
<hkern g1="z" 	g2="g" 	k="6" />
<hkern g1="z" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="z" 	g2="T" 	k="26" />
<hkern g1="z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="z" 	g2="Y,Yacute,Ydieresis" 	k="29" />
<hkern g1="z" 	g2="hyphen,endash,emdash" 	k="16" />
<hkern g1="z" 	g2="periodcentered" 	k="11" />
<hkern g1="z" 	g2="trademark" 	k="-13" />
<hkern g1="z" 	g2="v" 	k="4" />
<hkern g1="z" 	g2="y,yacute,ydieresis" 	k="4" />
<hkern g1="z" 	g2="registered" 	k="-25" />
<hkern g1="Z" 	g2="J" 	k="33" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="Z" 	g2="g" 	k="14" />
<hkern g1="Z" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="21" />
<hkern g1="Z" 	g2="t" 	k="14" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="23" />
<hkern g1="Z" 	g2="x" 	k="24" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="Z" 	g2="hyphen,endash,emdash" 	k="34" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="34" />
<hkern g1="Z" 	g2="periodcentered" 	k="60" />
<hkern g1="Z" 	g2="trademark" 	k="-20" />
<hkern g1="Z" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="6" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="Z" 	g2="S" 	k="30" />
<hkern g1="Z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="Z" 	g2="v" 	k="16" />
<hkern g1="Z" 	g2="V" 	k="6" />
<hkern g1="Z" 	g2="w" 	k="16" />
<hkern g1="Z" 	g2="W" 	k="6" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="Z" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="20" />
<hkern g1="Z" 	g2="registered" 	k="-20" />
<hkern g1="Z" 	g2="Z" 	k="16" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="J" 	k="20" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="j" 	k="-80" />
<hkern g1="colon,semicolon" 	g2="j" 	k="-4" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="26" />
<hkern g1="colon,semicolon" 	g2="asterisk" 	k="49" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-26" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="11" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="t" 	k="46" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="99" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v" 	k="35" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V" 	k="65" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w" 	k="22" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W" 	k="34" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteleft,quotedblleft" 	k="84" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteright,quotedblright" 	k="104" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle" 	k="96" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="asterisk" 	k="138" />
<hkern g1="hyphen,endash,emdash" 	g2="t" 	k="26" />
<hkern g1="hyphen,endash,emdash" 	g2="T" 	k="47" />
<hkern g1="hyphen,endash,emdash" 	g2="v" 	k="7" />
<hkern g1="hyphen,endash,emdash" 	g2="V" 	k="19" />
<hkern g1="hyphen,endash,emdash" 	g2="W" 	k="10" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="66" />
<hkern g1="hyphen,endash,emdash" 	g2="J" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="S" 	k="17" />
<hkern g1="hyphen,endash,emdash" 	g2="x" 	k="14" />
<hkern g1="hyphen,endash,emdash" 	g2="X" 	k="27" />
<hkern g1="hyphen,endash,emdash" 	g2="Z" 	k="20" />
<hkern g1="exclam" 	g2="quoteright,quotedblright" 	k="32" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="t" 	k="14" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="40" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="v" 	k="4" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="17" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="17" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="y,yacute,ydieresis" 	k="4" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="37" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="x" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="X" 	k="14" />
<hkern g1="guillemotright,guilsinglright" 	g2="t" 	k="33" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="46" />
<hkern g1="guillemotright,guilsinglright" 	g2="v" 	k="7" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="21" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="10" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis" 	k="7" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="55" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="30" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="30" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="24" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="14" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="18" />
<hkern g1="question" 	g2="quoteright,quotedblright" 	k="21" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-13" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-13" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-9" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="75" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="7" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="85" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="43" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="56" />
<hkern g1="quoteleft,quotedblleft" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="83" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclamdown" 	k="53" />
<hkern g1="quoteleft,quotedblleft" 	g2="questiondown" 	k="133" />
<hkern g1="quoteright,quotedblright" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="58" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="X" 	k="7" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="85" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="65" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="58" />
<hkern g1="quoteright,quotedblright" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="45" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="49" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="132" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde" 	k="13" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="26" />
<hkern g1="quotedbl,quotesingle" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="95" />
<hkern g1="quotedbl,quotesingle" 	g2="S" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="X" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="55" />
<hkern g1="quotedbl,quotesingle" 	g2="s" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="115" />
</font>
</defs></svg> 