/* === VARIABILI CSS === */
:root {
    --primary-color: #e22b2b;
    --primary-dark: #820000;
    --primary-darker: #400000;
    --primary-darkest: #1a0000;
    --primary-medium: #660000;
    --primary-light: #db7070;
    --accent-color: #ff0000;
    --accent-light: #ff3333;
    --bg-black: #000000;
    --bg-dark: #111111;
    --bg-darker: #1a1a1a;
    --bg-medium: #2a0000;
    --text-white: #ffffff;
    --text-gray: #aaaaaa;
    --text-light-gray: #ccc;
    --border-dark: #333333;
    --font-bangers: 'Bangers', cursive;
    --font-base: 'Comic Neue', cursive;
  }
/* === THEME: BLUE === */
body.theme-blue {
  --primary-color: #2b6de2;
  --primary-dark: #003d82;
  --primary-darker: #001a40;
  --primary-darkest: #000d1a;
  --primary-medium: #003366;
  --primary-light: #709ddb;
  --accent-color: #0080ff;
  --accent-light: #3399ff;
  --bg-medium: #001f33;
}
body.theme-green {
--primary-color: #2e8b57;
--primary-dark: #206846;
--primary-darker: #10432e;
--primary-darkest: #06251a;
--primary-medium: #1e5735;
--primary-light: #71c79c;
--accent-color: #00cc66;
--accent-light: #33ff99;
--bg-medium: #0f3b28;
}
body.theme-purple {
--primary-color: #8a2be2;
--primary-dark: #6a1c9c;
--primary-darker: #470f6e;
--primary-darkest: #2e0744;
--primary-medium: #5a1b86;
--primary-light: #b67fff;
--accent-color: #bb33ff;
--accent-light: #d97fff;
--bg-medium: #3a0b58;
}
body.theme-orange {
--primary-color: #ff8c00;
--primary-dark: #cc7000;
--primary-darker: #994f00;
--primary-darkest: #663300;
--primary-medium: #cc6600;
--primary-light: #ffb84d;
--accent-color: #ff9900;
--accent-light: #ffcc66;
--bg-medium: #4d2e00;
}
body.theme-neutral {
--primary-color: #666666;
--primary-dark: #4d4d4d;
--primary-darker: #333333;
--primary-darkest: #1a1a1a;
--primary-medium: #595959;
--primary-light: #b3b3b3;
--accent-color: #999999;
--accent-light: #cccccc;
--bg-medium: #2b2b2b;
}
body.theme-pink {
--primary-color: #e91e63;
--primary-dark: #b0003a;
--primary-darker: #800027;
--primary-darkest: #4d0015;
--primary-medium: #c2185b;
--primary-light: #f06292;
--accent-color: #ff4081;
--accent-light: #ff80ab;
--bg-medium: #550022;
}
body.theme-sky {
--primary-color: #00bcd4;
--primary-dark: #008fa1;
--primary-darker: #005f6b;
--primary-darkest: #00343d;
--primary-medium: #0097a7;
--primary-light: #4dd0e1;
--accent-color: #26c6da;
--accent-light: #80deea;
--bg-medium: #004d5c;
}
body.theme-lime {
--primary-color: #cddc39;
--primary-dark: #aab62b;
--primary-darker: #7d8d1f;
--primary-darkest: #4b580f;
--primary-medium: #afb42b;
--primary-light: #e6ee9c;
--accent-color: #d4e157;
--accent-light: #f0f4c3;
--bg-medium: #586317;
}
body.theme-amber {
    --primary-color: #ffc107;
    --primary-dark: #c79100;
    --primary-darker: #9c6b00;
    --primary-darkest: #664600;
    --primary-medium: #ffb300;
    --primary-light: #ffe082;
    --accent-color: #ffd54f;
    --accent-light: #ffecb3;
    --bg-medium: #6d4c00;
} 
body.theme-marvel {
    --primary-color: #e23636;         /* rosso vivo */
    --primary-dark: #aa1f1f;          /* rosso scuro */
    --primary-darker: #781111;
    --primary-darkest: #3d0000;
    --primary-medium: #c62828;        /* rosso centrale */
    --primary-light: #ff6b6b;

    --accent-color: #0f62fe;          /* blu vivo - Cap */
    --accent-light: #60a8ff;

    --bg-medium: #0a0a0f;             /* sfondo molto scuro ma non nero */
}
body.theme-marvel-mcu {
    --primary-color: #d32f2f;         /* rosso sangue */
    --primary-dark: #9a0007;
    --primary-darker: #600000;
    --primary-darkest: #2c0000;
    --primary-medium: #b71c1c;
    --primary-light: #ff6659;

    --accent-color: #ffd700;          /* oro */
    --accent-light: #ffe97d;

    --bg-medium: #121212;             /* scuro neutro elegante */
}
body.theme-marvel .character-tab-content {
    background-image: url('https://i.ibb.co/j9S62f2B/4872553.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-blend-mode: overlay;
    background-color: rgba(0, 0, 0, 0.8); /* per leggibilità */
}


  /* === BASE STYLES === */
  body {
    background-color: var(--bg-black);
    color: var(--text-white);
    padding-bottom: 30px;
  }

  /* === TYPOGRAPHY === */
  .character-name {
    font-size: 2.5rem;
    color: var(--primary-color);
    letter-spacing: 0.1em;
    font-family: var(--font-bangers);
    margin-bottom: 1rem;
    font-weight: 600;
     
  }

  .character-alias {
    font-size: 1.5rem;
    color: var(--primary-color);
    letter-spacing: 0.1em;
    font-family: var(--font-bangers);
    margin-bottom: 1rem;
    font-weight: 600;
     
  }

  /* === CHARACTER IMAGE === */
  .character-image-wrapper {
    position: relative;
    width: fit-content;
    display: inline-block;
  }

  .character-image {
    display: block;
    max-width: 450px; 
    max-height: 630px;
    height: auto;
    transition: opacity 0.5s ease-in-out;
    border: 2px solid var(--primary-color);
    align-self: center;
  }

  .character-image.hover {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    pointer-events: none;
  }

  .character-image-wrapper:hover .hover {
    opacity: 1;
  }

  .character-image-wrapper:hover .main {
    opacity: 0;
  }

  /* === DIAMOND STATS === */
  .diamond-stats-container {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
  }

  .diamond-stat {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 5px;
  }

  .diamond-shape {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 2px solid var(--accent-color);
    background-color: var(--bg-black);
    overflow: hidden;
  }

  .diamond-content {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 10px;
  }

  .stat-name {
    color: var(--text-white);
    font-size: 0.9rem;
    font-weight: bold;
    text-transform: uppercase;
    margin-bottom: 5px;
  }

  .stat-value {
    color: var(--text-white);
    font-size: 2rem;
    font-weight: bold;
    line-height: 1;
  }

  .stat-max {
    color: var(--text-white);
    font-size: 0.8rem;
    opacity: 0.7;
    line-height: 1;
    margin-top: 5px;
  }

  /* === ABILITY SECTIONS === */
  .ability-section {
    border: 1px solid var(--accent-color);
    overflow: hidden;
    background-color: var(--bg-black);
    color: var(--text-white);
    margin-top: 1rem;
  }

  .ability-header {
    background-color: var(--bg-black);
    border-bottom: 1px solid var(--accent-color);
    padding: 8px 15px;
  }

  .ability-title {
    color: var(--text-white);
    margin: 0;
    font-size: 1.2rem;
    font-weight: bold;
    text-transform: uppercase;
  }

  .ability-table-header {
    display: flex;
    background-color: var(--bg-dark);
    border-bottom: 1px solid var(--border-dark);
    font-size: 0.8rem;
    text-transform: uppercase;
  }

  .ability-row {
    display: flex;
    border-bottom: 1px solid var(--border-dark);
    background-color: var(--primary-darkest);
  }

  .ability-col {
    padding: 8px 10px;
    text-align: center;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ability-name {
    justify-content: flex-start;
    font-weight: bold;
    color: var(--text-white);
  }
  .ability-firstletter {
    font-size: 30px;
    font-weight: bold;
    padding-rigth: 2px;
    text-shadow: 4px 4px 0 #000, 8px 8px 0 rgba(0, 0, 0, 0.3);
    display: inline-block;
  }

  .ability-score, 
  .ability-attack {
    background-color: var(--primary-medium);
    color: var(--text-white);
    font-weight: bold;
  }

  .ability-score-input {
    background-color: transparent !important;
    text-align: center;
    color: var(--text-white);
    font-weight: bold;
    border:none;
  } 

  .btn {
    display: inline-block !important;
  }

  .ability-attack img{
    width: 25px;
    padding-right: 5px;
  }

  .ability-defense {
    color: var(--text-white);
  }

  /* === TABS === */
  .character-tabs {
    border-bottom: 2px solid var(--accent-color);
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .character-tabs .nav-link {
    color: var(--text-white);
    background-color: var(--bg-darker);
    border: 1px solid var(--accent-color);
    border-bottom: none;
    margin-right: 5px;
    border-radius: 0;
    font-weight: bold;
    transition: all 0.3s ease;
    position: relative;
    clip-path: polygon(0 0, 100% 0, 95% 100%, 0% 100%);
    padding-right: 25px;
    min-width: 150px;
    text-align: center;
  }

  .character-tabs .nav-link:hover {
    background-color: var(--primary-medium);
    border-color: var(--accent-light);
   
  }

  .character-tabs .nav-link.active {
    background-color: var(--primary-darker);
    color: var(--text-white);
    border: 1px solid var(--accent-color);
    border-bottom: none;
  }

  .character-tab-content {
    background-color: var(--bg-darker);
    border: 1px solid var(--accent-color);
    border-top: none;
    padding: 20px;
    color: var(--text-white);
    min-height: 300px;
    position: relative;
    z-index: 0;
    clip-path: polygon(0 0, 98% 0, 100% 3%, 100% 100%, 0 100%);
    width: 100%;
    margin: 0 auto;
  }

  .character-tab-content h3,
  .character-tab-content h4 {
    color: var(--accent-color);
    border-bottom: 1px solid var(--accent-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
  }

  /* === POWER SECTIONS === */
  .power-wrapper {
    margin-bottom: 2rem;
  }

  .power-header {
    background-color: var(--primary-dark);
    border: 2px solid var(--accent-color);
    padding: 10px 15px;
  }

  .power-row {
    background-color: var(--primary-darkest);
    color: var(--text-white);
    border: 2px solid var(--accent-color);
    margin-top: 10px;
    padding: 15px;
    clip-path: polygon(0 0, 98% 0, 100% 10px, 100% 100%, 0 100%);
    font-family: var(--font-base);
    align-items: center;
  }

  .power-name {
    font-weight: bold;
    font-size: 1.1rem;
  }

  .power-desc {
    font-size: 0.95rem;
  }

  .power-focus {
    font-size: 1.1rem;
    font-weight: bold;
  }

  .power-row .btn {
    padding: 2px 6px;
    font-size: 0.75rem;
    margin-right: 3px;
  }

  /* === BACKGROUND CONTENT === */
  .background-content {
    background-color: var(--bg-medium);
    border: 2px solid var(--accent-color);
    padding: 20px;
    clip-path: polygon(0 0, 98% 0, 100% 10px, 100% 100%, 0 100%);
  }
    /* === NOTE CONTENT === */
    .note-content {
    background-color: var(--bg-medium);
    border: 2px solid var(--accent-color);
    padding: 20px;
    clip-path: polygon(0 0, 98% 0, 100% 10px, 100% 100%, 0 100%);
  }

  /* === AUDIO PLAYER === */
  .custom-audio-player {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px 15px;
    border: 2px solid var(--accent-color);
    background-color: var(--bg-dark);
    max-width: 450px;
    margin: 20px auto;
    box-shadow: 0 0 15px rgba(255, 0, 0, 0.4);
    font-family: var(--font-bangers);
  }

  .audio-btn {
    background-color: var(--primary-darker);
    color: var(--text-white);
    border: 2px solid var(--accent-color);
    font-size: 1.3rem;
    cursor: pointer;
    transition: background 0.3s ease;
    height: 40px;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .audio-middle {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .audio-track {
    width: 100%;
    height: 8px;
    background-color: var(--border-dark);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
  }

  #progressBar {
    height: 100%;
    width: 0%;
    background-color: var(--accent-color);
    transition: width 0.2s linear;
  }

  .time-volume {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .time-display {
    font-size: 0.8rem;
    color: var(--text-light-gray);
    font-family: sans-serif;
  }

  .volume-control {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .volume-control i {
    color: var(--accent-color);
    font-size: 1rem;
  }

  .volume-control input[type=range] {
    width: 100px;
    height: 6px;
    background: var(--border-dark);
    border-radius: 3px;
    appearance: none;
    cursor: pointer;
  }

  .volume-control input[type=range]::-webkit-slider-thumb {
    appearance: none;
    width: 12px;
    height: 12px;
    background: var(--accent-color);
    border-radius: 50%;
    border: none;
  }

  /* === ICONS AND BUTTONS === */
  .icone {
    display: flow;
    gap: 10px;
    border: 2px solid var(--accent-color);
    background-color: var(--bg-dark);
    max-width: 450px;
    margin: 10px auto;
    text-align: center;
    box-shadow: 0 0 15px rgba(255, 0, 0, 0.4);
    font-family: var(--font-bangers);
  }

  .icone img {
    width: 50px;
    height: 50px;
    margin: 0 auto;
    cursor: pointer;
  }

  .bottoni {
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 6px;
    margin: 10px auto;
    font-family: var(--font-bangers);
  }

  /* Buttons styling consolidation */
  .btn-primary,
  .btn-success {
    background-color: var(--primary-darker);
    color: var(--text-white);
    border: 2px solid var(--accent-color);
    font-size: 1.3rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .btn-primary:hover,
  .btn-success:hover {
    background-color: var(--primary-medium);
    border-color: var(--accent-light);
  }

  /* === RESPONSIVE === */
  @media (max-width: 768px) {
    .row > div {
      margin-bottom: 30px;
    }
    
    .diamond-stats-container {
      flex-wrap: wrap;
      justify-content: center;
    }

    .character-name {
      font-size: 2rem;
    }

    .custom-audio-player {
      max-width: 100%;
      margin: 10px 0;
    }
  }

  /* === UTILITY CLASSES === */
  .section-box {
    padding-left: 20px;
    padding-right: 20px;
  }