<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

include_once __DIR__.'/../../../core.php';

use Modules\Bacheche\Bacheca;

// Recupera la bacheca se stiamo modificando
$bacheca = null;
if(get('idbacheca')) {
    $bacheca = Bacheca::find(get('idbacheca'));
}

// Determina il testo del pulsante e il titolo
$button_text = '<i class="fa fa-plus"></i> '.tr('Crea Bacheca');
$modal_title = tr('Nuova Bacheca');

if($bacheca) {
    $button_text = '<i class="fa fa-save"></i> '.tr('Salva Modifiche');
    $modal_title = tr('Modifica Bacheca');
}

// CSS personalizzato per il modal
echo '
<style>
.modal-bacheca {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 20px;
}

.form-section {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.section-title {
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e9ecef;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-modal {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-modal:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.checkbox-custom {
    display: flex;
    align-items: center;
    gap: 10px;
}

.checkbox-custom input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: #667eea;
}

.help-text {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 5px;
}
</style>';

echo '
<div class="modal-bacheca">
    <div class="form-section">
        <h4 class="section-title">
            <i class="fa fa-info-circle"></i> '.tr('Informazioni Bacheca').'
        </h4>
        
        <form id="bacheca-form" action="'.base_path().'/controller.php?id_module='.$id_module.'" method="post" enctype="multipart/form-data">
            <input type="hidden" name="op" value="manage-bacheca">
            <input type="hidden" name="backto" value="record-edit">
            <input type="hidden" name="idbacheca" value="'.($bacheca ? $bacheca->id : '').'">

            <div class="row">
                <div class="col-md-12">
                    {[ "type": "text", "label": "'.tr('Nome della Bacheca').'", "name": "nome", "value": "'.($bacheca ? $bacheca->nome : '').'", "required": 1, "help": "'.tr('Inserisci un nome descrittivo per la bacheca').'" ]}
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-2">
                    {[ "type": "checkbox", "label": "'.tr('Bacheca Attiva').'", "name": "sezione", "value": "'.($bacheca ? $bacheca->sezione : '0').'", "help": "'.tr('Le bacheche attive sono visibili a tutti gli utenti').'" ]}
                </div>';
            if( $user->gruppo=='Amministratori' ){
                echo '
                <div class="col-md-2">
                    {[ "type": "checkbox", "label": "'.tr('Permesso scrittura').'", "name": "type", "value": "'.$bacheca->type.'", "help": "'.tr('Tutti gli utenti possono rispondere ai post di questa sezione').'" ]}
                </div>';

                echo '
                <div class="col-md-3">
                    {[ "type": "select", "label": "'.tr('Gruppo').'", "name": "id_gruppo", "value": "'.$bacheca->id_gruppo.'" ]}
                </div>';

                echo '
                <div class="col-md-4">
                    {[ "type": "text", "label": "'.tr('Etichetta').'", "name": "etichetta", "value": "'.$bacheca->etichetta.'", "values":"query=SELECT id, nome AS descrizione FROM gdr_gruppi_gioco WHERE gdr_gruppi_gioco.deleted_at IS NULL ORDER BY descrizione ASC", "help": "'.tr('Questa sezione è privata per gli appartenenti ad uno specifico gruppo').'" ]}
                </div>';
            }
            echo '

                
                <div class="col-md-5">
                    {[ "type": "select", "label": "'.tr('Bacheca Genitore').'", "name": "id_parent", "ajax-source":"bacheche", "value": "'.($bacheca ? $bacheca->id_parent : '0').'", "select-options":'.json_encode(['sezione' => ($bacheca ? $bacheca->sezione : 0)]).', "help": "'.tr('Seleziona una bacheca genitore per creare una sottosezione').'" ]}
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="help-text">
                        <i class="fa fa-lightbulb"></i> 
                        <strong>'.tr('Suggerimento').':</strong> 
                        '.tr('Le bacheche possono essere organizzate in sezioni e sottosezioni per una migliore organizzazione dei contenuti.').'
                    </div>
                </div>
            </div>
        </form>
    </div>
    
    <div class="text-right">
        <button type="button" class="btn btn-secondary btn-modal" data-dismiss="modal">
            <i class="fa fa-times"></i> '.tr('Annulla').'
        </button>
        <button type="button" class="btn btn-primary btn-modal" onclick="salvaBacheca();">
            '.$button_text.'
        </button>
    </div>
</div>';

// JavaScript per la gestione del form
echo '
<script>
$(document).ready(function(){
    // Inizializza i componenti del form
    init();
    
    // Animazione di entrata
    $(".modal-bacheca").css("opacity", "0").animate({opacity: 1}, 300);
});

/**
 * Gestisce il cambio di stato della bacheca (attiva/non attiva)
 */
$("#sezione").on("change", function(){
    var isActive = $(this).is(":checked");
    
    // Aggiorna le opzioni per la selezione della bacheca genitore
    updateSelectOption("sezione", isActive ? 1 : 0);
    session_set("superselect,sezione", isActive ? 1 : 0, 0);
    
    // Reset della selezione bacheca genitore
    $("#id_parent").selectReset();
    
    // Feedback visivo
    if(isActive) {
        $(this).closest(".form-group").addClass("text-success");
    } else {
        $(this).closest(".form-group").removeClass("text-success");
    }
});

/**
 * Salva la bacheca (creazione o modifica)
 */
function salvaBacheca() {
    // Validazione del form
    if(!$("#nome").val().trim()) {
        swal("'.tr('Attenzione').'", "'.tr('Il nome della bacheca è obbligatorio').'", "warning");
        $("#nome").focus();
        return;
    }
    
    // Mostra indicatore di caricamento
    var submitBtn = $(".btn-primary.btn-modal");
    var originalText = submitBtn.html();
    submitBtn.html(\'<i class="fa fa-spinner fa-spin"></i> '.tr('Salvando...').'\').prop("disabled", true);
    
    // Invia il form
    salvaForm("#bacheca-form", {
        id_module: "'.$id_module.'",
    }).then(function(response) {
        // Chiude il modal
        $(".modal").modal("hide");
        
        // Ricarica la pagina per mostrare le modifiche
        location.reload();
        
        // Mostra messaggio di successo
        swal("'.tr('Successo').'", response.message || "'.tr('Bacheca salvata con successo!').'", "success");
        
    }).catch(function(error) {
        // Ripristina il pulsante in caso di errore
        submitBtn.html(originalText).prop("disabled", false);
        
        swal("'.tr('Errore').'", error.message || "'.tr('Errore durante il salvataggio').'", "error");
    });
}

/**
 * Gestisce l\'invio del form con Enter
 */
$("#bacheca-form").on("keypress", function(e) {
    if(e.which === 13) { // Enter key
        e.preventDefault();
        salvaBacheca();
    }
});

</script>';
