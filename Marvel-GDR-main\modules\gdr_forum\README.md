# Modulo Forum GDR - Interfaccia Social Media

## Descrizione
Questo modulo implementa un'interfaccia forum in stile social media per il sistema GDR, seguendo il design mostrato nell'immagine di riferimento fornita dall'utente.

## Caratteristiche Principali

### 🎨 Design Social Media
- **Interfaccia moderna**: Layout ispirato ai social network con card, avatar circolari e animazioni fluide
- **Responsive**: Completamente adattabile a dispositivi mobili e desktop
- **Gradients e ombre**: Utilizzo di gradienti CSS e ombre per un aspetto professionale
- **Animazioni**: Transizioni fluide e animazioni di hover per migliorare l'esperienza utente

### 📱 Struttura del Forum
- **Bacheche principali**: Organizzate in sezioni ON/OFF
- **Sottobacheche**: Sistema gerarchico per organizzare meglio i contenuti
- **Post in stile social**: Ogni post mostra avatar, nome utente, timestamp e contenuto
- **Azioni sui post**: <PERSON><PERSON><PERSON><PERSON> per like, commenti e condivisione (preparati per future implementazioni)

### 🔧 Funzionalità Tecniche
- **Gestione permessi**: Solo amministratori e proprietari possono modificare/eliminare contenuti
- **Editor avanzato**: Utilizzo di CKEditor per la formattazione dei post
- **AJAX**: Caricamento dinamico dei contenuti senza refresh della pagina
- **Validazione**: Controlli lato client e server per la sicurezza dei dati

## File Principali

### `edit.php`
File principale che mostra l'interfaccia del forum con:
- Header con gradiente e titolo accattivante
- Sezioni per bacheche attive e non attive
- CSS personalizzato per lo stile social media
- JavaScript per la gestione delle interazioni

### `actions.php`
Gestisce tutte le operazioni CRUD:
- `manage-bacheca`: Creazione/modifica bacheche
- `delete_bacheca`: Eliminazione bacheche e contenuti
- `add_risposta`: Aggiunta nuovi post
- `update_risposta`: Modifica post esistenti
- `delete_post`: Eliminazione post

### `ajax/complete.php`
Gestisce il caricamento dinamico dei post:
- Visualizzazione post in stile social media
- Form per nuovi post con avatar e selezione personaggio
- Gestione delle azioni sui post
- CSS e JavaScript integrati

### `modals/manage_bacheca.php`
Modal per la gestione delle bacheche:
- Form con validazione per nome, stato e bacheca genitore
- Interfaccia user-friendly con tooltip e animazioni
- Gestione degli stati attivo/non attivo

### `modals/manage_post.php`
Modal per la modifica dei post:
- Editor CKEditor integrato
- Selezione personaggio per amministratori
- Auto-save e ripristino backup
- Validazione completa

### `ajax/select.php`
Gestisce le selezioni dinamiche:
- `bacheche`: Selezione bacheche principali
- `sottobacheche`: Selezione sottobacheche
- `bacheche_attive`: Solo bacheche attive
- `bacheche_complete`: Vista completa con gerarchia

## Stili CSS Personalizzati

### Colori Principali
- **Primario**: Gradiente blu-viola (#667eea → #764ba2)
- **Sfondo**: Grigio chiaro (#f8f9fa)
- **Card**: Bianco con ombre sottili
- **Testo**: Scala di grigi per gerarchia visiva

### Componenti Stilizzati
- **Forum Header**: Gradiente con testo centrato e icone
- **Forum Topics**: Card con hover effects e transizioni
- **Social Posts**: Layout simile a Facebook/Twitter
- **Buttons**: Stile arrotondato con hover effects
- **Modals**: Design pulito con sezioni organizzate

## Funzionalità JavaScript

### Gestione Eventi
- Click sui topic per aprire i post
- Hover effects sui post e pulsanti
- Gestione form con validazione
- Auto-save per i contenuti

### AJAX
- Caricamento dinamico dei post
- Invio form senza refresh
- Aggiornamento in tempo reale
- Gestione errori con feedback utente

### Animazioni
- Fade-in per elementi della pagina
- Slide effects per i contenuti
- Loading spinners durante le operazioni
- Feedback visivo per le azioni

## Compatibilità
- **Browser**: Supporto per tutti i browser moderni
- **Mobile**: Design responsive per smartphone e tablet
- **Accessibilità**: Utilizzo di ARIA labels e struttura semantica
- **Performance**: Caricamento ottimizzato e lazy loading

## Note per lo Sviluppo
- Tutti i commenti sono in italiano come richiesto
- Utilizzo delle convenzioni del progetto esistente
- Integrazione con il sistema di autenticazione e permessi
- Compatibilità con il database esistente

## Future Implementazioni
- Sistema di like e reazioni
- Commenti ai post
- Notifiche in tempo reale
- Sistema di menzioni
- Upload di immagini nei post
- Ricerca avanzata nei contenuti
