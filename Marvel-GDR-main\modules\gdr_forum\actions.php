<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

include_once __DIR__.'/../../core.php';

use Modules\Bacheche\Bacheca;
use Modules\Bacheche\Post;

switch (filter('op')) {
    case 'manage-bacheca':
        // Gestione creazione/modifica bacheca
        try {
            if( post('idbacheca') ){
                // Modifica bacheca esistente
                $bacheca = Bacheca::find(post('idbacheca'));
                if(!$bacheca) {
                    throw new Exception(tr('Bacheca non trovata'));
                }
            } else {
                // Crea nuova bacheca
                $bacheca = Bacheca::build(post('nome'));
            }
            
            // Aggiorna i dati della bacheca
            $bacheca->nome = post('nome');
            $bacheca->type = post('type') ?: 0;
            $bacheca->sezione = post('sezione') ? 1 : 0;
            $bacheca->id_parent = post('id_parent') ?: null;
            $bacheca->id_gruppo = post('id_gruppo') ?: null;
            $bacheca->etichetta = post('etichetta');
            $bacheca->created_by = $user->id;
            $bacheca->save();

            flash()->info(post('idbacheca') ? tr('Bacheca modificata con successo!') : tr('Nuova bacheca creata con successo!'));

            echo json_encode([
                'response' => true,
                'message' => post('idbacheca') ? tr('Bacheca modificata!') : tr('Bacheca creata!'),
            ]);

        } catch (Exception $e) {
            flash()->error(tr('Errore durante il salvataggio della bacheca: ') . $e->getMessage());
            
            echo json_encode([
                'response' => false,
                'message' => tr('Errore durante il salvataggio'),
            ]);
        }
        break;

    case 'delete_bacheca':
        // Eliminazione bacheca e tutte le sue sottobacheche
        try {
            $id_bacheca = post('id_bacheca');
            
            // Verifica che l'utente abbia i permessi
            if($user->gruppo != 'Amministratori') {
                throw new Exception(tr('Non hai i permessi per eliminare le bacheche'));
            }
            
            // Elimina prima tutti i post delle sottobacheche
            $sottobacheche = $dbo->table('gdr_forum')->where('id_parent', $id_bacheca)->get();
            foreach($sottobacheche as $sottobacheca) {
                $dbo->query("DELETE FROM gdr_forum_post WHERE id_forum=".prepare($sottobacheca->id));
            }
            
            // Elimina i post della bacheca principale
            $dbo->query("DELETE FROM gdr_forum_post WHERE id_forum=".prepare($id_bacheca));
            
            // Elimina le sottobacheche
            $dbo->query("DELETE FROM gdr_forum WHERE id_parent=".prepare($id_bacheca));
            
            // Elimina la bacheca principale
            $dbo->query("DELETE FROM gdr_forum WHERE id=".prepare($id_bacheca));
            
            flash()->info(tr('Bacheca e tutti i suoi contenuti sono stati eliminati!'));

            echo json_encode([
                'response' => true,
                'message' => tr('Bacheca eliminata con successo!'),
            ]);

        } catch (Exception $e) {
            flash()->error(tr('Errore durante l\'eliminazione della bacheca: ') . $e->getMessage());
            
            echo json_encode([
                'response' => false,
                'message' => tr('Errore durante l\'eliminazione'),
            ]);
        }
        break;

    case 'add_risposta':
        // Aggiunta nuovo post
        try {
            // Validazione dei dati
            if(empty(post('messaggio'))) {
                throw new Exception(tr('Il messaggio non può essere vuoto'));
            }
            
            if(empty(post('nome'))) {
                throw new Exception(tr('Il nome non può essere vuoto'));
            }
            
            // Crea il nuovo post
            $post = Post::build(
                post('id_utente'),
                post('idpersonaggio'),
                post('id_forum'),
                post('nome')
            );
            $post->messaggio = post('messaggio');
            $post->save();

            flash()->info(tr('Post pubblicato con successo!'));

            echo json_encode([
                'response' => true,
                'message' => tr('Post pubblicato!'),
            ]);

        } catch (Exception $e) {
            flash()->error(tr('Errore durante la pubblicazione del post: ') . $e->getMessage());
            
            echo json_encode([
                'response' => false,
                'message' => tr('Errore durante la pubblicazione'),
            ]);
        }
        break;

    case 'update_risposta':
        // Modifica post esistente
        try {
            $post = Post::find(post('id_post'));
            if(!$post) {
                throw new Exception(tr('Post non trovato'));
            }
            
            // Verifica che l'utente possa modificare il post
            if($user->gruppo != 'Amministratori' && $post->id_utente != $user->id) {
                throw new Exception(tr('Non hai i permessi per modificare questo post'));
            }
            
            // Aggiorna i dati del post
            $post->messaggio = post('messaggio_edit');
            $post->nome = post('nome_edit');
            $post->idpersonaggio = post('idpersonaggio_edit') ?: post('idpersonaggio');
            $post->save();

            flash()->info(tr('Post modificato con successo!'));

            echo json_encode([
                'response' => true,
                'message' => tr('Post modificato!'),
            ]);

        } catch (Exception $e) {
            flash()->error(tr('Errore durante la modifica del post: ') . $e->getMessage());
            
            echo json_encode([
                'response' => false,
                'message' => tr('Errore durante la modifica'),
            ]);
        }
        break;

    case 'delete_post':
        // Eliminazione post
        try {
            $post = Post::find(post('idriga'));
            if(!$post) {
                throw new Exception(tr('Post non trovato'));
            }
            
            // Verifica che l'utente possa eliminare il post
            if($user->gruppo != 'Amministratori' && $post->id_utente != $user->id) {
                throw new Exception(tr('Non hai i permessi per eliminare questo post'));
            }
            
            $post->delete();

            flash()->info(tr('Post eliminato con successo!'));

            echo json_encode([
                'response' => true,
                'message' => tr('Post eliminato!'),
            ]);

        } catch (Exception $e) {
            flash()->error(tr('Errore durante l\'eliminazione del post: ') . $e->getMessage());
            
            echo json_encode([
                'response' => false,
                'message' => tr('Errore durante l\'eliminazione'),
            ]);
        }
        break;

    case 'toggle_like':
        // Gestione del toggle "Mi piace" sui post utilizzando i metodi del modello
        try {
            $id_post = post('id_post');
            $id_utente = $user->id;

            // Validazione dei dati
            if (empty($id_post)) {
                throw new Exception(tr('ID post non valido'));
            }

            // Verifica che il post esista
            $post = Post::find($id_post);
            if (!$post) {
                throw new Exception(tr('Post non trovato'));
            }

            // Utilizza il metodo del modello per gestire il toggle
            $result = $post->toggleLike($id_utente);

            echo json_encode([
                'success' => true,
                'user_liked' => $result['user_liked'],
                'likes_count' => $result['likes_count'],
                'message' => $result['user_liked'] ? tr('Mi piace aggiunto!') : tr('Mi piace rimosso!')
            ]);

        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => tr('Errore: ') . $e->getMessage()
            ]);
        }
        break;
}
