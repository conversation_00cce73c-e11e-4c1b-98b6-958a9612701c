<?php
/*
 * OpenSTAManager: il software gestionale open source per l'assistenza tecnica e la fatturazione
 * Copyright (C) DevCode s.r.l.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

use Carbon\Carbon;
use Models\Module;
use Models\Plugin;

include_once __DIR__.'/../../core.php';

?><form action="" method="post" id="edit-form">
    <fieldset>
        <input type="hidden" name="backto" value="record-edit">
        <input type="hidden" name="op" value="update">

        <!-- DATI ANAGRAFICI -->
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title"><?php echo tr('Dati skill'); ?></h3>
            </div>

            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        {[ "type": "text", "label": "<?php echo tr('Nome');?>", "name": "nome", "value":"$nome$", "required":1 ]}
                    </div>
                    <div class="col-md-3">
                        {["type":"select", "label":"<?php echo tr('Statistica');?>", "name":"statistica", "value":"$statistica$", "values":"list=\"car_0\":\"<?php echo tr('Mischia');?>\",\"car_1\":\"<?php echo tr('Agilità');?>\",\"car_2\":\"<?php echo tr('Resilienza');?>\",\"car_3\":\"<?php echo tr('Vigilanza');?>\",\"car_4\":\"<?php echo tr('Ego');?>\",\"car_5\":\"<?php echo tr('Logica');?>\" " ]}
                    </div>
                    <div class="col-md-3">
                        {["type":"select", "label":"<?php echo tr('Sezione');?>", "name":"sezione", "value":"$sezione$", "values":"list=\"intellettuali\":\"<?php echo tr('Intellettuali');?>\",\"fisiche\":\"<?php echo tr('Fisiche');?>\",\"sociali\":\"<?php echo tr('Sociali');?>\" " ]}
                    </div>
                    <div class="col-md-2">
                        {[ "type": "checkbox", "label": "<?php echo tr('Visibile');?>", "name": "is_visible", "value":"$is_visible$" ]}
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        {[ "type": "textarea", "label": "<?php echo tr('Descrizione');?>", "name": "descrizione", "value":"$descrizione$" ]}
                    </div>
                </div>
            </div>
        </div>

         <!-- DATI ANAGRAFICI -->
         <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title"><?php echo tr('Livelli skill'); ?></h3>
            </div>

            <div class="card-body">
                <div class="row">
                    <div class="col-md-12">
                        <a class="btn btn-primary" onclick="addLivello();"><i class="fa fa-plus"></i> <?php echo tr('Aggiungi livello'); ?></a>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-12">
                        <table class="table table-bordered table-condensed">
                            <thead>
                                <tr>
                                    <th width="10%"><?php echo tr('Soglia'); ?></th>
                                    <th><?php echo tr('Descrizione'); ?></th>
                                    <th width="3%"></th>
                                </tr>
                            </thead>
                            <tbody>
                            <?php
                            foreach ($skill->livelli as $index => $livello) {
                                echo '
                                <tr>
                                    <td>
                                        {[ "type": "text", "name": "soglia_'.$index.'", "value": "'.$livello['soglia'].'", "min-value": 0, "onchange": "aggiornaInline('.$index.')" ]}
                                    </td>
                                    <td>
                                        {[ "type": "textarea", "name": "descrizione_'.$index.'", "value": "'.$livello['descrizione'].'", "onchange": "aggiornaInline('.$index.')" ]}
                                    </td>
                                    <td class="text-center">
                                        <a type="button" class="btn btn-danger" onclick="deleteLivello(this,'.$index.')">
                                            <i class="fa fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>';
                            }
                            ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </fieldset>
</form>

<a class="btn btn-danger ask" data-backto="record-list">
    <i id ="elimina" class="fa fa-trash"></i> <?php echo tr('Elimina'); ?>
</a>

<script>
    async function addLivello() {
        // Salvataggio via AJAX
        await salvaForm("#edit-form");
        $.ajax({
            url: globals.rootdir + "/actions.php",
            type: "POST",
            data: {
                id_module: globals.id_module,
                id_record: globals.id_record,
                op: "manage_livelli",
            },
            success: function (response) {
                location.reload();
            }
        });
    }

    function deleteLivello(button, idriga) {
        $.ajax({
            url: globals.rootdir + "/actions.php",
            type: "POST",
            data: {
                id_module: globals.id_module,
                id_record: globals.id_record,
                op: "delete_livello",
                idriga: idriga,
            },
            success: function (response) {
                location.reload();
            }
        });
    }

    function aggiornaInline(id) {
        content_was_modified = false;
        var soglia = input("soglia_"+ id).get();
        var descrizione = input("descrizione_"+ id).get();

        $.ajax({
            url: globals.rootdir + "/actions.php",
            type: "POST",
            data: {
                id_module: globals.id_module,
                id_record: globals.id_record,
                op: "manage_livelli",
                idriga: id,
                soglia: soglia,
                descrizione: descrizione,
            },
            success: function (response) {
                renderMessages();
            },
            error: function() {
                renderMessages();
            }
        });
    }
</script>